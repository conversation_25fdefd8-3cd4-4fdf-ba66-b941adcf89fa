import config from '@/config'
import http from '@/utils/request'

export default {
    message:{
        url: `${config.API_URL}/api/notifications/message`,
        name: '获取消息通知列表',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    notice:{
        url: `${config.API_URL}/api/notifications/notice`,
        name: '获取公告列表',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    noticeDetail:{
        url: `${config.API_URL}/sysapi/notice/one`,
        name: '获取公告详情',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    mesread:{
        url: `${config.API_URL}/api/notifications/mesRead`,
        name: '获取消息通知列表',
        post: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.post(this.url, params)
        }
    },
    all:{
        url: `${config.API_URL}/sysapi/notice/allNotice`,
        name: '获取全部公告',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    myPublish:{
        url: `${config.API_URL}/sysapi/notice/myPublish`,
        name: '获取我发布的公告',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    myDraft:{
        url: `${config.API_URL}/sysapi/notice/myDraft`,
        name: '获取我的草稿',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    publish:{
        url: `${config.API_URL}/sysapi/notice/publish`,
        name: '发布公告',
        post: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.post(this.url, params)
        }
    },
    del:{
        url: `${config.API_URL}/sysapi/notice/del`,
        name: '删除公告',
        post: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.post(this.url, params)
        }
    },
    unread:{
        url: `${config.API_URL}/api/notifications/unread`,
        name: '获取未读消息',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    readall:{
        url: `${config.API_URL}/api/notifications/readAll`,
        name: '设置全部已读',
        post: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.post(this.url, params)
        }
    }
}