<template>
	<div v-if="show === false" class="notOpen">
		<el-empty description="暂未开通名师工作室">
			<el-button type="primary">开通名师工作室</el-button>
		</el-empty>
	</div>
	<el-main v-else>
		<div class="article">
			<el-header>
				<div class="left-panel">
					<div class="left-panel-search">
						<el-form-item label="">
							<el-input
								v-model="params.name"
								placeholder="请输入标题搜索"
								style="width: 300px"
								clearable
								@input="search"
							></el-input>
						</el-form-item>
					</div>
				</div>
				<div class="right-panel">
					<el-button v-if="userId === 0" type="primary" icon="el-icon-promotion" @click="add">发布视频</el-button>
				</div>
			</el-header>
			<ul v-loading="loading" element-loading-text="数据加载中...">
				<li v-for="(item, index) in list.rows" :key="index">
					<el-image :src="item.cover_url" fit="cover">
						<template #error>
							<div class="image-slot">
								<img
									style="width: 90px; height: 90px; object-fit: cover"
									class="logo"
									src="@/assets/img/material_cover.png"
								/>
							</div>
						</template>
					</el-image>
					<div class="content">
						<div class="title" @click="showArticle(item)">
							<el-tag v-if="item.is_public === 1" type="success" size="small">公开</el-tag> {{ item.title }}
						</div>
						<div class="desc">
							<span class="time">{{ item.created_at }}</span>
							<span class="read">点赞：{{ item.like_num }}</span>
							<span class="read">收藏：{{ item.collect_num }}</span>
							<span class="read">评论：{{ item.comment_num }}</span>
						</div>
					</div>
					<div class="action">
						<el-button-group>
							<el-button v-if="userId === 0" type="primary" text size="small" @click="edit(item)">编辑</el-button>
							<el-button v-if="userId === 0" type="danger" text size="small" @click="deleteArticle(item)"
								>删除</el-button
							>
						</el-button-group>
					</div>
				</li>
			</ul>
			<div v-if="list.rows.length === 0">
				<el-empty description="暂无文章，去发表吧"></el-empty>
			</div>
		</div>
	</el-main>
	<el-footer>
		<el-pagination
			v-model:current-page="params.page"
			v-model:page-size="params.pageSize"
			background
			layout="total, sizes, prev, pager, next, jumper"
			:page-sizes="[10, 20, 30, 50]"
			:total="list.total"
			small
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		/>
	</el-footer>
	<el-drawer
		v-model="showDetailDialog"
		:title="currentData.title"
		direction="rtl"
		size="50%"
		class="articleDetail"
		append-to-body
		destroy-on-close
		:close-on-press-escape="false"
	>
		<Show v-model="currentData" :only-show="userId > 0"></Show>
	</el-drawer>
	<Save
		v-if="saveDialog"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="saveDialog = false"
	></Save>
</template>

<script>
import Show from '../compoments/show.vue'
import Save from '../compoments/save.vue'

export default {
	name: 'videoComponent',
	components: { Show, Save },
	inject: ['userId'],
	data() {
		return {
			params: {
				name: '',
				page: 1,
				pageSize: 10,
				is_video: 1,
				userId: this.userId
			},
			currentData: {},
			show: true,
			saveTitle: '发布',
			loading: false,
			saveData: {},
			list: {
				rows: [],
				total: 0
			},
			showDetailDialog: false,
			saveDialog: false
		}
	},
	created() {
		this.getList()
	},
	methods: {
		handleSizeChange(size) {
			this.params.pageSize = size
			this.getList()
		},
		handleCurrentChange(page) {
			this.params.page = page
			this.getList()
		},
		search() {
			this.params.page = 1
			this.getList()
		},
		showArticle(item) {
			this.currentData = item
			this.showDetailDialog = true
		},
		getList() {
			this.loading = true
			this.$API.famous.resources.list.get(this.params).then((res) => {
				this.loading = false
				if (res.code === 200) {
					this.list = res.data
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		add() {
			this.saveDialog = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', 1)
			})
		},
		edit(item) {
			this.saveDialog = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', 1).setData(item)
			})
		},
		handleSaveSuccess() {
			this.getList()
		},
		deleteArticle(item) {
			this.$confirm('确认删除这条数据?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$API.famous.resources.del
					.post({
						id: item.id,
						tenant_id: item.tenant_id,
						campus_id: item.campus_id
					})
					.then((res) => {
						if (res.code === 200) {
							this.$message({
								type: 'success',
								message: '删除成功!'
							})
							this.getList()
						}
					})
			})
		}
	}
}
</script>
<style scoped lang="scss">
.article {
	li {
		display: flex;
		border: 1px solid var(--el-border-color-light);
		padding-right: 15px;
		margin-bottom: 15px;

		.el-image {
			width: 90px;
			height: 90px;
			min-width: 90px;
			margin-right: 10px;
		}

		.action {
			padding-top: 10px;
			min-width: 150px;
			text-align: right;
			margin-left: auto;
		}

		.title {
			padding-top: 10px;
			overflow: hidden;
			font-size: 14px;
			flex: 1;
			display: -webkit-box;
			cursor: pointer;
			text-overflow: ellipsis; //属性规定当文本溢出包含元素时发生的事情  text-overflow: clip|ellipsis|string; (修剪/省略号/指定字符串)
			-webkit-line-clamp: 2;
			/*要显示的行数*/
			/* autoprefixer: off */
			-webkit-box-orient: vertical; //属性规定框的子元素应该被水平或垂直排列
			/* autoprefixer: on */
			font-weight: bold;
		}
		.content {
			display: flex;
			height: 90px;
			flex-direction: column;
			flex: 1;
		}
		.desc {
			color: var(--el-text-color-regular);
			font-size: 12px;
			padding-top: 15px;
			line-height: 35px;

			span {
				margin-right: 10px;
			}
		}
	}
}

.el-header {
	padding: unset;
	height: 45px;
	margin-bottom: 10px;
	border-bottom: unset;
}

.el-header > .right-panel {
	height: unset;
}
</style>
<style>
.articleDetail .el-drawer__header {
	margin-bottom: 10px;
}

.articleDetail .el-drawer__body {
	padding: 0 20px;
}
</style>
