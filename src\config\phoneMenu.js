export default {
	teacher: [
		{
			// inform
			label: '学校信息',
			mark: 'inform',
			children: [
				{
					icon: '/home/<USER>/notice.svg',
					label: '校园公告',
					path: '/subPackageHome/notice/index',
					mark: 'notice'
				},
				{
					icon: '/home/<USER>/news.svg',
					label: '消息通知',
					path: '/subPackageHome/news/index',
					mark: 'news'
				},
				{
					icon: '/home/<USER>/addressBook.png',
					label: '校园通讯录',
					path: '/subPackageHome/addressBook/index',
					mark: 'addressBook'
				},
				{ icon: '/home/<USER>/campusFile.png', label: '校区文件', path: '', mark: 'campusFile' }
			]
		},
		// aboutMe
		{
			label: '关于我的',
			mark: 'aboutMe',
			children: [
				{
					icon: '/home/<USER>/class.png',
					label: '任教班级',
					path: '/subPackageHome/myClass/index',
					mark: 'myClass'
				},
				{
					icon: '/home/<USER>/studentEva.png',
					label: '学生评教',
					path: '/subPackageHome/pupilEvaluation/index',
					mark: 'pupilEvaluation'
				},
				{
					icon: '/home/<USER>/work.png',
					label: '学生作业',
					path: '/subPackageHome/studentWork/index',
					mark: 'studentWork'
				},
				{ icon: '/home/<USER>/studentPayment.png', label: '学生缴费', path: '', mark: 'studentPayment' },
				{
					icon: '/home/<USER>/curriculum.png',
					label: '我的课表',
					path: '/subPackageHome/mySchedule/index',
					mark: 'mySchedule'
				},
				{ icon: '/home/<USER>/personalData.png', label: '个人资料', path: '', mark: 'personalData' },
				{ icon: '/home/<USER>/workRecords.png', label: '我的考勤', path: '', mark: 'workRecords' },
				{ icon: '/home/<USER>/myFile.png', label: '我的文件', path: '', mark: 'myFile' },
				{ icon: '/home/<USER>/myStuff.png', label: '我的物品', path: '', mark: 'myStuff' },
				{ icon: '/home/<USER>/myCert.png', label: '我的证书', path: '', mark: 'myCert' },
			]
		},
		{
			// daily
			label: '日常办公',
			mark: 'daily',
			children: [
				{
					icon: '/home/<USER>/approval.png',
					label: '流程审批',
					path: '/subPackageHome/apply/index',
					mark: 'apply'
				},
				{
					icon: '/home/<USER>/roomReservation.png',
					label: '场室预约',
					path: '/subPackageHome/studioSubscribe/index',
					mark: 'studioSubscribe'
				},
				{ icon: '/home/<USER>/repair.png', label: '校园报修', path: '', mark: 'repair' },
				{
					icon: '/home/<USER>/topicAlbum.svg',
					label: '课例专辑',
					path: '/subPackageHome/courseAlbum/index',
					mark: 'courseAlbum'
				},
				// {
				// 	icon: '/home/<USER>/examine.png',
				// 	label: '考试安排',
				// 	path: '/subPackageHome/examine/index',
				// 	mark: 'examine'
				// },
			]
		},
		{
			// administration
			label: '行政办公',
			mark: 'administration',
			children: [
				{
					icon: '/home/<USER>/studentRecords.png',
					label: '学生记录',
					path: '',
					mark: 'studentRecords'
				},
				{
					icon: '/home/<USER>/classRecords.png',
					label: '班级记录',
					path: '',
					mark: 'classRecords'
				},
				{
					icon: '/home/<USER>/dormitoryRecords.png',
					label: '宿舍记录',
					path: '',
					mark: 'dormitoryRecords'
				},
				{ icon: '/home/<USER>/schedule.png', label: '课表查询', path: '', mark: 'schedule' },
				{ icon: '/home/<USER>/dorm.png', label: '宿舍管理', path: '', mark: 'dorm' },
				{ icon: '/home/<USER>/asset.png', label: '资产管理', path: '', mark: 'asset' },
				{
					icon: '/home/<USER>/consumables.png',
					label: '耗材管理',
					path: '',
					mark: 'consumables'
				},
				{ icon: '/home/<USER>/room.png', label: '场室管理', path: '', mark: 'room' },
				{ icon: '/home/<USER>/car.png', label: '车辆管理', path: '', mark: 'car' },
				{
					icon: '/home/<USER>/smartDevice.png',
					label: '智能设备管理',
					path: '/subPackageHome/smartDevice/index',
					mark: 'smartDevice'
				},
				
			]
		},
		{
			// data screening
			label: '数据总览',
			mark: 'dataView',
			children: [
				{
					icon: '/home/<USER>/edu.png',
					label: '教务总览',
					path: '/subPackageHome/eduView/index',
					mark: 'eduView'
				},
				{
					icon: '/home/<USER>/generalOverview.png',
					label: '总务总览',
					path: '/subPackageHome/affView/index',
					mark: 'affView'
				},
				{
					icon: '/home/<USER>/personnel.png',
					label: '学生总览',
					path: '/subPackageHome/studentView/index',
					mark: 'studentView'
				},
				{
					icon: '/home/<USER>/campus.png',
					label: '校区总览',
					path: '/subPackageHome/campusView/index',
					mark: 'campusView'
				}
			]
		},
		{
			label: '应用广场',
			mark: 'application',
			children: [
				{
					icon: '/home/<USER>/lot.png',
					label: '智慧物联',
					path: '/subPackageLot/home/<USER>',
					mark: 'lot',
					children: [
						{
							label: '设备接入',
							mark: 'lotDeviceManage',
							children:[
								{
									icon: '/home/<USER>/roomReservation.png',
									label: '设备管理',
									path: `/subPackageLot/manage/device/index`,
									mark: 'lotDevice'
								},
								{
									icon: '/lot/manage/dataView.png',
									label: '数据总览',
									path: `/subPackageLot/manage/dataView/index`,
									mark: 'lotDataView'
								},
							]
						},
						{
							label: '规则中心',
							mark: 'lotRuleManage',
							children:[
								{
									icon: '/lot/manage/ruleLog.png',
									label: '规则记录',
									path: `/subPackageLot/manage/rule/log`,
									mark: 'lotRuleLog'
								},
								{
									icon: '/lot/manage/ruleSet.png',
									label: '规则管理',
									path: `/subPackageLot/manage/rule/index`,
									mark: 'lotRuleSet'
								},
							]
						},
						{
							label: '告警中心',
							mark: 'lotAlertManage',
							children:[
								{
									icon: '/lot/manage/alertLog.png',
									label: '告警记录',
									path: `/subPackageLot/manage/alert/log`,
									mark: 'lotAlertLog'
								},
								{
									icon: '/lot/manage/alertStatics.png',
									label: '告警统计',
									path: `/subPackageLot/manage/alert/statistics`,
									mark: 'lotAlertStatics'
								},
								{
									icon: '/lot/manage/alert.png',
									label: '告警规则',
									path: `/subPackageLot/manage/alert/index`,
									mark: 'lotAlert'
								},
							]
						},
						{
							label: '场景中心',
							mark: 'lotSceneManage',
							children:[
								{
									icon: '/home/<USER>/studentRecords.png',
									label: '场景日志',
									path: `/subPackageLot/manage/scene/log`,
									mark: 'lotSceneLog'
								},
								{
									icon: '/lot/manage/scene.png',
									label: '场景管理',
									path: `/subPackageLot/manage/scene/index`,
									mark: 'lotScene'
								}
							]
						},
					]
				}
				/* {
        icon: "/home/<USER>/psym.png",
        label: "数字心育",
        path: "",
      },*/
			]
		}
	],
	student: [
		{
			// register
			label: '报到缴费',
			mark: 'register',
			children: [
				{
					icon: '/home/<USER>/1.svg',
					mark: 'report',
					label: '学生报到',
					path: '/subPackageMyInfo/info/index?source=register&isShow=all'
				},
				{ icon: '/home/<USER>/2.svg', label: '学费缴费', mark: 'payFees', path: '/subPackageHome/payFees/index' }
			]
		},
		{
			// inform
			label: '学校通知',
      mark: 'inform',
			children: [
				{ icon: '/home/<USER>/1.svg', label: '学校公告',  mark: 'notice',path: '/subPackageHome/notice/index' },
				{ icon: '/home/<USER>/2.svg', label: '消息通知',  mark: 'news',path: '/subPackageHome/news/index' }
			]
		},
		{
			// grade
			label: '班级协同',
      mark: 'grade',
			children: [
				{ icon: '/home/<USER>/1.svg', label: '我的班级', mark: 'myClass', path: '/subPackageHome/myClass/index' },
				{ icon: '/home/<USER>/2.svg', label: '我的考勤',  mark: 'clockingIn',path: '/subPackageHome/clockingIn/index' },
				{ icon: '/home/<USER>/3.svg', label: '我的课表',  mark: 'mySchedule',path: '/subPackageHome/mySchedule/index' },
				{ icon: '/home/<USER>/4.svg', label: '课程专辑',  mark: 'courseAlbum',path: '/subPackageHome/courseAlbum/index' },
				{ icon: '/home/<USER>/famousSpace.png', label: '名师风采', mark: 'famousSpace', path: '/subPackageHome/famousSpace/index' },
				{ icon: '/home/<USER>/5.svg', label: '学生评教',  mark: 'pupilEvaluation',path: '/subPackageHome/pupilEvaluation/index' },
				{ icon: '/home/<USER>/6.svg', label: '在线作业',  mark: 'onlineWork',path: '/subPackageHome/onlineWork/index' },
				// {
				// 	icon: '/home/<USER>/examine.png',
				// 	label: '考试安排',
				// 	path: '/subPackageHome/examine/index',
				// 	mark: 'examine'
				// },
			]
		},
		{
			// flow
			label: '流程申请',
      mark: 'flow',
			children: [
				{ icon: '/home/<USER>/1.svg', label: '场室预约',  mark: 'studioSubscribe',path: '/subPackageHome/studioSubscribe/index' },
				{ icon: '/home/<USER>/2.svg', label: '学生请假',  mark: 'leaveRecord',path: '/subPackageHome/leaveRecord/index' }
			]
		},
		{
			// archives
			label: '档案信息',
      mark: 'archives',
			children: [
				{
					icon: '/home/<USER>/1.svg',
					label: '基本信息',
          mark: 'myInfo',
					path: '/subPackageMyInfo/info/index?source=info&isShow=all'
				},
				{
					icon: '/home/<USER>/2.svg',
					label: '宿舍信息',
          mark: 'dormInfo',
					path: '/subPackageMyInfo/info/index?source=dorm&isShow=dorm'
				},
				{ icon: '/home/<USER>/myCert.png', label: '我的证书', path: '', mark: 'myCert' },
				{ icon: '/home/<USER>/4.svg', label: '记录', mark: 'record',path: '' }
			]
		}
	]
}
