<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList?.length > 1" label="">
						<el-select v-model="params.campus_id" placeholder="校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="" style="margin-left: 15px">
						<cusSelectSemester
							v-model="params.semester_id"
							:params="params"
							:show-default-value="true"
							:width="'214px'"
							clearable
							style="margin-right: 15px"
						/>
						<!--
						<cusCascader v-model="params.semester_id" placeholder="请选择学期" :options="getSemester"></cusCascader>
-->
					</el-form-item>
					<el-form-item>
						<el-cascader
							v-model="params.class_id"
							:options="myClass.list"
							placeholder="请选择班级"
							:props="{ multiple: false, label: 'name', value: 'id', children: 'class_list', emitPath: false }"
							clearable
						></el-cascader>
					</el-form-item>
					<el-form-item label="" prop="begin_time">
						<el-date-picker
							v-model="params.time"
							type="daterange"
							unlink-panels
							range-separator="至"
							start-placeholder="'开始日期'"
							end-placeholder="'结束日期'"
							format="YYYY-MM-DD"
							value-format="YYYY-MM-DD"
						></el-date-picker>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<el-table-column label="学生姓名" prop="student_name" width="260" fixed="left"></el-table-column>
				<el-table-column label="学年学期" prop="academic_name" width="260">
					<template #default="scope"> {{ scope.row.academic_name }} - {{ scope.row.semester_name }} </template>
				</el-table-column>
				<el-table-column label="年级班级" prop="grade_name" width="260">
					<template #default="scope"> {{ scope.row.grade_name }} - {{ scope.row.class_name }} </template>
				</el-table-column>
				<el-table-column label="报道时间" prop="created_at"></el-table-column>
			</scTable>
		</el-main>
	</el-container>
</template>

<script setup>
import { ref, getCurrentInstance, reactive, computed, onMounted, watch } from 'vue'
import cusTom from '@/utils/cusTom'
import CusSelectSemester from '@/components/custom/cusSelectSemester.vue'

const { tenantId, campusId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
let CampusManagementList = reactive(campusInfo)
// 获取当前组件实例
const instance = getCurrentInstance()
// 获取全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
// params参数
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null,
		grade_id: null,
		class_id: null,
		begin_time: '',
		end_time: '',
		time: []
	}
}
let params = ref(defaultParams())
let table = ref()
onMounted(() => {
	getClassData()
})
// 班级列表
let myClass = reactive({
	list: []
})
// 学期
let getSemester = computed(() => {
	return cusTom.arrayToTree(
		semesterInfo.filter((v) => v.campus_id == params.value.campus_id),
		'value',
		'parent_id'
	)
})
// 监听校区id
watch(
	() => params.value.campus_id,
	() => {
		params.value.semester_id = null
		getClassData()
	}
)
// 监听学期id
watch(
	() => params.value.semester_id,
	() => {
		params.value.class_id = null
		getClassData()
	}
)
// 监听年级id
watch(
	() => params.value.grade_id,
	() => {
		params.value.class_id = null
	}
)
// 获取班级列表
const getClassData = async () => {
	let res = await globalPropValue.eduGradeClass.class.all.get(params.value)
	myClass.list = res.data
}
// 请求列表数据
const apiObj = ref(globalPropValue.recruitStudent.recruit.list_report)
// 搜索操作
const upsearch = () => {
	if (params.value.time && params.value.time.length > 0) {
		params.value.begin_time = params.value.time[0]
		params.value.end_time = params.value.time[1]
	}
	table.value.upData(params.value)
}
// 重置操作
const refresh = () => {
	params.value = defaultParams()
	upsearch()
}
</script>

<style scoped></style>
