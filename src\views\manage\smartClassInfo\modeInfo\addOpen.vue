<template>
	<el-drawer v-model="addVisible" title="新增开放日关联信息" size="30%" destroy-on-close>
		<el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
			<el-form-item label="内容类型" prop="content_type">
				<el-radio-group v-model="form.content_type" size="large">
					<el-radio label="1" :value="1">学校宣传内容</el-radio>
					<el-radio label="2" :value="2">班级成长照片</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="展示类型" prop="display_type">
				<el-radio-group v-model="form.display_type" size="large">
					<el-radio label="1" :value="1">图片</el-radio>
					<el-radio label="2" :value="2">视频</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.display_type == 2" label="封面图" prop="cover_url">
				<sc-upload
					v-model="form.cover_url"
					:width="120"
					:height="120"
					icon="el-icon-pictureFilled"
					title="封面图"
					:cropper="false"
					fileTypeTag="platoonHotspot"
				></sc-upload>
			</el-form-item>

			<el-form-item label="资源地址" prop="hotspot_url">
				<sc-upload-file v-model="form.hotspot_url" :limit="1" fileTypeTag="platoonHotspot"></sc-upload-file>
			</el-form-item>
			<el-form-item label="同步所有班牌" prop="is_all">
				<el-switch
					v-model="form.is_all"
					:active-value="1"
					:inactive-value="0"
					class="ml-2"
					style="--el-switch-on-color: #13ce66; --el-switch-off-color: #909399"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="close">取消</el-button>
			<el-button type="primary" @click="confirm">确定</el-button>
		</template>
	</el-drawer>
</template>
<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const addVisible = ref(false)
const current = ref('')
const form = ref({
	content_type: 1,
	display_type: 1,
	is_all: 0,
	cover_url: '',
	hotspot_url: ''
})
const formRef = ref()
const rules = reactive({
	cover_url: [{ required: true, message: '请上传封面图', trigger: 'change' }],
	display_type: [{ required: true, message: '请选择展示类型', trigger: 'change' }],
	content_type: [{ required: true, message: '请选择内容类型', trigger: 'change' }],
	hotspot_url: [{ required: true, message: '请上传资源', trigger: 'change' }]
})
const emit = defineEmits(['addSuccess'])
const confirm = () => {
	formRef.value.validate((valid) => {
		if (valid) {
			globalPropValue.platoonhotspot.save_related
				.post({
					...form.value,
					device_sn: current.value.sn,
					relevance_id: current.value.idString,
					tenant_id: tenantId,
					campus_id: campusId
				})
				.then((res) => {
					if (res.code === 200) {
						ElMessage.success('新增开放日信息成功')
						emit('addSuccess')
						form.value = {
							content_type: 1,
							display_type: 1,
							is_all: 0,
							cover_url: '',
							hotspot_url: ''
						}
						addVisible.value = false
					}
				})
				.catch((err) => {
					console.log(`新增开放日信息失败： ${err.message}`)
				})
		}
	})
}

const show = (val) => {
	current.value = val
	addVisible.value = true
}
const close = () => {
	addVisible.value = false
}

defineExpose({
	show,
	close
})
</script>
