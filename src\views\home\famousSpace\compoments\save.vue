<template>
	<el-drawer v-model="visible" :title="type[model]" destroy-on-close size="50%">
		<el-form ref="formref" :model="form" :rules="rules">
			<el-form-item label="标题" prop="title" label-width="100px">
				<el-input v-model="form.title" placeholder="请输入标题"></el-input>
			</el-form-item>
			<el-form-item label="封面" label-width="100px">
				<scUpload v-model="form.cover_url" fileTypeTag="famous"></scUpload>
			</el-form-item>
			<el-form-item v-if="form.is_video === 1" label="视频文件" prop="file">
				<scUploadFile
					v-model="form.url"
					:limit="1"
					fileTypeTag="famous"
					:multiple="false"
					:maxSize="500"
					accept="video/*"
				></scUploadFile>
			</el-form-item>
			<el-form-item label="内容描述" prop="description">
				<textEditor v-model:value="form.description" height="300px"></textEditor>
			</el-form-item>
			<el-form-item prop="is_public" label="是否公开" label-width="100px">
				<el-radio-group v-model="form.is_public">
					<el-radio :label="1">是</el-radio>
					<el-radio :label="-1">否</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="附件文件" prop="file">
				<scUploadFile v-model="form.files" :limit="9" fileTypeTag="famous" accept="*"></scUploadFile>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="save">保 存</el-button>
		</template>
	</el-drawer>
</template>
<script>
import { cloneDeep } from 'lodash'
import { ElMessage } from 'element-plus'
import textEditor from '@/components/textEditor'

export default {
	name: 'save',
	components: { textEditor },
	props: {
		modelValue: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			visible: false,
			type: {
				add: '发布',
				edit: '编辑'
			},
			mode: 'add',
			isVideo: -1,
			form: {
				is_video: -1,
				title: '',
				description: '',
				cover_url: '',
				url: '',
				is_public: -1,
				files: [],
				id: 0
			},
			rules: {
				title: [
					{
						required: true,
						message: '请输入标题',
						trigger: 'blur'
					}
				]
			},
			isSaveing: false
		}
	},
	created() {},
	methods: {
		save() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					let formData = cloneDeep(this.form)
					if (formData.files) {
						formData.files = this.form.files?.map((item) => ({
							name: item.name,
							url: item.url
						}))
					}
					formData.files = JSON.stringify(formData.files)
					this.isSaveing = true
					if (formData.id > 0) {
						await this.updateSuccess(formData)
					} else {
						await this.creatSuccess(formData)
					}
					this.isSaveing = false
				}
			})
		},
		creatSuccess(data) {
			this.$API.famous.resources.creat.post(data).then((res) => {
				if (res.code === 200) {
					ElMessage.success('成功')
					this.visible = false
					this.$emit('success')
				} else {
					ElMessage({ type: 'error', message: res.message })
				}
			})
		},
		updateSuccess(data) {
			this.$API.famous.resources.edit.post(data).then((res) => {
				if (res.code === 200) {
					ElMessage.success('成功')
					this.visible = false
					this.$emit('success')
				} else {
					ElMessage({ type: 'error', message: res.message })
				}
			})
		},
		//显示
		open(mode = 'add', isVideo) {
			this.mode = mode
			this.visible = true
			if (isVideo === 1) {
				this.isVideo = 1
				this.form.is_video = 1
			} else {
				this.isVideo = -1
				this.form.is_video = -1
			}
			return this
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			this.form.files=JSON.parse(this.form.files)
		}
	}
}
</script>
<style scoped></style>
