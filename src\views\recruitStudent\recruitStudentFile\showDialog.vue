<template>
	<el-dialog v-model="visible" title="查看详情" destroy-on-close width="30%">
		<template #header>
			<div class="content">
				<div class="title">
					<span>共{{ total }}人 |</span>
					<span>未读({{ Unread() }}人)</span>
					<span>已读({{ Read() }}人)</span>
				</div>
				<!--				<el-input class="content-inp" placeholder="请输入" :prefix-icon="Search"></el-input>-->
			</div>
		</template>
		<scTable ref="showTableRef" :apiObj="apiObj" :params="form" hideDo>
			<el-table-column type="index"></el-table-column>
			<el-table-column label="姓名" prop="share_user"></el-table-column>
			<el-table-column label="状态" prop="read_status">
				<template #default="{ row }">
					<el-tag v-if="row.read_status === -1" type="danger">未读</el-tag>
					<el-tag v-else type="success">已读</el-tag>
				</template>
			</el-table-column>
<!--			<el-table-column label="操作">
				<template #default="{ row }">
					<el-button type="primary" text size="small" @click="Onshow(row)">设置已读</el-button>
				</template>
			</el-table-column>-->
		</scTable>
	</el-dialog>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params'])
let visible = ref(false)
let showTableRef = ref(null)
let total = ref(0)
const data = ref([])
const form = ref({
	tenant_id: props.params.tenant_id,
	campus_id: props.params.campus_id,
	share_id: null
})
const apiObj = ref(globalPropValue.fileManagement.file.file_share_details)

const Unread = () => {
	return data.value.filter((item) => item.read_status === -1).length
}
const Read = () => {
	return data.value.filter((item) => item.read_status === 1).length
}
const open = (id) => {
	form.value.share_id = id
	visible.value = true
	setTimeout(() => {
		total.value = showTableRef.value.total
		data.value = showTableRef.value.tableData
	}, 200)
}
// 查看事件
/*const Onshow = async (row) => {
	await globalPropValue.fileManagement.affairFile.file_share_set_read.post({
		tenant_id: props.params.tenant_id,
		campus_id: props.params.campus_id,
		share_id: row.id,
		file_id: row.file_id
	})
	showTableRef.value.update(form.value)
}*/

defineExpose({
	open
})
</script>
<style scoped lang="scss">
.content {
	display: flex;
	justify-content: center;
	align-items: center;
	.title {
		flex: 1;
		display: flex;
		gap: 15px;
		justify-content: center;
		align-items: center;
		background: #f6f8fa;
		color: #919191;
		height: 30px;
		margin-right: 8px;
	}
	.content-inp {
		flex: 1;
		margin-right: 12px;
	}
}
</style>
