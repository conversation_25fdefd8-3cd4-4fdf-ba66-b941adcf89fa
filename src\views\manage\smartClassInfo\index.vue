<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item label="">
						<el-select
							v-if="CampusManagementList.length > 1"
							v-model="params.campus_id"
							placeholder="校区"
							filterable
							style="margin-right: 15px"
						>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<cusSelectField v-model="params.room" :multiple="false" placeholder="请选择场室"> </cusSelectField>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.device_sn" placeholder="请输入设备SN搜索" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upSearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="reset">重置</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :apiObj="apiObj" :params="params" :hideDo="true">
				<el-table-column label="设备ID" prop="device_id_str" width="160"></el-table-column>
				<el-table-column label="设备SN" prop="device_sn" width="160"></el-table-column>
				<el-table-column label="班牌模式" prop="device_mode" width="150">
					<!-- 1 上课模式 2 考试模式 3 迎宾模式--开放日模式 4 迎宾模式--领导视察模式 5 霸屏模式 -->
					<template #default="{ row }">
						<el-tag v-if="row.device_mode == 1">上课模式</el-tag>
						<el-tag v-if="row.device_mode == 2">考试模式</el-tag>
						<el-tag v-if="row.device_mode == 3">开放日模式</el-tag>
						<el-tag v-if="row.device_mode == 4">领导视察模式</el-tag>
						<el-tag v-if="row.device_mode == 5">霸屏模式</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="所在场室" prop="room_info" width="180">
					<template #default="{ row }">
						{{ row.room_info.name }}
					</template>
				</el-table-column>
				<el-table-column label="绑定地域编码" prop="area_code" width="150">
					<template #default="{ row }">
						{{ row.area_code }}
						<el-button v-if="row.area_code" text type="primary" size="small" @click="changeDeviceArea(row)"
							>更换</el-button
						>
						<el-button v-else text type="primary" size="small" @click="changeDeviceArea(row)">设置</el-button>
					</template>
				</el-table-column>
				<el-table-column label="操作" min-width="400">
					<template #default="{ row }">
						<el-button text type="primary" size="small" @click="changeDeviceMode(row)">更换班牌模式</el-button>
						<el-button text type="primary" size="small" @click="table_show_baseMap(row)">班牌背景图</el-button>
						<el-button
							v-if="row.device_mode == 1 || row.device_mode == 2"
							text
							type="primary"
							size="small"
							@click="table_show_tab(row)"
							>班牌Tab配置</el-button
						>
						<el-dropdown type="text" trigger="hover">
							<span class="el-dropdown-link">
								班牌信息管理
								<el-icon class="el-icon--right">
									<el-icon-arrow-down />
								</el-icon>
							</span>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item command="hot" @click.native="table_show(row)">热点信息管理</el-dropdown-item>
									<el-dropdown-item command="open" @click.native="table_show_open(row)"
										>开放日信息管理</el-dropdown-item
									>
									<el-dropdown-item command="visit" @click.native="table_show_visit(row)"
										>领导视察信息管理</el-dropdown-item
									>
									<el-dropdown-item command="screen" @click.native="table_show_screen(row)"
										>霸屏信息管理</el-dropdown-item
									>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
						<!--						<el-button text type="primary" size="small" @click="table_show(row)">热点信息管理</el-button>
						<el-button text type="primary" size="small" @click="table_show_open(row)">开放日信息管理</el-button>
						<el-button text type="primary" size="small" @click="table_show_visit(row)">领导视察信息管理</el-button>
						<el-button text type="primary" size="small" @click="table_show_screen(row)">霸屏信息管理</el-button>-->
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<el-dialog v-model="showArea" title="更换绑定地域" width="500">
		<el-form ref="formRef" :model="currentInfo" label-width="100px" :rules="rules">
			<el-form-item label="地域编码" prop="area_code">
				<el-input v-model="currentInfo.area_code" placeholder="请输入地域编码"></el-input>
			</el-form-item>
			<el-form-item label="同步所有班牌" prop="is_all">
				<el-switch
					v-model="currentInfo.is_all"
					:active-value="1"
					:inactive-value="0"
					class="ml-2"
					style="--el-switch-on-color: #13ce66; --el-switch-off-color: #909399"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click=";(showArea = false), refresh()">取 消</el-button>
			<el-button type="primary" @click="changeAreaSubmit">确 定</el-button>
		</template>
	</el-dialog>

	<el-dialog v-model="showMode" title="更换班牌模式" width="500">
		<el-form ref="formRef" :model="currentInfo" label-width="100px" :rules="rules">
			<el-form-item label="班牌模式" prop="device_mode">
				<el-select v-model="currentInfo.device_mode" placeholder="请选择班牌模式">
					<el-option label="上课模式" :value="1"></el-option>
					<el-option label="考试模式" :value="2"></el-option>
					<el-option label="开放日模式" :value="3"></el-option>
					<el-option label="领导视察模式" :value="4"></el-option>
					<el-option label="霸屏模式" :value="5"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item v-if="currentInfo.device_mode === 5" label="持续时间" required>
				<el-col :span="11">
					<el-form-item prop="start_time">
						<el-date-picker
							v-model="currentInfo.start_time"
							type="datetime"
							placeholder="请选择开始时间"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
							style="width: 100%"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="2" style="text-align: center">
					<span class="text-gray-500">-</span>
				</el-col>
				<el-col :span="11">
					<el-form-item prop="end_time">
						<el-date-picker
							v-model="currentInfo.end_time"
							type="datetime"
							placeholder="请选择结束时间"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
							style="width: 100%"
						/>
					</el-form-item>
				</el-col>
			</el-form-item>
			<el-form-item label="同步所有班牌" prop="is_all">
				<el-switch
					v-model="currentInfo.is_all"
					:active-value="1"
					:inactive-value="0"
					class="ml-2"
					style="--el-switch-on-color: #13ce66; --el-switch-off-color: #909399"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click=";(showMode = false), refresh()">取 消</el-button>
			<el-button type="primary" @click="changeModeSubmit">确 定</el-button>
		</template>
	</el-dialog>
	<info ref="infoRef" @refresh="refresh"></info>
	<screenInfo ref="screenInfoRef" @refresh="refresh"></screenInfo>
	<baseMap ref="baseMapRef" @refresh="refresh"></baseMap>
	<visitInfo ref="visitInfoRef" @refresh="refresh"></visitInfo>
	<openInfo ref="openInfoRef" @refresh="refresh"></openInfo>
	<tabSet ref="baseTabRef" @refresh="refresh"></tabSet>
</template>
<script setup>
import info from './platoonhotspot/info.vue'
import baseMap from './modeInfo/baseMap.vue'
import tabSet from './modeInfo/tabSet.vue'
import screenInfo from './modeInfo/screenInfo.vue'
import visitInfo from './modeInfo/visitInfo.vue'
import openInfo from './modeInfo/openInfo.vue'
import cusTom from '@/utils/cusTom'
import { ElMessageBox, ElMessage } from 'element-plus'
const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const CampusManagementList = ref(campusInfo)
const defaultParams = () => {
	return {
		campus_id: campusId,
		tenant_id: tenantId,
		room_id: null,
		room: null,
		device_sn: null
	}
}
const params = ref(defaultParams())
const apiObj = ref(globalPropValue.platoonhotspot.list)
const table = ref(null)
const infoRef = ref(null)
const baseMapRef = ref(null)
const baseTabRef = ref(null)
const showMode = ref(false)
const showArea = ref(false)
const formRef = ref()

const rules = reactive({
	device_mode: [{ required: true, message: '请选择班牌模式', trigger: 'change' }],
	start_time: [{ type: 'date', required: true, message: '请选择开始时间', trigger: 'change' }],
	end_time: [{ type: 'date', required: true, message: '请选择结束时间', trigger: 'change' }]
})
const currentInfo = ref({
	tenant_id: null,
	device_sn: null,
	campus_id: null,
	device_mode: null,
	start_time: null,
	end_time: null,
	area_code: null,
	is_all: 0
})

const changeAreaSubmit = () => {
	if (!currentInfo.value.area_code) {
		ElMessage.error('请输入地域编码')
		return
	}
	globalPropValue.device.changePlatoonArea.post(currentInfo.value).then((res) => {
		if (res.code == 200) {
			ElMessage.success('更换绑定地域成功')
			showArea.value = false
			refresh()
		}
	})
}

const changeModeSubmit = () => {
	//验证
	formRef.value.validate((valid) => {
		if (valid) {
			console.log(currentInfo.value)
			globalPropValue.device.changePlatoonMode.post(currentInfo.value).then((res) => {
				if (res.code == 200) {
					ElMessage.success('更换班牌模式成功')
					showMode.value = false
					refresh()
				}
			})
		}
	})
}
const upSearch = () => {
	params.value.page = 1
	if (params.value.room) {
		params.value.room_id = params.value.room.id
	}
	console.log(params.value)
	table.value.upData(params.value)
}

const refresh = () => {
	/*params.value = defaultParams()
	params.value.room = null*/
	table.value.refresh(params.value)
}

const reset = () => {
	params.value = defaultParams()
	params.value.room = null
	table.value.upData(params.value)
}

// 热点信息
const table_show = (row) => {
	console.log(row)
	infoRef.value.show(row.device_sn)
}
// 班牌背景图
const table_show_baseMap = (row) => {
	console.log(row)
	baseMapRef.value.show(row)
}
// 班牌TAB
const table_show_tab = (row) => {
	console.log(row)
	baseTabRef.value.show(row)
}
// 开放日信息
const openInfoRef = ref()
const table_show_open = (row) => {
	console.log(row)
	openInfoRef.value.show(row.device_sn)
}
// 领导视察信息
const visitInfoRef = ref()
const table_show_visit = (row) => {
	console.log(row)
	visitInfoRef.value.show(row.device_sn)
}
// 霸屏信息
const screenInfoRef = ref()
const table_show_screen = (row) => {
	console.log(row)
	screenInfoRef.value.show(row.device_sn)
}

const changeDeviceMode = (row) => {
	showMode.value = true
	currentInfo.value = {
		tenant_id: row.tenant_id,
		device_sn: row.device_sn,
		campus_id: row.campus_id,
		device_mode: row.device_mode,
		start_time: row.start_time,
		end_time: row.end_time
	}
}

const changeDeviceArea = (row) => {
	showArea.value = true
	currentInfo.value = {
		tenant_id: row.tenant_id,
		device_sn: row.device_sn,
		campus_id: row.campus_id,
		area_code: row.area_code
	}
}
</script>
<style scoped>
.el-dropdown-link {
	cursor: pointer;
	color: var(--el-color-primary);
	display: flex;
	align-items: center;
	font-size: 12px;
	line-height: 24px;
}
</style>
