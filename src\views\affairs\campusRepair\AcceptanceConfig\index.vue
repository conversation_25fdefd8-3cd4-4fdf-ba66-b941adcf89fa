<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-plus" @click="add">新增人员</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
				<el-table-column l prop="img" width="80">
					<template #default="scope">
						<div v-if="scope.row.user">
							<cusHead
								loading="lazy"
								:lazy="true"
								fit="contain"
								style="width: 50px; height: 50px"
								:src="scope.row.user.img"
								:preview-src-list="[scope.row.user.img]"
								preview-teleported
							>
							</cusHead>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="人员" prop="name" width="150">
					<template #default="scope">
						<div v-if="scope.row.user">
							{{ scope.row.user.name }}
						</div>
					</template>
				</el-table-column>

				<el-table-column label="总务报修受理" prop="affairs" align="center" >
					<template #default="scope">
						<el-tag v-if="scope.row.affairs === 1" type="success">是</el-tag>
						<el-tag v-else type="info">否</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="信息化报修受理" prop="information" align="center" >
					<template #default="scope">
						<el-tag v-if="scope.row.information === 1" type="success">是</el-tag>
						<el-tag v-else type="info">否</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" fixed="right" width="150" align="left">
					<template #default="scope">
						<el-button-group>
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, repairTypeMap } = cusTom.getBaseQuery()
import saveDialog from './save.vue'
import cusHead from '@/components/custom/cusStaffHead.vue'
const defaultData = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		name: null
	}
}

export default {
	name: '',
	components: { cusHead, saveDialog },
	data() {
		return {
			params: defaultData(),
			apiObj: this.$API.campusRepair.acceptance.list,
			dialog: {
				save: false
			},
			repairTypeMap,
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					}
				]
			}
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.upsearch()
			}
		}
	},
	methods: {
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		refresh() {
			this.params = defaultData()
			this.upsearch()
		},
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.campusRepair.acceptance.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		handleSaveSuccess() {
			this.$refs.table.upData(this.params)
		}
	}
}
</script>

<style scoped></style>
