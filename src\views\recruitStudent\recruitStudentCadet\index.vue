<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList?.length > 1" label="">
						<el-select v-model="params.campus_id" placeholder="请选择校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="请输入学生姓名" clearable></el-input>
					</el-form-item>
					<el-form-item label="" prop="entrance_year">
						<el-date-picker
							v-model="params.entrance_year"
							placeholder="请选择入学年份"
							type="year"
							format="YYYY"
							value-format="YYYY"
						/>
					</el-form-item>

					<el-form-item label="" style="margin-left: 15px">
						<cusSelectSemester
							v-model="params.semester_id"
							:params="params"
							:show-default-value="true"
							:width="'214px'"
							clearable
							style="margin-right: 15px"
						/>
						<!--
						<cusCascader v-model="params.semester_id" placeholder="请选择学期" :options="getSemester"></cusCascader>
-->
					</el-form-item>
					<el-form-item>
						<el-cascader
							v-model="params.class_id"
							:options="myClass.list"
							placeholder="请选择班级"
							:props="{ multiple: false, label: 'name', value: 'id', children: 'class_list', emitPath: false }"
							clearable
						></el-cascader>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
					<el-button type="success" icon="el-icon-plus" @click="add">新增学员</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<el-table-column label="姓名" prop="student_name" width="100" fixed="left"></el-table-column>
				<!-- <el-table-column label="学号" prop="serial_number" width="150"></el-table-column> -->
				<el-table-column label="头像" prop="user_head" width="100" align="center">
					<template #default="scope">
						<cusHead
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.user_head"
							:preview-src-list="[scope.row.user_head]"
							preview-teleported
						>
						</cusHead>
					</template>
				</el-table-column>
				<el-table-column label="性别" prop="sex" width="70" :formatter="formatterSex"></el-table-column>
				<el-table-column label="身份证号" prop="idcard" width="170"></el-table-column>
				<el-table-column label="招生名称" prop="plan_name" width="170"></el-table-column>
				<el-table-column label="状态" prop="status" width="170">
					<template #default="{ row }">
						<el-tag :type="types[row.status]">{{ statusVal[row.status] }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="手机号" prop="phone" width="170"></el-table-column>
				<el-table-column label="入学学年" prop="entrance_year" width="170"></el-table-column>
				<el-table-column label="学年学期" prop="academic_name" width="170">
					<template #default="{ row }"> {{ row.academic_name }} - {{ row.semester_name }} </template>
				</el-table-column>
				<el-table-column label="年级班级" prop="grade_name" width="170">
					<template #default="{ row }"> {{ row.grade_name }} - {{ row.class_name }} </template>
				</el-table-column>
				<el-table-column label="邮箱" prop="email" width="170"></el-table-column>
				<el-table-column label="手机" prop="phone" width="170"></el-table-column>
				<el-table-column label="身份证" prop="idcard" width="170"></el-table-column>
				<el-table-column label="政治面貌" prop="political">
					<template #default="scope">
						{{ formData(politicalMap, scope.row.political) }}
					</template>
				</el-table-column>
				<el-table-column label="民族" prop="nation" width="120">
					<template #default="scope">
						{{ formData(nationMap, scope.row.nation) }}
					</template>
				</el-table-column>
				<el-table-column label="地址" prop="address" width="180"></el-table-column>
				<el-table-column label="出生日期" prop="birthdate" width="170"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="210">
					<template #default="scope">
						<el-button-group>
							<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm
								v-if="scope.row.status === 1"
								title="确定删除吗？"
								@confirm="table_del(scope.row, scope.$index)"
							>
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<saveDrawer ref="drawerRef" :params="params" @success="habdlerConfirm"></saveDrawer>
</template>

<script setup>
import { ref, reactive, computed, getCurrentInstance, watch, onMounted, nextTick } from 'vue'
import cusTom from '@/utils/cusTom'
import saveDrawer from './save.vue'
import { ElMessage } from 'element-plus'
import ScTable from '@/components/scTable/index.vue'
import cusHead from '@/components/custom/cusStudentHead.vue'
import CusSelectSemester from '@/components/custom/cusSelectSemester.vue'

/**
 * campusInfo 校区 默认列表
 * semesterInfo学期 学年默认列表
 */
const { campusId, tenantId, campusInfo, semesterInfo, nationMap, politicalMap } = cusTom.getBaseQuery()
let CampusManagementList = reactive(campusInfo)
const defaultParams = () => {
	return {
		name: '',
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null,
		grade_id: null,
		class_id: null,
		entrance_year: null,
		plan_id: null,
		status: null
	}
}
// 获取当前组件实例
const instance = getCurrentInstance()
// 获取全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
// 表单字段
let params = ref(defaultParams())
let table = ref()
let drawerRef = ref()
// status
let statusVal = ref({ 1: '预录入', 2: ' 信息已确定', 3: '已报到', 4: '已入学', 5: '已离校' })
// type
let types = ref({ 1: 'warning', 2: 'info', 3: 'success', 4: '', 5: 'info' })
// 获取学员列表
let apiObj = ref(globalPropValue.recruitStudent.recruit.cadet_list)
// 班级列表
let myClass = reactive({
	list: []
})
// 年级列表
let myGrade = reactive({
	list: []
})
let selectionArr = ref([])
// 学年
// 学期
let getSemester = computed(() => {
	return cusTom.arrayToTree(
		semesterInfo.filter((v) => v.campus_id === params.value.campus_id),
		'value',
		'parent_id'
	)
})
// 班级
let getClass = computed(() => {
	return myClass.list.filter(
		(v) => v.parent_id !== 0 && v.grade_id === params.value.grade_id && v.campus_id === params.value.campus_id
	)
})
onMounted(() => {
	getClassData()
})
// 监听校区id
watch(
	() => params.value.campus_id,
	() => {
		params.value.semester_id = null
		getClassData()
	}
)
// 监听学期id
watch(
	() => params.value.semester_id,
	() => {
		params.value.class_id = null
		getClassData()
	}
)
watch(
	() => params.value.grade_id,
	() => {
		params.value.class_id = null
	}
)
// 获取班级列表
const getClassData = async () => {
	let res = await globalPropValue.eduGradeClass.class.all.get(params.value)
	myClass.list = res.data
}
const formatterSex = (row) => {
	if (row.sex === 1) {
		return '男'
	} else if (row.sex === 2) {
		return '女'
	} else {
		return '未知'
	}
}
//数据回显格式化
const formData = (arr, val) => {
	return arr.find((v) => v.value === val)?.name || val
}
// 搜索操作
const upsearch = () => {
	table.value.upData(params.value)
}
// 重置操作
const refresh = () => {
	params.value = defaultParams()
	upsearch()
}
// 新增操作
const add = () => {
	nextTick(() => {
		drawerRef.value.visible = true
		drawerRef.value.open('add')
	})
}
// 编辑操作
const table_edit = (row) => {
	nextTick(() => {
		drawerRef.value.visible = true
		drawerRef.value.open('edit')
		drawerRef.value.setData(row)
	})
}
// 查看操作
const table_show = (row) => {
	drawerRef.value.visible = true
	nextTick(() => {
		drawerRef.value.open('show')
		drawerRef.value.setData(row)
	})
}
// 删除操作
const table_del = async (row) => {
	const reqData = { id: row.id, campus_id: row.campus_id, tenant_id: row.tenant_id, status: row.status }
	const res = await globalPropValue.recruitStudent.recruit.del_cadet.post(reqData)
	if (res.code === 200) {
		upsearch()
		ElMessage({ type: 'success', message: '删除成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
// 表格选择回调
const selectionChange = (selection) => {
	selectionArr.value = selection
}
// 自定义事件
const habdlerConfirm = (data, mode) => {
	if (mode === 'add') {
		upsearch()
	} else {
		table.value.refresh()
	}
}
</script>

<style lang="scss" scoped></style>
