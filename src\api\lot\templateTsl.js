import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/lot/templateTsl/list`,
		name: '获取物模型列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	listPage: {
		url: `${config.API_URL}/lot/templateTsl/listPage`,
		name: '获取物模型列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/lot/templateTsl/save`,
		name: '新增物模型/修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/lot/templateTsl/del`,
		name: '删除物模型',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	import: {
		url: `${config.API_URL}/lot/templateTsl/import`,
		name: '导入物模型',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	one: {
		url: `${config.API_URL}/lot/templateTsl/one`,
		name: '获取单个物模型',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	status: {
		url: `${config.API_URL}/lot/templateTsl/changeStatus`,
		name: '修改物模型状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	}
}
