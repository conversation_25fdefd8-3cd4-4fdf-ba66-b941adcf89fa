<template>
	<el-container v-loading="loading">
		<el-header>
			<el-page-header
				:content="`${deviceData?.device_name} （${deviceData?.device_key}）`"
				@back="goBack"
			></el-page-header>
		</el-header>
		<div v-if="deviceData" class="productInfo">
			<div class="productInfo-img">
				<el-image
					style="width: 150px; height: 150px"
					fit="contain"
					:src="deviceData.device_img"
					:preview-src-list="[deviceData.device_img]"
				>
					<template #error>
						<div class="image-slot">
							<el-icon>
								<sc-icon-psyResource />
							</el-icon>
						</div>
					</template>
				</el-image>
			</div>
			<div class="productInfo-content">
				<h2>
					{{ deviceData.device_name }}
				</h2>
				<el-row>
					<el-col :span="12">
						<el-form-item label="设备编号：">{{ deviceData.device_key }}</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="设备状态：">
							<sc-status-indicator :type="deviceStatusType[deviceData.status]"></sc-status-indicator>
							&nbsp;{{ enumConfig.deviceStatusMap.find((item) => item.value === deviceData.status)?.name }}
							&nbsp;
							<!---->

							<el-button
								v-if="deviceData.status !== 2 && deviceData.product_info?.node_type === 2"
								size="small"
								type="warning"
								@click="activation"
								>入网激活
							</el-button>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="所属产品：">
							<el-link type="primary" :underline="false" @click="showProduct(deviceData)">{{
								deviceData.product_info?.product_name
							}}</el-link>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="节点类型：">
							{{ enumConfig.nodeTypeMap.find((item) => item.value === deviceData.product_info?.node_type)?.name }}
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="设备位置：">
							{{ deviceData.room_info?.name }}
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="安装位置：">
							{{ deviceData.install_location }}
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="创建时间：">
							{{ deviceData.created_at }}
						</el-form-item>
					</el-col>

					<el-col :span="12">
						<el-form-item label="最近在线时间：">{{ deviceData.last_online_time }}</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="mac地址：">{{ deviceData.device_mac }}</el-form-item>
					</el-col>
					<el-col v-if="deviceData.product_info?.node_type == 1" :span="12">
						<el-form-item label="IP地址：">{{ deviceData.ip }}</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="设备版本：">{{ deviceData.device_version }}</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="模组标识号：">{{ deviceData.device_model_id }}</el-form-item>
					</el-col>
					<el-col :span="20">
						<el-form-item label="设备描述：">{{ deviceData.desc }}</el-form-item>
					</el-col>
				</el-row>
			</div>
			<div class="refresh">
				<el-button icon="el-icon-refresh" circle :loading="loading" @click="getDeviceInfo"></el-button>
			</div>
		</div>
		<div v-if="deviceData" class="productMain">
			<el-tabs v-model="activeName" class="detailTab" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
			<component :is="currComponent.component" :deviceInfo="deviceData" @refresh="getDeviceInfo"></component>
		</div>
	</el-container>
	<activation v-if="activationStatus" ref="activationDialog" @success="activationSuccess"></activation>
</template>

<script>
import cusTom from '@/utils/cusTom'
import properties from '../properties'
import activation from './activation.vue'
import panel from '../panel'
import service from '../service'
import log from '../log'
import events from '../events'
import debug from '../debug'
import subDevice from '../subDevice'

const { tenantId } = cusTom.getBaseQuery()
export default {
	name: 'templateList',
	data() {
		return {
			activeName: 'properties',
			currComponent: {},
			tabs: [
				{
					name: 'properties',
					label: '设备属性',
					component: properties
				},
				{
					name: 'events',
					label: '事件记录',
					component: events
				},
				{
					name: 'service',
					label: '服务记录',
					component: service
				},
				{
					name: 'log',
					label: '设备日志',
					component: log
				},
				{
					name: 'debug',
					label: '设备调试',
					component: debug
				}
			],
			deviceStatusType: {
				1: 'info',
				2: 'success',
				3: 'info',
				4: 'warning',
				5: 'danger'
			},
			enumConfig: {},
			deviceData: null,
			activationStatus: false,
			loading: false,
			device_id: 0,
			tenant_id: 0,
			campus_id: 0
		}
	},
	components: { activation },
	watch: {},
	created() {
		this.device_id = this.$route.query.id
		this.tenant_id = tenantId
		this.campus_id = this.$route.query.campus_id

		this.getDeviceInfo()
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	computed: {},
	methods: {
		showProduct(deviceData) {
			this.$router.push({
				path: '/lot/product/detail',
				query: {
					id: deviceData.product_id
				}
			})
		},
		activation() {
			this.activationStatus = true
			this.$nextTick(() => {
				this.$refs.activationDialog.open(this.deviceData)
			})
		},
		activationSuccess() {
			this.activationStatus = false
			this.getDeviceInfo()
		},
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
		},
		getDeviceInfo() {
			this.loading = true
			this.$LotApi.device.one
				.get({
					id: this.device_id,
					tenant_id: this.tenant_id,
					campus_id: this.campus_id
				})
				.then((res) => {
					this.loading = false
					if (res.code === 200) {
						this.deviceData = res.data
						if (this.deviceData.product_info?.panel_type > 0) {
							let index = this.tabs.findIndex((item) => item.name === 'panel')
							if (index === -1) {
								this.tabs.splice(1, 0, {
									name: 'panel',
									label: '控制面板',
									component: panel
								})
							}
						}
						if (this.deviceData.product_info?.node_type === 1) {
							let index = this.tabs.findIndex((item) => item.name === 'subDevice')
							if (index === -1) {
								this.tabs.splice(4, 0, {
									name: 'subDevice',
									label: '子设备',
									component: subDevice
								})
							}
						}
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
		},
		goBack() {
			this.$router.back()
		}
	}
}
</script>

<style scoped lang="scss">
.pannel {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.el-header {
	display: block;

	.el-divider--horizontal {
		margin: 10px 0;
	}
}

.productInfo {
	margin-top: 15px;
	background: var(--el-bg-color-overlay);
	padding: 15px;
	border-radius: 6px;
	display: flex;
}

.productMain {
	margin-top: 15px;
	padding: 5px 15px 15px 15px;
	background: var(--el-bg-color-overlay);
	border-radius: 6px;
}

.image-slot {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	border-radius: 6px;
	height: 100%;
	background: var(--el-color-primary-light-9);
	color: var(--el-color-primary-light-2);
	font-size: 30px;
}

.image-slot .el-icon {
	font-size: 30px;
}

.productInfo-img {
	width: 150px;
	height: 150px;
	border-radius: 6px;
}

.productInfo-content {
	padding-left: 20px;
	flex: 1;

	h2 {
		font-size: 18px;
		margin-bottom: 8px;
	}
}
.refresh {
	width: 30px;
}

:deep(.el-form-item--default) {
	margin-bottom: unset !important;

	.el-form-item__content {
		word-break: break-all !important;
		color: var(--el-text-color-secondary);
	}
}
:deep(.detailTab) {
	.el-tabs__item {
		font-size: 14px;
		font-weight: bold;
	}
}

/*:deep(.el-descriptions__label:not(.is-bordered-label)) {
	margin-right: unset !important;
}*/
</style>
