<template>
	<el-drawer v-model="visible" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'">
			<el-row>
				<el-col :span="12">
					<el-form-item label="名称" prop="student_name">
						<el-input v-model="form.student_name" placeholder="请输入名称" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item label="头像" prop="user_head">
				<scUpload v-model="form.user_head"></scUpload>
			</el-form-item>

			<el-row>
				<el-col :span="12">
					<el-form-item label="性别" prop="sex">
						<el-select v-model="form.sex" placeholder="请选择" filterable clearable>
							<el-option v-for="item in sexMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="身份证号" prop="idcard">
						<el-input v-model="form.idcard" placeholder="请输入身份证号" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row>
				<el-col :span="12">
					<el-form-item label="手机号" prop="phone">
						<el-input v-model="form.phone" placeholder="请输入手机号" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="邮箱" prop="email">
						<el-input v-model="form.email" placeholder="请输入邮箱" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row>
				<el-col :span="12">
					<el-form-item label="政治面貌" prop="political">
						<el-select v-model="form.political" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in politicalMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="民族" prop="nation">
						<el-select v-model="form.nation" placeholder="请选择" filterable clearable>
							<el-option v-for="item in nationMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row>
				<el-col :span="12">
					<el-form-item label="入学学年" prop="entrance_year">
						<el-date-picker
							v-model="form.entrance_year"
							type="year"
							placeholder="请选择日期"
							format="YYYY"
							value-format="YYYY"
						></el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="出生日期" prop="birthdate">
						<el-date-picker
							v-model="form.birthdate"
							type="date"
							placeholder="请选择日期"
							format="YYYY/MM/DD"
							value-format="YYYY-MM-DD"
						></el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item label="地址" prop="address">
				<el-input v-model="form.address" placeholder="请输入地址" clearable></el-input>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input v-model="form.remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import CusCascader from '@/components/custom/cusCascader.vue'
import cusTom from '@/utils/cusTom'

const {
	campusId,
	tenantId,
	sexMap,
	politicalMap,
	identityMap,
	workStatusMap,
	staffTypeMap,
	academicTypeMap,
	professionalTypeMap,
	compileTypeMap,
	nationMap
} = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		student_name: null,
		sex: null,
		birthday: null,
		political: null,
		identity: null,
		work_status: null,
		staff_type: null,
		compile_type: null,
		academic_type: null,
		professional_type: null,
		listorder: 1,
		remark: null,
		discipline_id: null,
		department_id: null,
		position_id: null,
		nation: null,
		entrance_year: null,
		campus_id: campusId,
		tenant_id: tenantId
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		disciplineOptions: {
			type: Object,
			default: () => []
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				student_name: [{ required: true, message: '请输入名称' }]
			},
			sexMap,
			politicalMap,
			identityMap,
			workStatusMap,
			staffTypeMap,
			academicTypeMap,
			professionalTypeMap,
			compileTypeMap,
			nationMap,
			course: [],
			discipline: [],
			treeData: [],
			position: []
		}
	},
	computed: {},
	mounted() {},
	created() {
		let political = []
		nationMap.map((item) => {
			if (item.value !== '') {
				political.push(item.name)
			}
		})
		console.log(political.join(','))
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					this.form.entrance_year = parseInt(this.form.entrance_year)
					var res = await this.$API.eduStudent.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			// this.form.id = data.id
			// this.form.label = data.label
			// this.form.status = data.status
			// this.form.sort = data.sort
			// this.form.parentId = data.parentId
			// this.form.remark = data.remark
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			data.entrance_year = data.entrance_year + ''
			Object.assign(this.form, data)
			console.log(this.form, 2222)
		}
	},
	components: { CusCascader }
}
</script>

<style></style>
