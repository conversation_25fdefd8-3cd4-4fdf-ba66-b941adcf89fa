<template>
	<el-container>
		<el-header style="border: none">
			<el-page-header style="width: 100%" @back="goBack">
				<template #content>
					<span class="text-large font-600 mr-3">{{ certTemplate.name }} </span>
				</template>
				<template #extra>
					<div style="display: flex; align-items: center; justify-content: space-between">
						<el-button icon="el-icon-picture" @click="changeBgImg">更换背景图</el-button>
						<el-button icon="el-icon-view" type="success" @click="preview">预览</el-button>
						<el-button icon="el-icon-promotion" type="primary" :loading="saveLoading" @click="saveConfig"
							>保存
						</el-button>
					</div>
				</template>
			</el-page-header>
		</el-header>
		<el-main class="box">
			<section class="left">
				<left
					v-model:list="certTemplateConfig.list"
					:canvasConfig="certTemplateConfig.canvasConfig"
					:certTemplate="certTemplate"
				></left>
			</section>
			<section class="content">
				<center-component
					v-model:list="certTemplateConfig.list"
					:canvasConfig="certTemplateConfig.canvasConfig"
					:draggable="true"
					:resizable="true"
					:preventDeactivation="true"
					@activatedAction="activatedAction"
					@resizeAction="resizeAction"
					@dragAction="dragAction"
					@updateList="updateList"
				></center-component>
			</section>
			<section class="right">
				<right
					v-model:list="certTemplateConfig.list"
					:canvasConfig="certTemplateConfig.canvasConfig"
					:currentNode="currentNode"
					@activatedAction="activatedAction"
				></right>
			</section>
		</el-main>
	</el-container>
	<el-dialog v-model="showBgDialog" title="背景图" width="500">
		<div style="display: flex; align-items: center; justify-content: center">
			<scUpload
				v-model="certTemplateConfig.canvasConfig.bgImg"
				:width="200"
				:height="200"
				img-fit="contain"
				fileTypeTag="certTemplate"
				title="背景图"
			></scUpload>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="showBgDialog = false">取 消</el-button>
				<el-button type="primary" @click="suc">保存</el-button>
			</span>
		</template>
	</el-dialog>
	<preview v-if="previewDialog" ref="previewDialog"></preview>
</template>
<script>
import left from './components/left.vue'
import right from './components/right.vue'
import preview from './preview.vue'
import centerComponent from './components/center.vue'
import cusTom from '@/utils/cusTom'
import tool from '@/utils/tool'

export default {
	components: {
		left,
		preview,
		right,
		centerComponent
	},
	data() {
		return {
			campusId: '',
			tenantId: '',
			certTemplate: {},
			currentNode: {},
			saveLoading: false,
			showBgDialog: false,
			previewDialog: false,
			certTemplateConfig: {
				canvasConfig: {
					width: 700,
					height: 500,
					sizeType: 3, //1 a5 2 a4 3 自定义
					directionType: 1, //1 纵向 2 横向
					show: true,
					bgImg: ''
				},
				list: []
			}
		}
	},
	created() {
		const { tenantId } = cusTom.getBaseQuery()
		this.campusId = this.$route.query.campusId
		this.tenantId = tenantId
	},
	mounted() {
		this.getCertData()
		window.addEventListener('keydown', this.handleKeydown)
	},
	unmounted() {
		window.removeEventListener('keydown', this.handleKeydown)
	},
	watch: {
		certTemplateConfig: {
			handler(newVal) {
				console.log(newVal)
				if (
					newVal &&
					(newVal.canvasConfig.bgImg === '' ||
						newVal.canvasConfig.bgImg === undefined ||
						newVal.canvasConfig.bgImg === null)
				) {
					this.delFristBg()
				}
			},
			deep: true
		}
	},
	methods: {
		handleKeydown(event) {
			if (event.keyCode !== 46 || !this.currentNode) {
				return false
			}
			if (this.currentNode.field === 'bgImg') {
				return false
			}
			this.certTemplateConfig.list = this.certTemplateConfig.list.filter((item) => item.id !== this.currentNode.id)
		},
		changeBgImg() {
			this.showBgDialog = true
		},
		preview() {
			this.previewDialog = true
			this.$nextTick(() => {
				this.$refs.previewDialog.open(this.certTemplateConfig, this.certTemplate)
			})
		},
		suc() {
			// 使用示例
			let _this = this
			this.getImage(this.certTemplateConfig.canvasConfig.bgImg, function (img) {
				if (img) {
					_this.certTemplateConfig.canvasConfig.directionType = 1
					_this.certTemplateConfig.canvasConfig.width = img.width
					_this.certTemplateConfig.canvasConfig.height = img.height
					_this.certTemplateConfig.canvasConfig.sizeType = 3
					_this.certTemplateConfig.canvasConfig.show = false
					//_this.canvasSet.bgImg = e.url
					_this.addFristBg(img.width, img.height, _this.certTemplateConfig.canvasConfig.bgImg)
					_this.$nextTick(() => {
						_this.certTemplateConfig.canvasConfig.show = true
						/*_this.current = null
						_this.$emit('activatedAction', _this.current)*/
					})
					_this.showBgDialog = false
				} else {
					this.$message({ type: 'error', message: '无法获取图片尺寸' })
				}
			})
		},
		delFristBg() {
			let fristItem = this.certTemplateConfig.list[0]
			if (fristItem && fristItem.field === 'bgImg') {
				this.certTemplateConfig.list.shift()
			}

			/*this.$nextTick(() => {
				this.$emit('activatedAction', this.current)
			})*/
		},
		addFristBg(w, h, url) {
			let item = {
				id: tool.getRandomString(16, 'alphanumeric'),
				type: 'image',
				field: 'bgImg',
				content: url,
				w: w,
				scale: true,
				active: false,
				x: 0,
				y: 0,
				h: h
			}
			let fristItem = this.certTemplateConfig.list[0]
			if (fristItem && fristItem.field === 'bgImg') {
				this.certTemplateConfig.list[0] = item
			} else {
				this.certTemplateConfig.list.unshift(item)
			}
			/*this.$nextTick(() => {
				this.$emit('activatedAction', this.current)
			})*/
		},
		getImage(url, callback) {
			function getBase64Image(img, width, height) {
				//width、height调用时传入具体像素值，控制大小 ,不传则默认图像大小
				var canvas = document.createElement('canvas')
				canvas.width = width ? width : img.width
				canvas.height = height ? height : img.height

				var ctx = canvas.getContext('2d')
				ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
				var dataURL = canvas.toDataURL()
				return dataURL
			}
			var img = new Image()
			img.crossOrigin = ''
			img.src = url
			img.onload = function () {
				let base64 = getBase64Image(img)
				callback({
					width: img.width,
					height: img.height,
					base64: base64
				})
			}
			img.onerror = function () {
				callback(null)
			}
		},
		activatedAction(current) {
			this.currentNode = current
			if (current) {
				this.certTemplateConfig.list.map((item) => {
					if (item.id === current.id) {
						item.active = true
					} else {
						item.active = false
					}
					return item
				})
			} else {
				this.certTemplateConfig.list.map((item) => {
					item.active = false
					return item
				})
			}
			console.log(this.certTemplateConfig.list)
		},
		updateList(list) {
			this.certTemplateConfig.list = list
		},
		resizeAction(item) {
			console.log('改变大小', item)
		},
		dragAction(item) {
			console.log('拖动', item)
		},
		getCertData() {
			let params = {
				tenant_id: this.tenantId,
				campus_id: this.campusId,
				id: this.$route.query.id
			}
			this.$API.cert.certTemplate.one.get(params).then((res) => {
				this.certTemplate = res.data
				if (this.certTemplate.extend_conf) {
					this.certTemplateConfig = JSON.parse(this.certTemplate.extend_conf)
				}
				if (
					this.certTemplateConfig.canvasConfig.bgImg === '' ||
					this.certTemplateConfig.canvasConfig.bgImg === undefined ||
					this.certTemplateConfig.canvasConfig.bgImg === null
				) {
					this.showBgDialog = true
				}
				console.log(res)
			})
		},
		saveConfig() {
			this.certTemplateConfig.list.forEach((item) => {
				item.active = false
				return item
			})
			let config = JSON.stringify(this.certTemplateConfig)
			let param = {
				tenant_id: this.certTemplate.tenant_id,
				campus_id: this.certTemplate.campus_id,
				id: this.certTemplate.id,
				extend_conf: config
			}
			this.saveLoading = true
			this.$API.cert.certTemplate.config.post(param).then((res) => {
				this.saveLoading = false
				if (res.code === 200) {
					this.$message({ type: 'success', message: '保存成功！' })
				} else {
					this.$message({ type: 'error', message: res.message })
				}
			})
		},
		goBack() {
			this.$router.push({ name: 'cert', query: { type: 'certTemplate' } })
		}
	}
}
</script>
<style lang="scss" scoped>
.box {
	display: flex;
	height: 100%;
}

.adminui-main > .el-container .el-main {
	background-color: unset;
	padding: unset;
}

.left {
	width: 300px;
	height: 100%;
	margin-right: 15px;
	background-color: var(--el-bg-color-overlay);

	border-radius: 6px;
}

.content {
	height: 100%;
	overflow: hidden;
	flex: 1;
	user-select: none;
	background: #f2f3f5;
	border-radius: 6px;
	position: relative;
}

.right {
	width: 300px;
	height: 100%;
	margin-left: 15px;
	background-color: var(--el-bg-color-overlay);
	padding: 10px;
	border-radius: 6px;
}
</style>
