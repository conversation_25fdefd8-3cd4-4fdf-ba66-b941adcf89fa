<template>
	<el-dialog v-model="visible" append-to-body destroy-on-close title="分享">
		<CusTeacher :multiple="true" @valChange="selectChange"></CusTeacher>
		<template #footer>
			<el-button @click="visible = false">取消</el-button>
			<el-button type="primary" @click="save">确定</el-button>
		</template>
	</el-dialog>
</template>
<script setup>
import CusTeacher from '@/components/custom/cusTeacher.vue'
import { ref, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params'])
console.log(props.params)
let visible = ref(false)
let formref = ref(null)
// 分享字段
const form = ref({
	tenant_id: null,
	campus_id: null,
	file_id: [],
	share_user: []
})

const open = (id) => {
	console.log(id instanceof Array)
	if (id instanceof Array) {
		form.value.file_id = [...id]
	} else {
		form.value.file_id.push(id)
	}
	form.value.campus_id = props.params.campus_id
	form.value.tenant_id = props.params.tenant_id
	visible.value = true
}
const save = async () => {
	const res = await globalPropValue.fileManagement.file.file_share.post(form.value)
	if (res.code === 200) {
		ElMessage.success('分享成功')
	} else {
		ElMessage.error('分享失败')
	}
	visible.value = false
}
// 自定义事件
const selectChange = (val) => {
	form.value.share_user = val.map((v) => v.id)
}
defineExpose({
	open
})
</script>
<style scoped lang="scss">
:deep(.el-tree-node__content) {
	height: 36px;
}
</style>
