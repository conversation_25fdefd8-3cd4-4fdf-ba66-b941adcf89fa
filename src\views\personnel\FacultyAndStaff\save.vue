<template>
	<el-drawer v-model="visible" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'">
			<el-row>
				<el-col :span="12">
					<el-form-item label="名称" prop="name">
						<el-input v-model="form.name" placeholder="请输入名称" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="工号" prop="serial_number">
						<el-input v-model="form.serial_number" placeholder="请输入工号,为空会自动生成" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item label="头像" prop="user_head">
				<scUpload v-model="form.user_head" :disabled="mode == 'show'"></scUpload>
			</el-form-item>

			<el-row>
				<el-col :span="12">
					<el-form-item label="性别" prop="sex">
						<el-select v-model="form.sex" placeholder="请选择" filterable clearable>
							<el-option v-for="item in sexMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="身份证号" prop="idcard">
						<el-input v-model="form.idcard" placeholder="请输入身份证号" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row>
				<el-col :span="12">
					<el-form-item label="手机号" prop="phone">
						<el-input v-model="form.phone" placeholder="请输入手机号" clearable></el-input> </el-form-item
				></el-col>
				<el-col :span="12">
					<el-form-item label="邮箱" prop="email">
						<el-input v-model="form.email" placeholder="请输入邮箱" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="部门" prop="department_id">
						<!-- <el-select v-model="form.department_id" placeholder="请选择">
							<el-option
								:label="item.department_name"
								:value="item.id"
								v-for="item in treeData"
								:key="item.id"
							></el-option>
						</el-select>  -->
						<cusCascader
							v-model="form.department_id"
							:options="treeData"
							:prop="{
								emitPath: false,
								value: 'id',
								label: 'department_name',
								checkStrictly: true
							}"
						></cusCascader> </el-form-item
				></el-col>
				<el-col :span="12">
					<el-form-item label="职位" prop="position_id">
						<el-select v-model="form.position_id" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in getPostion"
								:key="item.id"
								:label="item.position_name"
								:value="item.id"
							></el-option>
						</el-select> </el-form-item
				></el-col>
			</el-row>
			<el-row>
				<!--				<el-col :span="12">
					<el-form-item label="政治面貌" prop="political">
						<el-select v-model="form.political" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in politicalMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>-->
				<el-col :span="12">
					<el-form-item label="身份" prop="identity">
						<el-select v-model="form.identity" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in identityMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>

			<el-form-item label="地址" prop="address">
				<el-input v-model="form.address" placeholder="请输入地址" clearable></el-input>
			</el-form-item>

			<el-row>
				<el-col :span="12">
					<el-form-item label="入职时间" prop="entry_time">
						<el-date-picker
							v-model="form.entry_time"
							type="date"
							placeholder="请选择日期"
							format="YYYY/MM/DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
						></el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="出生日期" prop="birthdate">
						<el-date-picker
							v-model="form.birthdate"
							type="date"
							placeholder="请选择日期"
							format="YYYY/MM/DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
						></el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="学科负责人" prop="discipline_head">
						<el-switch v-model="form.discipline_head" :active-value="1" :inactive-value="-1"></el-switch> </el-form-item
				></el-col>
				<el-col :span="12">
					<el-form-item label="在职状态" prop="work_status">
						<el-select v-model="form.work_status" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in workStatusMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="学科" prop="course_id">
						<el-select v-model="form.course_id" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in getCourse"
								:key="item.id"
								:label="item.course_name"
								:value="item.id"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<!--				<el-col :span="12">
					<el-form-item label="课程" prop="course_id">
						<el-select v-model="form.course_id" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in getCourse"
								:key="item.id"
								:label="item.course_name"
								:value="item.id"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>-->
			</el-row>

			<el-row>
				<el-col :span="12">
					<el-form-item label="编制情况" prop="compile_type">
						<el-select v-model="form.compile_type" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in compileTypeMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="学历情况" prop="academic_type">
						<el-select v-model="form.academic_type" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in academicTypeMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="职工类别" prop="staff_type">
						<el-select v-model="form.staff_type" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in staffTypeMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select> </el-form-item
				></el-col>
				<el-col :span="12">
					<el-form-item label="职称" prop="professional_type">
						<el-select v-model="form.professional_type" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in professionalTypeMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12"> </el-col>
			</el-row>

			<el-form-item label="备注" prop="remark">
				<el-input v-model="form.remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import CusCascader from '@/components/custom/cusCascader.vue'
import cusTom from '@/utils/cusTom'
const {
	campusId,
	tenantId,
	campusInfo,
	tenantInfo,
	sexMap,
	politicalMap,
	identityMap,
	workStatusMap,
	staffTypeMap,
	academicTypeMap,
	professionalTypeMap,
	compileTypeMap
} = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		name: null,
		sex: null,
		birthday: null,
		political: null,
		identity: null,
		work_status: null,
		staff_type: null,
		compile_type: null,
		academic_type: null,
		professional_type: null,
		listorder: 1,
		remark: null,
		discipline_id: null,
		department_id: null,
		position_id: null,
		campus_id: null,
		tenant_id: null
	}
}
var isMobileNumber = (rule, value, callback) => {
	if (!value) {
		return new Error('请输入手机号码')
	} else {
		const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
		const isPhone = reg.test(value)
		value = Number(value) //转换为数字
		if (typeof value === 'number' && !isNaN(value)) {
			//判断是否为数字
			value = value.toString() //转换成字符串
			if (value.length < 0 || value.length > 12 || !isPhone) {
				//判断是否为11位手机号
				callback(new Error('请输入正确格式的手机号码'))
			} else {
				callback()
			}
		} else {
			callback(new Error('请输入手机号码'))
		}
	}
}
export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		disciplineOptions: {
			type: Object,
			default: () => []
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),

			//验证规则
			rules: {
				name: [{ required: true, message: '请输入姓名' }],
				phone: [
					{ required: true, message: '请输入手机号码' },
					{ validator: isMobileNumber, trigger: 'blur' }
				]
			},
			sexMap,
			politicalMap,
			identityMap,
			workStatusMap,
			staffTypeMap,
			academicTypeMap,
			professionalTypeMap,
			compileTypeMap,
			course: [],
			discipline: [],
			treeData: [],
			position: []
		}
	},
	computed: {
		getCourse() {
			return this.course.filter((item) => {
				return item.campus_id === this.form.campus_id
			})
		},
		getDiscipline() {
			return this.discipline.filter((item) => {
				return item.campus_id == this.form.campus_id
			})
		},
		getPostion() {
			return this.position.filter((item) => {
				return item.campus_id == this.form.campus_id && item.department_id == this.form.department_id
			})
		}
	},
	mounted() {},
	created() {
		this.getEduCourseSet()
		this.getEduDiscipline()
		this.getDept()
		this.getJobManagement()
	},
	methods: {
		async getEduCourseSet() {
			var res = await this.$API.eduCourseSet.course.all.get(this.params)
			this.course = res.data
		},
		async getEduDiscipline() {
			var res = await this.$API.eduDiscipline.discipline.all.get(this.params)
			this.discipline = res.data
		},
		async getDept() {
			const { data } = await this.$API.system.dept.all.get(this.params)
			this.treeData = cusTom.arrayToTree(data)
		},
		async getJobManagement() {
			const res = await this.$API.JobManagement.all.get(this.params)
			this.position = res.data
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.personnel.staff.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			// this.form.id = data.id
			// this.form.label = data.label
			// this.form.status = data.status
			// this.form.sort = data.sort
			// this.form.parentId = data.parentId
			// this.form.remark = data.remark
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
			console.log(this.form, 2222)
		}
	}
}
</script>

<style></style>
