<template>
	<div class="preview-style">
		<div class="cover" v-if="coverInfo.options?.defaultValue">
			<el-image :src="coverInfo.options?.defaultValue" fit="cover" style="width: 100%;height:100%;"></el-image>
		</div>
		<div class="form-content">
			<h1>{{ titleInfo.options.defaultValue }}</h1>
			<div>
				<el-form ref="generateForm" label-suffix=":" :model="state.model" :rules="state.rules"
					:size="previewItemNode.config.size" :label-position="previewItemNode.config.labelPosition"
					:label-width="`${previewItemNode.config.labelWidth}px`"
					:hide-required-asterisk="previewItemNode.config.hideRequiredAsterisk">
					<!-- {{ previewItemNode }} -->
					<template v-for="(element, index) of previewItemNode.list" :key="element.key">
						<previewItem :model="state.model" :element="previewItemNode.list[index]" :config="data.config"
							:disabled="disabled" />
					</template>
				</el-form>
			</div>
			<div class="form-btn">
				<el-button type="primary" style="width:50%;">提交</el-button>
			</div>
		</div>
	</div>
</template>

<script setup>
import { defineProps, reactive, watch, onMounted, nextTick, ref } from 'vue'
import { ElMessage } from 'element-plus'
import previewItem from './component/previewItem.vue'
import { widgetForm } from './components'

const props = defineProps({
	data: {
		type: Object,
		default: () => widgetForm
	},
	value: Object,
	disabled: Boolean
})
const previewItemNode = ref()
const coverInfo = ref({})
const titleInfo = ref({})
const state = reactive({
	generateForm: {},
	model: {},
	rules: {},
	widgetForm: props.data ? JSON.parse(JSON.stringify(props.data)) : widgetForm
})

const generateModel = (list) => {
	for (let index = 0; index < list.length; index++) {
		const model = list[index].model
		if (!model) {
			return
		}
		if (list[index].type === 'grid') {
			list[index].columns.forEach((col) => generateModel(col.list))
		} else {
			if (props.value && Object.keys(props.value).includes(model)) {
				state.model[model] = props.value[model]
			} else {
				state.model[model] = list[index].options.defaultValue
			}

			if (list[index].options.rules) {
				state.rules[model] = JSON.parse(JSON.stringify(list[index].options.rules))

				if (state.rules[model].enum) {
					state.rules[model].enum = eval(state.rules[model].enum) // Consider alternatives to eval if security is a concern
				}

				if (state.rules[model].pattern) {
					state.rules[model].pattern = eval(state.rules[model].pattern) // Consider alternatives to eval if security is a concern
					state.rules[model].type = 'string'
				}
			}
		}
	}
}

const generateOptions = (list) => {
	list.forEach((item) => {
		if (item.type === 'grid') {
			item.columns.forEach((col) => generateOptions(col.list))
		} else {
			if (item.options.remote && item.options.remoteFunc) {
				fetch(item.options.remoteFunc)
					.then((resp) => resp.json())
					.then((json) => {
						if (json instanceof Array) {
							item.options.remoteOptions = json.map((data) => ({
								label: data[item.options.props.label],
								value: data[item.options.props.value],
								children: data[item.options.props.children]
							}))
						}
					})
			}
		}
	})
}

const reset = () => {
	nextTick(() => {
		//state.generateForm.resetFields();
	})
}

watch(
	() => props.data,
	(val) => {
		state.widgetForm = val ? JSON.parse(JSON.stringify(val)) : widgetForm
		state.model = {}
		state.rules = {}
		generateModel(state.widgetForm.list)
		generateOptions(state.widgetForm.list)
		reset()
		previewItemNode.value = state.widgetForm
		coverInfo.value = state.widgetForm.list.find(item => item.type === 'headImage')
		titleInfo.value = state.widgetForm.list.find(item => item.type === 'title')
	},
	{ deep: true, immediate: true }
)

onMounted(() => {
	generateModel(state.widgetForm?.list ?? [])
	generateOptions(state.widgetForm?.list ?? [])
	reset()
})

const getData = () => {
	return new Promise((resolve, reject) => {
		generateForm
			.validate()
			.then((validate) => {
				if (validate) {
					resolve(state.model)
				} else {
					ElMessage.error('验证失败')
				}
			})
			.catch((error) => {
				reject(error)
			})
	})
}

// export { state, getData, reset };
</script>

<style lang="scss" scoped>
.preview-style {
	width: 100%;
	padding-bottom: 15px;

	.cover {
		width: 100%;
		height: 200px;
		margin-bottom: -50px;
	}

	.form-content {
		width: 90%;
		margin: 5%;
		background-color: #ffffff;
		border-radius: 5px;
		padding: 0 15px 15px;
		position: relative;
		z-index: 5;
		h1 {
			text-align: center;
			padding: 20px 10px;
		}
	}

	.form-btn {
		text-align: center;
	}
}
</style>
