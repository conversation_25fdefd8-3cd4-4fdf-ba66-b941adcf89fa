<template>
    <el-container>
        <el-main>
            <div class="searchContent">
                <cusSelectSemester v-model="params.semester_id" :params="params" :show-default-value="true"
                    :width="'214px'" style="margin-right: 15px" @semesterChange="semesterChange" />
                <el-select v-model="params.grade_id" placeholder="请选择考试年级" filterable clearable
                    style="margin-right: 15px">
                    <el-option v-for="item in gradeList" :key="item.id" :label="item.grade_name"
                        :value="item.id"></el-option>
                </el-select>
                <el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
                <el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
            </div>
            <view></view>
        </el-main>
    </el-container>
</template>
<script setup>
import cusSelectSemester from '@/components/custom/cusSelectSemester'
import cusTom from '@/utils/cusTom'

const { tenantId, campusId, semesterInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
    return {
        campus_id: campusId,
        tenant_id: tenantId,
        semester_id: null,
        grade_id: null,
        start_date: null,
        end_date: null,
    }
}
const params = ref(defaultParams())
const currentSemester = ref('')
const semesterChange = (val) => {
    currentSemester.value = val
    params.value.semester_id = val
    getGradeList()
}
const upsearch = () => {
    
}
const refresh = () => {
    params.value = defaultParams()
    params.value.semester_id = currentSemester.value
    upsearch()
}

// 获取年级列表
const gradeList = ref([])
const getGradeList = () => {
    globalPropValue.eduGradeClass.grade.all.get({
        tenant_id: tenantId,
        campus_id: campusId,
        semester_id: params.value.semester_id
    }).then(res => {
        if (res.code === 200) {
            gradeList.value = res.data
        }
    })
}
</script>
<style lang="scss" scoped>
.searchContent {
    display: flex;
    gap: 10px;
    align-items: center;
}
</style>
