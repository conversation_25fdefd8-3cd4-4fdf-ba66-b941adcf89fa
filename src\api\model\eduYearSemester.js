import config from '@/config'
import http from '@/utils/request'

export default {
	eduYear: {
		list: {
			url: `${config.API_URL}/eduapi/semester/list`,
			name: '获取学年列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/eduapi/semester/all`,
			name: '获取学年列表不分页',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/semester/save`,
			name: '新增学年/修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		syncCalendar: {
			url: `${config.API_URL}/manapi/campus_org/syncCalendar`,
			name: '生成校历',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/eduapi/semester/del`,
			name: '删除学年',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		status: {
			url: `${config.API_URL}/eduapi/semester/changeStatus`,
			name: '修改学年状态',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		setDefault: {
			url: `${config.API_URL}/eduapi/semester/setDefault`,
			name: '设置默认学年',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	}
}
