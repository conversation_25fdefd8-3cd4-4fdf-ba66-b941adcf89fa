<template>
	<el-drawer v-model="showDrawer" destroy-on-close append-to-body title="流程配置" direction="rtl" size="70%">
		<el-main>
<!--			<el-alert :closable="false" title="规则：" type="warning" show-icon>
				<p>条件路由组只允许存在一组且只能在第一个节点判断</p>
			</el-alert>-->
			<sc-workflow v-if="showFlow" v-model="data.nodeConfig" :scale="scale" :form-config="data.formConfig"></sc-workflow>

			<div class="scale">
				<el-button-group>
					<el-button
						size="small"
						type="info"
						icon="el-icon-zoom-out"
						@click="scale > 10 ? (scale = scale - 10) : (scale = 10)"
					></el-button>
					<el-button type="info" plain size="small">{{ scale }}</el-button>
					<el-button
						size="small"
						type="info"
						icon="el-icon-zoom-in"
						@click="scale < 100 ? (scale = scale + 10) : (scale = 100)"
					></el-button>
				</el-button-group>
			</div>
		</el-main>
		<template #footer>
			<el-button v-loading="saveLoading" element-loading-text="保存中" type="primary" @click="exportJson">保存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import scWorkflow from '@/components/scWorkflow'
import cusTom from '@/utils/cusTom'
import { domToPng } from 'modern-screenshot'
import { putObjectBig } from '@/utils/ossLib' //上传文件到oss
const { campusId, tenantId } = cusTom.getBaseQuery()

export default {
	name: 'workflow',
	components: {
		scWorkflow
	},

	data() {
		return {
			scale:100,
			data: {},
			showDrawer: false,
			showFlow: false,
			saveLoading: false
		}
	},
	mounted() {},
	methods: {
		async open(data) {
			await this.getWorkflow(data.id)
			this.showDrawer = true
		},
		exportJson() {
			// this.$message('返回值请查看F12控制台console.log()')
			 console.log(JSON.stringify(this.data.nodeConfig))
			this.saveLoading=true
			let dom = document.querySelector('.box-scale')
			let that = this
			domToPng(dom).then(dataUrl => {
				var blob = that.base64ToBlob(dataUrl.split(',')[1], 'image/png')
				that.upImg(blob)
			})
			/*return
			html2canvas(dom,{scale: window.devicePiexlRatio}).then(function (canvas) {
				const img = canvas.toDataURL('image/png')
				var blob = that.base64ToBlob(img.split(',')[1], 'image/png')
				that.upImg(blob)
			})*/
		},
		async upImg(img) {
			const data = await putObjectBig(img, '.png','workflow')
			if (data) {
				this.submitWorkflow(data.url)
			}
		},
		async submitWorkflow(img) {
			const { code, message } = await this.$API.approval.deploy.set.post({
				id: this.data.id,
				approval_conf: JSON.stringify(this.data.nodeConfig),
				flow_img: img,
				campus_id: campusId,
				tenant_id: tenantId
			})
			if (code === 200) {
				this.$message({ type: 'success', message: message })
				this.showDrawer = false
				this.saveLoading = false
				this.$emit('success')
			} else {
				this.saveLoading = false
				this.$message({ type: 'error', message: message })
			}
		},
		async getWorkflow(id) {
			const { data } = await this.$API.approval.deploy.detail.get({ id: id, campus_id: campusId, tenant_id: tenantId })
			this.data = {
				id: data.id,
				name: data.approval_name,
				nodeConfig: JSON.parse(data.approval_conf),
				formConfig: JSON.parse(data.form_config)
			}
			this.showFlow = true
		},
		base64ToBlob(base64, mimeType) {
			const byteCharacters = atob(base64)
			const byteArrays = []
			for (let offset = 0; offset < byteCharacters.length; offset += 512) {
				const slice = byteCharacters.slice(offset, offset + 512)
				const byteNumbers = new Array(slice.length)
				for (let i = 0; i < slice.length; i++) {
					byteNumbers[i] = slice.charCodeAt(i)
				}
				const byteArray = new Uint8Array(byteNumbers)
				byteArrays.push(byteArray)
			}
			return new Blob(byteArrays, { type: mimeType })
		}
	}
}
</script>

<style lang="scss">
.scale {
	position: absolute;
	left: auto;
	bottom: 65px;
	/* width: 200px; */
	right: 20px;
}
</style>
