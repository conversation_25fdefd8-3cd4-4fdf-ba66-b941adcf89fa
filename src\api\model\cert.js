import config from '@/config'
import http from '@/utils/request'

export default {
	certTemplate: {
		list: {
			//url: `${config.MOCK_URL}/system/user/list`,
			url: `${config.API_URL}/manapi/certTemplate/list`,
			name: '获取列表',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		one: {
			//url: `${config.MOCK_URL}/system/user/list`,
			url: `${config.API_URL}/manapi/certTemplate/one`,
			name: '获取单个',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			//url: `${config.MOCK_URL}/system/user/list`,
			url: `${config.API_URL}/manapi/certTemplate/all`,
			name: '获取所有',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/certTemplate/save`,
			name: '新增/修改',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		config: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/certTemplate/config`,
			name: '保存配置',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/certTemplate/del`,
			name: '删除',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		}
	},
	certGroup: {
		list: {
			//url: `${config.MOCK_URL}/system/user/list`,
			url: `${config.API_URL}/manapi/certGroup/list`,
			name: '获取列表',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		one: {
			//url: `${config.MOCK_URL}/system/user/list`,
			url: `${config.API_URL}/manapi/certGroup/one`,
			name: '获取单个',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			//url: `${config.MOCK_URL}/system/user/list`,
			url: `${config.API_URL}/manapi/certGroup/all`,
			name: '获取所有',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/certGroup/save`,
			name: '新增/修改',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		archive: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/certGroup/archive`,
			name: '归档',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		submitAudit: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/certGroup/submitAudit`,
			name: '提交审核',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		audit: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/certGroup/audit`,
			name: '审核',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/certGroup/del`,
			name: '删除',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		}
	},
	cert: {
		list: {
			//url: `${config.MOCK_URL}/system/user/list`,
			url: `${config.API_URL}/manapi/cert/list`,
			name: '获取列表',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		my: {
			//url: `${config.MOCK_URL}/system/user/list`,
			url: `${config.API_URL}/manapi/cert/my`,
			name: '获取我的证书',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		one: {
			//url: `${config.MOCK_URL}/system/user/list`,
			url: `${config.API_URL}/manapi/cert/one`,
			name: '获取单个',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			//url: `${config.MOCK_URL}/system/user/list`,
			url: `${config.API_URL}/manapi/cert/all`,
			name: '获取所有',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/cert/save`,
			name: '修改',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		creat: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/cert/creat`,
			name: '新增',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		archive: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/cert/archive`,
			name: '归档',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		audit: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/cert/audit`,
			name: '审核',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/cert/del`,
			name: '删除',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		generate: {
			//url: `${config.MOCK_URL}/system/user/save`,
			url: `${config.API_URL}/manapi/cert/generate`,
			name: '证书生成',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		}
	},
}
