<template>
	<el-container>
		<el-header>
			<el-page-header content="房间详情" @back="goBack"></el-page-header>
		</el-header>
		<el-main>
			<div class="room-info">
				<div class="room-info-top">
					<div v-if="roomData.room_info" class="room-info-left">
						<cusImage
							loading="lazy"
							:lazy="true"
							fit="cover"
							style="width: 150px; height: 150px; margin-right: 20px; border-radius: 6px"
							:src="roomData.room_info.room_img"
						>
						</cusImage>
						<div class="room-info-name">
							<h3>
								{{
									roomData.room_info.building_name + roomData.room_info.floor_name + ' ' + roomData.room_info.room_name
								}}
							</h3>
							<p>场室说明：{{ roomData.room_info.remark }}</p>
							<p>容纳人数：{{ formData(roomCapacityMap, roomData.room_info.room_capacity) }}</p>

							<p v-if="roomData.room_info.open_type === 1">
								可预约时间：{{ roomData.room_info.allow_start + '~' + roomData.room_info.allow_end }}
							</p>
							<p v-if="roomData.room_info.open_type === 2">按课程时段预约 : {{ roomData.room_info.periods?.name }}</p>

							<div>
								<el-tag v-for="i in roomData.room_info.tags_ary"> {{ i }}</el-tag>
							</div>
						</div>
					</div>
					<div class="room-info-right">
						<el-button type="primary" round @click="room_reservation">立即预约</el-button>
					</div>
				</div>
				<div class="room-info-bottom">
					<div class="date">
						<el-date-picker
							v-model="daterange"
							type="daterange"
							range-separator="至"
							start-placeholder="开始日期"
							end-placeholder="结束日期"
							size="default"
							clearable
							value-format="YYYY-MM-DD"
							@change="dateChange"
						/>
					</div>

					<div class="legend">
						<div v-for="i in legendList" :key="i.id" class="legend-item"><span></span>{{ i.name }}</div>
					</div>
					<div v-if="roomData.room_info?.open_type === 1" class="time">
						<ul v-for="(i, index) in daysList" :key="i.id" class="time-list">
							<div class="list-date">
								<p>
									<b>{{ i.date }}</b>
								</p>
								<p>{{ i.weekday }}</p>
							</div>
							<li
								v-for="item in timeFragment"
								:key="item.id"
								:class="{
									active: selectTimeIds.includes(item.id) && i.id == selectDayId,
									noClick: notAllowIds.includes(item.id),
									isSelected: roomData.selectDayIds.includes(i.id) && roomData.selectedRe[i.id].includes(item.id)
								}"
								@click="timeSelect(item, i.id)"
							>
								<!-- && roomData.selectDayIds.includes(i.id) -->
								<span v-if="index == 0">{{ item.id % 2 == 0 ? item.start : '' }}</span>
								<span v-if="index == 0" class="end-time">{{ item.id == timeFragment.length - 1 ? item.end : '' }}</span>
							</li>
						</ul>
					</div>
					<div v-if="roomData.room_info?.open_type === 2" class="time">
						<ul v-for="(i, index) in daysList" :key="i.id" class="time-list">
							<div class="list-date">
								<p>
									<b>{{ i.date }}</b>
								</p>
								<p>{{ i.weekday }}</p>
							</div>
							<li
								v-for="item in roomData.room_info.periods_list"
								:key="item.id"
								:class="{
									periodsItem: true,
									active: selectTimeIds.includes(item.id) && i.id === selectDayId,
									noClick: roomData.disabled_periods_list[i.weekday]?.includes(item.id),
									isSelected: roomData.selectDayIds.includes(i.id) && roomData.selectedRe[i.id].includes(item.id)
								}"
								@click="timeSelectPeriods(item, i.id)"
							>
								<!-- && roomData.selectDayIds.includes(i.id) -->
								<span v-if="index == 0">
									<p>
										<b>{{ item.item_name }}</b>
									</p>
									<p>({{ item.begin_time }}-{{ item.end_time }})</p>
								</span>
							</li>
						</ul>
					</div>
					<div class="btn">
						<el-button round @click="goBack">返 回</el-button>
						<el-button type="primary" round @click="reservation">提交预约</el-button>
					</div>
				</div>
			</div>
		</el-main>
		<save ref="saveDialog" @success="saveSuccess"></save>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
import { getDateWeekArray, isBeforeToday } from '@/utils/dayjs'
import save from './save.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { toRaw } from 'vue'
const { campusId, tenantId, roomCapacityMap } = cusTom.getBaseQuery()
export default {
	components: {
		save
	},
	data() {
		return {
			roomCapacityMap,
			roomData: {},
			legendList: [
				{
					name: '已被预约时段',
					id: 1
				},
				{
					name: '可预约时段',
					id: 2
				},
				{
					name: '不可预约时段',
					id: 3
				}
			],
			daysList: [],
			timeFragment: [],
			selectTimeIds: [],
			selectPeriodsItemId: 0,
			notAllowIds: [],
			daterange: [],
			date: null,
			selectDayId: null
		}
	},
	created() {
		this.daysList = getDateWeekArray()
		this.daterange = [this.daysList[0].date, this.daysList[this.daysList.length - 1].date]
		this.date = this.daysList[0].date
		this.getRoomData()
	},
	methods: {
		resetTime(data) {
			const startId = data.find((item) => item.end === this.roomData.room_info.allow_start)?.id
			const endId = data.find((item) => item.start === this.roomData.room_info.allow_end)?.id
			// 如果找到了目标 id，则获取所有 id 小于目标 id 的元素
			let startIds = data.filter((item) => item.id <= startId).map((item) => item.id)
			let endIds = data.filter((item) => item.id >= endId).map((item) => item.id)
			this.notAllowIds = startIds.concat(endIds)
			//console.log(this.notAllowIds, this.date, 'result')
		},
		timeSelect(item, id) {
			if (id !== this.selectDayId) {
				this.selectTimeIds = []
			}
			this.selectDayId = id
			if (this.selectTimeIds.includes(item.id)) {
				this.selectTimeIds = this.selectTimeIds.filter((items) => items != item.id)
			} else {
				this.selectTimeIds.push(item.id)
			}
			//console.log(item, this.selectTimeIds, 'timeSelect')
		},
		timeSelectPeriods(item, id) {
			this.selectTimeIds = []
			this.selectDayId = id
			if (this.selectTimeIds.includes(item.id)) {
				this.selectTimeIds = this.selectTimeIds.filter((items) => items != item.id)
			} else {
				this.selectTimeIds = [item.id]
			}
			this.selectPeriodsItemId = item.id
			//console.log(item, this.selectTimeIds, this.selectDayId, 'timeSelectPeriods')
		},
		goBack() {
			this.$router.go(-1)
		},
		dateChange(val) {
			if (val) {
				this.daterange = val
				this.daysList = getDateWeekArray(val[0], val[1])
			} else {
				this.daterange = [null, null]
			}
			this.getRoomData()
			//console.log(val, 'dateChange')
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		// 立即预约
		room_reservation() {
			this.$refs.saveDialog.open(this.roomData.room_info)
		},
		reservation() {
			const filterDate = toRaw(this.daysList.filter((obj) => obj.id == this.selectDayId))
			let form
			if (this.roomData.room_info.open_type === 1) {
				const filterTime = toRaw(this.timeFragment.filter((obj) => this.selectTimeIds.includes(obj.id)))
				if (filterTime.length == 0 || filterDate.length == 0) {
					ElMessage.error('请选择时间段')
					return
				}
				//console.log(filterTime, filterDate, 'reservation')
				form = {
					room_id: this.roomData.room_info.id,
					begin_time: filterDate[0].date + ' ' + filterTime[0].start,
					end_time: filterDate[0].date + ' ' + filterTime[filterTime.length - 1].end
				}
			} else {
				const filterTime = toRaw(
					this.roomData.room_info.periods_list.filter((obj) => this.selectPeriodsItemId === obj.id)
				)
				if (filterTime.length == 0 || filterDate.length == 0) {
					ElMessage.error('请选择对于日期的课节时段')
					return
				}
				//console.log(filterTime, filterDate, 'reservation')
				form = {
					room_id: this.roomData.room_info.id,
					begin_time: filterDate[0].date + ' ' + filterTime[0].begin_time,
					end_time: filterDate[0].date + ' ' + filterTime[0].end_time,
					periods_item_id: filterTime[0].id,
					open_type: this.roomData.room_info.open_type,
					periods_list: this.roomData.room_info.periods_list
				}
			}
			// 加上预约时选择时间判断
			if (isBeforeToday(form.begin_time)) {
				ElMessage.error('请注意选择的时段不能早于当前时间')
			} else {
				if (
					!this.isSequential(
						this.selectTimeIds.sort((a, b) => {
							return a - b
						})
					)
				) {
					ElMessage.error('请选择连续时间段')
				} else {
					this.$refs.saveDialog.open(form)
				}
			}
		},
		// 预约表单回调
		async saveSuccess(formVal) {
			//console.log(formVal, 'saveSuccess')
			let formData = {
				id: formVal.id,
				tenant_id: tenantId,
				campus_id: campusId,
				title: formVal.title,
				room_id: this.roomData.room_info.id,
				periods_item_id: formVal.periods_item_id,
				date: formVal.date,
				begin_time: formVal.date + ' ' + formVal.startTime,
				end_time: formVal.date + ' ' + formVal.endTime,
				teachers: formVal.teachers?.length ? formVal.teachers.map((item) => item.id).join(',') : '',
				students: formVal.students?.length ? formVal.students.map((item) => item.id).join(',') : '',
				remark: formVal.remark
			}
			const { code, message } = await this.$API.fieldReservation.add.post(formData)
			if (code === 200) {
				ElMessage.success(message)
				this.getRoomData()
			} else {
				ElMessage.error(message)
			}
		},
		async getRoomData() {
			const { data } = await this.$API.fieldReservation.room.get({
				room_id: this.$route.query.id,
				campus_id: campusId,
				tenant_id: tenantId,
				begin_date: this.daterange[0],
				end_date: this.daterange[1]
			})
			this.roomData = data
			this.timeFragment = this.timeSlots()
			this.resetTime(this.timeFragment)

			if (this.roomData.booking_list && this.roomData.booking_list.length > 0) {
				let idss = []
				let days = []
				let selectedRe = {}
				this.roomData.booking_list.forEach((items) => {
					const dayId = this.dayIds(items.begin_time)
					days.push(...dayId)
					try {
						let timeIds
						if (this.roomData.room_info.open_type === 1) {
							timeIds = this.sToe(items.begin_time, items.end_time)
							idss.push(...timeIds)
						} else {
							timeIds = items.periods_item_id
							idss.push(timeIds)
						}
						// 如果 dayId 已经存在于 selectedRe，则追加 timeIds 到已有的数组中
						if (selectedRe.hasOwnProperty(dayId)) {
							if (this.roomData.room_info.open_type === 1) {
								selectedRe[dayId].push(...timeIds)
							} else {
								selectedRe[dayId].push(timeIds)
							}
						} else {
							if (this.roomData.room_info.open_type === 1) {
								selectedRe[dayId] = timeIds
							} else {
								selectedRe[dayId] = [timeIds]
							}
						}
					} catch (error) {
						console.error('处理预订项目时出错', error)
					}
					//此处若存在重复的日期，会导致已预约的显示错误
					// days = days.concat(this.dayIds(items.begin_time))
					// // 使用 this.sToe 函数获取指定时间段内的 ID，并添加到 idss 中
					// //console.log(days.includes(this.dayIds(items.begin_time)[0]), 'days')
					// selectedRe[this.dayIds(items.begin_time)] = this.sToe(items.begin_time, items.end_time)
					// idss = idss.concat(this.sToe(items.begin_time, items.end_time));
				})
				this.roomData.selectDayIds = days
				this.roomData.selectTimeIds = idss
				this.roomData.selectedRe = selectedRe
			} else {
				this.roomData.selectTimeIds = []
				this.roomData.selectDayIds = []
				this.roomData.selectedRe = {}
			}
			//console.log(this.roomData, 'getRoomData')
		},
		isSequential(arr) {
			for (let i = 1; i < arr.length; i++) {
				if (arr[i] !== arr[i - 1] + 1) {
					return false
				}
			}
			return true
		},
		// 获取日期ids
		dayIds(date) {
			let dateb = date.slice(0, -3).split(' ')
			let dayIds = this.daysList.filter((item) => item.date == dateb[0])
			let selectDayIds = dayIds.map((item) => item.id)
			return selectDayIds
		},
		// 获取时间段ids
		sToe(start, end) {
			let dateb = start.slice(0, -3).split(' ')
			let datee = end.slice(0, -3).split(' ')
			let ids = this.timeFragment.filter((item) => item.start == dateb[1] || item.end == datee[1])
			return this.completeTimeIds(ids.map((item) => item.id))
		},
		completeTimeIds(ids) {
			const minId = Math.min(...ids)
			const maxId = Math.max(...ids)
			// 创建补全后的数组
			const completeIds = []
			for (let i = minId; i <= maxId; i++) {
				completeIds.push(i)
			}
			return completeIds
		},
		timeSlots() {
			const timeSlots = Array.from({ length: 48 }, (_, i) => {
				const hour = Math.floor(i / 2)
				const minute = (i % 2) * 30
				return {
					id: i,
					start: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
					end: `${(hour + (minute === 30 ? 1 : 0)).toString().padStart(2, '0')}:${minute === 30 ? '00' : '30'}`
				}
			})
			return timeSlots
		}
	}
}
</script>

<style lang="scss" scoped>
.room-info {
	padding: 20px;

	.room-info-top {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;

		.room-info-left {
			display: flex;

			// align-items: flex-end;
			.room-info-name {
				display: flex;
				flex-direction: column;
				justify-content: space-around;
			}
		}

		.room-info-right {
		}
	}

	.room-info-bottom {
		.date {
			width: 500px;
			margin: 10px 0 30px;
		}

		.legend {
			width: 100%;
			display: inline-flex;
			justify-content: center;
			margin-bottom: 60px;

			.legend-item {
				display: flex;
				align-items: center;
				margin-right: 12px;

				span {
					width: 15px;
					height: 15px;
					display: inline-block;
					margin-right: 8px;
					border: 1px dashed #ddd;
				}

				&:first-child {
					span {
						background-color: var(--el-color-primary-light-9);
					}
				}

				&:last-child {
					span {
						background-color: var(--el-color-info-light-8);
					}
				}
			}
		}

		.time {
			margin-bottom: 15px;
			border: 1px dashed #ddd;
		}

		.time-list {
			display: flex;
			align-items: center;

			&:first-child {
				li {
					border-top: none;
				}
			}

			&:last-child {
				.list-date {
					border-bottom: none;
				}
			}

			.list-date {
				width: 150px;
				height: 50px;
				padding-top: 8px;
				line-height: 18px;
				text-align: center;
				color: var(--el-color-primary-light-1);
				border-bottom: 1px dashed #eff0f0;
			}

			li {
				position: relative;
				flex-grow: 1;
				height: 50px;
				margin-right: 2px;
				cursor: pointer;
				border: 1px dashed #ddd;
				border-bottom: none;

				&:last-child {
					margin-right: 0;
					border-right: none;
				}

				span {
					position: absolute;
					top: -20px;
					left: -15px;
					color: var(--el-color-primary-light-1);
				}

				.end-time {
					left: 15px;
				}
			}
			li.periodsItem {
				&:last-child {
					margin-right: 0;
					border-right: none;
				}

				span {
					position: absolute;
					top: -50px;
					left: -1px;
					display: inline-block;
					width: calc(100% + 2px);
					height: 50px;
					padding-top: 5px;
					line-height: 15px;
					text-align: center;
					color: var(--el-color-primary-light-1);
					border: 1px dashed #ddd;
				}

				.end-time {
					left: 15px;
				}
			}
			.active {
				background-color: var(--el-color-primary);
			}

			.noClick {
				pointer-events: none;
				background-color: var(--el-color-info-light-8);
			}

			.isSelected {
				background-color: var(--el-color-primary-light-9);
				pointer-events: none;
			}
		}

		.btn {
			text-align: center;
		}
	}
}
.periods_item {
	height: 35px;
	padding: 0px 15px;
	line-height: 35px;
	text-align: center;
	border: 1px dashed #ddd;
	margin-right: 5px;
	border-radius: 4px;
	cursor: pointer;
}
</style>
