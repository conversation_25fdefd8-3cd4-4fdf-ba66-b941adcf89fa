import config from '@/config'
import http from '@/utils/request'

export default {
	all: {
		url: `${config.API_URL}/affapi/consumable/type_all`,
		name: '获取耗材类别列表不分页',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	create: {
		url: `${config.API_URL}/affapi/consumable/type_creat`,
		name: '新增耗材类别',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	save: {
		url: `${config.API_URL}/affapi/consumable/type_save`,
		name: '耗材类别修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},

	del: {
		url: `${config.API_URL}/affapi/consumable/type_del`,
		name: '删除耗材类别',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	myConsumable: {
		url: `${config.API_URL}/affapi/consumable/my`,
		name: '我的资产',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	rooms: {
		all:{
			url: `${config.API_URL}/affapi/consumable/all`,
			name: '获取耗材列表不分页',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		list: {
			url: `${config.API_URL}/affapi/consumable/list`,
			name: '获取耗材列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		create: {
			url: `${config.API_URL}/affapi/consumable/creat`,
			name: '新增耗材',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		update: {
			url: `${config.API_URL}/affapi/consumable/update`,
			name: '新增耗材',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},

		incr: {
			url: `${config.API_URL}/affapi/consumable/incr`,
			name: '耗材修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/affapi/consumable/del`,
			name: '删除耗材',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		action: {
			url: `${config.API_URL}/affapi/consumable/action`,
			name: '耗材出退库',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		record: {
			url: `${config.API_URL}/affapi/consumable/record`,
			name: '耗材出入库记录',
			get: async function (data = {}) {
				return await http.get(this.url, data)
			}
		},
		log: {
			url: `${config.API_URL}/affapi/consumable/log`,
			name: '单个耗材出入库记录',
			get: async function (data = {}) {
				return await http.get(this.url, data)
			}
		}
	},
	tree: {
		url: `${config.API_URL}/affapi/consumable/tree`,
		name: '获取耗材',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	}
}
