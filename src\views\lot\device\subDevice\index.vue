<template>
	<div class="left-panel" style="width: 100%; margin-top: 10px">
		<div class="left-panel-search">
			<el-form-item label="">
				<el-select v-model="params.status" style="width: 150px" placeholder="请选择设备状态" clearable>
					<el-option
						v-for="(item, index) in enumConfig.deviceStatusMap"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="">
				<el-input v-model="params.name" placeholder="请输入设备名称/设备ID搜索" clearable></el-input>
			</el-form-item>
			<el-form-item label="">
				<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
				<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
			</el-form-item>
		</div>
	</div>
	<el-table ref="table" v-loading="loading" row-key="id" :data="deviceList" size="small">
		<el-table-column label="设备ID" prop="device_key" width="180"></el-table-column>
		<el-table-column label="设备名称" prop="device_name" min-width="180"></el-table-column>
		<el-table-column label="所属产品" prop="product_info" min-width="180">
			<template #default="scope">
				{{ scope.row.product_info?.product_name }}
			</template>
		</el-table-column>
		<el-table-column label="节点类型" prop="node_type" width="100">
			<template #default="scope">
				{{ enumConfig.nodeTypeMap.find((item) => item.value === scope.row.product_info?.node_type)?.name }}
			</template>
		</el-table-column>
		<el-table-column label="设备位置" prop="room_info" min-width="200">
			<template #default="scope">
				<span v-if="scope.row.room_info">{{ scope.row.room_info.name }}</span>
				<span v-else>-</span>
			</template>
		</el-table-column>
		<el-table-column label="状态" prop="status" width="100">
			<template #default="scope">
				<sc-status-indicator :type="deviceStatusType[scope.row.status]"> </sc-status-indicator>
				{{ enumConfig.deviceStatusMap.find((item) => item.value === scope.row.status)?.name }}
			</template>
		</el-table-column>
		<el-table-column label="创建时间" prop="created_at" width="150"></el-table-column>
		<el-table-column label="最近在线时间" prop="last_online_time" width="150"></el-table-column>
		<el-table-column label="操作" fixed="right" align="left" width="200">
			<template #default="scope">
				<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">详情</el-button>
				<el-divider direction="vertical" />
				<el-button text type="danger" size="small" @click="table_del(scope.row, scope.$index)">删除</el-button>
			</template>
		</el-table-column>
	</el-table>
	<div class="page">
		<el-pagination
			v-if="total > 0"
			v-model:current-page="params.page"
			:page-sizes="[10, 20, 30, 50, 100]"
			:page-size="params.page_size"
			size="small"
			background
			layout="total,sizes, prev, pager, next,jumper"
			:total="total"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		/>
	</div>
</template>

<script>
import cusTom from '@/utils/cusTom'
import dayjs from 'dayjs'
import { ElMessageBox } from 'element-plus'

export default {
	name: 'logList',
	props: {
		deviceInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			currentPage1: 1,
			loading: false,
			total: 0,
			deviceList: [],
			tslList: [],
			enumConfig: {},
			deviceStatusType: {
				1: 'info',
				2: 'success',
				3: 'info',
				4: 'warning',
				5: 'danger'
			},
			params: {
				name: null,
				tenant_id: null,
				campus_id: null,
				parent_id: null,
				status: null,
				page: 1,
				page_size: 10
			}
		}
	},
	components: {},
	watch: {},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
		if (this.deviceInfo) {
			this.params.parent_id = this.deviceInfo.id
			this.params.tenant_id = this.deviceInfo.tenant_id
			this.params.campus_id = this.deviceInfo.campus_id
			this.getList()
		}
	},
	computed: {},
	methods: {
		table_show(row) {
			this.$router.push({
				path: '/lot/device/detail',
				query: {
					id: row.id,
					campus_id: row.campus_id
				}
			})
		},
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: row.tenant_id, campus_id: row.campus_id }
			ElMessageBox.confirm('谨慎操作！确保当前设备已拆除，是否确认删除当前设备！', '谨慎操作', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.device.del.post(reqData)
				if (res.code === 200) {
					this.$message.success('删除成功')
					this.$refs.table.refresh()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		handleSizeChange(page_size) {
			this.params.page_size = page_size
			this.getList()
		},
		handleCurrentChange(page) {
			this.params.page = page
			this.getList()
		},
		//搜索
		upsearch() {
			this.params.page = 1
			this.getList()
		},
		async getList() {
			this.loading = true
			const res = await this.$LotApi.device.list.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.deviceList = res.data.rows
				this.total = res.data.total
			}
		},
		refresh() {
			this.params.name = null
			this.params.status = null
			this.params.page = 1
			this.getList()
		}
	}
}
</script>

<style lang="scss" scoped>
.left-panel {
	padding-bottom: 15px;
}

.page {
	padding: 20px;
	display: flex;
	justify-content: flex-end;
}
</style>
