<template>
	<el-dialog
		v-model="visible"
		:title="titleMap[mode]"
		destroy-on-close
		width="500px"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		@closed="$emit('closed')"
	>
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'" label-width="90">
			<el-form-item label="标题" prop="title">
				<el-input v-model="form.title" placeholder="请输入标题" clearable></el-input>
			</el-form-item>
			<el-form-item label="类型" prop="is_video">
				<el-radio-group v-model="form.is_video">
					<el-radio-button label="图片" :value="-1" />
					<el-radio-button label="视频" :value="1" />
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.is_video === -1" label="照片" prop="url">
				<scUpload v-model="form.url" fileTypeTag="classMien"></scUpload>
			</el-form-item>

			<el-form-item v-if="form.is_video === 1" label="视频" prop="url">
				<scUploadFile
					v-model="form.url"
					:limit="1"
					fileTypeTag="classMien"
					:multiple="false"
					:maxSize="200"
					accept="video/*"
				></scUploadFile>
			</el-form-item>
			<el-form-item label="排序" prop="listorder">
				<el-input v-model="form.listorder" type="number" style="width: 100px" placeholder="请输入" clearable></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
const defaultData = () => {
	return {
		id: null,
		campus_id: null,
		tenant_id: null,
		class_id: null,
		is_video: -1,
		listorder: 0,
		url: ''
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				title: [{ required: true, message: '请输入标题' }],
				url: [{ required: true, message: '请上传相应视频或照片' }]
			},

			course: [],
			discipline: [],
			treeData: [],
			position: []
		}
	},
	computed: {},
	mounted() {},
	created() {
		this.form.class_id = this.params.class_id
		this.form.tenant_id = this.params.tenant_id
		this.form.campus_id = this.params.campus_id
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					this.form.listorder = parseInt(this.form.listorder)
					var res = await this.$API.classMien.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
