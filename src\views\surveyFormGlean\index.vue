<template>
    <div class="survey-form" v-loading="loading" element-loading-text="问卷加载中">
        <div class="cover" v-if="formInfo.cover">
            <el-image :src="formInfo.cover" fit="cover" style="width: 100%;height:100%;"></el-image>
        </div>
        <div class="form-content">
            <h1>{{ formInfo.title }}</h1>
            <div class="form-item">
                <formItem ref="formItemRef" v-model='form' :formItems="formItems"></formItem>
            </div>
            <div class="form-btn">
                <el-button type="primary" size="large" style="width:80%;" v-loading="submitLoading"
                    @click="submitForm">提交</el-button>
            </div>
        </div>
        <div class="form-footer">
            <el-button type="info" link @click="dialogVisible = true">问卷调查隐私声明</el-button>
        </div>
        <el-backtop :right="25" :bottom="100" />
    </div>
    <loginDialog ref="login" @success="loginSuccess"></loginDialog>
    <selectUser ref="selectUserRef" @select="userObject"></selectUser>
    <el-dialog v-if="dialogVisible" v-model="dialogVisible" title="问卷调查隐私声明" center align-center>
        <div class="dialog-content">
            <p>尊敬的参与者：</p>

            <p>我们非常重视您的隐私权和个人信息安全。在您参与本次调查问卷之前，特此向您详细阐述我们在收集、使用和保护您个人信息方面的政策与措施。请您仔细阅读以下隐私声明，以确保您对我们将如何处理您的个人信息有充分的了解。
            </p>

            <p>一、信息收集范围</p>

            <p>在您填写调查问卷的过程中，我们可能会收集您的姓名（如非匿名调查）、联系方式（如邮箱地址或电话号码，但仅在您自愿提供的情况下）、以及您在问卷中填写的各项信息，包括但不限于个人背景、兴趣爱好、消费习惯等。</p>

            <p>我们不会通过本调查问卷收集任何涉及您个人隐私的敏感信息，如银行账户信息等。</p>

            <p>二、信息使用目的</p>

            <p>我们将收集到的信息主要用于统计分析，以帮助我们改进产品或服务、优化市场策略、提升用户体验。</p>

            <p>在必要时，我们可能会对部分信息进行脱敏处理，用于学术研究、行业报告发布等非直接针对个人的用途。</p>

            <p>除非事先获得您的明确同意，否则我们不会将您的个人信息用于其他任何目的。</p>

            <p>三、信息共享与披露</p>

            <p>我们承诺对您的个人信息进行严格的保密管理，未经您允许，不会向第三方泄露您的个人信息。</p>

            <p>在特定情况下，如为了完成某项服务而需要与合作伙伴共享数据时，我们会确保合作伙伴同样遵守严格的隐私保护政策，并签订相应的保密协议。</p>

            <p>在法律要求或政府监管部门要求下，我们可能需要披露部分信息，但在此情况下，我们将尽力保护您的个人信息不被滥用。</p>

            <p>四、信息安全保障</p>

            <p>我们采用先进的技术手段和管理措施来保护您的个人信息免受丢失、被篡改和非法访问的风险。</p>

            <p>我们定期对系统进行安全检查和漏洞修复，以确保数据存储和处理的安全性。</p>

            <p>五、您的权利</p>

            <p>您有权随时查询、更正和删除我们持有的关于您的个人信息。如需行使这些权利，请与我们联系并提供相关证明文件。</p>

            <p>如果您发现我们在处理您的个人信息时存在违法行为或不当行为，您有权向我们提出投诉并要求我们立即改正。</p>

            <p>六、隐私声明的更新与修订</p>

            <p>随着法律法规的变化和业务发展的需要，我们可能会对本隐私声明进行更新和修订。</p>

            <p>任何对隐私声明的修改都将及时在我们的官方渠道上公布，并注明生效日期。建议您定期查阅我们的隐私声明，以确保您对我们的个人信息处理政策保持最新了解。</p>

            <p>感谢您对本隐私声明的关注和理解！如果您有任何疑问或建议，请随时与我们联系。我们将竭诚为您提供帮助和支持！</p>
        </div>
    </el-dialog>
</template>
<script setup>
import formItem from './components/formItem.vue'
import loginDialog from './components/login.vue'
import selectUser from './components/selectUser.vue'
import { ElMessage } from 'element-plus'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { push } = useRouter()
const { query } = useRoute()
const hasSubmit = ref(false)
const formInfo = ref({})
const formItems = ref([])
const form = ref({})
const login = ref()
const loading = ref(false)
const submitLoading = ref(false)
const selectUserRef = ref()
const formItemRef = ref()
const dialogVisible = ref(false)
const user_info = ref({
    user_type: 0,
    user_id: 0
})

const loginSuccess = (val) => {
    console.log(val)
    user_info.value = val
    getSubmitStatus()
}
const userObject = (val) => {
    formInfo.value.object = val
    login.value.open(formInfo.value)
}
const submitForm = async () => {
    const res = await formItemRef.value.validate()
    console.log(res, 'res')
    if (res) {
        submitLoading.value = true
        let submitParams = {
            survey_id: formInfo.value.id,
            tenant_id: formInfo.value.tenant_id,
            campus_id: formInfo.value.campus_id,
            ...user_info.value,
            submit_info: JSON.stringify(form.value)
        }
        const res = await globalPropValue.surveyFormGlean.submit.post(submitParams)
        submitLoading.value = false
        if (res.code === 200) {
            ElMessage.success('提交成功')
            if (formInfo.value.object == 1) {
                console.log('提交成功', formInfo.value.code)
                localStorage.setItem('surveyCode', formInfo.value.code)
            }
            push({
                name: 'surveyFormGleanResult'
            })
        } else if (res.code === 500100) {
            goResult(true)
        }
    }
    console.log(form.value, '999')
}

const goResult = (has) => {
    push({
        name: 'surveyFormGleanResult',
        query: {
            hasSubmit: has
        }
    })
}

const getSubmitStatus = async () => {
    let submitParams = {
        survey_id: formInfo.value.id,
        tenant_id: formInfo.value.tenant_id,
        campus_id: formInfo.value.campus_id,
        ...user_info.value,
    }
    const res = await globalPropValue.surveyFormGlean.status.get(submitParams)
    if (res.code === 200) {
        hasSubmit.value = res.data
        if (formInfo.value.allow_repeat == 1) {
            hasSubmit.value = false
        }
        if (hasSubmit.value) {
            goResult(hasSubmit.value)
        }
    }
}
const getFormInfo = async () => {
    loading.value = true
    const res = await globalPropValue.surveyFormGlean.formInfo.get({
        code: query.code
    })
    loading.value = false
    if (res.code === 200) {
        formInfo.value = res.data
        formItems.value = JSON.parse(res.data.form_config)
        if (formInfo.value.need_login == 1 && formInfo.value.object !== 4) {
            login.value.open(formInfo.value)
        }
        if (formInfo.value.object == 1 && formInfo.value.allow_repeat !== 1) {
            if (localStorage.getItem('surveyCode') == query.code) {
                hasSubmit.value = true
                if (formInfo.value.allow_repeat == 1) {
                    hasSubmit.value = false
                }
                goResult(hasSubmit.value)
            }
        }
        if (formInfo.value.object == 4) {
            selectUserRef.value.open()
        }
        document.title = "问卷调查-" + formInfo.value.title
    } else {
        setTimeout(() => {
            push({
                name: 'surveyFormGleanResult',
                query: {
                    hasError: true,
                    errorMsg: res.message
                }
            })
        }, 1000)
    }
    console.log(formItems.value)
}

onMounted(() => {
    getFormInfo()
})

</script>

<style>
#app,
body {
    /* min-width: 1600px; */
    min-width: 100%;
}
</style>

<style scoped lang="scss">
.survey-form {
    position: relative;
    font-size: 14px;
    padding-bottom: 20px;
}

.cover {
    width: 100%;
    height: 400px;
    margin-bottom: -100px;
}

.form-content {
    background-color: #fff;
    width: 90%;
    margin: 5%;
    border-radius: 8px;
    padding: 0 20px 20px;
    position: relative;
    z-index: 5;
    box-shadow:var(--el-box-shadow-light) ;

    h1 {
        text-align: center;
        padding: 20px 10px;
    }

    .form-btn {
        padding-top: 20px;
        padding-bottom: 10px;
        text-align: center;
    }

    .form-item {
        margin: 0 auto;
    }
}

.form-footer {
    text-align: center;
}

.dialog-content {
    max-height: 70vh;
    overflow-y: auto;

    p {
        line-height: 1.8;
    }
}

// 媒体查询,在pc设备上,宽度小于1024px,则使用50%的宽度,否则使用100%的宽度
@media (min-width: 1024px) {
    .form-item {
        width: 100%;
    }

    .form-content {
        width: 50%;
        margin: 0 auto 15px;
    }
}

@media (max-width: 1024px) {
    .form-item {
        width: 100%;
    }

    .form-content {
        width: 90%;
        padding: 0 15px 15px;
    }

    .cover {
        width: 100%;
        height: 200px;
    }
}
</style>