import config from '@/config'
import http from '@/utils/request'

export default {
	grade: {
		list: {
			url: `${config.API_URL}/eduapi/grade/list`,
			name: '获取学年列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/eduapi/grade/all`,
			name: '获取学年列表不分页',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/grade/save`,
			name: '新增学年/修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},

		del: {
			url: `${config.API_URL}/eduapi/grade/del`,
			name: '删除学年',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		status: {
			url: `${config.API_URL}/eduapi/grade/changeStatus`,
			name: '修改学年状态',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	},
	class: {
		list: {
			url: `${config.API_URL}/eduapi/class/list`,
			name: '获取学年列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/eduapi/class/all`,
			name: '获取学年列表不分页',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/class/save`,
			name: '新增学年/修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},

		del: {
			url: `${config.API_URL}/eduapi/class/del`,
			name: '删除学年',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		status: {
			url: `${config.API_URL}/eduapi/class/changeStatus`,
			name: '修改学年状态',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	},
	classStu: {
		list: {
			url: `${config.API_URL}/eduapi/class/students`,
			name: '获取学年列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/class/edit_student`,
			name: '编辑学生',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		add: {
			url: `${config.API_URL}/eduapi/class/add_student`,
			name: '新增学生',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/eduapi/class/del_student`,
			name: '移除学生',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	},
	classTeacher: {
		list: {
			url: `${config.API_URL}/eduapi/class/teacher`,
			name: '获取学年列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add: {
			url: `${config.API_URL}/eduapi/class/add_teacher`,
			name: '新增老师',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/eduapi/class/del_teacher`,
			name: '删除老师',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/class/edit_teacher`,
			name: '编辑老师',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	}
}
