<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<cusForm ref="dialogForm" v-model="form" :config="paramsConfig" :mode="mode" @itemClick="itemClick"> </cusForm>

		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>

	<el-dialog v-model="visibleone" title="新增类别" width="300" append-to-body>
		<el-form-item label="名称">
			<el-input v-model="oneName" placeholder="请输入名称"></el-input>
		</el-form-item>

		<template #footer>
			<el-button @click="visibleone = false">取 消</el-button>
			<el-button :loading="isSaveingone" @click="submitone()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { repairTypeMap } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		type: 1,
		name: null,
		parent_id: null,
		id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),

			oneLevel: [],
			visibleone: false,
			isSaveingone: false,
			oneName: null,
			paramsConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: '性质',
						name: 'type',
						value: null,
						component: 'radio',
						options: {
							placeholder: '请选择',
							items: repairTypeMap.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						},
						rules: [{ required: true, message: '请选择性质', trigger: 'blur' }]
					},
					{
						label: '上级类别',
						name: 'parent_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择上级类别',
							items: []
						}
					},
					{
						label: '类别名称',
						name: 'name',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入类别名称'
						},
						rules: [{ required: true, message: '请输入类别名称', trigger: 'blur' }]
					}
				]
			}
		}
	},
	computed: {},
	mounted() {},
	created() {},
	watch: {
		'form.type': {
			handler(val) {
				this.getLevelOne()
			}
		}
	},
	methods: {
		itemClick(row) {
			if (row.name == 'parent_id') {
				this.visibleone = true
			}
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			this.getLevelOne()
			return this
		},
		async getLevelOne() {
			var res = await this.$API.campusRepair.configAll.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			})
			let oneLevel = []
			res.data.forEach((item) => {
				if (item.id === this.form.type && item.child) {
					oneLevel.push(...item.child)
				}
			})
			this.paramsConfig.formItems[1].options.items = oneLevel.map((v) => {
				return {
					label: v.name,
					value: v.id
				}
			})
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					this.form.parent_id=parseInt(this.form.parent_id)
					var res = await this.$API.campusRepair.configSave.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		async submitone() {
			this.isSaveingone = true

			var res = await this.$API.campusRepair.configSave.post({
				campus_id: this.params.campus_id,
				tenant_id: this.params.tenant_id,
				parent_id: 0,
				name: this.oneName,
				type: this.form.type
			})
			this.isSaveingone = false
			if (res.code === 200) {
				this.$emit('success', this.form, this.mode)
				this.visibleone = false
				this.oneName = null
				this.getLevelOne()
				this.$message.success('操作成功')
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			if (this.form.parent_id === 0 || this.form.parent_id === '') {
				this.form.parent_id = ''
			}
		}
	}
}
</script>

<style></style>
