<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode"> </cusForm>

		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { semesterInfo, dormitoryEvaluateTypeMap } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		tenant_id: null,
		campus_id: null,
		type_id: null,
		evaluate_name: null,
		score: null,
		max_score: null,
		nature: null
	}
}
import { ElMessageBox } from 'element-plus'

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				oth_semester_id: [{ required: true, message: '请选择学期' }],
				oth_academic_id: [{ required: true, message: '请选择学年' }]
			},
			//所需数据选项
			groups: [],
			groupsProps: {
				value: 'id',
				label: 'semester_name',
				emitPath: false
			},
			gradeOptions: [],
			semesterInfo,
			grade: [],
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				labelWidth: '80px',

				formItems: [
					{
						label: '类别',
						name: 'type_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择',
							items: dormitoryEvaluateTypeMap.map((item) => ({ label: item.name, value: item.value }))
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '评价名称',
						name: 'evaluate_name',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入'
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '分值',
						name: 'score',
						value: null,
						component: 'number',
						options: {
							placeholder: '请输入',
							controlsPosition: '1',
							precision: 1
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '最大分值',
						name: 'max_score',
						value: null,
						component: 'number',
						options: {
							placeholder: '请输入',
							controlsPosition: '1',
							precision: 1
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '性质',
						name: 'nature',
						value: null,
						component: 'radio',
						options: {
							placeholder: '请输入',
							items: [
								{ label: '正面', value: 1 },
								{ label: '负面', value: -1 }
							]
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					}
				]
			}
		}
	},
	mounted() {},
	watch: {},
	computed: {},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.form.mode = mode
			if (mode == 'add') {
				this.form.campus_id = this.params.campus_id
				this.form.tenant_id = this.params.tenant_id
			}
			this.visible = true
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true

					var res = await this.$API.buildingRooms.evaluate.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style scoped lang="scss">
.tip {
	color: #808080;
	background-color: #f6f8fa;
	padding: 20px;
}
</style>
