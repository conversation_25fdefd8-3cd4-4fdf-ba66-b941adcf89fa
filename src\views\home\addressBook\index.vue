<template>
	<el-container>
		<el-aside>
			<el-container>
				<el-header>
					<ul class="tabs">
						<li
							v-for="item in tabsMap"
							:key="item.field"
							:class="{ active: item.name == tabName }"
							@click="tabsSelect(item)"
						>
							{{ item.title }}
						</li>
					</ul>
				</el-header>
				<el-main>
					<el-tree
						ref="group"
						class="menu"
						node-key="id"
						:data="groupData"
						highlight-current
						:current-node-key="treeNodeId"
						:expand-on-click-node="false"
						:props="treeDefaultProps"
						:default-expanded-keys="defaultExpanded"
						@node-click="groupClick"
					>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header class="el-h">
				<el-select v-model="params.campus_id" placeholder="校区" :teleported="false" @change="campusChange">
					<el-option
						v-for="item in CampusManagementList"
						:key="item.code"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
				<el-select
					v-model="params.staff_type"
					placeholder="教职工类型"
					:teleported="false"
					clearable
					@change="staffTypeChange"
				>
					<el-option
						v-for="(item, index) in staffTypeMap"
						:key="index"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
				<el-select
					v-model="params.work_status"
					placeholder="在职状态"
					:teleported="false"
					clearable
					@change="workStatusChange"
				>
					<el-option
						v-for="(item, index) in workStatusMap"
						:key="index"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>

				<el-input v-model="params.name" style="width: 350px; margin: 0" placeholder="请输入姓名" @input="searchKey">
					<template #prefix>
						<el-icon class="el-input__icon">
							<el-icon-search />
						</el-icon>
					</template>
				</el-input>
			</el-header>
			<el-main>
				<div v-if="addressBook?.length" style="height: 100%">
					<ul class="address-book">
						<li v-for="item in addressBook">
							<cusStaffHead :src="item.user_head" style="width: 50px; height: 50px"></cusStaffHead>
							<div class="info">
								<div class="name">{{ item.name }}</div>
								<div class="info-item">性别：{{ formData(sexMap, item.sex) }}</div>
								<div class="info-item">电话：{{ item.phone || '-' }}</div>
								<div class="info-item">邮箱：{{ item.email || '-' }}</div>
							</div>
						</li>
					</ul>
					<div class="pagination">
						<el-pagination
							size="small"
							:hide-on-single-page="true"
							background
							layout="total, prev, pager, next"
							:total="total"
							:page-size="20"
							class="mt-4"
							@current-change="pageChange"
						/>
					</div>
				</div>
				<el-empty v-else :image-size="200" />
			</el-main>
		</el-container>
	</el-container>
</template>
<script setup>
import cusStaffHead from '@/components/custom/cusStaffHead.vue'
import cusTom from '@/utils/cusTom'
import { ref, reactive, getCurrentInstance, onMounted, nextTick } from 'vue'

const { campusId, tenantId, campusInfo, workStatusMap, staffTypeMap, sexMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const CampusManagementList = ref(campusInfo)
const params = ref({
	campus_id: campusId,
	tenant_id: tenantId,
	work_status: '',
	staff_type: '',
	name: ''
})
const treeDefaultProps = ref({
	children: 'children',
	label: 'name'
})
const tabName = ref('department')
const tabsMap = ref([
	{
		name: 'department',
		title: '部门',
		field: 'department_id'
	},
	{
		name: 'subject',
		title: '学科',
		field: 'course_id'
	}
])
const tabsSelect = (val) => {
	page.value = 1
	tabName.value = val.name
	switch (val.name) {
		case 'department':
			getDept()
			break
		case 'subject':
			getEduDiscipline()
			break
	}
}
onMounted(() => {
	callSet(tabName.value)
	//getData()
})
//数据回显格式化
const formData = (arr, val) => {
	return arr.find((v) => v.value == val)?.name || val
}
const treeNodeId = ref(null)
const groupClick = (data) => {
	page.value = 1
	treeNodeId.value = data.id
	getData()
}
const campusChange = () => {
	page.value = 1
	callSet(tabName.value)
	getData()
}
const staffTypeChange = (val) => {
	page.value = 1
	params.value.staff_type = val
	getData()
}
const workStatusChange = (val) => {
	page.value = 1
	params.value.work_status = val
	getData()
}
const timer = ref(null)
const searchKey = () => {
	if (timer.value) clearTimeout(timer.value)
	timer.value = setTimeout(() => {
		page.value = 1
		getData()
	}, 700)
}
//组件副作用回调
const callSet = (val) => {
	treeNodeId.value = null
	groupData.value = []
	switch (val) {
		case 'department':
			getDept()
			break
		case 'subject':
			getEduDiscipline()
			break
	}
}
//获取部门
const getDept = async () => {
	const res = await globalPropValue.system.dept.all.get(params.value)
	transData(res, 'department_name', 'tree')
}
//获取学科
const getEduDiscipline = async () => {
	var res = await globalPropValue.eduCourseSet.course.all.get(params.value)
	transData(res, 'course_name')
}
const addressBook = ref([])
const total = ref(0)
const page = ref(1)
const pageChange = (val) => {
	page.value = val
	getData()
}
const getData = async () => {
	let customParams = {
		[tabsMap.value.find((v) => v.name === tabName.value).field]: treeNodeId.value,
		page: page.value,
		pageSize: 20
	}
	const res = await globalPropValue.personnel.staff.list.get({
		...params.value,
		...customParams
	})
	addressBook.value = res.data.rows
	total.value = res.data.total
}
const groupData = ref([])
const defaultExpanded = ref([])
const group = ref(null)
const transData = (res, field, type) => {
	if (!res.data) res.data = []
	groupData.value = res.data.map((v) => {
		v.name = v[field]
		return v
	})
	groupData.value.unshift({
		id: 0,
		name: '全部'
	})
	//treeNodeId.value = groupData.value[0].id
	if (type == 'tree') {
		groupData.value = cusTom.arrayToTree(groupData.value, 'id', 'parent_id')
		defaultExpanded.value[0] = groupData.value[0].id
		//treeNodeId.value = groupData.value[0].children[0].id
	}
	group.value?.setCurrentKey(treeNodeId.value, false)
	getData()
	// console.log(groupData.value,group.value, 'groupData')
}
</script>
<style lang="scss" scoped>
.el-h {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	border: none;

	> .el-select {
		margin-right: 15px;
		margin-top: 0;
		margin-bottom: 0;
	}
}

.tabs {
	display: flex;
	height: 32px;
	width: 100%;
	border: 1px solid var(--el-color-primary-light-8);
	border-radius: 5px;
	li {
		flex: 1;
		text-align: center;
		line-height: 32px;
		font-size: 14px;
		cursor: pointer;
	}

	.active {
		background: var(--el-color-primary-light-8);
		color: var(--el-color-primary);
		font-weight: bold;
	}
}

.address-book {
	overflow: auto;
	display: inline-flex;
	flex-wrap: wrap;
	width: 100%;

	li {
		width: 24%;
		margin-right: 10px;
		margin-bottom: 15px;
		display: flex;
		padding: 15px;
		border-radius: 5px;
		border: 1px solid var(--el-border-color-light);

		&:hover {
			box-shadow: 0 0 9px 2px var(--el-border-color-light);
		}

		.info {
			margin-left: 10px;

			.name {
				line-height: 35px;
				font-weight: bold;
				font-size: 16px;
			}

			.info-item {
				line-height: 20px;
			}
		}
	}
}

.pagination {
	width: 100%;
	height: 50px;
	line-height: 50px;
	background: var(--el-bg-color-overlay);
	display: inline-flex;
	justify-content: flex-start;
	border-top: 1px solid var(--el-border-color-light);
	position: sticky;
	bottom: -10px;
}
</style>
