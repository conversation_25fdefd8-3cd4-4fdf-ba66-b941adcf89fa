<template>
	<sc-dialog v-model="dialogShow" draggable :title="title" width="30%">
		<el-form ref="dialogForm" :model="formData" :rules="rules" :loading="loading" label-width="110">
			<el-form-item label="学校名称" prop="tenant_name">
				<el-input v-model="formData.tenant_name" placeholder="请输入学校名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="学校代号" prop="tenant_code">
				<el-input v-model="formData.tenant_code" placeholder="请输入学校代号,为空会自动生成" clearable></el-input>
			</el-form-item>
			<el-form-item label="学校LOGO" prop="tenant_logo">
				<scUpload v-model="formData.tenant_logo" fileTypeTag="tenant"></scUpload>
			</el-form-item>

			<el-form-item label="联系人" prop="contacts">
				<el-input v-model="formData.contacts" placeholder="请输入联系人" clearable></el-input>
			</el-form-item>
			<el-form-item label="电话" prop="phone">
				<el-input v-model="formData.phone" placeholder="请输入联系人电话" clearable></el-input>
			</el-form-item>
			<el-form-item label="邮箱" prop="email">
				<el-input v-model="formData.email" placeholder="请输入联系人邮箱" clearable></el-input>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input v-model="formData.remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>

		<template #footer>
			<el-button @click="dialogShow = false">取 消</el-button>
			<el-button type="primary" @click="confirm">确 定</el-button>
		</template>
	</sc-dialog>
</template>

<script>
import { ElMessage } from 'element-plus'

const defaultData = () => {
	return {
		id: null,
		tenant_name: null,
		tenant_code: null,
		tenant_logo: null,
		address: null,
		contacts: null,
		phone: null,
		email: null,
		remark: null
	}
}

export default {
	name: '',
	data() {
		return {
			title: null,
			openType: null,
			dialogShow: false,
			formData: defaultData(),
			loading: false,
			rules:{
				tenant_name: [{ required: true, message: '学校名称不能为空', trigger: 'blur' }],
			},
		}
	},
	methods: {
		async confirm() {
			const isSub = await this.$refs.dialogForm.validate()
			if (!isSub) return
			const { code } = await this.$API.tenant.save.post({
				...this.formData
			})
			if (code === 200) {
				ElMessage.success('操作成功')
				this.close()
			}
		},
		close() {
			this.dialogShow = false
			this.$emit('close')
		},
		open(row) {
			this.formData = defaultData()
			if (row) {
				this.openType = 'edit'
				this.title = '编辑学校'
				this.formData = row
			} else {
				this.openType = 'create'
				this.title = '新增学校'
			}
			this.dialogShow = true
		}
	}
}
</script>

<style scoped></style>
