<template>
	<div :class="{ hidden: hidden }" class="pagination-container">
		<el-pagination
			v-model:current-page="currentPage"
			v-model:page-size="pageSize"
			:background="background"
			:layout="layout"
			:page-sizes="pageSizes"
			:total="total"
			small
			v-bind="$attrs"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		/>
	</div>
</template>

<script>
import { defineComponent, computed } from 'vue'
const limitDefault = 20

export default defineComponent({
	name: 'pagination',
	props: {
		total: {
			required: true,
			type: Number
		},
		page: {
			type: Number,
			default: 1
		},
		size: {
			type: Number,
			default: limitDefault
		},
		pageSizes: {
			type: Array,
			default() {
				return [10, 20, 30, 50]
			}
		},
		layout: {
			type: String,
			default: 'total, sizes, prev, pager, next, jumper'
		},
		background: {
			type: Boolean,
			default: true
		},
		autoScroll: {
			type: Boolean,
			default: true
		},
		hidden: {
			type: Boolean,
			default: false
		}
	},
	setup(props, { emit }) {
		let currentPage = computed({
			get: () => {
				return props.page
			},
			set: (val) => {
				emit('update:page', val)
				return val
			}
		})
		let pageSize = computed({
			get: () => {
				return props.size
			},
			set: (val) => {
				emit('update:size', val)
				return val
			}
		})
		const handleSizeChange = (val) => {
			emit('pagination', { page: currentPage, size: val })
		}
		const handleCurrentChange = (val) => {
			emit('pagination', { page: val, size: pageSize })
		}
		return {
			currentPage,
			pageSize,
			handleSizeChange,
			handleCurrentChange
		}
	}
})
</script>

<style scoped>
.pagination-container {
	display: flex;
	align-items: center;
	padding: 0 15px;
}
.pagination-container.hidden {
	display: none;
}
</style>
