<template>
	<el-container>
		<div class="pageView">
			<div class="view_header">
				<div class="left-panel-search">
					<el-form-item label="校区">
						<el-select v-model="params.campus_id" placeholder="请选择校区" filterable @change="campusChange">
							<el-option v-for="item in campusInfoAdd" :key="item.code" :label="item.name" :value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="学期">
						<cusSelectSemester
							v-model="params.semester_id"
							:params="params"
							clearable
							placeholder="请选择学期"
							showDefaultValue
							style="margin-right: 15px"
							width="200px"
							@semesterChange="semesterChange"
						/>
					</el-form-item>
					<el-form-item style="margin-left: 10px">
						<el-switch
							v-model="params.is_spare_bed"
							active-text="只看空余床位"
							:inactive-value="0"
							:active-value="1"
							@change="init"
						/>
					</el-form-item>
				</div>
			</div>

			<div class="view_panel flex_ac">
				<div
					v-for="item in viewInfo"
					:key="item.label"
					class="view_panel_item"
					:style="{ 'background-image': `url(${item.img})` }"
				>
					<div class="label">{{ item.label }}</div>
					<div class="val">{{ item.value }}</div>
				</div>
			</div>

			<div class="roompart">
				<el-tabs type="border-card" tab-position="top">
					<el-tab-pane v-for="item in tabs" :key="item.building_id" :label="item.building_name">
						<div v-for="v in item.floor_list" :key="item.floor_id" class="roompart_item">
							<div class="roompart_item_floor">{{ v.floor_name }}</div>
							<div class="roompart_item_right">
								<div
									v-for="i in v.room_list"
									:key="i.room_id"
									class="roompart_item_room"
									:class="{ empty: i.room_empty_num == 0 }"
								>
									<div class="roompart_item_room_code">
										{{ i.room_code }}
									</div>
									<p v-if="i.room_empty_num">空余{{ i.room_empty_num }}人</p>
									<p v-else>满人</p>
									<div class="roompart_item_room_student">
										<el-icon><el-icon-UserFilled /></el-icon>
										{{ i.room_occupancy_num }}人
									</div>
								</div>
							</div>
						</div>
					</el-tab-pane>
				</el-tabs>
			</div>
		</div>
	</el-container>
</template>

<script>
import cusSelectSemester from '@/components/custom/cusSelectSemester'
import cusTom from '@/utils/cusTom'
import { loadImageUrl, loadImageUrl2 } from '@/utils/load'
const { campusId, tenantId, campusInfo, tenantInfo, semesterInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		campus_id: campusId,
		tenant_id: tenantId,
		academic_year_id: null,
		semester_id: null,
		is_spare_bed: 0
	}
}

import qss from '@/assets/img/custom/qss.png'
import cws from '@/assets/img/custom/cws.png'
import xss from '@/assets/img/custom/xss.png'
import kcs from '@/assets/img/custom/kcs.png'
import rzl from '@/assets/img/custom/rzl.png'

export default {
	name: 'DormitoryView',
	components: {
		cusSelectSemester
	},
	data() {
		return {
			campusInfo,
			params: defaultParams(),
			viewInfo: [
				{
					label: '宿舍楼',
					value: 0,
					img: qss
				},
				{
					label: '床位数',
					value: 0,
					img: cws
				},
				{
					label: '学生数',
					value: 0,
					img: xss
				},
				{
					label: '空余床位数',
					value: 0,
					img: kcs
				},
				{
					label: '入住率',
					value: 0,
					img: rzl
				}
			],
			tabs: [],
			semesterInfo
		}
	},
	watch: {
		params: {
			handler(val) {
				this.init()
			},
			deep: true
		},
		'params.campus_id': {
			handler(val) {
				this.params.academic_year_id = null
			}
		},
		'params.academic_year_id': {
			handler(val) {
				this.params.semester_id = null
			}
		}
	},
	computed: {
		campusInfoAdd() {
			return [
				{
					name: '全部',
					code: '-1',
					value: -1
				},
				...campusInfo
			]
		},
		getAcademic_year() {
			return this.semesterInfo.filter((v) => v.parent_id == 0 && v.campus_id == this.params.campus_id)
		},
		getSemester() {
			return this.semesterInfo.filter(
				(v) => v.parent_id != 0 && v.parent_id == this.params.academic_year_id && v.campus_id == this.params.campus_id
			)
		}
	},
	methods: {
		campusChange(val) {
			this.params.campus_id = val
			this.init()
		},
		semesterChange(val) {
			this.params.semester_id = val
			this.init()
		},
		async init() {
			const { data } = await this.$API.buildingRooms.view.get(this.params)
			this.viewInfo[0].value = data.total_room
			this.viewInfo[1].value = data.total_capacity
			this.viewInfo[2].value = data.total_user
			this.viewInfo[3].value = data.total_empty_capacity
			this.viewInfo[4].value = data.occupancy_rate
			this.tabs = data.list
		}
	},
	created() {
		this.init()
	}
}
</script>

<style lang="scss" scoped>
.view_header {
	width: 100%;
	height: 50px;
	background-color: var(--el-bg-color-overlay);
	display: flex;
	align-items: center;
	padding: 0 8px;
}

.view_panel {
	margin-top: 10px;
	width: 100%;
	background-color: var(--el-bg-color-overlay);
	padding: 20px;

	.view_panel_item {
		width: 200px;
		height: 100px;
		border-radius: 8px;
		background-color: #eee;
		padding: 10px 20px;
		box-sizing: border-box;
		background-size: 100% 100%;

		.label {
			font-size: 18px;
		}

		.val {
			font-size: 30px;
			font-weight: 500;
			width: 100%;
			text-align: center;
			margin-top: 10px;
		}
	}
}

.roompart {
	width: 100%;
	margin-top: 10px;
	flex: 1;
	overflow: hidden;

	::v-deep(.el-tabs--border-card) {
		height: 100%;
		display: flex;
		flex-direction: column;
		border-radius: 5px;

		.el-tabs__header {
			border-radius: 5px 5px 0 0;
			overflow: hidden;
		}

		.el-tabs__content {
			flex: 1;
			overflow: auto;
			border-radius: 0 0 5px 5px;
		}
	}

	.roompart_item {
		display: flex;
		--borderColor: #4080ff;
		--backgroundColor: #9fceff;
		--itemBg: #4080ff;
		--itemCode: hsla(0, 0%, 100%, 0.3);
		--itemStu: hsla(0, 0%, 100%, 0.3);

		.roompart_item_floor {
			display: flex;
			align-items: center;
			justify-content: center;
			box-sizing: border-box;
			width: 32px;
			border-radius: 4px;
			border: 1px solid var(--borderColor);
			min-height: 110px;
			padding: 5px;
			text-align: center;
			background-color: var(--backgroundColor);
			flex-shrink: 0;
			margin-bottom: 10px;
			margin-right: 10px;
		}

		.roompart_item_right {
			display: flex;
			flex-wrap: wrap;
		}

		.roompart_item_room {
			position: relative;
			width: 166px;
			height: 110px;
			box-shadow: 0 2px 3px 2px hsla(0, 0%, 80%, 0.5);
			border-radius: 4px;
			background-color: var(--itemBg);
			margin-bottom: 10px;
			margin-left: 10px;
			overflow: hidden;

			.roompart_item_room_code {
				background: var(--itemCode);
				width: 60px;
				height: 28px;
				border-bottom-right-radius: 4px;
				line-height: 28px;
				text-align: center;
				color: #fff;
			}

			p {
				text-align: center;
				color: #fff;
				margin-top: 20px;
				font-weight: bold;
				font-size: 14px;
			}

			.roompart_item_room_student {
				position: absolute;
				bottom: 0;
				left: 0;
				width: 100%;
				height: 32px;
				text-align: center;
				line-height: 32px;
				background: var(--itemStu);
			}
		}

		.empty {
			background-color: var(--el-bg-color-overlay);

			.roompart_item_room_code {
				background-color: var(--el-bg-color-overlay);
				color: unset;
			}

			.roompart_item_room_student {
				background-color: #f4f4f4;
			}

			p {
				color: unset;
				font-weight: bold;
			}
		}
	}
}
</style>
