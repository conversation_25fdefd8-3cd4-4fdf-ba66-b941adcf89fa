<template>
	<el-scrollbar height="500px">
		<div v-for="(item, index) in dataValue" :key="index" class="slice" @click="handlerTime(item.time_begin)">
			<el-image style="width: 80px; height: 80px; float: left; margin-right: 10px" :src="item.cover_url" fit="cover" />
			<div class="desc">
				<p>
					<b>{{ item.name }} （{{ item.time_begin }}s）</b>
				</p>
				<p>{{ item.description }}</p>
			</div>
		</div>
		<!--
		<el-table :data="dataValue">
			<el-table-column label="切片名称" prop="name"></el-table-column>
			<el-table-column label="切片描述" prop="description"></el-table-column>
&lt;!&ndash;			<el-table-column label="开始时间" prop="time_begin">
				<template #default="{ row }">
					<el-link type="primary"> {{ row.time_begin + 's' }}</el-link>
				</template>
			</el-table-column>&ndash;&gt;
		</el-table>-->
	</el-scrollbar>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
let dataValue = ref([])
const props = defineProps(['data', 'sliceList'])
const emit = defineEmits(['changeTime'])
const getList = async () => {
	if (props.sliceList.length > 0) {
		dataValue.value = props.sliceList
		return
	}
	const res = await globalPropValue.eduMaterials.material.get_video_slice_list.get({
		tenant_id: props.data.tenant_id,
		campus_id: props.data.campus_id,
		material_id: props.data.id
	})
	if (res.code === 200) {
		dataValue.value = res.data
	}
}
onMounted(() => {
	getList()
})
const handlerTime = (time) => {
	emit('changeTime', time)
}
</script>

<style scoped lang="scss">
.slice {
	min-height: 80px;
	margin-bottom: 10px;
	background: var(--el-fill-color-light);
	cursor: pointer;
	border-radius: 6px;
}

.desc {
	padding: 5px 0;
	line-height: 25px;
}
</style>
