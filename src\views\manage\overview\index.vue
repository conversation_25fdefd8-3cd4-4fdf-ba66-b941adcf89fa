<template>
	<el-container>
		<el-header v-if="CampusManagementList.length > 1">
			<div class="left-panel">
				<div class="left-panel-search">
					<el-select
						v-if="CampusManagementList.length > 1"
						v-model="params.campus_id"
						placeholder="校区"
						filterable
						style="margin-right: 15px"
						@change="campusChange"
					>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</div>
			</div>
		</el-header>
		<el-main style="background-color: unset; padding: unset">
			<div v-loading="loading" class="echartsOut">
				<el-row :gutter="20">
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.campus_num">
								<template #title>校区</template>
								<template #suffix>个</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.grade_num">
								<template #title>年级</template>
								<template #suffix>个</template>
							</el-statistic>
						</el-card>
					</el-col>

					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.class_count">
								<template #title>班级</template>
								<template #suffix>个</template>
							</el-statistic>
						</el-card>
					</el-col>

					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.student_count">
								<template #title>学生</template>
								<template #suffix>名</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.teacher_count">
								<template #title>教师</template>
								<template #suffix>名</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.course_count">
								<template #title>学科</template>
								<template #suffix>个</template>
							</el-statistic>
						</el-card>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="6">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="teacherGrade" width="100%" height="300px"></scEcharts>
						</el-card>
					</el-col>
					<el-col :span="6">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="teacherAge" width="100%" height="300px"></scEcharts>
						</el-card>
					</el-col>

					<el-col :span="6">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="teacherIdentity" width="100%" height="300px"></scEcharts>
						</el-card>
					</el-col>

					<el-col :span="6">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="teacherCourse" width="100%" height="300px"></scEcharts>
						</el-card>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.notice_num">
								<template #title>学校公告</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.evaluation_num">
								<template #title>学生评教</template>
								<template #suffix>次</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.student_leave_count">
								<template #title>学生请假</template>
								<template #suffix>次</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.teacher_leave_count">
								<template #title>教职工请假</template>
								<template #suffix>次</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.room_booking_count">
								<template #title>场室预约</template>
								<template #suffix>次</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.room_count">
								<template #title>场室</template>
								<template #suffix>个</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.open_room_count">
								<template #title>开放场室</template>
								<template #suffix>个</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.assets_count">
								<template #title>资产设备</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.consumable_count">
								<template #title>耗材</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.car_count">
								<template #title>车辆</template>
								<template #suffix>辆</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.repair_count">
								<template #title>校园报修</template>
								<template #suffix>次</template>
							</el-statistic>
						</el-card>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="6">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="studentGrade" width="100%" height="300px"></scEcharts>
						</el-card>
					</el-col>
					<el-col :span="6">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="studentSex" width="100%" height="300px"></scEcharts>
						</el-card>
					</el-col>
				</el-row>
			</div>
		</el-main>
	</el-container>
</template>
<script>
import cusTom from '@/utils/cusTom'
import scEcharts from '@/components/scEcharts/index.vue'
import { ElMessage } from 'element-plus'

const { campusInfo, tenantId, campusId } = cusTom.getBaseQuery()

export default {
	name: 'index',
	components: {
		scEcharts
	},
	data() {
		return {
			params: {
				tenant_id: tenantId,
				campus_id: campusId,
				semester_id: null
			},
			loading: false,
			overviewData: {},
			CampusManagementList: [],
			teacherGrade: {
				color: [
					'#F6BD16',
					'#6F5EF9',
					'#6DC8EC',
					'#945FB9',
					'#FF9845',
					'#1E9493',
					'#FF99C3',
					'#AABA01',
					'#BC7CFC',
					'#237CBC',
					'#2DE379',
					'#CE8032',
					'#FF7AF4',
					'#545FD3',
					'#AFE410',
					'#D8C608',
					'#FFA1E0'
				],
				title: {
					text: '教师年级分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			teacherAge: {
				color: [
					'#6F5EF9',
					'#6DC8EC',
					'#945FB9',
					'#FF9845',
					'#1E9493',
					'#FF99C3',
					'#AABA01',
					'#BC7CFC',
					'#237CBC',
					'#2DE379',
					'#CE8032',
					'#FF7AF4',
					'#545FD3',
					'#AFE410',
					'#D8C608',
					'#FFA1E0'
				],
				title: {
					text: '教师年龄分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			teacherIdentity: {
				title: {
					text: '教师身份',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			teacherCourse: {
				title: {
					text: '学科教师',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},

			studentGrade: {
				color: [
					'#545FD3',
					'#237CBC',
					'#2DE379',
					'#CE8032',
					'#FF7AF4',
					'#AFE410',
					'#D8C608',
					'#FFA1E0',
					'#6F5EF9',
					'#6DC8EC',
					'#945FB9',
					'#FF9845',
					'#1E9493',
					'#FF99C3',
					'#AABA01',
					'#BC7CFC'
				],
				title: {
					text: '学生年级分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			studentSex: {
				color: ['#FFA1E0', '#237CBC', '#CE8032'],
				title: {
					text: '学生性别分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			}
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	created() {
		//获取总览数据
		this.CampusManagementList = campusInfo
		this.getData()
	},
	methods: {
		campusChange(val) {
			this.params.campus_id = val
			this.getData()
		},
		async getData() {
			this.loading = true
			const res = await this.$API.common.campusOverview.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.overviewData = res.data

				if (res.data.teacher_grade !== null) {
					this.teacherGrade.series[0].data = res.data.teacher_grade.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.teacherGrade.series[0].data = []
				}

				if (res.data.teacher_age !== null) {
					this.teacherAge.series[0].data = res.data.teacher_age.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.teacherAge.series[0].data = []
				}

				if (res.data.teacher_course !== null) {
					this.teacherCourse.series[0].data = res.data.teacher_course.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.teacherCourse.series[0].data = []
				}

				if (res.data.teacher_identity !== null) {
					this.teacherIdentity.series[0].data = res.data.teacher_identity.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.teacherIdentity.series[0].data = []
				}
				if (res.data.student_grade !== null) {
					this.studentGrade.series[0].data = res.data.student_grade.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.studentGrade.series[0].data = []
				}

				if (res.data.student_sex !== null) {
					this.studentSex.series[0].data = res.data.student_sex.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.studentSex.series[0].data = []
				}
			} else {
				ElMessage({ type: 'error', message: res.message })
			}
		}
	}
}
</script>
<style scoped lang="scss">
.echartsOut {
	margin-top: 10px;
}

:deep(.el-statistic) {
	.el-statistic__number {
		font-size: 26px;
	}

	.el-statistic__head {
		font-size: 16px;
		font-weight: bold;
		line-height: 30px;
	}

	.el-statistic__content {
		text-align: center;
		line-height: 30px;
	}

	.el-statistic__suffix {
		color: var(--el-text-color-secondary);
		font-size: 14px;
	}
}

:deep(.room) {
	.el-statistic__suffix {
		font-size: 26px;
		color: var(--el-text-color-primary);
	}
}
</style>
