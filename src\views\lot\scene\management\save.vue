<template>
	<el-drawer v-model="visible" :title="titleMap[mode]" :size="700" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-position="top">
			<el-card header="基础信息">
				<el-form-item label="场景名称" prop="scene_name">
					<el-input v-model="form.scene_name" placeholder="请输入场景名称" clearable></el-input>
				</el-form-item>

				<el-form-item label="启用状态" prop="status">
					<el-switch
						v-model="form.status"
						:active-value="1"
						:inactive-value="-1"
						active-text="开"
						inline-prompt
						style="--el-switch-on-color: #00b42a"
						inactive-text="关"
					></el-switch>
				</el-form-item>
				<el-form-item label="生效场室" prop="room_list">
					<cusSelectField
						v-model="form.room_list"
						width="100%"
						:multiple="true"
						placeholder="请选择生效场室"
					></cusSelectField>
				</el-form-item>
				<el-form-item label="场景描述" prop="scene_desc">
					<el-input v-model="form.scene_desc" rows="2" clearable type="textarea"></el-input>
				</el-form-item>
			</el-card>
			<el-card header="触发动作" style="margin-top: 15px">
				<div v-for="(triggerItem, index) in form.action_list" :key="index" class="triggerItem">
					<el-row :gutter="10">
						<el-col :span="6">
							<el-form-item
								label="设备类别"
								:prop="`action_list.${index}.filter_type`"
								:rules="{ required: true, message: '请选择设备类别', trigger: 'blur' }"
							>
								<el-select
									v-model="form.action_list[index].filter_type"
									style="width: 100%; max-width: unset"
									placeholder="设备类别"
									:disabled="filterDisable"
								>
									<el-option label="单个设备" :value="1" />
									<el-option label="单类设备" :value="2" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item
								label="产品品类"
								:prop="`action_list.${index}.product_id`"
								:rules="{ required: true, message: '请选择产品品类', trigger: 'blur' }"
							>
								<el-select
									v-model="form.action_list[index].product_id"
									style="width: 100%; max-width: unset"
									placeholder="请选择产品品类"
									clearable
									@change="(value) => changeProduct(value, index)"
								>
									<el-option
										v-for="(item, i) in productList"
										:key="i"
										:label="item.product_name"
										:value="item.id"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col v-if="form.action_list[index].filter_type === 1" :span="12">
							<el-form-item
								label="设备"
								:prop="`action_list.${index}.device_id`"
								:rules="{ required: true, message: '请选择设备', trigger: 'blur' }"
							>
								<el-select
									v-model="form.action_list[index].device_id"
									style="width: 100%; max-width: unset"
									placeholder="请选择设备"
									clearable
								>
									<el-option
										v-for="(item, i) in triggerDeviceList[index]"
										:key="i"
										:label="item.device_name"
										:value="item.id"
									>
										<span style="float: left">{{ item.device_name }}</span>
										<span
											v-if="item.room_info"
											style="float: right; color: var(--el-text-color-secondary); font-size: 12px"
										>
											{{ item.room_info.name }}
										</span>
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item
								label="功能属性"
								:prop="`action_list.${index}.tsl_id`"
								:rules="{ required: true, message: '请选择功能属性', trigger: 'blur' }"
							>
								<el-select
									v-model="form.action_list[index].tsl_id"
									style="width: 100%; max-width: unset"
									placeholder="请选择功能属性"
									clearable
									@change="(value) => selectTriggerTsl(value, index)"
								>
									<el-option v-for="(item, i) in triggerWriterTsl[index]" :key="i" :label="item.name" :value="item.id">
										<span style="float: left">{{ item.name }}</span>
										<span style="float: right; color: var(--el-text-color-secondary); font-size: 12px">
											{{ item.code }}
										</span>
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								v-if="
									this.selectTriggerTslInfo[index]?.type_spec?.type !== 'bool' &&
									this.selectTriggerTslInfo[index]?.type_spec?.type !== 'enum'
								"
								label="设定"
								:prop="`action_list.${index}.value`"
								:rules="{ required: true, message: '请选择输入设定值', trigger: 'blur' }"
							>
								<el-input
									v-model="form.action_list[index].value"
									placeholder="请输入值"
									style="width: 100%; max-width: unset"
								/>
							</el-form-item>

							<el-form-item
								v-if="this.selectTriggerTslInfo[index]?.type_spec?.type === 'enum'"
								label="设定"
								prop="value"
							>
								<el-select
									v-model="form.action_list[index].value"
									style="width: 100%; max-width: unset"
									placeholder="请选择"
									clearable
								>
									<el-option
										v-for="(item, i) in this.selectTriggerTslInfo[index]?.type_spec?.specs"
										:key="i"
										:label="item.name"
										:value="item.value + ''"
									/>
								</el-select>
							</el-form-item>
							<el-form-item
								v-if="this.selectTriggerTslInfo[index]?.type_spec?.type === 'bool'"
								label="设定"
								prop="value"
							>
								<el-select
									v-model="form.action_list[index].value"
									style="width: 100%; max-width: unset"
									placeholder="请选择"
									clearable
								>
									<el-option
										v-for="(item, i) in this.selectTriggerTslInfo[index]?.type_spec?.specs"
										:key="i"
										:label="item"
										:value="i + ''"
									/>
								</el-select>
							</el-form-item>
						</el-col>
						<div class="delAction">
							<el-button type="danger" text @click="delItem(index)">
								<el-icon size="18">
									<el-icon-CircleClose />
								</el-icon>
							</el-button>
						</div>
					</el-row>
				</div>
				<div>
					<el-button
						v-if="form.action_list.length < 15"
						plain
						round
						type="success"
						style="width: 100%"
						icon="el-icon-CirclePlus"
						@click="addTrigger"
						>添加</el-button
					>
				</div>
			</el-card>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import cusTom from '@/utils/cusTom'

const { tenantId, campusId } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		scene_name: '',
		scene_desc: '',
		room_id: [],
		room_list: [],
		action_list: [
			{
				scene_id: null,
				filter_type: 2,
				product_id: null,
				device_id: null,
				tsl_id: null,
				value: null
			}
		],
		status: 1
	}
}

export default {
	emits: ['success', 'closed'],
	props: {},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增规则',
				edit: '编辑规则',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			enumConfig: [],
			productList: [],
			triggerWriterTsl: [],
			triggerDeviceList: [],
			selectTriggerTslInfo: [],
			filterDisable: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				scene_name: [{ required: true, message: '请输入场景名称' }],
				room_list: [{ required: true, message: '请选择生效场室' }],
				status: [{ required: true, message: '请选择告警启用状态' }]
			}
		}
	},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	watch: {
		'form.room_list': function (val) {
			console.log(val)
			if (val.length > 1) {
				this.form.action_list.map((item, index) => {
					item.filter_type = 2
				})
				this.filterDisable = true
			} else {
				/*this.form.action_list.map((item, index) => {
					item.filter_type = 1
				})*/
				this.filterDisable = false
			}
		}
	},
	mounted() {},
	methods: {
		delItem(index) {
			if (this.form.action_list.length > 1) {
				this.form.action_list.splice(index, 1)
				this.selectTriggerTslInfo.splice(index, 1)
				this.triggerWriterTsl.splice(index, 1)
				this.triggerDeviceList.splice(index, 1)
			}
		},
		async getProductList() {
			var reqData = {
				node_type: 2,
				tenant_id: this.form.tenant_id,
				status: 1
			}
			var res = await this.$LotApi.product.all.get(reqData)
			this.isSaveing = false
			if (res.code === 200) {
				this.productList = res.data
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		addTrigger() {
			this.form.action_list.push({
				trigger_id: null,
				filter_type: 1,
				product_id: null,
				device_id: null,
				tsl_id: null,
				value: null
			})
		},
		changeProduct(val, index) {
			if (val) {
				this.getProductTslList(val, index)
				this.getProductDevicelList(val, index)
			} else {
				this.triggerDeviceList[index] = []
				this.triggerWriterTsl[index] = []
			}
			this.form.action_list[index].device_id = null
			this.form.action_list[index].tsl_id = null
			this.form.action_list[index].value = null
		},
		getProductTslList(product_id, index = -1) {
			console.log(product_id, index)
			var reqData = {
				product_id: product_id,
				tenant_id: this.form.tenant_id
			}
			this.$LotApi.productTsl.list.get(reqData).then((res) => {
				if (res.code === 200) {
					this.tslList = res.data
					let readTslList = []
					let writerTslList = []
					let eventTslList = []
					res.data.forEach((item) => {
						if (item.type_spec.specs) {
							item.type_spec.specs = JSON.parse(item.type_spec.specs)
						}
						if (item.type_spec.type === 'date' || item.type_spec.type === 'struct' || item.type_spec.type === 'array') {
							return
						}
						if (item.tsl_type === 1 && (item.access_mode === 1 || item.access_mode === 2)) {
							readTslList.push(item)
						}
						if (item.tsl_type === 1 && (item.access_mode === 2 || item.access_mode === 3)) {
							writerTslList.push(item)
						}
						if (item.tsl_type === 2) {
							eventTslList.push(item)
						}
					})
					this.triggerWriterTsl[index] = writerTslList
					if (this.form.action_list[index].tsl_id) {
						this.selectTriggerTsl(this.form.action_list[index].tsl_id, index, false)
					}
				} else {
					this.triggerWriterTsl[index] = []
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		getProductDevicelList(product_id, index = -1) {
			let room_id = []
			if (this.form.room_list && this.form.room_list.length > 0) {
				this.form.room_list.forEach((item) => {
					room_id.push(item.id)
				})
			} else {
				room_id = []
			}
			var reqData = {
				product_id: product_id,
				tenant_id: this.form.tenant_id,
				campus_id: this.form.campus_id,
				room_id: room_id.join(',')
			}
			this.$LotApi.device.all.get(reqData).then((res) => {
				if (res.code === 200) {
					if (index >= 0) {
						this.triggerDeviceList[index] = res.data
					} else {
						this.deviceList = res.data
					}
				} else {
					if (index >= 0) {
						this.triggerDeviceList[index] = []
					} else {
						this.deviceList = []
					}
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		selectTriggerTsl(val, index, type = true) {
			let selectTslInfo = this.triggerWriterTsl[index]?.filter((item) => item.id === val)
			if (selectTslInfo.length > 0) {
				this.selectTriggerTslInfo[index] = selectTslInfo[0]
			} else {
				this.selectTriggerTslInfo[index] = {}
			}
			if (type) {
				this.form.action_list[index].value = null
			}
		},
		//显示
		open(mode = 'add', campus_id) {
			this.form.campus_id = campus_id
			this.mode = mode
			this.getProductList()
			this.visible = true
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let subForm
					if (this.mode === 'add') {
						subForm = { ...this.params, ...this.form }
					} else if (this.mode === 'edit') {
						subForm = { ...this.form }
					}
					if (this.form.room_list && this.form.room_list.length > 0) {
						let room_id = []
						this.form.room_list.forEach((item) => {
							room_id.push(item.id)
						})
						subForm.room_id = room_id
					} else {
						subForm.room_id = []
					}
					var res = await this.$LotApi.scene.save.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		async getDetailInfo() {
			var reqData = {
				id: this.form.id,
				tenant_id: this.form.tenant_id,
				campus_id: this.form.campus_id
			}
			var res = await this.$LotApi.scene.one.get(reqData)
			this.isSaveing = false
			if (res.code === 200) {
				let scene_action = res.data.scene_action

				if (scene_action && scene_action.length > 0) {
					this.form.action_list = scene_action
					for (let i = 0; i < scene_action.length; i++) {
						await this.getProductTslList(scene_action[i].product_id, i)
						await this.getProductDevicelList(scene_action[i].product_id, i)
					}
				}
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//表单注入数据
		async setData(data) {
			console.log(data)
			Object.assign(this.form, data)
			let room_list = []
			if (data && data.room_info) {
				data.room_info.forEach((item) => {
					room_list.push({ label: item.name, id: item.id })
				})
			}
			this.form.room_list = room_list
			if (this.mode === 'edit') {
				await this.getDetailInfo()
			}
			/*if (data.scene_action && data.scene_action.length > 0) {
				this.form.action_list = data.scene_action.length
				for (let i = 0; i < data.scene_action.length; i++) {
					await this.getProductTslList(data.scene_action[i].product_id, i)
					await this.getProductDevicelList(data.scene_action[i].product_id, i)
				}
			}*/
		}
	}
}
</script>

<style scoped lang="scss">
:deep(.el-card__header) {
	font-size: 14px;
}
.triggerItem {
	border: 1px dashed var(--el-color-primary);
	padding: 10px 10px 0px 10px;
	margin-bottom: 15px;
	border-radius: 6px;
	position: relative;
}
.delAction {
	position: absolute;
	right: 10px;
	bottom: 18px;
}
</style>
