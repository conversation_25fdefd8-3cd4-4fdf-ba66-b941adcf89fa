<template>
	<el-drawer
		v-model="visible"
		size="40%"
		title="导入导出数据"
		class="importExport"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<el-tabs v-model="activeName" class="demo-tabs">
			<el-tab-pane label="导出数据" name="export">
				<el-form-item label="">
					<el-alert type="warning" :closable="false">导出当前列表搜索条件的所有数据。</el-alert>
				</el-form-item>
				<el-form-item label="">
					<el-button type="primary" :loading="loading" :disabled="loading" icon="el-icon-download" @click="exportData"
						>导出数据
					</el-button>
				</el-form-item>
			</el-tab-pane>
			<el-tab-pane label="导入数据" name="import">
				<el-form label-width="100px">
					<el-form-item label="当前导入校区">
						<el-select v-model="results.campus_id" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in campusInfo"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-alert type="warning" :closable="false"
							>请先
							<el-link
								type="primary"
								href="https://educdn.xjzredu.cn/scms/system/front/pc/template/%E6%95%99%E5%B8%88%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx"
								plain
								>下载导入模板
							</el-link>
							，然后按照模板格式填写数据，上传填写号的数据文件即可完成导入，每次导入限制数据条数5000条，超出请分批导入；
						</el-alert>
					</el-form-item>
					<el-form-item label="导入数据">
						<el-upload
							ref="uploader"
							accept=".xls, .xlsx"
							maxSize="20"
							:auto-upload="false"
							:http-request="null"
							:limit="1"
							style="margin-top: 10px"
							:data="null"
							:show-file-list="false"
							:before-upload="before"
							:on-change="handleChange"
						>
							<slot name="uploader">
								<el-button
									:loading="loading"
									element-loading-text="数据导入中"
									:disabled="loading"
									type="primary"
									icon="el-icon-upload"
									plain
									>选择文件
								</el-button>
							</slot>
						</el-upload>
						<span style="color: #666; padding-left: 10px; line-height: 30px"
							>请上传小于或等于 {{ maxSize }}M 的 .xls, .xlsx 格式文件</span
						>
					</el-form-item>
				</el-form>
			</el-tab-pane>
		</el-tabs>
	</el-drawer>
</template>

<script>
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

import cusTom from '@/utils/cusTom'

const {
	campusId,
	tenantId,
	sexMap,
	politicalMap,
	identityMap,
	studentStatusMap,
	workStatusMap,
	academicTypeMap,
	staffTypeMap,
	professionalTypeMap,
	compileTypeMap,
	campusInfo,
	nationMap
} = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		data: [],
		campus_id: campusId,
		tenant_id: tenantId
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			activeName: 'export',
			mode: 'export',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				student_name: [{ required: true, message: '请输入名称' }]
			},
			sexMap,
			politicalMap,
			identityMap,
			academicTypeMap,
			studentStatusMap,
			staffTypeMap,
			professionalTypeMap,
			campusInfo,
			workStatusMap,
			compileTypeMap,
			nationMap,
			course: [],
			discipline: [],
			treeData: [],
			position: [],
			maxSize: 20,
			results: {
				campus_id: campusId,
				tenant_id: tenantId,
				data: []
			},
			loading: false
		}
	},
	computed: {},
	mounted() {},
	created() {},
	methods: {
		async exportData() {
			let studentData = []
			this.loading = true
			var res = await this.$API.personnel.staff.all.get(this.form)
			if (res.code === 200) {
				studentData = res.data
			} else {
				this.loading = false
				this.$alert(res.message, '提示', { type: 'error' })
				return
			}

			const data = [
				[
					'教师姓名',
					'手机号码',
					'身份证号码',
					'教师工号',
					'在职状态',
					'性别',
					'邮箱',
					'地址',
					'身份',
					'出生日期',
					'入职时间',
					'学科',
					'职工类别',
					'编制情况',
					'学历情况',
					'职称'
				]
			]
			let _this = this
			studentData.forEach(function (value, index) {
				data.push([
					value.name,
					value.phone,
					value.idcard,
					value.serial_number,
					_this.formData(_this.workStatusMap, value.work_status),
					_this.formData(_this.sexMap, value.sex),
					value.email,
					value.address,
					_this.formData(_this.identityMap, value.identity),
					value.birthdate,
					value.entry_time,
					value.course_name,
					_this.formData(_this.staffTypeMap, value.staff_type),
					_this.formData(_this.compileTypeMap, value.compile_type),
					_this.formData(_this.academicTypeMap, value.academic_type),
					_this.formData(_this.professionalTypeMap, value.professional_type)
				])
			})
			// 将数据转换为工作表
			const worksheet = XLSX.utils.aoa_to_sheet(data)
			// 创建工作簿并添加工作表
			const workbook = XLSX.utils.book_new()
			XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
			// 生成Excel文件
			const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
			// 使用blob和FileReader创建一个Blob URL
			const dataBlob = new Blob([excelBuffer], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
			})
			const blobUrl = window.URL.createObjectURL(dataBlob)
			// 使用saveAs下载文件
			saveAs(dataBlob, '教职工导出数据-' + Date.now() + '.xlsx')
			// 清理
			this.loading = false
			window.URL.revokeObjectURL(blobUrl)
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || '-'
		},
		formateData(arr, val) {
			return arr.find((v) => v.name == val)?.value || 0
		},
		//显示
		before(file) {
			const maxSize = file.size / 1024 / 1024 < this.maxSize
			if (!maxSize) {
				this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!请分批导入`)
				return false
			}
		},
		handleChange(file) {
			if (!file) {
				this.$message.error('请选择要上传的文件')
				return false
			}
			this.loading = true
			const reader = new FileReader()
			reader.onload = (e) => {
				const data = new Uint8Array(e.target.result)
				const workbook = XLSX.read(data, { type: 'array' })
				const firstSheetName = workbook.SheetNames[0]
				const worksheet = workbook.Sheets[firstSheetName]
				const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
				const list = jsonData.slice(4)
				if (list.length <= 0) {
					this.$message.error('请导入正确格式的Excel文件')
					return false
				}
				list.map((item) => {
					if (item.length !== 0 && item[0] !== '' && item[1] !== '') {
						this.results.data.push({
							name: item[0],
							phone: item[1]+"",
							sex: this.formateData(this.sexMap, item[2]),
							idcard: item[3],
							serial_number: item[4]+"",
							identity: this.formateData(this.identityMap, item[5]),
							birthdate: this.excelDate(item[6]),
							academic_type: this.formateData(this.academicTypeMap, item[7])
						})
					}
				})
				if (this.results.data.length <= 0) {
					this.$message.error('请导入正确格式的Excel文件!')
					return false
				}
				console.log(this.results)
				//提交到后端
				this.importData(this.results)
				/*this.results.data.forEach((item) => {
					let index = results.findIndex((e) => e.phone === item.phone)
					item.student_name = results[index].student_name
					item.phone = results[index].phone
					item.idcard = results[index].idcard
					item.serial_number = results[index].serial_number
					item.sex = results[index].sex
					item.entrance_year = results[index].entrance_year
					item.political = results[index].political
					item.nation = results[index].nation
					item.birthdate = results[index].birthdate
				})*/
				/*this.headers = jsonData[0]
				this.data = jsonData.slice(1)*/
			}
			reader.readAsArrayBuffer(file.raw)
			this.loading = false
			return false
		},
		importData(results) {
			if (results.data.length <= 0) {
				this.$message.error('请导入正确格式的Excel文件!!')
				return false
			}
			this.loading = true
			this.$API.personnel.staff.import.post(results).then((res) => {
				this.loading = false
				if (res.code === 200) {
					this.$emit('success', this.form, this.mode)
					console.log(res.data)
					if (res.data) {
						this.$alert(res.data.join('<br/>'), '提示', { type: 'warning' })
					} else {
						this.$message.success('导入成功')
					}
					//this.visible = false
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.results.tenant_id = this.params.tenant_id
			this.results.campus_id = this.params.campus_id
			Object.assign(this.form, this.params)
			return this
		},
		export() {},
		excelDate(date) {
			// 将Excel日期转换为JavaScript日期
			const jsDate = new Date((date - 25569) * 86400 * 1000)
			// 将JavaScript日期转换为指定格式：yyyy-MM-dd
			const year = jsDate.getFullYear()
			const month = String(jsDate.getMonth() + 1).padStart(2, '0')
			const day = String(jsDate.getDate()).padStart(2, '0')
			return `${year}-${month}-${day}`
		},
		//表单注入数据
		setData(data) {
			data.entrance_year = data.entrance_year + ''
			Object.assign(this.form, data)
		}
	},
	components: {}
}
</script>

<style scoped lang="scss">
:deep.el-drawer__header {
	margin-bottom: 10px !important;
}
</style>
