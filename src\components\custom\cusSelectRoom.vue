<!--
 * @Descripttion: 表格选择器组件
 * @version: 1.3
 * @Author: sakuya
 * @Date: 2021年6月10日10:04:07
 * @LastEditors: Xujianchen
 * @LastEditTime: 2023-03-18 13:12:15
-->

<template>
	<!-- 暂存在此 -->
	<!-- <el-select ref="select" v-model="defaultValue" :size="size" popper-class="pop-class" :clearable="clearable"
		:multiple="true" :collapse-tags="collapseTags" :collapse-tags-tooltip="collapseTagsTooltip"
		:filterable="filterable" :placeholder="placeholder" :disabled="disabled" :filter-method="filterMethod"
		:style="{ width: width }" @remove-tag="removeTag" @visible-change="visibleChange" @clear="clear"
		@focus="showDialog">
	</el-select> -->
	<el-input ref="select" :disabled="disabled" :readonly="readonly" :style="{ width: width }" @focus="showDialog"
		:placeholder="defaultValue.length == 0 ? placeholder : ''">
		<template #prefix>
			<template v-for="item, index in defaultValue.slice(0, 2)" :key="item.id">
				<el-tag effect="dark" closable type="info" @close="removeTag(item, index)">{{ item.label }}</el-tag>
			</template>
			<template v-if="defaultValue.length > 2">
				<el-popover placement="bottom" trigger="hover" popper-class="input-tag-popover">
					<template #reference>
						<el-tag effect="dark" type="info">+{{ defaultValue.length - 2 }}</el-tag>
					</template>
					<el-tag v-for="item, index in defaultValue.slice(2)" effect="dark" closable type="info"
						@close="removeTag(item, index+2)">{{ item.label }}</el-tag>
				</el-popover>
			</template>
		</template>
		<template #suffix>
			<el-icon class="el-input__icon"><el-icon-arrow-down /></el-icon>
		</template>
	</el-input>
	<el-dialog v-model="dialogVisible" title="选择宿舍" width="900" append-to-body :close-on-press-escape="false"
		@closed="closeDialog" class="select-dialog">
		<el-container class="con">
			<div class="el-h">
				<el-select v-model="params.campus_id" placeholder="校区" v-if="CampusManagementList.length > 1"
					:teleported="false">
					<el-option :label="item.name" :value="item.value" v-for="item in CampusManagementList"
						:key="item.code"></el-option>
				</el-select>
			</div>
			<el-tabs type="border-card">
				<el-tab-pane label="宿舍">
					<div v-loading="loading" class="sc-table-select__table">
						<div class="roomBox">
							<div class="roomBox__one">
								<!-- {{ checkedBuild }} -->
								<!-- {{ activeIndex }} -->
								<template v-if="multiple">
									<el-checkbox v-model="checkAll" :indeterminate="isIndeterminate"
										@change="handleCheckAllChange">
										全部
									</el-checkbox>
									<el-checkbox-group v-model="checkedBuild" @change="handleCheckedBuildChange">
										<section v-for="(v, index) in groupData" :key="v.id"
											:class="{ is_active: index == activeIndex }" @click="clickBuild(v, index)">
											<el-checkbox :value="v.id" :label="v.id" :disabled="!v.children.length"
												@change="changeBuild($event, v)" :indeterminate="v.indeterminate">
												<div class="st" @click.prevent="
														{
												}
													">
													{{ v.label }}
												</div>
											</el-checkbox>
										</section>
									</el-checkbox-group>
								</template>
								<template v-else>
									<section v-for="(v, index) in groupData" :key="v.id"
										:class="{ is_active: index == activeIndex }" class="single-section"
										@click="clickBuild(v, index)">
										{{ v.label }}
									</section>
								</template>
							</div>
							<div class="roomBox__two">
								<template v-if="multiple">
									<template v-if="floorData.length">
										<el-checkbox-group v-model="checkedFloor" @change="handleCheckedFloorChange">
											<section v-for="(v, index) in floorData" :key="v.id"
												:class="{ is_active: index == activeIndex1 }"
												@click="clickFloor(v, index)">
												<el-checkbox :label="v.id" :value="v.id" :disabled="!v.children.length"
													@change="changeFloor($event, v)" :indeterminate="v.indeterminate">
													<div class="st" @click.prevent="">{{ v.label }}</div>
												</el-checkbox>
											</section>
										</el-checkbox-group>
									</template>
									<template v-else>
										<el-empty description="暂无数据" :image-size="80"></el-empty>
									</template>
								</template>
								<template v-else>
									<template v-if="floorData.length">
										<section v-for="(v, index) in floorData" :key="v.id"
											:class="{ is_active: index == activeIndex1 }" @click="clickFloor(v, index)"
											class="single-section">
											{{ v.label }}
										</section>
									</template>
									<template v-else>
										<el-empty description="暂无数据" :image-size="80"></el-empty>
									</template>
								</template>
							</div>
							<div class="roomBox__three">
								<template v-if="multiple">
									<template v-if="roomData.length">
										<el-checkbox-group v-model="checkedRoom">
											<section v-for="(v, index) in roomData" :key="v.id">
												<el-checkbox-button class="el-b" :label="v.id" :value="v.id"
													@click="changeRoom($event, v)">
													<span>{{ v.label }}</span>
												</el-checkbox-button>
											</section>
										</el-checkbox-group>
									</template>
									<template v-else>
										<el-empty description="暂无数据" :image-size="80"></el-empty>
									</template>
								</template>
								<template v-else>
									<template v-if="roomData.length">
										<section v-for="(v, index) in roomData" :key="v.id">
											<el-button class="el-b"
												:type="checkedRoom.includes(v.id) ? 'primary' : 'default'"
												@click="changeRoom($event, v)">
												<span>{{ v.label }}</span>
											</el-button>
										</section>
									</template>
									<template v-else>
										<el-empty description="暂无数据" :image-size="80"></el-empty>
									</template>
								</template>
							</div>
						</div>
					</div>
				</el-tab-pane>
				<!-- 已选择 -->
				<el-tab-pane label="已选">
					<div class="roomBox selectBox">
						<div class="roomBox__one">
							<template v-if="multiple">
								<div v-for="(v, index) in statusData" :key="v.id"
									:class="{ is_active: index == activeIndexStatus }" class="st"
									@click="clickStatus(v, index)">
									{{ v.label }}
								</div>
							</template>
							<template v-else>
								<template v-if="statusData.length">
									<section v-for="(v, index) in statusData" :key="v.id"
										:class="{ is_active: index == activeIndexStatus }" class="single-section"
										@click="clickStatus(v, index)">
										{{ v.label }}
									</section>
								</template>
							</template>
						</div>
						<div class="roomBox__three">
							<template v-if="multiple">
								<template v-if="statusData[activeIndexStatus].children.length">
									<section v-for="(v, index) in statusData[activeIndexStatus].children" :key="v.id"
										class="selectItem">
										<el-button type="primary" class="el-b">
											<span>{{ v.label }}</span>
										</el-button>
									</section>
								</template>
								<template v-else>
									<el-empty description="暂无数据" :image-size="80"></el-empty>
								</template>
							</template>
							<template v-else>
								<template v-if="statusData.length">
									<section v-for="(v, index) in statusData[activeIndexStatus].children" :key="v.id">
										<el-button type="primary" class="el-b">
											<span>{{ v.label }}</span>
										</el-button>
									</section>
								</template>
								<template v-else>
									<el-empty description="暂无数据" :image-size="80"></el-empty>
								</template>
							</template>
						</div>
					</div>
				</el-tab-pane>
			</el-tabs>

			<div class="el-f">
				<span>已选择
					<span style="color: red">{{ checkedRes.length }}</span>
					间</span>
				<el-button type="primary" @click="confirm">确定</el-button>
			</div>
		</el-container>
	</el-dialog>
</template>

<script>
import config from '@/config/tableSelect'
import api from '@/api'
import cusTom from '@/utils/cusTom'
export default {
	props: {
		modelValue: null,
		apiObj: {
			type: Object,
			default: () => {
				return api.buildingRooms.rooms.list
			}
		},

		placeholder: { type: String, default: '请选择' },
		size: { type: String, default: 'default' },
		clearable: { type: Boolean, default: true },
		multiple: { type: Boolean, default: false },
		filterable: { type: Boolean, default: false },
		collapseTags: { type: Boolean, default: true },
		collapseTagsTooltip: { type: Boolean, default: true },
		disabled: { type: Boolean, default: false },
		tableWidth: { type: Number, default: 600 },
		treeWidth: { type: Number, default: 240 },
		width: { type: String, default: '200px' },

		mode: { type: String, default: 'popover' },
		props: { type: Object, default: () => { } }
	},
	data() {
		return {
			loading: false,
			keyword: null,
			defaultValue: [],
			tableData: [],
			pageSize: config.pageSize,
			total: 0,
			currentPage: 1,
			defaultProps: {
				label: config.props.label,
				value: config.props.value,
				page: config.request.page,
				pageSize: config.request.pageSize,
				keyword: config.request.keyword
			},
			formData: {},
			params: {
				campus_id: '',
				tenant_id: '',
				grade_id: null
			},
			CampusManagementList: [],
			groupData: [],
			floorData: [],
			roomData: [],
			timer: null,
			checkedBuild: [],
			checkedFloor: [],
			checkedRoom: [],
			isIndeterminate: false,
			checkAll: false,
			activeIndex: -1,
			activeIndex1: 0,
			checkedRes: [],
			dialogVisible: false,
			statusData: [
				{ id: 1, label: '未提交', children: [], data: [] },
				{ id: 2, label: '已提交', children: [], data: [] }
			],
			activeIndexStatus: 0,
			shuldBlur: false
		}
	},
	computed: {
		getWidth() {
			return this.tableWidth + this.treeWidth + 20 + 'px'
		},
		groupsAdd() {
			let arr = [...this.groupData]
			return arr
		}
	},
	watch: {
		modelValue: {
			handler(val) {
				if (val) {
					this.defaultValue = val
					this.statusData[1].children = val
					this.statusData[1].data = val.map(item => item.value)
				}
			},
			immediate: true,
			deep: true
		},
		'params.campus_id': {
			handler() {
				this.checkedRoom = []
				this.checkedFloor = []
				this.checkedBuild = []
				this.groupData = []
				this.floorData = []
				this.roomData = []
				this.checkedRes = []
				this.activeIndex = -1
				this.activeIndex1 = -1
			},
			deep: true
		},
		checkedRes: {
			handler(val) {
				if (!this.multiple) {
					this.statusData[0].children = val
					this.statusData[0].data = val.map(item => item.id)
				} else {
					this.statusData[0].children = val
					this.statusData[0].data = val.map(item => item.id)
				}
			},
			deep: true
		}
	},
	created() {
		const { campusId, tenantId, campusInfo } = cusTom.getBaseQuery()
		this.CampusManagementList = campusInfo
		this.params.campus_id = campusId
		this.params.tenant_id = tenantId
	},
	mounted() {
		this.defaultProps = Object.assign(this.defaultProps, this.props)
		this.defaultValue = this.modelValue
		this.autoCurrentLabel()
	},
	methods: {
		showDialog() {
			if (!this.shuldBlur) {
				this.dialogVisible = true
				this.getDept()

			}
		},
		closeDialog() {
			this.blur()
		},
		confirm() {
			let res = this.checkedRes.map((item) => {
				return {
					label: item.label,
					id: item.id
				}
			})
			this.$emit('update:modelValue', res)
			this.dialogVisible = false
			this.shuldBlur = true
			this.$nextTick(() => {
				this.blur()
			})
		},
		// 点击更改显示未提交/已提交的children
		clickStatus(val, index) {
			console.log(val, index, 'clickStatus')
			this.activeIndexStatus = index

		},
		clickBuild(val, index) {
			let isChecked = this.checkedBuild.includes(val.id)
			this.activeIndex = index
			this.changeBuildCall(isChecked, val)
		},
		clickFloor(val, index) {
			let isChecked = this.checkedFloor.includes(val.id)
			if (isChecked) {
				this.checkedRoom = val.children.map((v) => v.id)
			} else {
				this.checkedRoom = []
				let ids = val.children.map((v) => v.id)
				ids.forEach((id) => {
					if (this.checkedRes.find((item) => item.id == id)) {
						this.checkedRoom.push(id)
					}
				})
			}

			this.roomData = val.children
			this.activeIndex1 = index
		},
		//修正
		changeBuild(e, v) {
			if (e) {
				let checkeArr = []
				v.children.forEach((item) => {
					item.children.forEach((item1) => {
						checkeArr.push(item1)
					})
				})

				let ids = this.checkedRes.map((item) => item.id)
				checkeArr.forEach((item) => {
					if (!ids.includes(item.id)) this.checkedRes.push(item)
				})

				this.changeBuildCall(e, v)
			} else {
				let ids = []
				v.children.forEach((item) => {
					item.children.forEach((item1) => {
						ids.push(item1.id)
					})
				})
				this.checkedRes = this.checkedRes.filter((item) => {
					return !ids.includes(item.id)
				})
				this.changeBuildCall(e, v)
			}
		},
		changeBuildCall(e, v) {
			this.floorData = v.children
			this.roomData = this.floorData[this.activeIndex1]
				? this.floorData[this.activeIndex1].children
				: this.floorData[0]
					? this.floorData[0].children
					: []
			if (e) {
				this.checkedFloor = v.children.map((v) => v.id)
				this.checkedRoom = v.children[0].children.map((v) => v.id)
				this.activeIndex1 = 0
				v.indeterminate = false
			} else {
				let checkids = []
				this.floorData.forEach((item) => {
					let ids = item.children.map((v) => v.id)
					if (ids.every((v) => this.checkedRes.map((vv) => vv.id).includes(v))) {
						checkids.push(item.id)
					}
				})
				this.checkedFloor = checkids
				this.checkedRoom =
					this.roomData.map((v) => v.id).filter((v) => this.checkedRes.map((v) => v.id).includes(v)) || []
			}
		},
		changeFloor(e, v) {
			if (e) {
				//选中整层 选中的寝室合集中 没有当前层的寝室 则添加
				this.checkedRoom = v.children.map((v) => v.id)
				let ids = this.checkedRes.map((item) => item.id)
				v.indeterminate = false
				v.children.forEach((vv) => {
					if (!ids.includes(vv.id)) this.checkedRes.push(vv)
				})
				//如果本层选中后 父级包含所有的子级 则父级全选
				let fids = this.floorData.map((v) => v.id)
				let isAll = fids.every((item) => this.checkedFloor.includes(item))
				let isOne = fids.some((item) => this.checkedFloor.includes(item))

				if (isAll) {
					this.groupData[this.activeIndex].indeterminate = false
					if (!this.checkedBuild.includes(this.groupData[this.activeIndex].id)) {
						this.checkedBuild.push(this.groupData[this.activeIndex].id)
					}
				}
				if (isOne && !isAll) {
					this.groupData[this.activeIndex].indeterminate = true
					if (this.checkedBuild.includes(this.groupData[this.activeIndex].id)) {
						this.checkedBuild = this.checkedBuild.filter((item) => item != this.groupData[this.activeIndex].id)
					}
				}
			} else {
				//取消选中整层 选中的寝室合集中 有当前层的寝室 则删除
				this.checkedRoom = []
				this.checkedRes = this.checkedRes.filter((vv) => !v.children.map((vvv) => vvv.id).includes(vv.id))
			}
		},
		changeRoom(e, v) {
			if (!this.multiple) {
				this.checkedRes = [v]
				this.checkedRoom = [v.id]
				return false
			}
			//判断已选中的数组中有没有本条数据 取反操作
			let has = this.checkedRes.some((item) => item.id == v.id)
			if (has) {
				this.checkedRes = this.checkedRes.filter((item) => item.id != v.id)
			} else {
				this.checkedRes.push(v)
			}
			//校验寝室级是否全部选中
			let ids = this.floorData[this.activeIndex1]?.children?.map((v) => v.id)
			if (!ids) return
			let isAll = ids.every((item) => this.checkedRes.map((vv) => vv.id).includes(item))
			let isOne = ids.some((item) => this.checkedRes.map((vv) => vv.id).includes(item))
			//所有选中
			if (isAll) {
				this.$nextTick(() => {
					this.floorData[this.activeIndex1].indeterminate = false
				})
				//当前 楼层 选中数组是否有当前楼层，没有就加上
				if (!this.checkedFloor.includes(this.floorData[this.activeIndex1].id)) {
					this.checkedFloor.push(this.floorData[this.activeIndex1].id)
				}
			}
			//单一选中
			if (isOne && !isAll) {
				this.floorData[this.activeIndex1].indeterminate = true
				//当前 楼层 选中数组是否有当前楼层，有就去掉
				if (!this.checkedFloor.includes(this.floorData[this.activeIndex1].id)) {
					this.checkedFloor = this.checkedFloor.filter((item) => item != this.floorData[this.activeIndex1].id)
				}
			}
			//没有选中
			if (!isAll && !isOne) {
				//当前 楼层 选中数组是否有当前楼层，有就去掉
				if (!this.checkedFloor.includes(this.floorData[this.activeIndex1].id)) {
					this.checkedFloor = this.checkedFloor.filter((item) => item != this.floorData[this.activeIndex1].id)
				}
			}
		},
		handleCheckAllChange(val) {
			this.checkedBuild = val ? this.groupData.map((v) => v.id) : []
			if (val) {
				let t = []
				this.checkedFloor = this.groupData[this.activeIndex].children.map((v) => v.id)

				this.groupData.forEach((v) => {
					v.children.forEach((vv) => {
						t.push(...vv.children)
					})
				})
				this.checkedRes.push(...t)
			} else {
				this.checkedRes = []
				this.checkedFloor = []
				this.checkedRoom = []
			}
		},
		handleCheckedBuildChange(val) {
			const count = val.length
			this.checkAll = count === this.groupData.length
			this.isIndeterminate = count > 0 && count < this.groupData.length
		},
		handleCheckedFloorChange(val, index) { },
		//获取宿舍和楼层
		async getDept() {
			const res = await this.$API.buildingRooms.tree.get(this.params)
			this.groupData = res.data
			let temp = []
			let ids = []
			if (this.modelValue.length > 0) {
				ids = this.modelValue.map((item) => Number(item.id))
			}
			let t = [] //选中的楼栋
			let t1 = [] //选中的楼层

			//根据表格回显选中的数据
			this.groupData.forEach((v) => {
				let vid = []
				v.all = false
				v.indeterminate = false

				v.children.forEach((vv) => {
					vv.indeterminate = false
					vv.all = false
					let vvids = []
					//循环每一层的楼 如果是已选中的 则改变父级的中间状态
					vv.children.forEach((vvv) => {
						vvids.push(vvv.id)
						if (ids.includes(vvv.id)) {
							temp.push(vvv)
							t.push(v.id)
							t1.push(vv.id)
							v.indeterminate = true
							vv.indeterminate = true
						}
					})

					if (vvids.every((id) => ids.includes(id))) {
						vv.indeterminate = false
						vv.all = true
					}
				})
				if (v.children.every((v1) => v1.all)) {
					v.indeterminate = false
					v.all = true
				}
			})
			this.checkedBuild = this.groupData.filter((v) => v.all).map((v) => v.id)
			this.checkAll = this.groupData.every((v) => v.all)
			//回显选中的第一个楼栋
			let buildObj = this.groupData.find((v, index) => {
				if (v.id == t[0]) {
					this.activeIndex = index
				}
				return v.id == t[0]
			})

			//有楼栋 则选中改楼栋的楼层
			if (buildObj) {
				this.floorData = buildObj.children
				this.activeIndex1 = this.floorData.findIndex((v) => v.id == t1[0])
				this.checkedFloor = buildObj.children.filter((v) => v.all).map((v) => v.id)
			}

			this.checkedRes = temp
		},

		//表格显示隐藏回调
		visibleChange(visible) {
			if (visible) {
				this.currentPage = 1
				this.keyword = null
				this.formData = {}
				this.getDept()
			} else {
				this.autoCurrentLabel()
			}
		},

		//插糟表单提交
		formSubmit() {
			this.currentPage = 1
			this.keyword = null
			this.getData()
		},
		//分页刷新表格
		reload() {
			this.getData()
		},
		//自动模拟options赋值
		autoCurrentLabel() {
			this.$nextTick(() => { })
		},
		//表格变化树处理逻辑
		autoSetTreeNode(rows) {
			let bId = [],
				fId = []
			rows.forEach((v) => {
				bId.push(v.building_id)
				fId.push(v.floor_id)
			})
			this.building_idArr = [...new Set(bId)]
			this.floor_idArr = [...new Set(cId)]
		},
		//表格勾选事件
		select(rows, row) {
			var isSelect = rows.length && rows.indexOf(row) !== -1
			if (isSelect) {
				this.defaultValue.push({
					label: `${row.building_name}-${row.room_name}`,
					value: row.id
				})
			} else {
				this.defaultValue.splice(
					this.defaultValue.findIndex((item) => item.value === row.id),
					1
				)
			}
			this.autoCurrentLabel()
			this.autoSetTreeNode(rows)

			this.$emit('update:modelValue', this.defaultValue)
			this.$emit('change', this.defaultValue)
		},

		//tags删除后回调
		removeTag(tag, index) {
			if (this.multiple) {

				this.checkedRes.splice(index, 1)
				this.defaultValue.splice(index, 1)
				this.checkedRoom = this.checkedRoom.filter((item) => item !== tag.id)
				console.log(this.checkedRes, this.defaultValue,this.checkedRoom)
			} else {
				this.checkedRes = []
				this.defaultValue = []
			}
			this.$emit('update:modelValue', this.defaultValue)
		},
		//清空后的回调
		clear() {
			this.$emit('update:modelValue', this.defaultValue)
		},
		// 关键值查询表格数据行
		findRowByKey(value) {
			return this.tableData.find((item) => item[this.defaultProps.value] === value)
		},
		filterMethod(keyword) {
			if (!keyword) {
				this.keyword = null
				return false
			}
			this.keyword = keyword
			this.getDept()
		},
		// 触发select隐藏
		blur() {
			if (this.shuldBlur) {
				this.shuldBlur = false
				return false
			}
			this.$refs.select.blur()
		},
		// 触发select显示
		focus() {
			this.$refs.select.focus()
		}
	}
}
</script>
<style>
.pop-class {
	display: none;
}
</style>
<style scoped lang="scss">
.selectTitle {
	padding: 10px;
	font-weight: bold;
	color: #333;
	font-size: 18px;
	border-bottom: 1px solid var(--color);
}

.con {
	--color: #eee;
	--bacColor: #e9ecf7;
	flex-direction: column;

	.el-h {
		height: 50px;
		display: flex;
		align-items: center;
	}

	.is_active {
		background-color: var(--bacColor);
		position: relative;

		&::after {
			content: '';
			width: 3px;
			height: 100%;
			position: absolute;
			right: 0;
			background-color: var(--el-color-primary-light-3);
		}
	}

	.sc-table-select__table {
		padding: 0px;
	}

	.sc-table-select__page {
		padding-top: 12px;
	}

	.sc-table-select__header {
		margin-bottom: 10px;
		display: flex;
		align-items: center;
	}

	.roomBox {
		display: flex;
		min-height: 300px;
		border: 1px solid var(--color);

		section {
			padding: 0 10px;
		}

		&>div {
			flex: 1;
			padding: 10px;
		}

		&__two {
			border-left: 1px solid var(--color);
			border-right: 1px solid var(--color);
		}

		&__three {
			max-height: 300px;
			overflow: auto;

			.selectItem {
				display: inline-block;
				width: 85px;

				.el-b {
					min-width: 100%;

					:deep(.el-button--default) {
						width: 100%;
					}
				}
			}

			section+section {
				margin-top: 5px;
			}
		}
	}

	.el-b {
		width: 40%;
		margin-bottom: 5px;
		margin-right: 8px;
		display: inline-block;

		:deep(.el-button--default) {
			width: 100%;
		}
	}

	.selectBox {
		.roomBox__one {
			flex: 1;
			padding-right: 0 !important;
			border-right: 1px solid #eee;
		}

		.roomBox__three {
			flex: 3;
		}
	}
}

.el-f {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 10px;
	height: 50px;
}

.st {
	height: 35px;
	font-size: 16px;
	padding-left: 10px;
	display: flex;
	align-items: center;
}

// 去掉作为显示的hover效果
:deep(.el-button:hover) {
	background-color: #2745B2;
	color: white;
}

.single-section {
	height: 32px;
	line-height: 32px;
}
</style>
