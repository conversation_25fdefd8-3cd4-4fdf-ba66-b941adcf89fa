<template>
	<el-dialog v-model="visible" :title="title" width="30%" destroy-on-close @close="close">
		<el-form ref="dialogForm" :model="formData" :rules="rules" label-width="auto">
			<el-form-item label="考试名称" prop="examine_name">
				<el-input v-model="formData.examine_name" placeholder="请输入考试名称"></el-input>
			</el-form-item>
			<el-form-item label="考试年级" prop="grade_id">
				<el-select
					v-model="formData.grade_id"
					placeholder="请选择考试年级"
					filterable
					clearable
					style="max-width: unset; width: 100%"
					@change="gradeChange"
				>
					<el-option v-for="item in gradeList" :key="item.id" :label="item.grade_name" :value="item.id"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="考试科目" prop="course_ids">
				<el-select
					v-model="formData.course_ids"
					placeholder="请选择考试科目"
					no-data-text="请先选择考试年级"
					filterable
					clearable
					multiple
					collapse-tags
					:max-collapse-tags="6"
					collapse-tags-tooltip
					style="max-width: unset; width: 100%"
				>
					<el-option
						v-for="item in courseList"
						:key="item.course_id"
						:label="item.course_name"
						:value="item.course_id"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="考试时间" prop="date">
				<el-date-picker
					v-model="formData.date"
					type="daterange"
					range-separator="至"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
					value-format="YYYY-MM-DD"
				>
				</el-date-picker>
			</el-form-item>
			<el-form-item label="考试类型" prop="examine_type">
				<el-select
					v-model="formData.examine_type"
					placeholder="请选择考试类型"
					filterable
					clearable
					style="max-width: unset; width: 100%"
				>
					<el-option
						v-for="item in examineTypeMap"
						:key="item.value"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<!--            <el-form-item label="考试座位排列类型" prop="arrange_type">
                <el-select v-model="formData.arrange_type" placeholder="考试座位排列类型" filterable clearable
                    style="max-width: unset; width: 100%">
                    <el-option v-for="item in arrangeTypeMap" :key="item.value" :label="item.name"
                        :value="item.value"></el-option>
                </el-select>
            </el-form-item>-->
			<el-form-item label="座位类型" prop="seat_type">
				<!--				<el-select
					v-model="formData.seat_type"
					placeholder="座位类型"
					filterable
					clearable
					style="max-width: unset; width: 100%"
				>
					<el-option label="不同科目相同考场及座位" :value="1"></el-option>
					<el-option label="不同科目不同考场及座位" :value="2"></el-option>
				</el-select>-->
				<el-radio-group v-model="formData.seat_type">
					<el-radio label="不同考试科目，安排相同考场及座位" :value="1" />
					<el-radio label="不同考试科目，安排不同考场及座位" :value="2" />
				</el-radio-group>
			</el-form-item>
			<el-form-item label="签到" prop="sign">
				<el-radio-group v-model="formData.sign">
					<el-radio-button label="开启" :value="1" />
					<el-radio-button label="不开启" :value="-1" />
				</el-radio-group>
			</el-form-item>
			<!-- <el-form-item label="状态" prop="status">
				<el-switch
					v-model="formData.status"
					:active-value="1"
					:inactive-value="-1"
					active-text="启用"
					inline-prompt
					style="--el-switch-on-color: #00b42a"
					inactive-text="关闭"
				></el-switch>
			</el-form-item> -->
			<!-- <el-form-item label="学生签到" prop="student_sign">
				<el-radio-group v-model="formData.student_sign">
					<el-radio-button label="开启" :value="1" />
					<el-radio-button label="不开启" :value="-1" />
				</el-radio-group>
			</el-form-item> -->
			<el-form-item label="考试说明" prop="remark">
				<el-input v-model="formData.remark" type="textarea" placeholder="请输入考试说明"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="close">取 消</el-button>
				<el-button type="primary" @click="save">确 定</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const arrangeTypeMap = reactive([
	{
		name: '竖排',
		value: 1
	},
	{
		name: '横排',
		value: 2
	},
	{
		name: '竖排蛇形',
		value: 3
	},
	{
		name: '横排蛇形',
		value: 4
	}
])

const props = defineProps({
	params: {
		type: Object,
		default: () => {}
	}
})
const defaultData = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: props.params.semester_id,
		examine_name: null,
		grade_id: null,
		course_ids: [],
		date: [],
		examine_type: null,
		arrange_type: 1,
		seat_type: 1,
		//status: 1,
		sign: -1,
		remark: null
	}
}

const visible = ref(false)
const title = ref('新增考试')
const formData = ref(defaultData())
const rules = ref({
	examine_name: [{ required: true, message: '请输入考试名称', trigger: 'blur' }],
	grade_id: [{ required: true, message: '请选择考试年级', trigger: 'change' }],
	course_ids: [{ required: true, message: '请选择考试科目', trigger: 'change' }],
	date: [{ required: true, message: '请选择考试时间', trigger: 'blur' }],
	examine_type: [{ required: true, message: '请选择考试类型', trigger: 'change' }]
})

// 保存
const emit = defineEmits(['saveSuccess'])
const dialogForm = ref()
const save = () => {
	dialogForm.value.validate((valid) => {
		if (valid) {
			formData.value.begin_date = formData.value.date[0]
			formData.value.end_date = formData.value.date[1]
			globalPropValue.examine.save.post(formData.value).then((res) => {
				if (res.code === 200) {
					ElMessage({ type: 'success', message: '保存成功' })
					close()
					emit('saveSuccess')
				}
			})
		}
	})
}
const open = (type, data) => {
	title.value = type === 'add' ? '新增考试' : '编辑考试'
	formData.value = defaultData()
	if (type === 'edit') {
		getOne(data.id)
	}
	visible.value = true
	getGradeList()
}

const close = () => {
	visible.value = false
}

const getOne = (id) => {
	globalPropValue.examine.one
		.get({
			id,
			tenant_id: tenantId,
			campus_id: campusId
		})
		.then((res) => {
			if (res.code === 200) {
				formData.value = res.data
				formData.value.date = [res.data.begin_date, res.data.end_date]
				console.log(formData.value)
				getCourseList()
			}
		})
}

// 获取年级列表
const gradeList = ref([])
const getGradeList = () => {
	globalPropValue.eduGradeClass.grade.all
		.get({
			tenant_id: tenantId,
			campus_id: campusId,
			semester_id: props.params.semester_id
		})
		.then((res) => {
			if (res.code === 200) {
				gradeList.value = res.data
			}
		})
}

// 选择年级时间
const gradeChange = (val) => {
	formData.value.grade_id = val
	getCourseList()
}
// 获取考试科目
const courseList = ref([])
const getCourseList = () => {
	globalPropValue.eduCourseSet.course.single_course
		.get({
			tenant_id: tenantId,
			campus_id: campusId,
			id: formData.value.grade_id
		})
		.then((res) => {
			if (res.code === 200) {
				courseList.value = res.data
			}
		})
}

defineExpose({
	open,
	close
})
</script>
