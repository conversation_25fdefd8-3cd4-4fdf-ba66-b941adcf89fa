<template>
	<el-drawer v-model="showForm" :title="title" direction="rtl" size="50%" @close="closeForm">
		<el-form ref="formref" :model="form" label-width="150px" label-position="right" :rules="formRules">
			<el-form-item label="公告标题" prop="title">
				<el-input v-model="form.title" placeholder="请输入公告标题" />
			</el-form-item>
			<el-form-item label="是否为重要" prop="importance">
				<el-switch
					v-model="form.importance"
					:active-value="1"
					:inactive-value="-1"
					@change="handleSwitchChange"
				></el-switch>
			</el-form-item>
			<el-form-item label="接收对象" prop="receiver_type">
				<el-radio-group v-model="form.receiver_type">
					<el-radio :label="1">全部人员</el-radio>
					<el-radio :label="2">选择人员</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.receiver_type === 2" label="选择接收对象" prop="receiver">
				<cusSelectTeacher v-model="form.receiver" multiple collapseTags width="100%" />
			</el-form-item>
			<el-form-item label="同步至学员" prop="sync_student">
				<el-radio-group v-model="form.sync_student">
					<el-radio :label="1">同步</el-radio>
					<el-radio :label="-1">不同步</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.sync_student === 1" label="同步至学员年级" prop="sync_grade">
				<cusSelectInfo
					v-model="form.sync_grade"
					type="select-grade"
					placeholder="请选择同步至学员年级"
					multiple
					width="100%"
				></cusSelectInfo>
			</el-form-item>
			<el-form-item label="同步班牌展示" prop="sync_class_dev">
				<el-radio-group v-model="form.sync_class_dev">
					<el-radio :label="1">同步</el-radio>
					<el-radio :label="-1">不同步</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="班牌对象" v-if="form.sync_class_dev === 1"  prop="class_dev_type">
				<el-radio-group v-model="form.class_dev_type">
					<el-radio :label="1">全部</el-radio>
					<el-radio :label="2">选择</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.sync_class_dev === 1&&form.class_dev_type === 2" label="选择班牌" prop="room_ids">
				<el-select v-model="form.room_ids" multiple placeholder="请选择同步展示的教室班牌" no-data-text="暂无绑定班牌教室" style="width: 100%;max-width: unset" filterable>
					<el-option v-for="item in roomList" :key="item.id" :label="item.position.name" :value="item.id">
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item v-if="form.sync_class_dev === 1" label="展示截止时间" prop="end_time">
				<el-date-picker v-model="form.end_time" format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" type="datetime" placeholder="请选择展示截止时间" />
			</el-form-item>
			<el-form-item label="公告内容" prop="content">
				<cusEditor ref="editor" v-model="form.content" placeholder="请输入公告内容" mode="default"></cusEditor>
			</el-form-item>
			<el-form-item label="附件" prop="file">
				<scUploadFile v-model="form.file" :limit="9" accept="*" fileTypeTag="notice"></scUploadFile>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="saveDraft">存草稿</el-button>
			<el-button type="primary" @click="publish">直接发布</el-button>
		</template>
	</el-drawer>
</template>
<script>
import cusSelectTeacher from '@/components/custom/cusSelectTeacher.vue'
import cusSelectInfo from '@/components/custom/cusSelectInfo.vue'
import cusEditor from '@/components/custom/cusEditor.vue'

import cusTom from '@/utils/cusTom'
const {  tenantId } = cusTom.getBaseQuery()

import { cloneDeep } from 'lodash'
export default {
	components: { cusSelectTeacher, cusSelectInfo, cusEditor },
	data() {
		return {
			showForm: false,
			title: '',
			roomList:[],
			form: {
				campus_id: 0,
				tenant_id: 0,
				id: 0,
				title: '',
				importance: -1,
				receiver_type: 1,
				receiver: [],
				sync_student: -1,
				sync_class_dev: -1,
				class_dev_type: 1,
				room_ids: [],
				sync_grade: [],
				content: '',
				file: []
			},
			formRules: {
				title: [{ required: true, message: '请输入公告标题', trigger: 'blur' }],
				receiver_type: [{ required: true, message: '请选择接收对象', trigger: 'change' }],
				receiver: [{ required: true, message: '请选择接收对象', trigger: 'change' }],
				sync_student: [{ required: true, message: '请选择是否同步至学员', trigger: 'change' }],
				sync_grade: [{ required: true, message: '请选择同步至学员年级', trigger: 'change' }],
				room_ids: [{ required: true, message: '请选择同步展示的教室班牌', trigger: 'change' }],
				end_time: [{ required: true, message: '请选择公告在班牌展示的截止时间', trigger: 'change' }],
				content: [{ required: true, message: '请输入公告内容', trigger: 'blur' }]
			}
		}
	},
	created() {
		//this.getRoomList()
	},
	methods: {
		getRoomList(){
			 this.$API.device.allClassRoomDeviceList.get({
			 	campus_id: this.form.campus_id,
			 	tenant_id: this.form.tenant_id
			 }).then((res) => {
				var roomList = []
				res.data.forEach(item => {
					if((item.device_type===123||item.device_type===124)&&(item.meeting_id==='0'||item.meeting_id==='') && item.device_sn!==''){
						roomList.push(item)
					}
				})
			 	this.roomList = roomList
			 })
		},
		open(type, campus_id) {
			this.form.campus_id = campus_id
			this.form.tenant_id = tenantId
			 this.getRoomList()
			this.showForm = true
			this.title = type === 'edit' ? '编辑公告' : '新增公告'
			this.$nextTick(() => {
				if (type !== 'edit') {
					this.$refs.formref.resetFields() // 重置表单
					this.form = {
						id: 0,
						campus_id: campus_id,
						tenant_id: tenantId,
						title: '',
						importance: -1,
						receiver_type: 1,
						receiver: [],
						sync_student: -1,
						sync_grade: [],
						sync_class_dev: -1,
						class_dev_type: 1,
						room_ids: [],
						content: '',
						file: []
					}
					this.$refs.editor.clearContent()
				}
			})
		},
		submitForm(status) {

			this.$refs.formref.validate((valid) => {
				if (valid) {
					let formData=cloneDeep(this.form)
					formData.status = status
					if (formData.file) {
						formData.file = this.form.file?.map((item) => ({
							name: item.name,
							url: item.url
						}))
					}
					if (formData.receiver_type === 2){
						formData.receiver = this.form.receiver.map((item) => item.id)
					}
					console.log(formData, 'formData')
					this.$emit('success', formData)
					this.showForm = false
				}
			})
		},
		saveDraft() {
			this.submitForm(2)
			console.log(this.form, 'saveDraft')
		},
		publish() {
			this.submitForm(1)
			console.log(this.form, 'publish')
		},
		handleSwitchChange(item) {
			this.form.importance = item
		},
		closeForm() {
			this.$refs.formref.resetFields()
		},
		setData(data) {
			console.log(data, 'setData')
			let formData = cloneDeep(data)
			if (formData.sync_grade?.length) {
				formData.sync_grade = formData.sync_grade.map((item) => item.id)
			}
			if (formData.receiver?.length) {
				formData.receiver = formData.receiver.map((item) => {
					return { id: item.id, label: item.name }
				})
			}

			if (formData.file && !Array.isArray(formData.file)) {
				formData.file = JSON.parse(formData.file)
			}
			if (formData.room_list?.length) {
				formData.room_ids = formData.room_list.map((item) => {
					return { id: item.id, label: item.name }
				})
			}
			this.form = { ...formData }
			// this.form = { ...data }
		}
	}
}
</script>
<style lang="scss" scoped></style>
