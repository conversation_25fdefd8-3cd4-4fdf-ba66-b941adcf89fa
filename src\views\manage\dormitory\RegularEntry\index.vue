<template>
	<el-container>
		<el-main class="el-main-hascontainer">
			<el-container>
				<el-aside width="270px">
					<el-container>
						<el-header v-if="CampusManagementList.length > 1">
							<div class="left-panel-search">
								<el-form-item label="校区">
									<el-select v-model="params.campus_id" placeholder="校区" filterable>
										<el-option
											v-for="item in CampusManagementList"
											:key="item.code"
											:label="item.name"
											:value="item.value"
										></el-option>
									</el-select>
								</el-form-item>
							</div>
						</el-header>
						<el-main>
							<el-tree
								ref="group"
								class="menu"
								node-key="id"
								:data="groupData"
								:highlight-current="true"
								:expand-on-click-node="false"
								:props="defaultProps"
								:default-expanded-keys="defaultKeys"
								@node-click="groupClick"
							>
								<template #default="{ node, data }">
									<span class="custom-tree-node">
										<span>{{ node.label }}</span>
									</span>
								</template>
							</el-tree>
						</el-main>
					</el-container>
				</el-aside>
				<el-container>
					<el-header>
						<div class="left-panel">
							<div class="left-panel-search">
								<el-form-item label="">
									<cusSelectSemester
										v-model="params.semester_id"
										:params="params"
										:show-default-value="true"
										:width="'214px'"
										clearable
										style="margin-right: 15px"
									/>
								</el-form-item>
								<!--								<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>-->

								<el-radio-group v-model="chooseType" style="margin-left: 10px" @change="chooseTypeFn">
									<el-radio-button :label="1" :value="1">寝室选择</el-radio-button>
									<el-radio-button :label="2" :value="2">住宿生选择</el-radio-button>
								</el-radio-group>
								<span v-if="chooseType == 1" style="margin-left: 10px"
									>已选择<span style="color: red; font-weight: bold; margin: 0 10px">{{ checkRoomList.length }}</span
									>间寝室,最多选择<span style="color: orange">1</span>间</span
								>
								<span v-else style="margin-left: 10px"
									>已选择<span style="color: red; font-weight: bold; margin: 0 10px">{{ checkUser.length }}</span
									>位住宿生</span
								>
							</div>
						</div>
						<div class="right-panel">
							<el-button type="primary" icon="el-icon-edit" @click="editRecord">写记录</el-button>
						</div>
					</el-header>
					<el-main>
						<template v-if="chooseType == 1">
							<div class="roomList">
								<div
									v-for="(item, index) in roomList"
									:key="index"
									class="roomItem"
									:class="{ roomItem1: checkRoomList.some((v) => v.id == item.id) }"
									@click="roomItemClick(item)"
								>
									{{ item.label }}
								</div>
							</div>
						</template>

						<template v-if="chooseType == 2">
							<div class="roomList1">
								<div
									v-for="(item, index) in bedList"
									class="roomItem"
									:class="{ currentBed: item.has == true }"
									@click="chooseStatus(item)"
								>
									<template v-if="!item.staff_info && !item.student_info">
										<el-empty>
											<template #description>
												<text></text>
											</template>
											<el-tag type="warning">空余床位</el-tag>
										</el-empty>
									</template>

									<template v-if="item.staff_info">
										<div class="roomItem-head">
											<div class="flex_bc">
												<el-image
													:lazy="true"
													fit="contain"
													style="width: 50px; height: 50px; border-radius: 50%; border: 1px solid #ccc"
													:src="item.staff_info.user_head"
													:preview-src-list="[item.staff_info.user_head]"
													preview-teleported
												/>

												<text style="margin-left: 10px">{{ item.staff_info.name }}</text>
											</div>

											<div>
												<el-tag type="success">{{ item.bed_number }}床</el-tag>
											</div>
										</div>
										<div class="roomItem-body">
											<div>
												<el-tag v-if="item.is_head" type="danger">宿舍长</el-tag>
												<el-tag type="warning">教职工</el-tag>
												<el-tag v-if="item.accommodation_type == 1" type="info">全寄</el-tag>
												<el-tag v-if="item.accommodation_type == 2" type="info">半寄</el-tag>
											</div>
											<div class="roomItem-body-row">性别：{{ formData(sexMap, item.staff_info.sex) }}</div>
											<div class="roomItem-body-row">工号：{{ item.staff_info.serial_number }}</div>
											<div class="roomItem-body-row">手机：{{ item.staff_info.phone }}</div>
											<div class="roomItem-body-row">
												<div class="label">备注：</div>
												<el-input
													v-model="item.remark"
													type="textarea"
													:autosize="{ minRows: 4, maxRows: 6 }"
													:disabled="!item.isCanEdit"
													placeholder=""
												/>
											</div>
										</div>
									</template>
									<template v-if="item.student_info">
										<div class="roomItem-head">
											<div class="flex_bc">
												<el-image
													:lazy="true"
													fit="contain"
													style="width: 50px; height: 50px; border-radius: 50%; border: 1px solid #ccc"
													:src="item.student_info.user_head"
													:preview-src-list="[item.student_info.user_head]"
													preview-teleported
												/>

												<text style="margin-left: 10px">{{ item.student_info.student_name }}</text>
											</div>

											<div>
												<el-tag type="success">{{ item.bed_number }}床</el-tag>
											</div>
										</div>
										<div class="roomItem-body">
											<div>
												<el-tag v-if="item.is_head" type="danger">宿舍长</el-tag>
												<el-tag>学生</el-tag>
												<el-tag v-if="item.accommodation_type == 1" type="info">全寄</el-tag>
												<el-tag v-if="item.accommodation_type == 2" type="info">半寄</el-tag>
											</div>
											<div class="roomItem-body-row">性别：{{ formData(sexMap, item.student_info.sex) }}</div>
											<div class="roomItem-body-row">
												班级：{{ item.student_info.grade_name }}-{{ item.student_info.class_name }}
											</div>
											<div class="roomItem-body-row">手机：{{ item.student_info.phone }}</div>
											<div class="roomItem-body-row">
												班主任：{{ item.student_info.head_teacher_name }} - {{ item.student_info.head_teacher_phone }}
											</div>
											<div class="roomItem-body-row">
												<div class="label">备注：</div>
												<el-input
													v-model="item.remark"
													type="textarea"
													:autosize="{ minRows: 4, maxRows: 6 }"
													:disabled="!item.isCanEdit"
													placeholder=""
												/>
											</div>
										</div>
									</template>
								</div>
							</div>
						</template>
					</el-main>
				</el-container>
			</el-container>
		</el-main>

		<edit-dialog
			v-if="dialog.edit"
			ref="editDialog"
			:params="params"
			:chooseType="chooseType"
			@closed="dialog.edit = false"
			@success="handleSaveSuccess"
		></edit-dialog>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'

import editDialog from './edit.vue'

const defaultProps = {
	children: 'children',
	label: 'label'
}

const { campusId, tenantId, campusInfo, semesterInfo, semesterId, academicYearId, sexMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'
import CusSelectSemester from '@/components/custom/cusSelectSemester.vue'
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: null,
		academic_id: null,
		room_id: null
	}
}

export default {
	name: 'buildingRooms',
	data() {
		return {
			currentId: -1,
			groupFilterText: '',
			groupData: [],
			defaultProps,
			list: {
				apiObj: this.$API.buildingRooms.personnel.list
			},
			params: defaultParams(),

			CampusManagementList: campusInfo,
			dialog: {
				save: false,
				leave: false,
				edit: false
			},
			dialog1: {
				save: false
			},
			showTools: false,
			semesterInfo,
			bedList: [],
			defaultKeys: [],
			sexMap,
			room_capacity: 0,
			setCurrentBed: -1,
			Editing: false,
			roomList: [],
			checkRoomList: [],
			chooseType: 1, //1 寝室 2学生
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'academic_id',
						value: null,
						component: 'select',

						options: {
							noClearable: true,
							placeholder: '请选择学年',
							items: []
						}
					},
					{
						label: null,
						name: 'semester_id',
						value: null,
						component: 'select',
						options: {
							noClearable: true,
							placeholder: '请选择学期',
							items: []
						}
					}
				]
			}
		}
	},
	components: { CusSelectSemester, editDialog },
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.academic_id = null
				this.searchConfig.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
					.filter((v) => v.parent_id == 0 && v.campus_id == val)
					.map((v) => {
						return {
							label: v.name,
							value: v.value
						}
					})

				this.params.academic_id = this.searchConfig.formItems.find(
					(v) => v.name === 'academic_id'
				).options.items[0]?.value
			},
			immediate: true
		},
		'params.academic_id': {
			handler(val) {
				this.searchConfig.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
					.filter(
						(v) => v.parent_id != 0 && v.parent_id == this.params.academic_id && v.campus_id == this.params.campus_id
					)
					.map((v) => {
						return {
							label: v.name,
							value: v.value
						}
					})
				this.params.semester_id = this.searchConfig.formItems.find(
					(v) => v.name === 'semester_id'
				).options.items[0]?.value
				this.getLou()
			},
			immediate: true
		}
	},
	computed: {
		checkUser() {
			return this.bedList.filter((v) => v.has)
		},
		getAcademic_year() {
			return this.semesterInfo.filter((v) => v.parent_id == 0 && v.campus_id == this.params.campus_id)
		},
		getSemester() {
			return this.semesterInfo.filter(
				(v) => v.parent_id != 0 && v.parent_id == this.params.academic_year_id && v.campus_id == this.params.campus_id
			)
		}
	},
	async created() {},
	mounted() {},
	methods: {
		chooseStatus(item) {
			if (!item.user_id) {
				return false
			}
			item.has = !item.has
		},
		editRecord() {
			let checkUser = this.bedList.filter((v) => v.has)
			this.dialog.edit = true
			this.$nextTick(() => {
				if (this.chooseType == 2) {
					if (!checkUser.length) {
						this.$message.error('请选择学生')
						return
					}

					this.$refs.editDialog.open('edit').setData(checkUser)
				} else {
					if (!this.checkRoomList.length) {
						this.$message.error('请选择寝室')
						return
					}
					this.$refs.editDialog.open('edit').setData({
						checkRoomList: this.checkRoomList
					})
				}
			})
		},

		async editRemark(row) {
			this.Editing = true
			var res = await this.$API.buildingRooms.personnel.save.post(row)
			this.Editing = false
			if (res.code === 200) {
				this.$message.success('操作成功')
				this.handleSaveSuccess()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		setCurrent(index) {
			if (this.setCurrentBed == index) this.setCurrentBed = -1
			this.setCurrentBed = index
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		handleShowTools(item) {
			this.currentId = item.id
		},
		dataChange(val) {
			if (!val.data.rows) return
			let arr = val.data.rows.map((v) => {
				return {
					text: v.room_name,
					value: v.room_name
				}
			})
			this.roomNameFilters = cusTom.uniqueByValue(arr, 'text')
		},
		filterHandler(value, row, column) {
			const property = column['property']
			return row[property] === value
		},
		chooseTypeFn() {
			this.getLou()
		},
		//获取宿舍楼
		async getLou() {
			const { data } = await this.$API.buildingRooms.buildingTree.get({ ...this.params, building_type: null })
			if (!data) {
				this.groupData = []
				this.roomList = []
				this.bedList = []
				return
			}
			data.forEach((v) => {
				v.uid = v.id
				v.id = v.id + 'a'
				v.children.forEach((vv) => {
					vv.uid = vv.id
					vv.id = vv.id + 'b'
					if (this.chooseType == 1) {
						vv.children1 = JSON.parse(JSON.stringify(vv.children))
						vv.children = []
					} else {
						vv.children.forEach((vvv) => {
							vvv.uid = vvv.id
							vvv.id = vvv.id + 'c'
						})
					}
				})
			})
			this.groupData = data
			this.getGroupDataDefault()
		},
		getGroupDataDefault() {
			if (this.groupData.length) {
				if (this.chooseType == 1) {
					this.defaultKeys = [this.groupData[0].children[0].id]
				} else {
					this.defaultKeys = [this.groupData[0].children[0].children[0].id]
				}
				this.$nextTick(() => {
					this.$refs.group?.setCurrentKey(...this.defaultKeys)
					const curr = this.$refs.group?.getCurrentNode()
					if (this.chooseType == 1) {
						this.roomList = curr.children1
					} else {
						this.room_capacity = curr.room_capacity
						this.params.room_id = curr.uid
						this.getRoom()
					}
				})
			}
		},
		//获取房间列表
		async getRoom() {
			if (!this.params.room_id) return false
			const { data } = await this.$API.buildingRooms.personnel.list.get(this.params)

			let data1
			Array.isArray(data) ? (data1 = data) : (data1 = [])
			while (data1.length < this.room_capacity) {
				data1.push({})
			}
			this.bedList = data1.map((v) => {
				return {
					...v,
					isCanEdit: false,
					has: false,
					remarkCopy: v.remark
				}
			})
		},

		//树点击事件
		groupClick(data, node, TreeNode) {
			if (this.chooseType == 1) {
				if (data.id.indexOf('b') == -1) {
					ElMessage.warning('请选择楼层')
					this.$refs.group?.setCurrentKey(...this.defaultKeys)
					return false
				} else {
					this.defaultKeys = [data.id]
				}
				this.roomList = data.children1
				this.params.room_id = null
			} else {
				if (data.id.indexOf('c') == -1) {
					ElMessage.warning('请选择房间')
					this.$refs.group?.setCurrentKey(...this.defaultKeys)
					return false
				} else {
					this.defaultKeys = [data.id]
				}

				this.room_capacity = data.room_capacity
				this.params.room_id = data.uid
				this.getRoom()
			}
		},
		//房间点击
		roomItemClick(item) {
			if (this.checkRoomList.some((v) => v.id == item.id)) {
				this.checkRoomList = this.checkRoomList.filter((v) => v.id != item.id)
			} else {
				// this.checkRoomList.push(item)
				this.checkRoomList = [item]
			}
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		},
		//新增宿舍楼
		addLou() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},
		remove() {
			let hasData = this.bedList[this.setCurrentBed]
			if (!hasData.id) return ElMessage.warning('请选择非空床位')
			this.dialog.leave = true

			this.$nextTick(() => {
				this.$refs.leaveDialog.open('edit').setData(hasData)
			})
		},
		//楼层保存回调
		handleSaveSuccess() {
			this.getRoom()
		}
	}
}
</script>

<style scoped lang="scss">
.currentBed {
	position: relative;
	&::after {
		content: '';
		width: 100%;
		height: 100%;
		border: 3px solid gold;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		border-radius: 8px;
		pointer-events: none;
	}
}
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;
	.el-button {
		width: 80%;
	}
}
.roomList {
	display: flex;
	flex-wrap: wrap;
	.roomItem {
		padding: 10px 20px;
		background-color: var(--el-color-info-light-8);
		border-radius: 8px;
		cursor: pointer;
		margin-right: 10px;
		margin-bottom: 10px;
	}
	.roomItem1 {
		background-color: var(--el-color-primary);
		color: var(--el-color-white);
	}
}
.roomList1 {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	grid-gap: 20px;
	.roomItem {
		--cc: #eee;

		height: auto;
		border: 1px solid var(--cc);
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		&-head {
			width: 100%;
			padding: 10px 20px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			position: relative;
			border-bottom: 1px solid var(--cc);
		}
		&-body {
			padding: 10px;
			flex: 1;
			&-row {
				display: flex;
				margin-top: 10px;
				color: #615d5d;
				.label {
					flex-shrink: 0;
				}
			}
		}
		&-foot {
			padding: 10px 20px;
			text-align: right;
			text {
				cursor: pointer;
			}
		}
	}
}
</style>
