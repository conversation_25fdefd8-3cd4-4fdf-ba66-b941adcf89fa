import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/eduapi/work/list`,
		name: '获取作业列表',
		get: async function(params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	add: {
		url: `${config.API_URL}/eduapi/work/save`,
		name: '新增作业',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/eduapi/work/del`,
		name: '删除作业',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	publish: {
		url: `${config.API_URL}/eduapi/work/publish`,
		name: '发布作业',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	cancel: {
		url: `${config.API_URL}/eduapi/work/cancel`,
		name: '撤销发布作业',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	saveResult: {
		url: `${config.API_URL}/eduapi/work/saveResult`,
		name: '保存成绩',
		post: async function(data) {
			return await http.post(this.url, data)
		}
	},
	one: {
		url: `${config.API_URL}/eduapi/work/one`,
		name: '获取单个作业',
		get: async function(params) {
			return await http.get(this.url, params)
		}
	},
	storeList: {
		url: `${config.API_URL}/eduapi/topic_store/list`,
		name: '题库列表',
		get: async function(params) {
			return await http.get(this.url, params)
		}
	},
	saveStore: {
		url: `${config.API_URL}/eduapi/topic_store/save`,
		name: '新增题目',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	delStore: {
		url: `${config.API_URL}/eduapi/topic_store/del`,
		name: '删除题目',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	storeOne: {
		url: `${config.API_URL}/eduapi/topic_store/one`,
		name: '题目信息',
		get: async function(params) {
			return await http.get(this.url, params)
		}
	},
	workDetail: {
		url: `${config.API_URL}/eduapi/work/detail`,
		name: '作业答题情况',
		get: async function(params) {
			return await http.get(this.url, params)
		}
	},
	answerDetail: {
		url: `${config.API_URL}/eduapi/work/answer_detail`,
		name: '学生答题详情',
		get: async function(params) {
			return await http.get(this.url, params)
		}
	},
	workTopicList: {
		url: `${config.API_URL}/eduapi/work_topic/list`,
		name: '作业题目列表',
		get: async function(params) {
			return await http.get(this.url, params)
		}
	},
	workTopicSave: {
		url: `${config.API_URL}/eduapi/work_topic/save`,
		name: '作业题目列表',
		post: async function(data) {
			return await http.post(this.url, data)
		}
	},
	workTopicDel: {
		url: `${config.API_URL}/eduapi/work_topic/del`,
		name: '删除作业题目',
		post: async function(data) {
			return await http.post(this.url, data)
		}
	},
	workCorrect: {
		url: `${config.API_URL}/eduapi/work/correct`,
		name: '批改作业',
		post: async function(data) {
			return await http.post(this.url, data)
		}
	},
	workReviewDesc: {
		url: `${config.API_URL}/eduapi/work/reviewDesc`,
		name: '提交评价',
		post: async function(data) {
			return await http.post(this.url, data)
		}
	},
	workCommentList: {
		url: `${config.API_URL}/eduapi/work_comment/list`,
		name: '评论列表',
		get: async function(params) {
			return await http.get(this.url, params)
		}
	},
	workCommentDel: {
		url: `${config.API_URL}/eduapi/work_comment/del`,
		name: '删除评论',
		post: async function(data) {
			return await http.post(this.url, data)
		}
	},
	workCommentReply: {
		url: `${config.API_URL}/eduapi/work_comment/reply`,
		name: '回复评论',
		post: async function(data) {
			return await http.post(this.url, data)
		}
	}
}
