<template>
	<el-dialog v-model="visible" width="500" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-width="100">
			<el-form-item label="审核结果" prop="audit_status">
				<el-radio-group v-model="form.audit_status">
					<el-radio :value="1">通过</el-radio>
					<el-radio :value="2">驳回</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.audit_status === 2" label="驳回备注" prop="reject_remark">
				<el-input v-model="form.reject_remark" type="textarea" rows="3" placeholder="请输入备注" clearable></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'

const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		id: null,
		cert_group_id: null,
		audit_status: 1,
		reject_remark: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			type: 'certGroup',
			templateMap: [],
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看',
				audit: '审核'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {},
			course: [],
			discipline: [],
			treeData: [],
			position: []
		}
	},
	watch: {},
	mounted() {},
	computed: {},
	created() {},
	methods: {
		async getTemplate() {
			if (!this.form.type || !this.form.object) {
				return
			}
			const { data } = await this.$API.cert.certTemplate.all.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				type: this.form.type,
				object: this.form.object
			})
			this.templateMap = data
		},
		//显示
		open(mode = 'audit', type = 'certGroup') {
			this.mode = mode
			this.visible = true
			this.type = type
			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let res
					if (this.type === 'certGroup') {
						res = await this.$API.cert.certGroup.audit.post(this.form)
					} else {
						this.form.cert_ids = this.form.id
						res = await this.$API.cert.cert.audit.post(this.form)
					}
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			this.form.tenant_id = data.tenant_id
			this.form.campus_id = data.campus_id
			this.form.id = data.id
			this.form.cert_group_id = data.cert_group_id
		}
	}
}
</script>

<style scoped lang="scss">
.el-select {
	width: 100%;
	max-width: unset;
}
</style>
