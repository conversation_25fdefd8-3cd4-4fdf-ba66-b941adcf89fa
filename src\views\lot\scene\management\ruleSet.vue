<template>
	<el-drawer v-model="visible" title="规则设置" size="850" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-position="top">
			<el-card header="触发条件">
				<el-row :gutter="10" style="width: 100%">
					<el-col :span="6">
						<el-form-item label="触发方式" prop="engine_type">
							<el-select v-model="form.engine_type" style="width: 100%; max-width: unset" placeholder="触发方式">
								<el-option label="设备触发" :value="1" />
								<el-option label="定时触发" :value="2" />
							</el-select>
						</el-form-item>
					</el-col>
					<template v-if="form.engine_type === 1">
						<el-col :span="8">
							<el-form-item label="设备类别" prop="filter.filter_type">
								<el-select
									v-model="form.filter.filter_type"
									style="width: 100%; max-width: unset"
									placeholder="设备类别"
								>
									<el-option label="单个设备" :value="1" />
									<el-option label="单类设备" :value="2" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="10">
							<el-form-item label="动作类型" prop="filter.trigger_mode">
								<el-select
									v-model="form.filter.trigger_mode"
									style="width: 100%; max-width: unset"
									placeholder="请选择动作类型"
									clearable
									@change="changeTriggerMode"
								>
									<el-option label="设备属性触发" :value="1" />
									<el-option label="设备事件触发" :value="2" />
									<el-option label="设备状态触发" :value="3" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="产品品类" prop="filter.product_id">
								<el-select
									v-model="form.filter.product_id"
									style="width: 100%; max-width: unset"
									placeholder="请选择产品品类"
									clearable
									@change="changeProduct"
								>
									<el-option
										v-for="(item, index) in productList"
										:key="index"
										:label="item.product_name"
										:value="item.id"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col v-if="form.filter.filter_type === 1" :span="12">
							<el-form-item label="设备" prop="filter.device_id">
								<el-select
									v-model="form.filter.device_id"
									style="width: 100%; max-width: unset"
									placeholder="请选择设备"
									clearable
								>
									<el-option
										v-for="(item, index) in deviceList"
										:key="index"
										:label="item.device_name"
										:value="item.id"
									>
										<span style="float: left">{{ item.device_name }}</span>
										<span
											v-if="item.room_info"
											style="float: right; color: var(--el-text-color-secondary); font-size: 12px"
										>
											{{ item.room_info.name }}
										</span>
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col v-if="form.filter.trigger_mode === 2" :span="12">
							<el-form-item label="设备事件" prop="filter.tsl_id">
								<el-select
									v-model="form.filter.tsl_id"
									style="width: 100%; max-width: unset"
									placeholder="请选择事件"
									clearable
								>
									<el-option v-for="(item, index) in eventTsl" :key="index" :label="item.name" :value="item.id">
										<span style="float: left">{{ item.name }}</span>
										<span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
											{{ item.code }}
										</span>
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col v-if="form.filter.trigger_mode === 3" :span="12">
							<el-form-item label="设备状态" prop="filter.device_status">
								<el-select
									v-model="form.filter.device_status"
									style="width: 100%; max-width: unset"
									placeholder="请选择设备状态"
									clearable
								>
									<el-option label="设备上线" :value="2" />
									<el-option label="设备离线" :value="3" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col v-if="form.filter.trigger_mode === 1" :span="12">
							<el-form-item label="功能属性" prop="filter.tsl_id">
								<el-select
									v-model="form.filter.tsl_id"
									style="width: 100%; max-width: unset"
									placeholder="请选择功能属性"
									clearable
									@change="selectTsl"
								>
									<el-option v-for="(item, index) in propertiesTsl" :key="index" :label="item.name" :value="item.id">
										<span style="float: left">{{ item.name }}</span>
										<span style="float: right; color: var(--el-text-color-secondary); font-size: 12px">
											{{ item.code }}
										</span>
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<template v-if="form.filter.trigger_mode === 1">
							<el-col :span="12">
								<el-row
									v-if="
										this.selectTslInfo?.type_spec?.type !== 'bool' && this.selectTslInfo?.type_spec?.type !== 'enum'
									"
									:gutter="10"
									style="width: 100%"
								>
									<el-col :span="12">
										<el-form-item label="条件" prop="filter.condition">
											<el-select
												v-model="form.filter.condition"
												style="width: 100%; min-width: unset; max-width: unset"
												placeholder="判断条件"
												clearable
											>
												<template v-if="this.selectTslInfo?.type_spec?.type !== 'text'">
													<el-option label="等于" value="eq" />
													<el-option label="大于" value="gt" />
													<el-option label="大于等于" value="gte" />
													<el-option label="小于" value="lt" />
													<el-option label="小于等于" value="lte" />
													<el-option label="不等于" value="neq" />
												</template>
												<template v-if="this.selectTslInfo?.type_spec?.type === 'text'">
													<el-option
														v-if="this.selectTslInfo?.type_spec?.type === 'text'"
														label="包含"
														value="include"
													/>
													<el-option
														v-if="this.selectTslInfo?.type_spec?.type === 'text'"
														label="不包含"
														value="notinclude"
													/>
												</template>
											</el-select>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item label="值" prop="filter.value">
											<el-input
												v-model="form.filter.value"
												placeholder="请输入判断值"
												style="width: 100%; max-width: unset"
											/>
										</el-form-item>
									</el-col>
								</el-row>
								<el-form-item v-if="this.selectTslInfo?.type_spec?.type === 'enum'" label="条件" prop="filter.value">
									<el-select
										v-model="form.filter.value"
										style="width: 100%; max-width: unset"
										placeholder="请选择判断条件"
										clearable
									>
										<el-option
											v-for="(item, index) in this.selectTslInfo?.type_spec?.specs"
											:key="index"
											:label="item.name"
											:value="item.value + ''"
										/>
									</el-select>
								</el-form-item>
								<el-form-item v-if="this.selectTslInfo?.type_spec?.type === 'bool'" label="条件" prop="filter.value">
									<el-select
										v-model="form.filter.value"
										style="width: 100%; max-width: unset"
										placeholder="请选择判断条件"
										clearable
									>
										<el-option
											v-for="(item, index) in this.selectTslInfo?.type_spec?.specs"
											:key="index"
											:label="item"
											:value="index + ''"
										/>
									</el-select>
								</el-form-item>
							</el-col>
						</template>
					</template>
					<template v-if="form.engine_type === 2">
						<el-col :span="4">
							<el-form-item label="执行时间" prop="cron_time">
								<el-time-select v-model="form.cron_time" start="00:00" step="00:05" end="23:55" placeholder="时间" />
							</el-form-item>
						</el-col>
						<el-col :span="14">
							<el-form-item label="执行周期" prop="cron_week">
								<el-checkbox-group v-model="form.cron_week">
									<el-checkbox-button :value="1">周一</el-checkbox-button>
									<el-checkbox-button :value="2">周二</el-checkbox-button>
									<el-checkbox-button :value="3">周三</el-checkbox-button>
									<el-checkbox-button :value="4">周四</el-checkbox-button>
									<el-checkbox-button :value="5">周五</el-checkbox-button>
									<el-checkbox-button :value="6">周六</el-checkbox-button>
									<el-checkbox-button :value="0">周日</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>
						</el-col>
					</template>
				</el-row>
			</el-card>
			<el-card header="触发动作" style="margin-top: 15px">
				<div v-for="(triggerItem, index) in form.triggerList" :key="index" class="triggerItem">
					<el-row :gutter="10">
						<el-col :span="6">
							<el-form-item
								label="设备类别"
								:prop="`triggerList.${index}.filter_type`"
								:rules="{ required: true, message: '请选择设备类别', trigger: 'blur' }"
							>
								<el-select
									v-model="form.triggerList[index].filter_type"
									style="width: 100%; max-width: unset"
									placeholder="设备类别"
								>
									<el-option label="单个设备" :value="1" />
									<el-option label="单类设备" :value="2" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item
								label="产品品类"
								:prop="`triggerList.${index}.product_id`"
								:rules="{ required: true, message: '请选择产品品类', trigger: 'blur' }"
							>
								<el-select
									v-model="form.triggerList[index].product_id"
									style="width: 100%; max-width: unset"
									placeholder="请选择产品品类"
									clearable
									@change="(value) => changeTriggerProduct(value, index)"
								>
									<el-option
										v-for="(item, i) in productList"
										:key="i"
										:label="item.product_name"
										:value="item.id"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col v-if="form.triggerList[index].filter_type === 1" :span="12">
							<el-form-item
								label="设备"
								:prop="`triggerList.${index}.device_id`"
								:rules="{ required: true, message: '请选择设备', trigger: 'blur' }"
							>
								<el-select
									v-model="form.triggerList[index].device_id"
									style="width: 100%; max-width: unset"
									placeholder="请选择设备"
									clearable
								>
									<el-option
										v-for="(item, i) in triggerDeviceList[index]"
										:key="i"
										:label="item.device_name"
										:value="item.id"
									>
										<span style="float: left">{{ item.device_name }}</span>
										<span
											v-if="item.room_info"
											style="float: right; color: var(--el-text-color-secondary); font-size: 12px"
										>
											{{ item.room_info.name }}
										</span>
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item
								label="功能属性"
								:prop="`triggerList.${index}.tsl_id`"
								:rules="{ required: true, message: '请选择功能属性', trigger: 'blur' }"
							>
								<el-select
									v-model="form.triggerList[index].tsl_id"
									style="width: 100%; max-width: unset"
									placeholder="请选择功能属性"
									clearable
									@change="(value) => selectTriggerTsl(value, index)"
								>
									<el-option v-for="(item, i) in triggerWriterTsl[index]" :key="i" :label="item.name" :value="item.id">
										<span style="float: left">{{ item.name }}</span>
										<span style="float: right; color: var(--el-text-color-secondary); font-size: 12px">
											{{ item.code }}
										</span>
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item
								v-if="
									this.selectTriggerTslInfo[index]?.type_spec?.type !== 'bool' &&
									this.selectTriggerTslInfo[index]?.type_spec?.type !== 'enum'
								"
								label="设定"
								:prop="`triggerList.${index}.value`"
								:rules="{ required: true, message: '请选择输入设定值', trigger: 'blur' }"
							>
								<el-input
									v-model="form.triggerList[index].value"
									placeholder="请输入值"
									style="width: 100%; max-width: unset"
								/>
							</el-form-item>

							<el-form-item
								v-if="this.selectTriggerTslInfo[index]?.type_spec?.type === 'enum'"
								label="设定"
								prop="value"
							>
								<el-select
									v-model="form.triggerList[index].value"
									style="width: 100%; max-width: unset"
									placeholder="请选择"
									clearable
								>
									<el-option
										v-for="(item, i) in this.selectTriggerTslInfo[index]?.type_spec?.specs"
										:key="i"
										:label="item.name"
										:value="item.value + ''"
									/>
								</el-select>
							</el-form-item>
							<el-form-item
								v-if="this.selectTriggerTslInfo[index]?.type_spec?.type === 'bool'"
								label="设定"
								prop="value"
							>
								<el-select
									v-model="form.triggerList[index].value"
									style="width: 100%; max-width: unset"
									placeholder="请选择"
									clearable
								>
									<el-option
										v-for="(item, i) in this.selectTriggerTslInfo[index]?.type_spec?.specs"
										:key="i"
										:label="item"
										:value="i + ''"
									/>
								</el-select>
							</el-form-item>
						</el-col>
						<div class="delAction">
							<el-button
								type="danger"
								text
								@click="form.triggerList.length > 1 ? form.triggerList.splice(index, 1) : null"
							>
								<el-icon size="18">
									<el-icon-CircleClose />
								</el-icon>
							</el-button>
						</div>
					</el-row>
				</div>
				<div>
					<el-button
						v-if="form.triggerList.length < 10"
						plain
						round
						type="success"
						style="width: 100%"
						icon="el-icon-CirclePlus"
						@click="addTrigger"
						>添加</el-button
					>
				</div>
			</el-card>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import cusTom from '@/utils/cusTom'
const defaultData = () => {
	return {
		tenant_id: null,
		campus_id: null,
		engine_id: null,
		engine_type: 1,
		cron_week: [],
		cron_time: null,
		filter: {
			filter_type: 1,
			product_id: null,
			device_id: null,
			tsl_id: null,
			trigger_mode: 1,
			device_status: null,
			condition: null,
			value: null
		},
		triggerList: [
			{
				trigger_id: null,
				filter_type: 1,
				product_id: null,
				device_id: null,
				tsl_id: null,
				value: null
			}
		]
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},

	data() {
		return {
			visible: false,
			isSaveing: false,
			enumConfig: [],
			productList: [],
			deviceList: [],
			triggerDeviceList: [],
			triggerWriterTsl: [],
			engineInfo: {},
			propertiesTsl: [],
			eventTsl: [],
			rule_filter: {},
			selectTslInfo: null,
			selectTriggerTslInfo: [],
			detailInfo: {},
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				cron_week: [{ required: true, message: '请选择执行周期' }],
				cron_time: [{ required: true, message: '请选择执行时间' }],
				engine_type: [{ required: true, message: '请选择触发方式' }],
				'filter.filter_type': [{ required: true, message: '请选择类别' }],
				'filter.product_id': [{ required: true, message: '请选择产品品类' }],
				'filter.device_id': [{ required: true, message: '请选择设备' }],
				'filter.tsl_id': [{ required: true, message: '请选择功能属性' }],
				'filter.trigger_mode': [{ required: true, message: '请选择动作类型' }],
				'filter.device_status': [{ required: true, message: '请选择设备状态' }],
				'filter.condition': [{ required: true, message: '请选择判断条件' }],
				'filter.value': [{ required: true, message: '请输入判断值' }]
			}
		}
	},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	mounted() {},
	watch: {
		/*'form.product_id': function (val, oldVal) {
			if (val) {
				this.getProductTslList(val)
				this.getProductDevicelList(val)
			} else {
				this.propertiesTsl = []
				this.eventTsl = []
			}
			if (oldVal !== val) {
				this.form.device_id = null
				this.form.tsl_id = null
				this.selectTslInfo = null
			}
		},*/
		'form.filter_type': function (val) {
			if (val === 2) {
				this.form.device_id = null
			}
		}
	},
	methods: {
		addTrigger() {
			this.form.triggerList.push({
				trigger_id: null,
				filter_type: 1,
				product_id: null,
				device_id: null,
				tsl_id: null,
				value: null
			})
		},
		changeTriggerMode() {
			this.form.filter.tsl_id = null
			this.selectTslInfo = null
			this.form.filter.value = null
		},
		changeTriggerProduct(val, index) {
			if (val) {
				this.getProductTslList(val, index)
				this.getProductDevicelList(val, index)
			} else {
				this.triggerDeviceList[index] = []
				this.triggerWriterTsl[index] = []
			}
			this.form.triggerList[index].device_id = null
			this.form.triggerList[index].tsl_id = null
			this.form.triggerList[index].value = null
		},
		changeProduct(val) {
			if (val) {
				this.getProductTslList(val)
				this.getProductDevicelList(val)
			} else {
				this.propertiesTsl = []
				this.eventTsl = []
			}
			this.form.filter.device_id = null
			this.form.filter.tsl_id = null
			this.selectTslInfo = null
			this.form.filter.value = null
		},
		selectTsl(val, type = true) {
			let selectTslInfo = this.propertiesTsl.filter((item) => item.id === val)
			if (selectTslInfo.length > 0) {
				this.selectTslInfo = selectTslInfo[0]
			} else {
				this.selectTslInfo = {}
			}
			if (type) {
				this.form.filter.condition = null
				this.form.filter.value = null
			}
		},
		selectTriggerTsl(val, index, type = true) {
			let selectTslInfo = this.triggerWriterTsl[index]?.filter((item) => item.id === val)
			if (selectTslInfo.length > 0) {
				this.selectTriggerTslInfo[index] = selectTslInfo[0]
			} else {
				this.selectTriggerTslInfo[index] = {}
			}
			if (type) {
				this.form.triggerList[index].value = null
			}
		},
		//显示
		async open(engineInfo) {
			this.engineInfo = engineInfo
			await this.getDetailInfo()
			await this.getProductList()
			this.visible = true
			this.form.tenant_id = this.engineInfo.tenant_id
			this.form.campus_id = this.engineInfo.campus_id
			this.form.engine_id = this.engineInfo.id
			if (this.detailInfo.triggerList.length > 0) {
				this.form.triggerList = this.detailInfo.triggerList
				console.log(this.detailInfo.triggerList)
				for (let i = 0; i < this.detailInfo.triggerList.length; i++) {
					await this.getProductTslList(this.detailInfo.triggerList[i].product_id, i)
					await this.getProductDevicelList(this.detailInfo.triggerList[i].product_id, i)
				}
			}
		},
		getProductTslList(product_id, index = -1) {
			console.log(product_id, index)
			var reqData = {
				product_id: product_id,
				tenant_id: this.engineInfo.tenant_id
			}
			this.$LotApi.productTsl.list.get(reqData).then((res) => {
				if (res.code === 200) {
					this.tslList = res.data
					let readTslList = []
					let writerTslList = []
					let eventTslList = []
					res.data.forEach((item) => {
						if (item.type_spec.specs) {
							item.type_spec.specs = JSON.parse(item.type_spec.specs)
						}
						if (item.type_spec.type === 'date' || item.type_spec.type === 'struct' || item.type_spec.type === 'array') {
							return
						}
						if (item.tsl_type === 1 && (item.access_mode === 1 || item.access_mode === 2)) {
							readTslList.push(item)
						}
						if (item.tsl_type === 1 && (item.access_mode === 2 || item.access_mode === 3)) {
							writerTslList.push(item)
						}
						if (item.tsl_type === 2) {
							eventTslList.push(item)
						}
					})
					if (index >= 0) {
						this.triggerWriterTsl[index] = writerTslList
						if (this.form.triggerList[index].tsl_id) {
							this.selectTriggerTsl(this.form.triggerList[index].tsl_id, index, false)
						}
					} else {
						this.propertiesTsl = readTslList
						this.eventTsl = eventTslList
						if (this.form.tsl_id && this.form.trigger_mode === 1) {
							this.selectTsl(this.form.tsl_id, false)
						}
					}
				} else {
					if (index >= 0) {
						this.triggerWriterTsl[index] = []
					} else {
						this.propertiesTsl = []
						this.eventTsl = []
					}
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		getProductDevicelList(product_id, index = -1) {
			var reqData = {
				product_id: product_id,
				tenant_id: this.engineInfo.tenant_id,
				campus_id: this.engineInfo.campus_id
			}
			this.$LotApi.device.all.get(reqData).then((res) => {
				if (res.code === 200) {
					if (index >= 0) {
						this.triggerDeviceList[index] = res.data
					} else {
						this.deviceList = res.data
					}
				} else {
					if (index >= 0) {
						this.triggerDeviceList[index] = []
					} else {
						this.deviceList = []
					}
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		async getDetailInfo() {
			var reqData = {
				id: this.engineInfo.id,
				tenant_id: this.engineInfo.tenant_id,
				campus_id: this.engineInfo.campus_id
			}
			var res = await this.$LotApi.ruleEngine.one.get(reqData)
			this.isSaveing = false
			if (res.code === 200) {
				this.detailInfo = res.data
				this.rule_filter = res.data.filter
				this.form.engine_type = res.data.engine_type
				this.form.cron_time = res.data.cron_time
				this.form.cron_week = JSON.parse(res.data.cron_week)
				if (this.rule_filter && this.form.engine_type === 1) {
					this.form.filter = this.rule_filter
					await this.getProductTslList(this.form.filter.product_id)
					await this.getProductDevicelList(this.form.filter.product_id)
				}
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		async getProductList() {
			var reqData = {
				node_type: 2,
				tenant_id: this.engineInfo.tenant_id,
				status: 1
			}
			var res = await this.$LotApi.product.all.get(reqData)
			this.isSaveing = false
			if (res.code === 200) {
				this.productList = res.data
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let subForm
					this.form.tenant_id = this.engineInfo.tenant_id
					this.form.campus_id = this.engineInfo.campus_id
					this.form.engine_id = this.engineInfo.id
					this.form.cron_week = JSON.stringify(this.form.cron_week)
					subForm = { ...this.form }
					var res = await this.$LotApi.ruleEngine.setConfig.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, 'edit')
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
:deep(.el-card__header) {
	font-size: 14px;
}
.triggerItem {
	border: 1px dashed var(--el-color-primary);
	padding: 10px 10px 0px 10px;
	margin-bottom: 15px;
	border-radius: 6px;
	position: relative;
}
.delAction {
	position: absolute;
	right: 10px;
	bottom: 18px;
}
</style>
