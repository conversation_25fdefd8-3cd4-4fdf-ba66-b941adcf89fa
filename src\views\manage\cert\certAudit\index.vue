<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList.length > 1" label="">
						<el-select
							v-model="params.campus_id"
							placeholder="请选择校区"
							filterable
							style="width: 180px"
							@change="campusChange"
						>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="" prop="semester_id">
						<cusSelectSemester
							v-model="params.semester_id"
							:params="params"
							:show-default-value="true"
							:width="'214px'"
							clearable
							style="margin-right: 15px"
							@semesterChange="semesterChange"
						/>
					</el-form-item>
					<el-form-item label="">
						<el-select
							v-model="params.type"
							placeholder="请选择证书类型"
							filterable
							style="width: 180px"
							clearable
							@change="upsearch"
						>
							<el-option
								v-for="item in certTypeMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select
							v-model="params.object"
							placeholder="请选择证书对象"
							filterable
							style="width: 180px"
							clearable
							@change="upsearch"
						>
							<el-option
								v-for="item in certObjectMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input
							v-model="params.name"
							placeholder="请输入证书组名称"
							clearable
							style="width: 240px"
							@input="upsearch"
						></el-input>
					</el-form-item>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
				<el-table-column
					label="证书组名称"
					prop="name"
					min-width="200"
					fixed="left"
					show-overflow-tooltip
				></el-table-column>
				<el-table-column label="学年学期" prop="academic_name" width="180" show-overflow-tooltip>
					<template #default="{ row }"> {{ row.academic_name }} - {{ row.semester_name }} </template>
				</el-table-column>
				<el-table-column label="证书类型" prop="signed" width="100">
					<template #default="{ row }">
						{{ formData(certTypeMap, row.type) }}
					</template>
				</el-table-column>
				<el-table-column label="证书对象" prop="issue" width="100">
					<template #default="{ row }">
						{{ formData(certObjectMap, row.object) }}
					</template>
				</el-table-column>
				<el-table-column label="证书模板" prop="cert_template" width="150" show-overflow-tooltip>
					<template #default="{ row }">
						<span v-if="row.cert_template">{{ row.cert_template.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="证书数量" prop="object_num" width="100">
					<template #default="{ row }">
						{{ row.object_num }}
					</template>
				</el-table-column>
				<el-table-column label="备注" prop="remark" min-width="120" show-overflow-tooltip>
					<template #default="{ row }"> {{ row.remark }} </template>
				</el-table-column>
				<el-table-column label="状态" prop="status" width="100">
					<template #default="scope">
						<el-tag v-if="scope.row.status === 2" type="primary">待审核</el-tag>
						<el-tag v-else-if="scope.row.status === 4" type="danger">已驳回</el-tag>
						<el-tag v-else type="success">已审核</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="驳回原因" prop="reject_remark" min-width="120" show-overflow-tooltip>
					<template #default="{ row }"> {{ row.reject_remark }} </template>
				</el-table-column>
				<el-table-column label="审核人" prop="auditer" width="100">
					<template #default="scope">
						<span v-if="scope.row.auditer">{{ scope.row.auditer?.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="审核时间" prop="audit_at" width="200"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="120">
					<template #default="scope">
						<el-button-group>
							<el-button
								v-if="scope.row.status === 2 || scope.row.status === 4"
								text
								type="danger"
								size="small"
								@click="table_audit(scope.row, scope.$index)"
								>审核</el-button
							>
							<el-button text type="primary" size="small" @click="table_detail(scope.row, scope.$index)"
								>详情</el-button
							>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>

	<audit-dialog
		v-if="dialog.audit"
		ref="auditDialog"
		:disciplineOptions="disciplineOptions"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></audit-dialog>

	<detail-dialog
		v-if="dialog.detail"
		ref="detailDialog"
		:info="currentGroup"
		@success="handleSaveSuccess"
		@closed="handleSaveSuccess"
	></detail-dialog>
</template>

<script>
import auditDialog from './audit.vue'
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
import detailDialog from '@/views/manage/cert/certGroup/detail.vue'
import CusSelectSemester from '@/components/custom/cusSelectSemester.vue'
const { campusId, tenantId, campusInfo, semesterInfo, certTypeMap, certObjectMap, certStatusMap } =
	cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		audit: 1,
		semester_id: null,
		name: null,
		type: null,
		object: null
	}
}
export default {
	name: '',
	components: {
		CusSelectSemester,
		detailDialog,
		auditDialog
	},
	data() {
		return {
			certTypeMap,
			certObjectMap,
			certStatusMap,
			semesterInfo,
			currentGroup: null,
			certStatusTagMap: {
				1: 'info',
				2: 'primary',
				3: 'success',
				4: 'danger',
				5: 'primary',
				6: 'warning',
				7: 'success',
				8: 'warning',
				9: 'success'
			},
			dialog: {
				audit: false,
				detail: false
			},
			apiObj: this.$API.cert.certGroup.list,
			selection: [],
			params: defaultParams(),
			CampusManagementList: campusInfo,
			treeData: null,
			disciplineOptions: []
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.semester_id = null
			},
			immediate: true
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	async created() {},
	methods: {
		//添加
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add')
			})
			// this.upsearch()
		},
		table_detail(row) {
			this.dialog.detail = true
			this.currentGroup = row
			this.$nextTick(() => {
				this.$refs.detailDialog.open('audit')
			})
		},
		//编辑
		table_audit(row) {
			this.dialog.audit = true
			this.$nextTick(() => {
				this.$refs.auditDialog.open('audit').setData(row)
			})
		},
		semesterChange(val) {
			this.params.semester_id = val
			this.$nextTick(() => {
				this.$refs.table.upData(this.params)
			})
		},
		campusChange(val) {
			this.params.campus_id = val
			this.params.semester_id = null
			this.$refs.table.upData(this.params)
		},
		table_config(row) {
			this.$message.info('开发中')
		},
		changeStatus(val, item) {
			item.status = val
			this.$API.cert.certGroup.save.post(item).then((res) => {
				if (res.code === 200) {
					ElMessage({ type: 'success', message: '操作成功！' })
				} else {
					ElMessage({ type: 'error', message: res.message })
				}
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }

			this.$confirm(`证书组删除后无法恢复，且相关证书也会同步删除，确定要删除吗？`, '提示', {
				type: 'warning'
			})
				.then(async () => {
					const loading = this.$loading()
					var res = await this.$API.cert.certGroup.del.post(reqData)
					loading.close()
					if (res.code === 200) {
						this.$message.success('删除成功')
						this.upsearch()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
				.catch(() => {})
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value === val)?.name || '-'
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else {
				this.$refs.table.refresh()
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.el-header > .left-panel {
	flex: 3;
}
</style>
