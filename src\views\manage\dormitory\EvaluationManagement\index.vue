<template>
	<el-container>
		<el-aside width="270px">
			<el-container>
				<el-header v-if="CampusManagementList.length > 1">
					<el-select v-model="params.campus_id" placeholder="校区" filterable>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-header>
				<el-main>
					<el-tree
						ref="group"
						class="menu"
						node-key="id"
						:data="groupsAdd"
						:highlight-current="true"
						:props="defaultProps"
						:default-expanded-keys="[0]"
						@node-click="groupClick"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span>{{ node.label }}</span>
							</span>
						</template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<div class="left-panel"></div>
				<div class="right-panel">
					<el-button type="primary" icon="el-icon-plus" @click="addRoom">新增类别</el-button>
				</div>
			</el-header>
			<el-main>
				<scTable ref="table" row-key="id" stripe :apiObj="list.apiObj" :params="params" @dataChange="dataChange">
					<el-table-column label="评价配置名称" prop="evaluate_name" fixed="left"></el-table-column>
					<el-table-column label="分值" prop="score" width="150"></el-table-column>
					<el-table-column label="最大分值" prop="max_score" width="150"></el-table-column>
					<el-table-column label="分值步长" prop="step_score" width="150"></el-table-column>

					<el-table-column label="性质" prop="nature" width="150">
						<template #default="scope">
							<el-tag v-if="scope.row.nature === 1" type="success">正面</el-tag>
							<el-tag v-if="scope.row.nature === -1" type="danger">负面</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="类型" prop="type_name" width="150"> </el-table-column>

					<el-table-column label="操作" fixed="right" align="center" width="180">
						<template #default="scope">
							<el-button-group>
								<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)"
									>查看</el-button
								>
								<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)"
									>编辑</el-button
								>
								<el-popconfirm title="确定删除吗？" @confirm="delRoom(scope.row, scope.$index)">
									<template #reference>
										<el-button type="danger" size="small" text>删除</el-button>
									</template>
								</el-popconfirm>
							</el-button-group>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
		<!-- {{ $refs.table }}--- -->
		<!-- {{ roomNameFilters }}  -->
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
	<saveRoomDialog
		v-if="dialog1.save"
		ref="saveRoomDialog"
		:params="params"
		:groupData="groupData"
		@success="handleSaveSuccess1"
		@closed="dialog1.save = false"
	>
	</saveRoomDialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import saveDialog from './save'
import saveRoomDialog from './saveRoom'
const defaultProps = {
	children: 'children',
	label: 'name'
}
const { campusId, tenantId, campusInfo, dormitoryEvaluateTypeMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		type_id: null
	}
}

export default {
	name: 'buildingRooms',
	data() {
		return {
			currentId: -1,
			groupFilterText: '',
			groupData: dormitoryEvaluateTypeMap,
			defaultProps,
			list: {
				apiObj: this.$API.buildingRooms.evaluate.list
			},
			params: defaultParams(),

			CampusManagementList: campusInfo,
			JobData: [],
			dialog: {
				save: false
			},
			dialog1: {
				save: false
			},
			showTools: false,
			roomNameFilters: []
		}
	},
	components: {
		saveDialog,
		saveRoomDialog
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.getRoom()
			}
		}
	},
	computed: {
		groupsAdd() {
			let arr = [...this.groupData]
			return arr
		}
	},
	async created() {},
	methods: {
		handleShowTools(item) {
			this.currentId = item.id
		},
		dataChange(val) {
			if (!val.data.rows) return
			let arr = val.data.rows.map((v) => {
				return {
					text: v.room_name,
					value: v.room_name
				}
			})
			this.roomNameFilters = cusTom.uniqueByValue(arr, 'text')
		},
		filterHandler(value, row, column) {
			const property = column['property']
			return row[property] === value
		},

		//获取房间列表
		async getRoom() {
			this.$refs.table.upData(this.params1)
		},

		//树点击事件
		groupClick(data) {
			this.params.type_id = data.value
			this.getRoom()
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		},
		//新增宿舍楼
		addLou() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},

		//楼层保存回调
		handleSaveSuccess() {
			this.getLou()
		},
		//房间保存回调
		handleSaveSuccess1(data, mode) {
			if (mode == 'add') {
				this.$refs.table.upData(this.search)
			} else if (mode == 'edit') {
				this.$refs.table.refresh()
			}
		},
		//编辑
		table_edit(row) {
			this.dialog1.save = true
			this.$nextTick(() => {
				this.$refs.saveRoomDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog1.save = true
			this.$nextTick(() => {
				this.$refs.saveRoomDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.JobManagement.del.post(reqData)

			if (res.code === 200) {
				this.$message.success('删除成功')
				this.getRoom()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status
			}
			this.$API.JobManagement.status.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.getLou()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//添加层
		appendCeng(data) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', 2, data.id)
			})
		},
		//添加房间
		addRoom() {
			this.dialog1.save = true
			this.$nextTick(() => {
				this.$refs.saveRoomDialog.open('add')
			})
		},
		//楼层树 编辑
		edit(node, data) {
			let obj = JSON.parse(JSON.stringify(data))
			delete obj.children
			obj.building_name = [{ value: obj.building_name }]
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit', obj.building_type, obj.parent_id).setData(obj)
			})
		},
		// 楼层树 删除
		del() {},
		//删除房间
		async delRoom(row, index) {
			var res = await this.$API.buildingRooms.evaluate.del.post({
				tenant_id: row.tenant_id,
				campus_id: row.campus_id,
				id: row.id
			})
			if (res.code === 200) {
				this.$emit('success')
				this.visible = false
				this.$message.success('操作成功')
				// this.$refs.table.upData(this.params)
				this.$refs.table.refresh()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		}
	}
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;
	.el-button {
		width: 80%;
	}
}
.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;
	box-sizing: border-box;
	a {
		font-size: 18px;
	}
}
</style>
