<template>
	<div class="sc-upload-file">
		<el-upload
			v-model:file-list="defaultFileList"
			:disabled="disabled"
			:auto-upload="autoUpload"
			:action="action"
			:name="name"
			:data="data"
			:http-request="request"
			:show-file-list="showFileList"
			:drag="drag"
			:accept="accept"
			:multiple="multiple"
			:limit="limit"
			:before-upload="before"
			:on-progress="onProgress"
			:on-success="success"
			:on-error="error"
			:on-preview="handlePreview"
			:on-exceed="handleExceed"
		>
			<slot>
				<el-button type="primary" :disabled="disabled">{{ buttonText }}</el-button>
			</slot>
			<template #tip>
				<div v-if="tip" class="el-upload__tip">{{ tip }}</div>
			</template>
		</el-upload>
		<span style="display: none !important"><el-input v-model="value"></el-input></span>
	</div>
</template>

<script>
import config from '@/config/upload'
import { getNewFileKey, getOssConfig } from '@/utils/ossLib'
import { useCommonStore } from '@/stores/common'
import pinia from '@/stores/pinia'
import OSS from 'ali-oss' //上传文件到oss

export default {
	props: {
		modelValue: { type: [String, Array], default: '' },
		buttonText: { type: String, default: '点击上传' },
		tip: { type: String, default: '' },
		action: { type: String, default: '' },
		fileTypeTag: { type: String, default: 'file' },
		apiObj: {
			type: Object,
			default: () => {}
		},
		name: { type: String, default: config.filename },
		data: {
			type: Object,
			default: () => {}
		},
		allowSuffix: { type: Array, default: () => [] },
		accept: { type: String, default: '' },
		maxSize: { type: Number, default: config.maxSizeFile },
		limit: { type: Number, default: 0 },
		autoUpload: { type: Boolean, default: true },
		showFileList: { type: Boolean, default: true },
		drag: { type: Boolean, default: false },
		multiple: { type: Boolean, default: true },
		disabled: { type: Boolean, default: false },
		onSuccess: {
			type: Function,
			default: () => {
				return true
			}
		}
	},
	data() {
		return {
			value: '',
			defaultFileList: []
		}
	},
	watch: {
		modelValue(val) {
			if (Array.isArray(val)) {
				if (JSON.stringify(val) !== JSON.stringify(this.formatArr(this.defaultFileList))) {
					this.defaultFileList = val
					this.value = val
				}
			} else {
				if (val !== this.toStr(this.defaultFileList)) {
					this.defaultFileList = this.toArr(val)
					this.value = val
				}
			}
		},
		defaultFileList: {
			handler(val) {
				this.$emit(
					'update:modelValue',
					Array.isArray(this.modelValue) ? this.formatArr(val) : this.limit > 1 ? this.formatArr(val) : this.toStr(val)
				)
				this.value = this.toStr(val)
			},
			deep: true
		}
	},
	mounted() {
		this.defaultFileList = Array.isArray(this.modelValue) ? this.modelValue : this.toArr(this.modelValue)
		this.value = this.modelValue
	},
	methods: {
		//默认值转换为数组
		toArr(str) {
			var _arr = []
			var arr = str.split(',')
			arr.forEach((item) => {
				if (item) {
					var urlArr = item.split('/')
					var fileName = urlArr[urlArr.length - 1]
					_arr.push({
						name: fileName,
						url: item
					})
				}
			})
			return _arr
		},
		//数组转换为原始值
		toStr(arr) {
			return arr.map((v) => v.url).join(',')
		},
		//格式化数组值
		formatArr(arr) {
			var _arr = []
			arr.forEach((item) => {
				if (item) {
					_arr.push({
						name: item.name,
						url: item.url
					})
				}
			})
			return _arr
		},
		before(file) {
			const maxSize = file.size / 1024 / 1024 < this.maxSize
			if (!maxSize) {
				this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!`)
				return false
			}
			console.log(this.allowSuffix)
			let index = file.name?.lastIndexOf('.')
			let ext = file.name?.substr(index + 1)
			if (this.allowSuffix.length > 0 && this.allowSuffix.indexOf(ext?.toLowerCase()) === -1) {
				this.$message.warning(`文件合适不支持 仅支持格式 ${this.allowSuffix.join(',')}`)
				return false
			}
		},
		onProgress(e) {
			const tmpCount = Math.floor(e.percent)
			e.percent = tmpCount
		},
		success(res, file) {
			var os = this.onSuccess(res, file)
			if (os !== undefined && os === false) {
				return false
			}
			// var response = config.parseData(res)
			// affairFile.name = response.fileName
			// affairFile.url = response.src
			if (res) {
				file.url = res.resultData.url
			}
		},
		error(err) {
			this.$notify.error({
				title: '上传文件未成功',
				message: err
			})
		},
		beforeRemove(uploadFile) {
			return this.$confirm(`是否移除 ${uploadFile.name} ?`, '提示', {
				type: 'warning'
			})
				.then(() => {
					return true
				})
				.catch(() => {
					return false
				})
		},
		handleExceed() {
			this.$message.warning(`当前设置最多上传 ${this.limit} 个文件，请移除后上传!`)
		},
		handlePreview(uploadFile) {
			window.open(uploadFile.url)
		},
		// request(param) {
		// 	var apiObj = config.apiObjFile
		// 	if (this.apiObj) {
		// 		apiObj = this.apiObj
		// 	}
		// 	const data = new FormData()
		// 	data.append(param.filename, param.affairFile)
		// 	for (const key in param.data) {
		// 		data.append(key, param.data[key])
		// 	}
		// 	apiObj
		// 		.post(data, {
		// 			onUploadProgress: (e) => {
		// 				const complete = parseInt(((e.loaded / e.total) * 100) | 0, 10)
		// 				param.onProgress({ percent: complete })
		// 			}
		// 		})
		// 		.then((res) => {
		// 			var response = config.parseData(res)
		// 			if (response.code === config.successCode) {
		// 				param.onSuccess(res)
		// 			} else {
		// 				param.onError(response.msg || '未知错误')
		// 			}
		// 		})
		// 		.catch((err) => {
		// 			param.onError(err)
		// 		})
		// },
		async request(params) {
			params.onProgress({ percent: 0 })
			var myUpFileReuslt = await this.putObjectBig(params.file, params.file.name, this.fileTypeTag, params)
			if (myUpFileReuslt) {
				params.onSuccess({
					resultData: myUpFileReuslt
				})
				params.onProgress({ percent: 100 })
				this.$emit('suc', myUpFileReuslt)
			} else {
				params.onError('上传失败')
			}
		},
		async putObjectBig(data, fileName, fileTypeTag, params) {
			const commonStore = useCommonStore(pinia)
			const info = {
				tenant_id: commonStore.tenant_id,
				campus_id: commonStore.campus_id
			}
			console.log('分片上传文件')
			var fileKey = getNewFileKey(fileName)
			var ossConfig = await getOssConfig(info)
			const newFileBascFolder = ossConfig.PrefixPath

			let client = new OSS({
				region: ossConfig.Region,
				accessKeyId: ossConfig.AccessKeyId,
				accessKeySecret: ossConfig.AccessKeySecret,
				bucket: ossConfig.Bucket,
				stsToken: ossConfig.SecurityToken
			})
			var ossKey = `${newFileBascFolder}${fileKey}`

			if (fileTypeTag && fileTypeTag != '') {
				ossKey = `${newFileBascFolder}${fileTypeTag}/${fileKey}`
			}
			var ossKeyUrl = `${ossConfig.bascAccessUrlHttps}/${ossKey}`
			try {
				let result = await client.multipartUpload(ossKey, data, {
					// 获取分片上传进度、断点和返回值。
					progress: (percentage) => {
						console.log(percentage)
						params.onProgress({ percent: (percentage * 100).toFixed(2) })
					},
					// 设置并发上传的分片数量。
					parallel: 8,
					// 设置分片大小。默认值为1 MB，最小值为100 KB。
					partSize: 1024 * 1024,
					headers: {
						'Cache-Control': 'public'
					}
					// 自定义元数据，通过HeadObject接口可以获取Object的元数据。
					// meta: { year: 2020, people: "test" },
					// mime: "text/plain",
				})
				var res = {
					key: ossKey,
					url: ossKeyUrl,
					size: data.size,
					name: fileName,
					type: data.type
				}
				return res
			} catch (e) {
				console.log(e)
				return null
			}
		}
	}
}
</script>

<style scoped>
.el-form-item.is-error .sc-upload-file:deep(.el-upload-dragger) {
	border-color: var(--el-color-danger);
}

.sc-upload-file {
	width: 100%;
}

.sc-upload-file:deep(.el-upload-list__item) {
	transition: none !important;
}
</style>
