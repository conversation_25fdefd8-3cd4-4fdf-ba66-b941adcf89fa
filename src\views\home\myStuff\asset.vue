<template>

	<el-main>
		<el-container>
			<el-header>
				<div class="left-panel">
					<el-select
						v-if="CampusManagementList.length > 1"
						v-model="params.campus_id"
						placeholder="校区"
						filterable
						@change="upsearch"
					>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
					<el-input
						v-model="params.name"
						placeholder="资产名称或资产编号"
						style="width: 200px; margin-left: 10px"
						@input="upsearch"
					></el-input>
				</div>
				<div class="right-panel">
					<el-radio-group v-model="params.is_current" @change="upsearch">
						<el-radio-button label="当前领用" :value="1" />
						<el-radio-button label="全部记录" :value="0" />
					</el-radio-group>
				</div>
			</el-header>
			<el-main>
				<scTable ref="table" row-key="id" stripe :apiObj="list.apiObj" :params="params">
					<el-table-column label="资产名称" prop="assets_name" show-overflow-tooltip></el-table-column>
					<el-table-column label="资产编号" prop="serial_number" show-overflow-tooltip></el-table-column>
					<el-table-column label="资产类型" prop="type_name" width="200"></el-table-column>
					<el-table-column label="操作类型" prop="log_type" width="120">
						<template #default="scope">
							<el-tag v-if="scope.row.log_type === 1" type="success">入库</el-tag>
							<el-tag v-if="scope.row.log_type === 2" type="warning">领用</el-tag>
							<el-tag v-if="scope.row.log_type === 3" type="danger">报废</el-tag>
							<el-tag v-if="scope.row.log_type === 4">归还</el-tag>
							<el-tag v-if="scope.row.log_type === 5" type="success">返库</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="操作时间" prop="created_at" width="180"></el-table-column>
					<el-table-column label="领用/归还人" prop="log_user_name" width="120"></el-table-column>
					<el-table-column label="操作人" prop="action_user_name" width="120"></el-table-column>
					<el-table-column label="位置" prop="room" show-overflow-tooltip>
						<template #default="scope">
							<span v-if="scope.row.room !== null"> {{ scope.row.room.name }}</span>
						</template>
					</el-table-column>
					<el-table-column label="备注" prop="log_remark" show-overflow-tooltip></el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-main>
</template>

<script>
import cusTom from '@/utils/cusTom'

const { campusId, tenantId, campusInfo, roomTypeMap, roomCapacityMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		name: '',
		is_current: 1
	}
}

export default {
	name: 'asserRecord',
	data() {
		return {
			list: {
				apiObj: this.$API.assets.myAssets
			},
			params: defaultParams(),
			CampusManagementList: campusInfo,
			roomNameFilters: [],
			roomTypeMap,
			roomCapacityMap
		}
	},
	computed: {},
	created() {
	},
	methods: {
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		}
	}
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;

	.el-button {
		width: 80%;
	}
}

.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;
	box-sizing: border-box;

	a {
		font-size: 18px;
	}
}
</style>
