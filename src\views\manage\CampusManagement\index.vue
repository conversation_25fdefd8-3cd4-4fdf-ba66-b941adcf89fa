<template>
	<cusTable ref="cusTable" :apiObj="list.apiObj" :params="params" row-key="id" stripe>
		<template #searchForm>
			<cusForm ref="formref" v-model="params" :config="config" :inline="true" style="margin-right: 12px"> </cusForm>
			<el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
			<el-button type="primary" icon="el-icon-refresh" @click="reload">重置</el-button>
		</template>
		<template #searchTool>
			<div>
				<el-button type="primary" icon="el-icon-plus" @click="add">新增校区</el-button>
			</div>
		</template>
		<template #content>
			<!-- <el-table-column type="selection" width="50"></el-table-column> -->
			<el-table-column label="校区名称" prop="campus_name" width="150"></el-table-column>
			<el-table-column label="校区编码" prop="campus_code" width="150"> </el-table-column>
			<el-table-column label="校区地址" prop="address" width="250"></el-table-column>
			<el-table-column label="校区联系人" prop="contacts" width="150"></el-table-column>
			<el-table-column label="校区联系号码" prop="phone" width="150"> </el-table-column>
			<el-table-column label="校区联系邮箱" prop="email" width="180"></el-table-column>
			<el-table-column label="备注" prop="remark"></el-table-column>
			<el-table-column label="状态" align="center" width="140">
				<template #default="scope">
					<el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
					<el-tag v-if="scope.row.status === -1" type="danger">停用</el-tag>
					<el-switch
						style="margin-left: 10px"
						v-model="scope.row.status"
						:active-value="1"
						:inactive-value="-1"
						@change="statusChange(scope.row)"
					></el-switch>
				</template>
			</el-table-column>
			<el-table-column label="操作" align="center" width="150">
				<template #default="scope">
					<el-button type="primary" size="small" link @click="edit(scope.row)">编辑</el-button>
					<el-popconfirm title="确定删除吗？" @confirm="del(scope.row, scope.$index)">
						<template #reference>
							<el-button text type="danger" size="small">删除</el-button>
						</template>
					</el-popconfirm>
				</template>
			</el-table-column>
		</template>
	</cusTable>
	<Dialog ref="Dialog" @close="refeshData"></Dialog>
</template>

<script>
import Dialog from './cpn/Dialog'
import { ElMessageBox, ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		id: null,
		name: null,
		tenant_id: tenantId
	}
}

const config = {
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: '校区名称',
			name: 'name',
			value: '',
			component: 'input',
			options: {
				placeholder: '请输入校区名称'
			}
		}
	]
}

export default {
	name: 'CampusManagement',
	components: {
		Dialog
	},
	data() {
		return {
			list: {
				apiObj: this.$API.CampusManagement.list
			},
			params: defaultData(),
			searchFormData: {},
			config
		}
	},
	created() {
		this.params.tenant_id = this.$TOOL.data.get('USER_INFO').tenant_id
	},
	methods: {
		add() {
			this.$refs.Dialog?.open()
		},
		edit(row) {
			this.$refs.Dialog?.open(row)
		},
		async del(row) {
			const { code, message } = await this.$API.CampusManagement.del.post({
				id: row.id,
				tenant_id: row.tenant_id
			})
			if (code == 200) {
				ElMessage.success({
					message: '删除成功!'
				})
				this.search()
			}
		},
		refeshData() {
			this.$refs.cusTable.$.refs.table.refresh()
		},
		search() {
			this.$refs.cusTable.$.refs.table.upData(this.params)
		},
		reload() {
			this.$refs.cusTable.$.refs.table.reload(defaultData())
			this.params = defaultData()
		},
		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status,
				tenant_id: row.tenant_id
			}
			this.$API.CampusManagement.edit.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style scoped></style>
