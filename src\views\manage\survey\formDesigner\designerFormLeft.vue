<template>
	<componentsGroup title="基础字段" :fields="basicFields" :list="basicComponents"></componentsGroup>
	<componentsGroup title="高级字段" :fields="advanceFields" :list="advanceComponents"></componentsGroup>
</template>

<script setup>
import { ref } from 'vue'
import componentsGroup from './component/ComponentGroup.vue'
import { basicComponents, advanceComponents } from './components'
const basicFields = ref([
	'input',
	'textarea',
	'number',
	'radio',
	'checkbox',
	'time',
	'date',
	'rate',
	'select',
	'switch',
	'text'
])
const advanceFields = ref(['img-upload', 'file-upload', 'divider', 'insert-img'])
</script>
