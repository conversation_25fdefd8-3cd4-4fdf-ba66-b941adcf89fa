<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
					<el-option
						v-for="item in CampusManagementList"
						:key="item.code"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
				<el-date-picker
					v-model="params.date"
					type="datetimerange"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					size="default"
					value-format="YYYY-MM-DD HH:mm:ss"
					style="width: 500px; margin-left: 15px; flex-grow: unset"
					@change="dataChange"
				/>
			</div>
			<div class="right-panel"></div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" stripe :apiObj="list.apiObj" :params="params">
				<el-table-column label="资产名称" prop="assets_name"></el-table-column>
				<el-table-column label="资产类型" prop="type_name"></el-table-column>
				<el-table-column label="操作类型" prop="log_type">
					<template #default="scope">
						<el-tag v-if="scope.row.log_type === 1" type="success">入库</el-tag>
						<el-tag v-if="scope.row.log_type === 2" type="warning" >领用</el-tag>
						<el-tag v-if="scope.row.log_type === 3" type="danger">报废</el-tag>
						<el-tag v-if="scope.row.log_type === 4">归还</el-tag>
						<el-tag v-if="scope.row.log_type === 5" type="success">返库</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作时间" prop="created_at"></el-table-column>
				<el-table-column label="领用/归还人" prop="log_user_name"></el-table-column>
				<el-table-column label="操作人" prop="action_user_name"></el-table-column>
				<el-table-column label="位置" prop="room">
					<template #default="scope">
					<span v-if="scope.row.room!==null">	{{ scope.row.room.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="备注" prop="log_remark"></el-table-column>
			</scTable>
		</el-main>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, roomTypeMap, roomCapacityMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		begin_time: null,
		end_time: null,
		date: []
	}
}

export default {
	name: 'asserRecord',
	data() {
		return {
			list: {
				apiObj: this.$API.assets.rooms.record
			},
			params: defaultParams(),
			CampusManagementList: campusInfo,
			roomNameFilters: [],
			roomTypeMap,
			roomCapacityMap
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {}
		}
	},
	computed: {},
	created() {},
	methods: {
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		dataChange(val) {
			console.log(val, 'dataChange')
			if (val) {
				this.params.begin_time = val[0]
				this.params.end_time = val[1]
			} else {
				this.params.begin_time = null
				this.params.end_time = null
			}
			this.$refs.table.upData(this.params)
		},
	}
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;

	.el-button {
		width: 80%;
	}
}

.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;
	box-sizing: border-box;

	a {
		font-size: 18px;
	}
}
</style>
