<template>
    <el-dialog v-if="dialogFormVisible" v-model="dialogFormVisible" :title="titleMap[mode1]" width="500">
        <cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode1"></cusForm>
        <template #footer>
            <el-button @click="dialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="confirm">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance, nextTick, onMounted } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, formTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params'])
const emit = defineEmits(['success'])
let mode1 = ref('add')
const dialogFormVisible = ref(false)
const defaultData = () => {
    return {
        tenant_id: tenantId,
        campus_id: campusId,
        title: '',
        form_type: '',
        begin_time: '',
        end_time: '',
        cover_url: null,
        multiple: null,
        mode: null
    }
}
let form = ref(defaultData())
let formConfig = ref({
    labelPosition: 'right',
    size: 'medium',
    formItems: [
        {
            label: '表单名称',
            name: 'title',
            value: null,
            component: 'input',
            options: {
                placeholder: '请输入表单名称',
                items: []
            },
            rules: [{ required: true, message: '请输入表单名称', trigger: 'blur' }]
        },
        {
            label: '表单类型',
            name: 'form_type',
            value: null,
            component: 'select',
            options: {
                placeholder: '请选择表单类型',
                items: formTypeMap.map((v) => {
                    return {
                        label: v.name,
                        value: v.value
                    }
                })
            },
            rules: [{ required: true, message: '请选择表单类型', trigger: 'blur' }]
        },
        // {
        //     label: '开始时间',
        //     name: 'begin_time',
        //     value: null,
        //     component: 'cusDate',
        //     options: {
        //         type: 'datetime',
        //         placeholder: '请选择开始时间',
        //         items: []
        //     },
        // },
        // {
        //     label: '结束时间',
        //     name: 'end_time',
        //     value: null,
        //     component: 'cusDate',
        //     options: {
        //         type: 'datetime',
        //         placeholder: '请选择结束时间',
        //         items: []
        //     },
        // },
        {
            label: '表单封面',
            name: 'cover_url',
            value: null,
            component: 'upload',
            options: {
                items: [
                    {
                        name: 'cover_url',
                        label: '表单封面',
                        type: 'cover'
                    }
                ]
            }
        }
    ]
})

let formref = ref(null)
let titleMap = ref({ add: '新增', edit: '编辑' })
onMounted(async () => {

})
const open = async (mode = 'add') => {
    dialogFormVisible.value = true
    mode1.value = mode
    form.value.mode = mode
    if (mode === 'add') {
        form.value = defaultData()
        setTimeout(() => {
            formref.value.resetFields()
        }, 0)
    }
    if (mode === 'edit') {
        nextTick(() => {
            formref.value.resetFields()
        })
    }
}
const confirm = async () => {
    await formref.value.validate()
    let subForm = { ...form.value }
    const handleSuccess = (data) => {
        emit('success', {data,mode1: mode1.value});
        dialogFormVisible.value = false;
        ElMessage({ type: 'success', message: '操作成功' });
    };
    const handleError = (message) => {
        ElMessage({ type: 'error', message });
    };
    if (mode1.value === 'add') {
        delete subForm.id
        const res = await globalPropValue.form.form.add.post(subForm)
        if (res.code === 200) {
            handleSuccess(res.data)
        } else {
            handleError(res.message);
        }
    } else if (mode1.value === 'edit') {
        const res = await globalPropValue.form.form.add.post(subForm)
        if (res.code === 200) {
            handleSuccess(res.data)
        } else {
            handleError(res.message);
        }
    }
}

//表单注入数据
const setData = (data) => {
    Object.assign(form.value, data)
    // form.value = { ...data }
}

defineExpose({
    dialogFormVisible,
    open,
    setData
})
</script>

<style lang="scss" scoped></style>
