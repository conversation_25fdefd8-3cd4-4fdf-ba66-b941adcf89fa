<template>
	<el-dialog
		v-model="visible"
		:title="titleMap[mode] + (form.student_name ? form.student_name : '批量操作')"
		:width="500"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<el-form ref="dialogForm" :rules="rules" :model="form" :disabled="mode == 'show'">
			<el-form-item label="离校日期" prop="leave_time">
				<el-date-picker
					v-model="form.date"
					type="date"
					placeholder="请选择日期"
					format="YYYY/MM/DD"
					value-format="YYYY-MM-DD"
				></el-date-picker>
			</el-form-item>
			<el-form-item label="离校原因" prop="leave_reason">
				<el-select v-model="form.leave_reason" filterable clearable>
					<el-option
						v-for="(item, key) in leaveReasonMap"
						:key="key"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="备注" prop="leave_remark">
				<el-input v-model="form.leave_remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { leaveReasonMap } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		tenant_id: '',
		campus_id: '',
		leave_reason: '',
		student_id: null,
		student_name: null,
		leave_remark: null,
		leave_time: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',

			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			titleMap: {
				edit: '离校学生-'
			},
			//验证规则
			rules: {
				leave_time: [{ required: true, message: '请输入离校日期' }],
				leave_reason: [{ required: true, message: '请选择离校原因' }]
			},
			//所需数据选项
			leaveReasonMap
		}
	},
	mounted() {},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.campus_id = this.params.campus_id
			this.form.tenant_id = this.params.tenant_id
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					this.form.student_id = this.form.id
					var res = await this.$API.eduStudent.leave.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			if (data.leave_reason === 0) {
				data.leave_reason = ''
			}
			Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
