<template>
	<!-- 通栏布局 -->
	<template v-if="layout === 'header'">
		<header class="adminui-header">
			<div class="adminui-header-left">
				<div class="logo-bar">
					<img class="logo" :src="appLogo" />
					<span>{{ appName }}</span>
				</div>
				<ul v-if="!ismobile" class="nav">
					<li v-for="item in menu" :key="item" :class="pmenu.path == item.path ? 'active' : ''" @click="showMenu(item)">
						<el-icon :style="{ color: item.meta.color }">
							<component :is="item.meta.icon || 'el-icon-menu'" />
						</el-icon>
						<span :style="{ color: item.meta.color }">{{ item.meta.title }}</span>
					</li>
				</ul>
			</div>
			<div class="adminui-header-right">
				<Userbar></Userbar>
			</div>
		</header>
		<section class="aminui-wrapper">
			<div
				v-if="(!ismobile && nextMenu.length > 0) || !pmenu.component"
				:class="menuIsCollapse ? 'aminui-side isCollapse' : 'aminui-side'"
			>
				<!--					<div v-if="!menuIsCollapse" class="adminui-side-top">
											<h2>{{ pmenu.meta.title }}</h2>
										</div>-->
				<div class="adminui-side-scroll">
					<el-scrollbar>
						<el-menu
							:default-active="active"
							router
							:collapse="menuIsCollapse"
							:unique-opened="$CONFIG.MENU_UNIQUE_OPENED"
						>
							<NavMenu :navMenus="nextMenu"></NavMenu>
						</el-menu>
					</el-scrollbar>
				</div>
				<div class="adminui-side-bottom" @click="toggleMenuIsCollapse">
					<el-icon>
						<el-icon-expand v-if="menuIsCollapse" />
						<el-icon-fold v-else />
					</el-icon>
				</div>
			</div>
			<Side-m v-if="ismobile"></Side-m>
			<div class="aminui-body el-container">
				<!--<Topbar v-if="!ismobile"></Topbar>-->
				<Tags v-if="!ismobile && layoutTags"></Tags>
				<div id="adminui-main" class="adminui-main">
					<router-view v-slot="{ Component }">
						<!--:include="keepAliveStore.keepLiveRoute"-->
						<keep-alive :max="20">
							<component :is="Component" v-if="keepAliveStore.routeShow" :key="$route.fullPath" />
						</keep-alive>
					</router-view>
					<iframe-view></iframe-view>
					<div v-if="ismobile" class="mobile_place"></div>
				</div>
			</div>
		</section>
	</template>

	<!-- 经典布局 -->
	<template v-else-if="layout === 'menu'">
		<header class="adminui-header">
			<div class="adminui-header-left">
				<div class="logo-bar">
					<img class="logo" :src="appLogo" />
					<span>{{ appName }}</span>
				</div>
				<Topbar v-if="!ismobile" :layout="'adminui-topbar-' + layout"></Topbar>
			</div>
			<div class="adminui-header-right">
				<Userbar></Userbar>
			</div>
		</header>
		<section class="aminui-wrapper">
			<div v-if="!ismobile" :class="menuIsCollapse ? 'aminui-side isCollapse' : 'aminui-side'">
				<div class="adminui-side-scroll">
					<el-scrollbar>
						<el-menu
							:default-active="active"
							router
							:collapse="menuIsCollapse"
							:unique-opened="$CONFIG.MENU_UNIQUE_OPENED"
						>
							<NavMenu :navMenus="menu"></NavMenu>
						</el-menu>
					</el-scrollbar>
				</div>
				<div class="adminui-side-bottom" @click="toggleMenuIsCollapse">
					<el-icon>
						<el-icon-expand v-if="menuIsCollapse" />
						<el-icon-fold v-else />
					</el-icon>
				</div>
			</div>
			<Side-m v-if="ismobile"></Side-m>
			<div class="aminui-body el-container">
				<Tags v-if="!ismobile && layoutTags"></Tags>
				<div id="adminui-main" class="adminui-main">
					<router-view v-slot="{ Component }">
						<!--:include="keepAliveStore.keepLiveRoute"-->
						<keep-alive :max="20">
							<component :is="Component" v-if="keepAliveStore.routeShow" :key="$route.fullPath" />
						</keep-alive>
					</router-view>
					<iframe-view></iframe-view>
					<div v-if="ismobile" class="mobile_place"></div>
				</div>
			</div>
		</section>
	</template>

	<!-- 功能坞布局 -->
	<template v-else-if="layout === 'dock'">
		<header class="adminui-header">
			<div class="adminui-header-left">
				<div class="logo-bar">
					<img class="logo" :src="appLogo" />
					<span>{{ appName }}</span>
				</div>
				<div v-if="!ismobile" class="adminui-header-menu">
					<ul v-if="!ismobile" class="nav">
						<li
							v-for="item in menu"
							:key="item"
							:class="pmenu.path == item.path ? 'active' : ''"
							@click="showDockMenu(item)"
						>
							<el-icon :style="{ color: item.meta.color }">
								<component :is="item.meta.icon || 'el-icon-menu'" />
							</el-icon>
							<span :style="{ color: item.meta.color }">{{ item.meta.title }}</span>
						</li>
					</ul>
					<el-drawer
						v-model="showDockSubMenu"
						size="auto"
						:with-header="false"
						:close-on-click-modal="true"
						modal-class="dockSubMenuModal"
						class="dockSubMenu"
						direction="ttb"
						destroy-on-close
					>
						<div class="two_menus">
							<!-- @click="goPath(item)"-->
							<template v-for="childItem in nextMenu" :key="childItem.path">
								<div
									v-if="!childItem.children || childItem.children?.length <= 1"
									class="two_menu_item"
									@click="goPath(childItem)"
								>
									<div class="icon_item" :style="{ color: childItem.meta.color }">
										<el-icon :style="{ color: childItem.meta.color }">
											<component :is="childItem.meta.icon || 'el-icon-menu'" />
										</el-icon>
									</div>
									<div class="secondary_menu_title_box">
										<span class="secondary_menu_title">{{ childItem.meta.title }}</span>
									</div>
								</div>
								<template v-else>
									<div
										v-for="childItem in childItem.children"
										:key="childItem.path"
										class="two_menu_item"
										@click="goPath(childItem)"
									>
										<div class="icon_item" :style="{ color: childItem.meta.color }">
											<el-icon :style="{ color: childItem.meta.color }">
												<component :is="childItem.meta.icon || 'el-icon-menu'" />
											</el-icon>
										</div>
										<div class="secondary_menu_title_box">
											<span class="secondary_menu_title">{{ childItem.meta.title }}</span>
										</div>
									</div>
								</template>
							</template>
						</div>
					</el-drawer>
					<!--<el-menu mode="horizontal" :default-active="active" router ellipsis style="max-width: 600px">
							<NavMenu :navMenus="menu"></NavMenu>
						</el-menu>-->
				</div>
			</div>
			<div class="adminui-header-right">
				<Userbar></Userbar>
			</div>
		</header>
		<section class="aminui-wrapper">
			<Side-m v-if="ismobile"></Side-m>
			<div class="aminui-body el-container">
				<Tags v-if="!ismobile && layoutTags"></Tags>
				<div id="adminui-main" class="adminui-main">
					<router-view v-slot="{ Component }">
						<keep-alive :max="20">
							<component :is="Component" v-if="keepAliveStore.routeShow" :key="$route.fullPath" />
						</keep-alive>
					</router-view>
					<iframe-view></iframe-view>
					<div v-if="ismobile" class="mobile_place"></div>
				</div>
			</div>
		</section>
	</template>

	<!-- 默认布局 -->
	<template v-else>
		<section class="aminui-wrapper">
			<div v-if="!ismobile" class="aminui-side-split">
				<div class="aminui-side-split-top">
					<router-link :to="$CONFIG.DASHBOARD_URL">
						<img class="logo" :title="appName" :src="appLogo" />
					</router-link>
				</div>
				<div class="adminui-side-split-scroll">
					<el-scrollbar>
						<ul>
							<li
								v-for="item in menu"
								:key="item"
								:class="pmenu.path == item.path ? 'active' : ''"
								@click="showMenu(item)"
							>
								<el-icon :style="{ color: item.meta.color }">
									<component :is="item.meta.icon || 'el-icon-menu'" />
								</el-icon>
								<p :style="{ color: item.meta.color }">{{ item.meta.title }}</p>
							</li>
						</ul>
					</el-scrollbar>
				</div>
			</div>
			<div
				v-if="(!ismobile && nextMenu.length > 0) || !pmenu.component"
				:class="menuIsCollapse ? 'aminui-side isCollapse' : 'aminui-side'"
			>
				<div v-if="!menuIsCollapse" class="adminui-side-top">
					<h2>{{ pmenu.meta.title }}</h2>
				</div>
				<div class="adminui-side-scroll">
					<el-scrollbar>
						<el-menu
							:default-active="active"
							router
							:collapse="menuIsCollapse"
							:unique-opened="$CONFIG.MENU_UNIQUE_OPENED"
						>
							<NavMenu :navMenus="nextMenu"></NavMenu>
						</el-menu>
					</el-scrollbar>
				</div>
				<div class="adminui-side-bottom" @click="toggleMenuIsCollapse">
					<el-icon>
						<el-icon-expand v-if="menuIsCollapse" />
						<el-icon-fold v-else />
					</el-icon>
				</div>
			</div>
			<Side-m v-if="ismobile"></Side-m>
			<div class="aminui-body el-container">
				<Topbar>
					<Userbar></Userbar>
				</Topbar>
				<Tags v-if="!ismobile && layoutTags"></Tags>
				<div id="adminui-main" class="adminui-main">
					<router-view v-slot="{ Component }">
						<keep-alive :max="20">
							<component :is="Component" v-if="keepAliveStore.routeShow" :key="$route.fullPath" />
						</keep-alive>
					</router-view>
					<iframe-view></iframe-view>
					<div v-if="ismobile" class="mobile_place"></div>
				</div>
			</div>
		</section>
	</template>

	<div class="main-maximize-exit" @click="exitMaximize">
		<el-icon>
			<el-icon-close />
		</el-icon>
	</div>

	<!--	<div class="layout-setting" @click="openSetting">
				<el-icon><el-icon-brush-filled /></el-icon>
			</div>

			<el-drawer v-model="settingDialog" title="布局实时演示" :size="400" append-to-body destroy-on-close>
				<setting></setting>
			</el-drawer>-->

	<auto-exit></auto-exit>
</template>

<script>
import SideM from './components/sideM.vue'
import Topbar from './components/topbar.vue'
import Tags from './components/Tags.vue'
import NavMenu from './components/NavMenu.vue'
import Userbar from './components/Userbar.vue'
/*
import setting from './components/setting.vue'
*/
import logo from '@/assets/img/logo.svg'

import iframeView from './components/IframeView.vue'
import autoExit from './other/autoExit.js'

import { useGlobalStore } from '@/stores/global.js'
import { useKeepAliveStore } from '@/stores/keepAlive.js'
import cusTom from '@/utils/cusTom'

export default {
	name: 'index',
	components: {
		SideM,
		Topbar,
		Tags,
		NavMenu,
		Userbar,
		//setting,
		iframeView,
		autoExit
	},
	data() {
		return {
			settingDialog: false,
			showDockSubMenu: false,
			menu: [],
			nextMenu: [],
			pmenu: {},
			appName: '',
			appLogo: logo,
			userInfo: {},
			keepAliveStore: useKeepAliveStore(),
			globalStore: useGlobalStore(),
			active: ''
			//layoutWaterMark: this.$CONFIG.LAYOUT_WATER_MARK || false
		}
	},
	computed: {
		ismobile() {
			return this.globalStore.ismobile
		},
		layout() {
			return this.globalStore.layout
		},
		layoutTags() {
			return this.globalStore.layoutTags
		},
		menuIsCollapse() {
			return this.globalStore.menuIsCollapse
		}
	},
	created() {
		this.onLayoutResize()
		window.addEventListener('resize', this.onLayoutResize)
		var menu = this.$router.sc_getMenu()
		this.menu = this.filterUrl(menu)
		this.showThis()
		this.userInfo = this.$TOOL.data.get('USER_INFO')
		if (this.userInfo.tenant_info) {
			this.appName = this.userInfo.tenant_info.name ? this.userInfo.tenant_info.name : this.$CONFIG.APP_NAME
			this.appLogo = this.userInfo.tenant_info.img ? this.userInfo.tenant_info.img : this.appLogo
		}
	},
	watch: {
		$route() {
			this.showThis()
		},
		layout: {
			handler(val) {
				document.body.setAttribute('data-layout', val)
				//document.body.classList.add('layout-' + val)
				document.body.className = 'layout-' + val
			},
			immediate: true
		}
	},
	methods: {
		goPath(item) {
			this.showDockSubMenu = false
			this.$nextTick(() => {
				this.$router.push({ path: item.path })
			})
		},
		openSetting() {
			this.settingDialog = true
		},
		onLayoutResize() {
			var browserWidth = window.innerWidth || document.documentElement.clientWidth
			var isMobile = browserWidth < 992
			if (isMobile) {
				document.body.classList.add('mobileWd')
			} else {
				document.body.classList.remove('mobileWd')
			}
			this.globalStore.SET_ismobile(isMobile)
		},

		//路由监听高亮
		showThis() {
			this.pmenu = this.$route.meta.breadcrumb ? this.$route.meta.breadcrumb[0] : {}
			this.nextMenu = this.filterUrl(this.pmenu.children)
			this.$nextTick(() => {
				this.active = this.$route.meta.active || this.$route.fullPath
			})
		},
		//点击显示
		showMenu(route) {
			this.pmenu = route
			this.nextMenu = this.filterUrl(route.children)
			if ((!route.children || route.children.length === 0) && route.component) {
				this.$router.push({ path: route.path })
			}
		},
		//点击显示
		showDockMenu(route) {
			this.showDockSubMenu = false
			this.pmenu = route
			this.nextMenu = this.filterUrl(route.children)
			if ((!route.children || route.children.length === 0) && route.component) {
				this.$router.push({ path: route.path })
			} else {
				this.showDockSubMenu = true
			}
		},
		toggleMenuIsCollapse() {
			this.globalStore.TOGGLE_menuIsCollapse()
		},
		//转换外部链接的路由
		filterUrl(map) {
			var newMap = []
			map &&
				map.forEach((item) => {
					item.meta = item.meta ? item.meta : {}
					//处理隐藏
					if (item.meta.hidden || item.meta.type === 'button') {
						return false
					}
					//处理http
					if (item.meta.type === 'iframe') {
						item.path = `/i/${item.name}`
					}
					//递归循环
					if (item.children && item.children.length > 0) {
						item.children = this.filterUrl(item.children)
					}
					newMap.push(item)
				})
			return newMap
		},
		//退出最大化
		exitMaximize() {
			document.getElementById('app').classList.remove('main-maximize')
		}
	}
}
</script>
