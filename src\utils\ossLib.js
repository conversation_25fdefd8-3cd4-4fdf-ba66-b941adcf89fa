import tool from '@/utils/tool'
import http from '@/utils/request'
import config from '@/config'
import OSS from 'ali-oss'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import pinia from '@/stores/pinia.js'
import { useCommonStore } from '@/stores/common'
const ossKey = 'scms_oss_config_key'

let info = {
	tenant_id: null,
	campus_id: null
}

export function clearOssConfig() {
	// clearStorage(ossKey)
	tool.data.remove(ossKey)
}

export function setOssConfig(ossConfig) {
	// clearStorage(ossKey)
	// setStorage(ossKey, ossConfig)
	tool.data.remove(ossKey)
	tool.data.set(ossKey, ossConfig)
}

async function refreshOssConfig() {
	return new Promise((resolve, reject) => {
		http
			.get(`${config.API_URL}/api/common/getStsInfo`, info)
			.then((response) => {
				const { data } = response
				data.bascAccessUrlHttps = `${data.Protocol}://${data.Host}`
				setOssConfig(data)
				resolve(data)
			})
			.catch((error) => {
				console.log(error)
				reject(error)
			})
	})
}

export function getFileLimitMB() {
	var ossConfig = tool.data.get(ossKey)
	if (ossConfig && ossConfig.fileLimitMB > 0) {
		return ossConfig.fileLimitMB
	}
	return 250
}

export async function getOssConfigProcess() {
	var obj = tool.data.get(ossKey)
	if (obj) {
		const startTime = dayjs()
		const endTime = dayjs(obj.exTime)
		if (dayjs(endTime).diff(dayjs(startTime), 'seconds') > 0) {
			console.log('使用旧的ossconfig')
			return obj
		}
	}
	obj = await refreshOssConfig()
	console.log('使用新的ossconfig')
	return obj
}

export async function getOssConfig(baseInfo) {
	if (baseInfo) {
		info = baseInfo
	}
	var ossConfig = await getOssConfigProcess()
	if (ossConfig && ossConfig.isMoreThanCloudLimit) {
		var s = '您上传的文件总量已超出限制，建议您清理3个月以前上传的文件数据后再次使用上传功能'
		ElMessage({
			type: 'error',
			message: s
		})
		throw s
		return
	}
	return ossConfig
}

function getRandomIntInclusive(min, max) {
	min = Math.ceil(min)
	max = Math.floor(max)
	return Math.floor(Math.random() * (max - min + 1)) + min //含最大值，含最小值
}
//生成随机字符串默认8位
function randomString(len = 8) {
	let $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678' /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
	let maxPos = $chars.length
	let pwd = ''
	for (let i = 0; i < len; i++) {
		pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
	}
	return pwd
}

export function getNewFileKey(fileName) {
	// var startIndex = fileName.lastIndexOf('.')
	// var suffix = fileName.substring(startIndex, fileName.length).toLowerCase()
	// let date = dayjs().format('YYYYMMDD')
	// let time = dayjs().format('HHmmss')
	// let randomNum = getRandomIntInclusive(100, 999)
	// return `${date}/a${time}${randomNum}${suffix}`

	var startIndex = fileName.lastIndexOf('.')
	var suffix = fileName.substring(startIndex, fileName.length).toLowerCase()

	let date = dayjs().format('YYYYMMDD')
	let randomNum = getRandomIntInclusive(100, 999)
	//获取时间戳
	let time = dayjs().valueOf()
	return `${date}/${randomString()}${time}${suffix}`
}

function getNewFileKey2(fileName) {
	var suffix = fileName
	let date = moment().format('YYYYMMDD')
	let randomNum = getRandomIntInclusive(100, 999)
	return `${date}/${randomNum}${suffix}`
}

const headers = {
	'Cache-Control': 'public'
}
export async function putObject(data, fileName, fileTypeTag) {
	var fileKey = getNewFileKey(fileName)
	var ossConfig = await getOssConfig()

	const newFileBascFolder = ossConfig.PrefixPath

	let client = new OSS({
		region: ossConfig.Region,
		accessKeyId: ossConfig.AccessKeyId,
		accessKeySecret: ossConfig.AccessKeySecret,
		bucket: ossConfig.Bucket,
		stsToken: ossConfig.SecurityToken
	})
	var ossKey = `${newFileBascFolder}${fileKey}`

	if (fileTypeTag && fileTypeTag != '') {
		ossKey = `${newFileBascFolder}${fileTypeTag}/${fileKey}`
		// ossKey = fileKey
	}
	var ossKeyUrl = `${ossConfig.bascAccessUrlHttps}/${ossKey}`
	try {
		let result = await client.put(ossKey, data, { headers })
		var res = {
			key: ossKey,
			url: ossKeyUrl
		}
		return res
	} catch (e) {
		console.log(e)
		return null
	}
}

export async function putObject2(data, fileName, fileTypeTag) {
	var fileKey = getNewFileKey2(fileName)
	var ossConfig = await getOssConfig()

	let client = new OSS({
		region: ossConfig.Region,
		accessKeyId: ossConfig.AccessKeyId,
		accessKeySecret: ossConfig.AccessKeySecret,
		bucket: ossConfig.Bucket,
		stsToken: ossConfig.SecurityToken
	})
	var ossKey = `${newFileBascFolder}${fileKey}`

	if (fileTypeTag && fileTypeTag != '') {
		ossKey = `${newFileBascFolder}${fileTypeTag}/${fileKey}`
	}
	var ossKeyUrl = `${ossConfig.bascAccessUrlHttps}/${ossKey}`
	try {
		let result = await client.put(ossKey, data, { headers })
		var res = {
			key: ossKey,
			url: ossKeyUrl
		}
		return res
	} catch (e) {
		console.log(e)
		return null
	}
}

const options = {
	// 获取分片上传进度、断点和返回值。
	progress: (percentage) => {
		console.log(percentage)
		//params.onProgress({ percent: percentage.toFixed(2) * 100 })
	},
	// 设置并发上传的分片数量。
	parallel: 8,
	// 设置分片大小。默认值为1 MB，最小值为100 KB。
	partSize: 1024 * 1024,
	headers
	// 自定义元数据，通过HeadObject接口可以获取Object的元数据。
	// meta: { year: 2020, people: "test" },
	// mime: "text/plain",
}

export async function putObjectBig(data, fileName, fileTypeTag, params) {
	const commonStore = useCommonStore(pinia)
	info.tenant_id = commonStore.tenant_id
	info.campus_id = commonStore.campus_id

	console.log('分片上传文件')
	var fileKey = getNewFileKey(fileName)
	var ossConfig = await getOssConfig()
	const newFileBascFolder = ossConfig.PrefixPath

	let client = new OSS({
		region: ossConfig.Region,
		accessKeyId: ossConfig.AccessKeyId,
		accessKeySecret: ossConfig.AccessKeySecret,
		bucket: ossConfig.Bucket,
		stsToken: ossConfig.SecurityToken
	})
	var ossKey = `${newFileBascFolder}${fileKey}`

	if (fileTypeTag && fileTypeTag != '') {
		ossKey = `${newFileBascFolder}${fileTypeTag}/${fileKey}`
	}
	var ossKeyUrl = `${ossConfig.bascAccessUrlHttps}/${ossKey}`
	try {
		let result = await client.multipartUpload(ossKey, data, {
			// 获取分片上传进度、断点和返回值。
			progress: (percentage) => {
				console.log(percentage)
				if (params) {
					params.onProgress({ percent: (percentage * 100).toFixed(2) })
				}
			},
			// 设置并发上传的分片数量。
			parallel: 8,
			// 设置分片大小。默认值为1 MB，最小值为100 KB。
			partSize: 1024 * 1024,
			headers
			// 自定义元数据，通过HeadObject接口可以获取Object的元数据。
			// meta: { year: 2020, people: "test" },
			// mime: "text/plain",
		})
		console.log(result)
		var res = {
			key: ossKey,
			url: ossKeyUrl,
			size: data.size,
			name: fileName,
			type: data.type
		}
		return res
	} catch (e) {
		console.log(e)
		return null
	}
}
