<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<el-select
					v-if="CampusManagementList.length > 1"
					v-model="params.campus_id"
					placeholder="校区"
					filterable
					style="margin-right: 15px"
				>
					<el-option
						v-for="item in CampusManagementList"
						:key="item.code"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
				<el-date-picker
					v-model="params.date"
					type="daterange"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					size="default"
					value-format="YYYY-MM-DD"
					style="width: 500px; margin-left: 15px; flex-grow: unset"
					@change="dataChange"
				/>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" stripe :apiObj="list.apiObj" :params="params">
				<el-table-column label="预约房间" prop="room_name" width="200">
					<template #default="scope">
						<div v-if="scope.row.room" style="padding: 10px 0">{{ scope.row.room.name }}</div>
					</template>
				</el-table-column>
				<el-table-column label="预约主题" prop="title" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="预约时间" prop="begin_time" width="300">
					<template #default="scope"> {{ scope.row.begin_time }} - {{ scope.row.end_time }}</template>
				</el-table-column>
				<el-table-column label="参与老师" prop="teachers_list" width="300">
					<template #default="scope">
						<el-tag
							v-for="(item, index) in scope.row.teachers_list"
							:key="index"
							size="small"
							style="margin-bottom: 8px"
							>{{ item.name }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="参与学生" prop="students_list" width="350">
					<template #default="scope">
						<el-tag
							v-for="(item, index) in scope.row.students_list"
							:key="index"
							size="small"
							style="margin-bottom: 8px"
							>{{ item.name }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="备注" prop="remark"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="280">
					<template #default="scope">
						<template v-if="scope.row.room">
							<el-popover
								v-if="scope.row.parent_room != null"
								placement="left"
								title="请选择开门场室"
								:width="300"
								trigger="hover"
								content="请选择开门场室"
							>
								<template #reference>
									<el-button plain type="primary" size="small">远程开门</el-button>
								</template>
								<div>
									<p>
										<el-button
											v-if="scope.row.parent_room"
											plain
											type="primary"
											style="margin-bottom: 10px"
											size="small"
											@click="open(scope.row, scope.row.parent_room.id)"
											>{{ scope.row.parent_room.name }}</el-button
										>
									</p>
									<p>
										<el-button
											v-if="scope.row.room"
											plain
											type="primary"
											size="small"
											@click="open(scope.row, scope.row.room.id)"
											>{{ scope.row.room.name }}</el-button
										>
									</p>
								</div>
							</el-popover>

							<el-button v-else plain type="primary" size="small" @click="open(scope.row, scope.row.room.id)"
								>远程开门</el-button
							>

							<el-popover
								v-if="scope.row.parent_room != null"
								placement="left"
								title="请选择场室"
								:width="300"
								trigger="hover"
								content="请选择场室"
							>
								<template #reference>
									<el-button plain type="warning" size="small">临时密码</el-button>
								</template>
								<div>
									<p>
										<el-button
											v-if="
												scope.row.parent_room &&
												scope.row.parent_room.device_info?.device_type === 96 &&
												scope.row.parent_room.device_info?.device_sn
											"
											plain
											type="primary"
											style="margin-bottom: 10px"
											size="small"
											@click="temporaryPwd2(scope.row)"
											>{{ scope.row.parent_room.name }}</el-button
										>
									</p>
									<p>
										<el-button
											v-if="
												scope.row.room &&
												scope.row.room.device_info?.device_type === 96 &&
												scope.row.room.device_info?.device_sn
											"
											plain
											type="primary"
											size="small"
											@click="temporaryPwd(scope.row)"
											>{{ scope.row.room.name }}</el-button
										>
									</p>
								</div>
							</el-popover>

							<el-button
								v-else-if="scope.row.room.device_info?.device_type === 96 && scope.row.room.device_info?.device_sn"
								plain
								type="warning"
								size="small"
								@click="temporaryPwd(scope.row)"
								>临时密码</el-button
							>

							<el-button plain type="danger" size="small" @click="table_cancel(scope.row, scope.$index)"
								>取消预约</el-button
							>
						</template>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<save ref="saveDialog" @success="saveSuccess"></save>
	</el-container>
	<el-dialog v-model="dialogPwd" title="临时密码" width="500" center>
		<h1 style="text-align: center; font-size: 48px; line-height: 60px; color: var(--el-color-danger)">
			{{ pwdInfo.pwd }}
		</h1>
		<p style="width: 90%; padding-left: 10%; font-size: 12px; line-height: 20px; color: var(--el-color-info)">
			1. 密码有效期：<span style="font-weight: bold; color: var(--el-color-danger)">{{ pwdInfo.expired }}</span>
			分钟，将在
			<span style="font-weight: bold; color: var(--el-color-danger)">{{ pwdInfo.effective_end_time }}</span>
			以后失效，<br />2. 请妥善使用，切勿随意告知给别人。
		</p>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" plain @click="copyPwd(pwdInfo)">复制密码</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import save from './save.vue'
import { ElMessage } from 'element-plus'
import useClipboard from 'vue-clipboard3'

const { campusId, tenantId, campusInfo, roomTypeMap, roomCapacityMap } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		begin_date: null,
		end_date: null,
		date: []
	}
}
export default {
	components: {
		save
	},
	data() {
		return {
			CampusManagementList: campusInfo,
			params: defaultParams(),
			pwdInfo: {},
			dialogPwd: false,
			list: {
				apiObj: this.$API.fieldReservation.myBooking
			}
		}
	},
	methods: {
		async copyPwd(pwdInfo) {
			try {
				let encoding =
					'临时开门密码：' +
					pwdInfo.pwd +
					'；密码有效期：' +
					pwdInfo.expired +
					'分钟，过期时间：' +
					pwdInfo.effective_end_time
				const { toClipboard } = useClipboard()
				await toClipboard(encoding)
				this.$message.success('复制成功')
			} catch (error) {
				this.$message.error('复制失败')
			}
		},
		async temporaryPwd2(row) {
			var reqData = {
				device_sn: row.parent_room.device_info?.device_sn,
				tenant_id: row.tenant_id,
				campus_id: row.campus_id,
				book_id: row.id
			}
			var res = await this.$API.fieldReservation.temporaryPwd.post(reqData)
			if (res.code === 200) {
				this.$message.success('生成成功')
				this.dialogPwd = true
				this.pwdInfo = res.data
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		async temporaryPwd(row) {
			var reqData = {
				device_sn: row.room.device_info?.device_sn,
				tenant_id: row.tenant_id,
				campus_id: row.campus_id,
				book_id: row.id
			}
			var res = await this.$API.fieldReservation.temporaryPwd.post(reqData)
			if (res.code === 200) {
				this.$message.success('生成成功')
				this.dialogPwd = true
				this.pwdInfo = res.data
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		dataChange(val) {
			console.log(val, 'dataChange')
			if (val) {
				this.params.begin_date = val[0]
				this.params.end_date = val[1]
			} else {
				this.params.begin_date = null
				this.params.end_date = null
			}
			this.$refs.table.upData(this.params)
		},
		//修改预约
		table_edit(row) {
			this.$refs.saveDialog.open(row)
		},
		// 修改成功
		async saveSuccess(formVal) {
			console.log(formVal, 'saveSuccess')
			let formData = {
				id: formVal.id,
				tenant_id: tenantId,
				campus_id: campusId,
				title: formVal.title,
				room_id: formVal.room_id[formVal.room_id.length - 1] || formVal.room_id,
				begin_time: formVal.date + ' ' + formVal.startTime,
				end_time: formVal.date + ' ' + formVal.endTime,
				teachers: formVal.teachers.map((item) => item.id).join(','),
				students: formVal.students.map((item) => item.id).join(','),
				remark: formVal.remark
			}
			const { code, message } = await this.$API.fieldReservation.edit.post(formData)
			if (code == 200) {
				ElMessage.success(message)
				this.$refs.table.upData(this.params)
			} else {
				ElMessage.error(message)
			}
		},
		//取消预约
		async table_cancel(row) {
			this.$confirm(`是否取消当前预约？`, '提示', {
				type: 'warning'
			})
				.then(async () => {
					let delData = {
						id: row.id,
						tenant_id: tenantId,
						campus_id: campusId
					}
					const { code, message } = await this.$API.fieldReservation.del.post(delData)
					if (code == 200) {
						ElMessage.success(message)
						// this.$refs.table.upData(this.params)
						this.$refs.table.refresh()
					} else {
						ElMessage.error(message)
					}
				})
				.catch(() => {})
		},
		async open(row, room_id) {
			this.$confirm(`是否远程打开预约场室？`, '提示', {
				type: 'warning'
			})
				.then(async () => {
					let data = {
						id: row.id,
						tenant_id: tenantId,
						campus_id: campusId,
						room_id: room_id
					}
					const { code, message } = await this.$API.fieldReservation.open.post(data)
					if (code === 200) {
						ElMessage.success(message)
						//this.$refs.table.upData(this.params)
					} else {
						ElMessage.error(message)
					}
				})
				.catch(() => {})
		}
	}
}
</script>

<style lang="scss" scoped></style>
