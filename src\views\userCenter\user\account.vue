<template>
	<el-card shadow="never" header="个人信息">
		<el-form ref="dialogForm" :model="userInfo" :rules="rules" label-width="120px" style="width: 600px">
			<el-form-item label="头像" prop="user_head">
				<sc-upload
					v-model="userInfo.user_head"
					:width="150"
					:height="150"
					round
					icon="el-icon-avatar"
					title="选择头像"
					:cropper="true"
					:compress="1"
					fileTypeTag="user_head"
					:aspectRatio="1 / 1"
				></sc-upload>
			</el-form-item>

			<el-form-item label="名称" prop="name">
				<el-input v-model="userInfo.name" placeholder="请输入名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="手机号" prop="phone">
				<el-input v-model="userInfo.phone" placeholder="请输入手机号" clearable></el-input>
			</el-form-item>
			<el-form-item label="工号" prop="serial_number">
				<el-input
					v-model="userInfo.serial_number"
					disabled
					placeholder="请输入工号,为空会自动生成"
					clearable
				></el-input>
			</el-form-item>

			<el-form-item label="性别" prop="sex">
				<el-select v-model="userInfo.sex" placeholder="请选择" filterable clearable>
					<el-option v-for="item in sexMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="身份证号" prop="idcard">
				<el-input v-model="userInfo.idcard" placeholder="请输入身份证号" clearable></el-input>
			</el-form-item>
			<el-form-item label="地址" prop="address">
				<el-input v-model="userInfo.address" placeholder="请输入地址" clearable></el-input>
			</el-form-item>

			<el-form-item label="邮箱" prop="email">
				<el-input v-model="userInfo.email" placeholder="请输入邮箱" clearable></el-input>
			</el-form-item>
			<el-form-item label="政治面貌" prop="political">
				<el-select v-model="userInfo.political" placeholder="请选择" filterable clearable>
					<el-option v-for="item in politicalMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="身份" prop="identity">
				<el-select v-model="userInfo.identity" placeholder="请选择" filterable clearable>
					<el-option v-for="item in identityMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="入职时间" prop="entry_time">
				<el-date-picker
					v-model="userInfo.entry_time"
					type="date"
					placeholder="请选择日期"
					format="YYYY-MM-DD"
					value-format="YYYY-MM-DD"
				/>
			</el-form-item>
			<el-form-item label="出生日期" prop="birthdate">
				<el-date-picker
					v-model="userInfo.birthdate"
					type="date"
					placeholder="请选择日期"
					format="YYYY-MM-DD"
					value-format="YYYY-MM-DD"
				/>
			</el-form-item>
			<el-form-item label="在职状态" disabled prop="work_status">
				<el-select v-model="userInfo.work_status" disabled placeholder="请选择" filterable clearable>
					<el-option v-for="item in workStatusMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="学历情况" prop="academic_type">
				<el-select v-model="userInfo.academic_type" placeholder="请选择" filterable clearable>
					<el-option
						v-for="item in academicTypeMap"
						:key="item.value"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="职工类别" prop="staff_type">
				<el-select v-model="userInfo.staff_type" disabled placeholder="" filterable clearable>
					<el-option v-for="item in staffTypeMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="职称" prop="professional_type">
				<el-select v-model="userInfo.professional_type" disabled placeholder="请选择" filterable clearable>
					<el-option
						v-for="item in professionalTypeMap"
						:key="item.value"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="备注" prop="remark">
				<el-input v-model="userInfo.remark" clearable type="textarea"></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" :loading="isSaveing" @click="saveUserInfo">保存</el-button>
			</el-form-item>
		</el-form>
	</el-card>
</template>

<script>
import cusTom from '@/utils/cusTom'

const {
	sexMap,
	politicalMap,
	identityMap,
	workStatusMap,
	staffTypeMap,
	academicTypeMap,
	professionalTypeMap,
	compileTypeMap
} = cusTom.getBaseQuery()
export default {
	data() {
		return {
			fileurl6: '',
			form: {
				id: null,
				tenant_id: null,
				campus_id: null
			},
			isSaveing: false,
			userInfo: {
				name: null,
				sex: null,
				birthday: null,
				political: null,
				identity: null,
				work_status: null,
				staff_type: null,
				compile_type: null,
				academic_type: null,
				professional_type: null,
				listorder: 1,
				remark: null,
				discipline_id: null,
				department_id: null,
				position_id: null,
				campus_id: null,
				tenant_id: null
			},
			rules: {
				name: [{ required: true, message: '请输入名称' }],
				phone: [{ required: true, message: '请输入手机号码' }]
			},
			sexMap,
			politicalMap,
			identityMap,
			workStatusMap,
			staffTypeMap,
			academicTypeMap,
			professionalTypeMap,
			compileTypeMap
		}
	},
	created() {
		let user = this.$TOOL.data.get('USER_INFO')
		this.form.tenant_id = user.tenant_id
		this.form.campus_id = user.campus_id
		this.form.id = user.userId
		//获取用户信息
		this.getUserInfo()
	},
	methods: {
		getUserInfo() {
			this.$API.personnel.staff.one.get(this.form).then((res) => {
				if (res.code === 200) {
					this.userInfo = res.data
					console.log(this.userInfo)
				} else {
					this.$message.warning(res.message)
				}
			})
		},
		saveUserInfo() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.personnel.staff.save.post(this.userInfo)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.$message.success('修改成功！')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		}
	}
}
</script>

<style></style>
