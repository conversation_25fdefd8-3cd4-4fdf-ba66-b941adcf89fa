import tool from '@/utils/tool'
import pinia from '@/stores/pinia.js'
import { useCommonStore } from '@/stores/common'
import common from '@/api/lot/common'
export default {
	//生成父子级嵌套数组
	arrayToTree(arr, id = 'id', parent_id = 'parent_id', children = 'children') {
		const map = {}
		const tree = []

		// 将数组中的每个元素转换为 { id, parent_id, children } 对象
		arr.forEach((item) => {
			map[item[id]] = { [children]: [], ...item }
		})
		// 将每个元素的 children 属性设置为它的子元素
		Object.values(map).forEach((item) => {
			if (item[parent_id] == 0 || item[parent_id] == undefined) {
				tree.push(item)
			} else {
				const parent = map[item[parent_id]]
				if (parent) {
					parent[children].push(item)
				}
			}
		})
		return tree
	},
	//将树还原成数组
	treeToArray(tree, id = 'id', parent_id = 'parent_id') {
		const result = []

		// 遍历树形结构，将每个节点的 children 属性转换为数组
		function traverseTree(node) {
			result.push({ ...node, [parent_id]: node[parent_id] })
			if (node.children) {
				node.children.forEach(traverseTree)
			}
		}

		tree.forEach(traverseTree)

		return result
	},
	//获取基础数据 默认学校 默认校区等
	getBaseQuery() {
		const commonData = tool.data.get('COMMON_DATA')
		const commonStore = useCommonStore(pinia)
		if (commonData) {
			const campusInfo = commonData.campusInfo
			const tenantInfo = commonData.tenantInfo
			let campusId = 0
			if (campusInfo) {
				campusId = campusInfo[0]?.value
			}
			const tenantId = tool.data.get('USER_INFO') && tool.data.get('USER_INFO').tenant_id
			const semesterInfo1 = commonData.semesterInfo
			let academicYearInfo = null
			let semesterInfo = null
			let academicYearId = 0
			let semesterId = 0
			if (semesterInfo1) {
				academicYearInfo = commonData.academicYearInfo
				semesterInfo = [...semesterInfo1, ...academicYearInfo]
				academicYearId = commonData.academicYearInfo[0] && commonData.academicYearInfo[0].value
				semesterId = commonData.semesterInfo.filter((v) => v.parent_id == academicYearId)[0].value
			}
			const fileSuffixTypeMap = commonData.fileSuffixTypeMap
			const paymentTypeMap = commonData.paymentTypeMap
			const formTypeMap = commonData.formTypeMap
			const approvalModelMap = commonData.approvalModelMap
			const studentVacateTypeMap = commonData.studentVacateTypeMap

			commonStore.SET_campus_id(campusId)
			commonStore.SET_tenant_id(tenantId)

			return {
				campusId, //校区默认Id
				tenantId, //学校默认Id
				semesterId, //学期默认Id
				academicYearId, //学年默认Id
				campusInfo, //校区 默认列表
				tenantInfo, //学校 默认列表
				academicYearInfo, // 学年默认列表
				semesterInfo1, //学期 默认列表
				semesterInfo, //学期 学年默认列表
				fileSuffixTypeMap, //文件类型
				academicTypeMap: commonData.academicTypeMap, //学历
				identityMap: commonData.identityMap, //身份
				politicalMap: commonData.politicalMap, //政治面貌
				professionalTypeMap: commonData.professionalTypeMap, //职称类型
				sexMap: commonData.sexMap, //性别
				workStatusMap: commonData.workStatusMap, //工作状态
				staffTypeMap: commonData.staffTypeMap, //教职工类型
				compileTypeMap: commonData.compileTypeMap, //编制类型
				roomTypeMap: commonData.roomTypeMap, //房间类型 场室
				roomCapacityMap: commonData.roomCapacityMap, //房间容纳人数 场室
				nationMap: commonData.nationMap, //民族
				leaveReasonMap: commonData.leaveReasonMap, //离职原因
				studentSourceMap: commonData.studentSourceMap, //学生来源
				studentStatusMap: commonData.studentStatusMap, //学生状态
				repairStatusMap: commonData.repairStatusMap, //报修状态
				repairTypeMap: commonData.repairTypeMap, //报修类型
				attendanceStatusMap: commonData.attendanceStatusMap, //考勤状态
				attendanceModeMap: commonData.attendanceModeMap, //考勤方式
				attendanceTypeMap: commonData.attendanceTypeMap, //考勤类型
				dormitoryEvaluateTypeMap: commonData.dormitoryEvaluateTypeMap, //宿舍评价类型
				paymentTypeMap, //缴费类型
				formTypeMap, // 表单类型
				approvalModelMap, //审批模块
				studentVacateTypeMap, //请假类型
				messageTypeMap: commonData.messageTypeMap, //消息类型
				certTypeMap: commonData.certTypeMap, //证书类型
				certObjectMap: commonData.certObjectMap, //证书对象
				certStatusMap: commonData.certStatusMap, //证书状态
				assetsStatusMap: [
					//资产状态
					{
						name: '空闲',
						value: 1
					},
					{
						name: '领用',
						value: 2
					},
					{
						name: '报废',
						value: 3
					}
				],
				assetsActionStatusMap: [
					//资产操作
					{
						name: '归还',
						value: 1
					},
					{
						name: '领用',
						value: 2
					},
					{
						name: '报废',
						value: 3
					}
				],
				assetslogMap: [
					{
						name: '资产入库',
						value: 1
					},
					{
						name: '领用',
						value: 2
					},
					{
						name: '报废',
						value: 3
					},
					{
						name: '归还',
						value: 4
					},
					{
						name: '返库',
						value: 5
					}
				],
				examineTypeMap: commonData.examineTypeMap //考试类型
			}
		} else {
			return false
		}
	},
	async getUnitConfig() {
		let unitConfig = tool.data.get('unitConfig')
		if (unitConfig) {
			return unitConfig
		} else {
			var res = await common.unitList.get()
			if (res.code === 200) {
				tool.data.set('unitConfig', res.data, 2 * 60 * 60)
				return res.data
			}
		}
	},
	async getEnumConfig() {
		let enumConfig = tool.data.get('enumConfig')
		if (enumConfig) {
			return enumConfig
		} else {
			var res = await common.enumList.get()
			if (res.code === 200) {
				tool.data.set('enumConfig', res.data, 2 * 60 * 60)
				return res.data
			}
		}
	},
	//去重
	uniqueByValue(arr, key) {
		let m = new Map()
		return arr.filter((ele) => !m.has(ele[key]) && m.set(ele[key], ''))
	}
}
