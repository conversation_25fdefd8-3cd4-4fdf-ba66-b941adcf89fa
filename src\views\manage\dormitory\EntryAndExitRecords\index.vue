<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
					<el-form-item label="" style="margin-left: 15px">
						<cusSelectSemester
							v-model="params.semester_id"
							:params="params"
							:show-default-value="true"
							:width="'214px'"
							clearable
							style="margin-right: 15px"
						/>
						<!--
						<cusCascader v-model="params.semester_id" placeholder="请选择学期" :options="getSemester"></cusCascader>
-->
					</el-form-item>
					<el-form-item>
						<el-cascader
							v-model="params.class_id"
							:options="classList"
							placeholder="请选择班级"
							:props="{ multiple: false, label: 'name', value: 'id', children: 'class_list' }"
							clearable
						></el-cascader>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<el-table-column label="记录类型" prop="entry_exit_type" width="100" fixed="left">
					<template #default="scope">
						<el-tag :type="['success', 'warning'][scope.row.entry_exit_type - 1]">
							{{ $formatDictionary(entryExitMap, scope.row.entry_exit_type) }}
						</el-tag>
					</template>
				</el-table-column>

				<el-table-column label="时间" prop="created_at" width="150"></el-table-column>
				<el-table-column label="考勤状态" prop="attendance_status" width="100">
					<template #default="scope">
						{{ $formatDictionary(attendanceStatusMap, scope.row.attendance_status) }}
					</template>
				</el-table-column>
				<el-table-column label="姓名" prop="user_name" width="110" fixed="left"></el-table-column>
				<el-table-column label="工号/学号" prop="serial_number" width="150"></el-table-column>
				<el-table-column label="头像" prop="user_head" width="100">
					<template #default="scope">
						<cusHead
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.user_head"
							:preview-src-list="[scope.row.user_head]"
							preview-teleported
						>
						</cusHead>
					</template>
				</el-table-column>
				<el-table-column label="用户类型" prop="user_type" width="100">
					<template #default="scope">
						{{ $formatDictionary(userTypeMap, scope.row.user_type) }}
					</template>
				</el-table-column>
				<el-table-column label="班级名称" prop="class_name" width="250">
					<template #default="scope"> {{ scope.row.grade_name }}{{ scope.row.class_name }} </template>
				</el-table-column>
				<el-table-column label="宿舍楼" prop="building_name" width="250"></el-table-column>
				<el-table-column label="楼层" prop="floor_name" width="180"></el-table-column>
				<el-table-column label="房间" prop="room_name" width="180"></el-table-column>
				<el-table-column label="备注" prop="remark"></el-table-column>
			</scTable>
		</el-main>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
import cusHead from '@/components/custom/cusStaffHead.vue'
import CusSelectSemester from '@/components/custom/cusSelectSemester.vue'
const { campusId, tenantId, campusInfo, tenantInfo, attendanceStatusMap, semesterInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null,
		grade_id: null,
		class_id: null,
		begin_time: null,
		end_time: null,
		date: [],
		name: null
	}
}
export default {
	name: 'dept',
	components: { CusSelectSemester, cusHead },
	data() {
		return {
			classList: [],
			dialog: {
				save: false
			},
			apiObj: this.$API.buildingRooms.rooms.record,
			selection: [],
			semesterInfo,
			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			entryExitMap: [
				{ name: '进', value: 1 },
				{ name: '出', value: 2 }
			],
			userTypeMap: [
				{ name: '学员', value: 1 },
				{ name: '教职工', value: 2 }
			],
			attendanceStatusMap,
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'date',
						value: null,
						component: 'cusDate',
						options: {
							placeholder: '请选择校区',
							type: 'daterange'
						}
					}
				]
			},
			grade: [],
			class: []
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.getClassData()
			},
			immediate: true
		},
		'params.semester_id': {
			handler(val) {
				console.log(val)
				// this.params.semester_id
				this.getClassData()
			}
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	async created() {},
	methods: {
		async getClassData() {
			let res = await this.$API.eduGradeClass.class.all.get(this.params)
			this.classList = res.data
		},
		//添加
		add(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', row)
			})
			// this.upsearch()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.buildingRooms.AccommodationArrangements.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},

		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		//搜索
		upsearch() {
			if (this.params.class_id != null && this.params.class_id.length > 1) {
				this.params.class_id = this.params.class_id[this.params.class_id.length - 1]
			}
			this.params.begin_time = this.params.date ? this.params.date[0] : null
			this.params.end_time = this.params.date ? this.params.date[1] : null
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped>
.roomItem {
	& + & {
		margin-top: 5px;
	}
}
</style>
