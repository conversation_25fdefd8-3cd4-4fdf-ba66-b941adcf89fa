<template>
	<el-container v-loading="saveLoading" element-loading-text="操作中">
		<el-container>
			<el-header style="border: none">
				<el-page-header style="width: 100%" @back="goBack">
					<template #content>
						<span class="text-large font-600 mr-3">{{ rulesData.rule_info?.rule_name }} </span>
					</template>
					<template #extra>
						<div style="display: flex; align-items: center; justify-content: space-between">
							<el-button @click="staging">暂存</el-button>
							<el-button type="primary" @click="save">保存</el-button>
						</div>
					</template>
				</el-page-header>
			</el-header>
			<el-main>
				<div class="class-type-content">
					<ul class="table th">
						<li class="row">
							<div class="row-data">
								<span class="cell">时间 / 日期</span>
								<span v-for="item in daysList" :key="item" class="cell">{{ item }}</span>
							</div>
						</li>
					</ul>
					<ul class="table td">
						<li v-for="item in rulesData.periods_list" class="row">
							<div v-if="item.value" class="row-data">
								<span class="cell">{{ item.item_name }}<br />{{ item.begin_time + '~' + item.end_time }}</span>
								<span
									v-for="items in item.value"
									class="cell"
									:class="[
										'bg' + items.classroom_type,
										{
											selected: items.rule_item_id == selectCellId
										}
									]"
									@click="tableCellClick(item, items)"
									>{{ formData(classType, items.classroom_type) }}</span
								>
							</div>
						</li>
					</ul>
				</div>
			</el-main>
		</el-container>
		<el-aside width="400px" style="border: none; margin-left: 10px">
			<el-container>
				<el-header style="border: none">
					<ul class="tab-list">
						<li v-for="item in tabList" :class="{ active: selectTab == item.value }" @click="tabSelect(item)">
							{{ item.label }}
						</li>
					</ul>
				</el-header>
				<el-main>
					<div class="class-type">
						<h3>课堂属性</h3>
						<ul class="class-type-list">
							<li v-for="item in classType" :class="getClassName(item)" @click="classTypeSelect(item)">
								{{ item.label }}
							</li>
						</ul>
					</div>
					<template v-if="classTypeSelectVal === 1">
						<div class="no-course">
							<div class="no-course-header">
								<div class="title">
									<h3>禁排课程</h3>
									<span>{{ noCourseList?.length || 0 }}</span>
								</div>
								<div>
									<el-button type="primary" size="small" @click="showCourse">选择</el-button>
									<el-button size="small" @click="clearCourse">清空</el-button>
								</div>
							</div>
							<div class="no-course-content">
								<div v-for="item in noCourseList" v-if="noCourseList?.length" :key="item.value" class="no-course-item">
									<el-tag closable @close="delCourseSelect(item)">{{ item.name }}</el-tag>
								</div>
								<el-empty v-else :image-size="100"></el-empty>
							</div>
						</div>
						<div class="no-teacher">
							<div class="no-teacher-header">
								<div class="title">
									<h3>禁排人员</h3>
									<span>{{ noTeacherList?.length || 0 }}</span>
								</div>
								<div>
									<el-button type="primary" size="small" @click="showTeacher">选择</el-button>
									<el-button size="small" @click="clearTeacher">清空</el-button>
								</div>
							</div>
							<div class="no-teacher-content">
								<div v-for="item in noTeacherList" v-if="noTeacherList?.length" :key="item.id" class="no-teacher-item">
									<el-tag closable @close="delTeacherSelect(item)">{{ item.name }}</el-tag>
								</div>
								<el-empty v-else :image-size="100"></el-empty>
							</div>
						</div>
					</template>
					<template v-else>
						<div class="is-adjust">
							<!-- <div class="is-adjust-title">
                                是否可调：
                            </div>
                            <div class="is-adjust-content">
                                <el-radio-group @change="isAdjustChange" v-model="isAdjust">
                                    <el-radio :label="1">是</el-radio>
                                    <el-radio :label="-1">否</el-radio>
                                </el-radio-group>
                            </div> -->
						</div>
					</template>
				</el-main>
			</el-container>
		</el-aside>
		<courseDialog ref="course" @success="courseSuccess"></courseDialog>

		<teacherDialog ref="teacher" @success="teacherSuccess"></teacherDialog>
	</el-container>
</template>
<script setup>
import courseDialog from './dialog/courseDialog.vue'
import teacherDialog from './dialog/teacherDialog.vue'
import { ref, getCurrentInstance, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, semesterInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const router = useRouter()
const course = ref(null)
const teacher = ref(null)
const rulesData = ref({})
const tabList = ref([
	{
		label: '排课属性',
		value: 'class'
	}
])
const selectTab = ref('class')
const saveLoading = ref(false)
const classType = ref([
	{
		label: '行政班',
		value: 1
	},
	// {
	//     label: '走班',
	//     value: 2
	// },
	{
		label: '班会',
		value: 3
	},
	{
		label: '自习',
		value: 4
	},
	{
		label: '活动',
		value: 5
	},
	{
		label: '劳动',
		value: 6
	}
])
const daysList = ref([])

//数据回显格式化
const formData = (arr, val) => {
	return arr.find((v) => v.value == val)?.label || val
}
// 转换函数
const transformArrays = (first, second) => {
	return first.map((firstItem) => {
		const resultItem = { ...firstItem }
		const matchedItems = second.filter((secondItem) => secondItem.periods_item_id === firstItem.id)
		resultItem.value = matchedItems

		return resultItem
	})
}
const selectCellId = ref(null)
const selectCellData = ref({})
const tableCellClick = (item, items) => {
	console.log(item, items, '点击单元格')
	if (selectCellId.value == items.rule_item_id) {
		selectCellId.value = null
		isAdjust.value = -1
		noCourseList.value = []
		noTeacherList.value = []
	} else {
		selectCellId.value = items.rule_item_id
		isAdjust.value = items.is_adjust
		noCourseList.value = items.forbid_course_list
		noTeacherList.value = items.forbid_user_list
	}
	console.log(selectCellId.value,rulesData.value.periods_list,'selectCellId')
	classTypeSelectVal.value = items.classroom_type
	selectCellData.value = items
}
const tabSelect = (item) => {
	selectTab.value = item.value
}
const classTypeSelectVal = ref(1)
const classTypeSelect = (item) => {
	classTypeSelectVal.value = item.value
	selectCellData.value.classroom_type = item.value
	updateData()
}
const isAdjust = ref(-1)
const isAdjustChange = (val) => {
	selectCellData.value.is_adjust = val
	updateData()
	console.log(val)
}

const updateData = () => {
	rulesData.value.periods_list.map((item) => {
		if (item.id === selectCellId.value) {
			item.value.push(selectCellData.value)
			item.value = cusTom.uniqueByValue(item.value, 'id')
		}
		return item
	})
	console.log(rulesData.value.periods_list)
}
// 课堂属性动态class名
const getClassName = (item) => {
	return item.value == classTypeSelectVal.value ? 'active-' + item.value : ''
}
// 禁排课程
const noCourseList = ref([])
const showCourse = () => {
	course.value.cleanCourseData()
	course.value.open(noCourseList.value)
}
const clearCourse = () => {
	noCourseList.value = []
	selectCellData.value.forbid_course = []
	selectCellData.value.forbid_course_list = []
	updateData()
}
const courseSuccess = (val) => {
	noCourseList.value = val
	selectCellData.value.forbid_course = val.map((item) => item.id)
	selectCellData.value.forbid_course_list = val
	updateData()
	console.log(val)
}
const delCourseSelect = (data) => {
	noCourseList.value = noCourseList.value.filter((item) => item.id != data.id)
	selectCellData.value.forbid_course = noCourseList.value.map((item) => item.id)
	updateData()
}
// 禁排人员
const noTeacherList = ref([])
const showTeacher = () => {
	teacher.value.showDialog(noTeacherList.value)
}
const clearTeacher = () => {
	noTeacherList.value = []
	selectCellData.value.forbid_user = []
	selectCellData.value.forbid_user_list = []
	teacher.value.cleanTeacherData()
	updateData()
}
const teacherSuccess = (val) => {
	noTeacherList.value = val
	selectCellData.value.forbid_user = val.map((item) => item.id)
	selectCellData.value.forbid_user_list = val
	updateData()
	console.log(val)
}
const delTeacherSelect = (data) => {
	noTeacherList.value = noTeacherList.value.filter((item) => item.id != data.id)
	selectCellData.value.forbid_user = noTeacherList.value.map((item) => item.id)
	updateData()
}
const ruleId = ref(null)
onMounted(() => {
	ruleId.value = Number(router.currentRoute.value.query.id)
	getData()
})
const goBack = () => {
	router.go(-1)
}
const staging = () => {
	rulesSet(1)
}
const save = () => {
	rulesSet(2)
}

const rulesSet = (type) => {
	let params = {
		rule_id: ruleId.value,
		tenant_id: tenantId,
		campus_id: campusId,
		status: type,
		attr: rulesData.value.periods_list.reduce((accumulator, currentValue) => {
			return accumulator.concat(currentValue.value)
		}, [])
	}
	saveLoading.value=true
	globalPropValue.eduSchedule.rules.set.post(params).then((res) => {
		if (res.code === 200) {
			setTimeout(() => {
				saveLoading.value=false
				if (type === 1) {
					ElMessage.success('暂存成功')
				} else {
					ElMessage.success('保存成功')
				}
				getData()
			}, 1000)
		} else {
			saveLoading.value=false
			ElMessage.error(res.msg)
		}
	})
}
const getData = () => {
	globalPropValue.eduSchedule.rules.detail
		.get({
			id: ruleId.value,
			tenant_id: tenantId,
			campus_id: campusId
		})
		.then((res) => {
			rulesData.value = res.data
			daysList.value = res.data?.rule_info.attend_day.split(',')
			rulesData.value.periods_list = transformArrays(res.data.periods_list, res.data.attr)
		})
}
</script>
<style lang="scss" scoped>
.bg1 {
	color: var(--el-color-primary);

	&:hover {
		background-color: var(--el-color-primary-light-8);
	}
}

.bg3 {
	color: var(--el-color-success);

	&:hover {
		background-color: var(--el-color-success-light-8);
	}
}

.bg4 {
	color: var(--el-color-warning);

	&:hover {
		background-color: var(--el-color-warning-light-8);
	}
}

.bg5 {
	color: var(--el-color-info);

	&:hover {
		background-color: var(--el-color-info-light-8);
	}
}

.bg6 {
	color: var(--el-color-danger);

	&:hover {
		background-color: var(--el-color-danger-light-8);
	}
}

.class-type-content {
	width: 100%;
	display: flex;
	flex-direction: column;

	// border: 1px solid var(--el-border-color-light);
	.table {
		width: 100%;
		display: flex;

		.row {
			border-left: 1px solid var(--el-border-color-light);
			border-right: 1px solid var(--el-border-color-light);
			border-bottom: 1px solid var(--el-border-color-light);

			.row-data {
				display: flex;
				text-align: center;

				.bg1.selected {
					color: var(--el-color-primary);
					background-color: var(--el-color-primary-light-8);
				}

				.bg3.selected {
					color: var(--el-color-success);
					background-color: var(--el-color-success-light-8);
				}

				.bg4.selected {
					color: var(--el-color-warning);
					background-color: var(--el-color-warning-light-8);
				}

				.bg5.selected {
					color: var(--el-color-info);
					background-color: var(--el-color-info-light-8);
				}

				.bg6.selected {
					color: var(--el-color-danger);
					background-color: var(--el-color-danger-light-8);
				}
			}

			.cell {
				flex: 1;
				text-align: center;
				min-width: 100px;
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				border-right: 1px solid var(--el-border-color-light);

				&:first-child {
					flex: 0;
					min-width: 90px;
				}

				&:last-child {
					border-right: none;
				}
			}
		}
	}

	.th {
		.row {
			width: 100%;
			border-top: 1px solid var(--el-border-color-light);
			background-color: var(--el-border-color-extra-light);
		}

		.row-data {
			width: 100%;
			height: 50px;
		}
	}

	.td {
		flex-direction: column;

		.row {
			.row-data {
				height: 70px;
				.cell {
					&:first-child {
						background-color: var(--el-border-color-extra-light);
					}
				}
			}
		}
	}
}

.tab-list {
	display: flex;
	justify-content: center;
	height: 32px;
	width: 100%;

	li {
		height: 100%;
		line-height: 32px;
		font-size: 14px;
	}

	.active {
		color: var(--el-color-primary);
		border-bottom: 2px solid var(--el-color-primary);
	}
}

.class-type {
	margin-bottom: 20px;

	h3 {
		font-size: 16px;
		margin-bottom: 10px;
	}

	.class-type-list {
		display: flex;
		flex-wrap: wrap;

		li {
			font-size: 14px;
			width: 60px;
			height: 30px;
			line-height: 30px;
			text-align: center;
			// padding: 5px 10px;
			background-color: var(--el-color-info-light-9);
			border-radius: 5px;
			margin-right: 5px;
			margin-bottom: 5px;
			cursor: pointer;
		}

		.active-1 {
			color: var(--el-color-primary);
			background-color: var(--el-color-primary-light-8);
		}

		.active-3 {
			color: var(--el-color-success);
			background-color: var(--el-color-success-light-8);
		}

		.active-4 {
			color: var(--el-color-warning);
			background-color: var(--el-color-warning-light-8);
		}

		.active-5 {
			color: var(--el-color-info);
			background-color: var(--el-color-info-light-8);
		}

		.active-6 {
			color: var(--el-color-danger);
			background-color: var(--el-color-danger-light-8);
		}
	}
}

.no-course,
.no-teacher {
	.no-course-header,
	.no-teacher-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;

		.title {
			display: flex;
			align-items: center;

			span {
				font-size: 14px;
				line-height: 14px;
				text-align: center;
				display: block;
				width: 24px;
				height: 24px;
				padding: 5px;
				border-radius: 50%;
				color: var(--el-color-info);
				background-color: var(--el-color-info-light-8);
			}
		}

		h3 {
			font-size: 16px;
			margin-right: 5px;
		}
	}

	.no-course-content,
	.no-teacher-content {
		min-height: 150px;
		width: 100%;

		.no-course-item,
		.no-teacher-item {
			height: 25px;
			display: inline-block;
			margin-right: 5px;
			margin-bottom: 5px;
		}
	}
}
</style>
