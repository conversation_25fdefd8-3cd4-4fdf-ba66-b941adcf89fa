<template>
	<el-main>
		<el-container>
			<el-header>
				<div class="left-panel">
					<el-select
						v-if="CampusManagementList.length > 1"
						v-model="params.campus_id"
						placeholder="校区"
						filterable
						@change="upsearch"
					>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
					<el-input
						v-model="params.name"
						placeholder="耗材名称或耗材编号"
						style="width: 200px; margin-left: 10px"
						@input="upsearch"
					></el-input>
				</div>
			</el-header>
			<el-main>
				<scTable ref="table" row-key="id" stripe :apiObj="list.apiObj" :params="params">
					<el-table-column label="耗材名称" prop="consumable_name" show-overflow-tooltip></el-table-column>
					<el-table-column label="耗材编号" prop="serial_number" show-overflow-tooltip></el-table-column>
					<el-table-column label="耗材类型" prop="type_name" width="200"></el-table-column>
					<el-table-column label="操作类型" prop="log_type" width="150">
						<template #default="scope">
							<el-tag v-if="scope.row.log_type === 1" type="success">入库</el-tag>
							<el-tag v-if="scope.row.log_type === 2" type="warning">出库</el-tag>
							<el-tag v-if="scope.row.log_type === 3" type="danger">退库</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="操作数量" prop="action_num" width="120"></el-table-column>
					<el-table-column label="操作时间" prop="created_at" width="180"></el-table-column>
					<el-table-column label="领用人" prop="log_user_name" width="150"></el-table-column>
					<el-table-column label="操作人" prop="action_user_name" width="150"></el-table-column>
					<el-table-column label="备注" prop="log_remark" show-overflow-tooltip></el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-main>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, roomTypeMap, roomCapacityMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		begin_time: null,
		end_time: null,
		date: []
	}
}

export default {
	name: 'record',
	data() {
		return {
			list: {
				apiObj: this.$API.consumables.myConsumable
			},
			params: defaultParams(),
			CampusManagementList: campusInfo,
			roomNameFilters: [],
			roomTypeMap,
			roomCapacityMap
		}
	},
	computed: {},
	created() {},
	methods: {
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		upsearch() {
			this.$refs.table.upData(this.search)
		},
		dataChange(val) {
			console.log(val, 'dataChange')
			if (val) {
				this.params.begin_time = val[0]
				this.params.end_time = val[1]
			} else {
				this.params.begin_time = null
				this.params.end_time = null
			}
			this.$refs.table.upData(this.params)
		},
	}
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;

	.el-button {
		width: 80%;
	}
}

.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;
	box-sizing: border-box;

	a {
		font-size: 18px;
	}
}
</style>
