<template>
	<el-dialog v-model="dialogFormVisible" :title="actionType == 1 ? '通过' : '驳回'" width="500">
		<cusForm ref="formref" v-model="form" :config="formConfig"></cusForm>
		<template #footer>
			<el-button @click="dialogFormVisible = false">取消</el-button>
			<el-button type="primary" @click="confirm">确定</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	name: 'actionDialog',
	data() {
		return {
			actionType: '',
			dialogFormVisible: false,
			form: {
				apply_id: null,
				step: null,
				remark: null,
				files: null
			},
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: '审批意见',
						name: 'remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入审批意见',
							items: []
						},
						rules: [{ required: true, message: '请输入审批意见', trigger: 'blur' }]
					},
					{
						label: '附件',
						name: 'files',
						value: null,
						component: 'upload',
						options: {
							items: [
								{
									name: 'file',
									label: '附件'
								}
							]
						}
					}
				]
			}
		}
	},
	methods: {
		open(type, applyId, step) {
			console.log(applyId)
			this.form.apply_id = applyId
			this.form.step = step
			this.actionType = type
			this.dialogFormVisible = true
		},
		confirm() {
			this.$refs.formref.validate((valid) => {
				if (valid) {
					this.$emit('success', { ...this.form, action: this.actionType })
					this.dialogFormVisible = false
					this.$refs.formref.resetFields()
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped></style>
