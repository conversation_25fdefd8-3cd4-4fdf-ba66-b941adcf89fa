<template>
	<el-dialog v-if="dialogFormVisible" v-model="dialogFormVisible" :title="titleMap[mode1]" width="500">
		<cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode1"></cusForm>

		<template #footer>
			<el-button @click="dialogFormVisible = false">取消</el-button>
			<el-button type="primary" @click="confirm">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance, nextTick, onMounted } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
import { set } from 'nprogress';
import { deepCopy } from 'ali-oss/lib/common/utils/deepCopy'
const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params', 'order_items'])
const emit = defineEmits(['success'])
let mode1 = ref('add')
const dialogFormVisible = ref(false)
const defaultData = () => {
	return {
		id: null,
		order_name: '',
		class_ids: [],
		remark: '',
		semester_id: null,
		academic_id: null,
		tenant_id: tenantId,
		campus_id: campusId,
		mode: null
	}
}
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
	}
}
let params = ref(defaultParams())
let form = ref(defaultData())
let formConfig = ref({
	labelWidth: '100px',
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: '缴费单名称',
			name: 'order_name',
			value: null,
			component: 'input',
			options: {
				placeholder: '请输入缴费单名称',
				items: []
			},
			rules: [{ required: true, message: '请输入缴费单名称', trigger: 'blur' }]
		},
		{
			label: '学年',
			name: 'academic_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学年',
				items: semesterInfo.map((v) => {
					return {
						label: v.name,
						value: v.value
					}
				})
			},
			rules: [{ required: true, message: '请选择学年', trigger: 'blur' }]
		},
		{
			label: '学期',
			name: 'semester_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学期',
				items: []
			},
			rules: [{ required: true, message: '请选择学期', trigger: 'blur' }]
		},
		// {
		// 	label: '年级',
		// 	name: 'grade_id',
		// 	value: null,
		// 	component: 'select',
		// 	options: {
		// 		placeholder: '请选择年级',
		// 		items: []
		// 	},
		// 	rules: [{ required: true, message: '请选择年级', trigger: 'blur' }]
		// },
		{
			label: '关联班级',
			name: 'class_ids',
			value: null,
			component: 'cascader',
			options: {
				placeholder: '请选择班级',
				items: [],
				prop: { multiple: true },
				collapseTags: true,
				collapseTagsTooltip: true
			},
			rules: [{ required: true, message: '请选择年级', trigger: 'blur' }]
		},
		{
			label: '备注',
			name: 'remark',
			value: null,
			component: 'textarea',
			options: {
				placeholder: '请输入备注',
				items: []
			},
		}
	]
})
let formref = ref(null)
let titleMap = ref({ add: '新增', edit: '编辑' })
onMounted(async () => {
	formConfig.value.formItems.find((v) => v.name === 'class_ids').options.items = await getClassList()
})
// 获取年级列表
const getGradeClassList = async (id) => {
	const { data } = await globalPropValue.eduGradeClass.grade.all.get({ tenant_id: tenantId, campus_id: campusId, semester_id: id })
	return data.map((v) => {
		return {
			label: v.grade_name,
			value: v.id
		}
	})
}
// 获取班级列表
const getClassList = async () => {
	const { code, data } = await globalPropValue.eduGradeClass.class.all.get({ tenant_id: tenantId, campus_id: campusId })
	if (code == 200) {
		return data.map((v) => {
			return {
				label: v.name,
				value: v.id,
				children: v.class_list.map((v) => {
					return {
						label: v.name,
						value: v.id
					}
				})
			}
		})
	} else {
		return []
	}
}
const open = async (mode = 'add') => {
	dialogFormVisible.value = true
	mode1.value = mode
	form.value.mode = mode
	if (mode === 'add') {
		form.value = defaultData()
		setTimeout(() => {
			formref.value.resetFields()
			form.value.campus_id = props.params.campus_id
			form.value.tenant_id = props.params.tenant_id
		}, 0)
	}
	if (mode === 'edit') {
		nextTick(() => {
			formref.value.resetFields()
		})
	}
}
const confirm = async () => {
	await formref.value.validate()
	let subForm = { ...form.value }
	if (mode1.value === 'add') {
		subForm = { ...form.value, ...params.value, class_ids: form.value.class_ids.map((item) => String(item[1])).join(','), order_items: props.order_items.join(',') }
		const res = await globalPropValue.finance.recruit.orderAdd.post(subForm)
		if (res.code === 200) {
			emit('success', form.value, mode1.value)
			dialogFormVisible.value = false
			ElMessage({ type: 'success', message: '操作成功' })
		} else {
			ElMessage({ type: 'error', message: res.message })
		}
	} else if (mode1.value === 'edit') {
		console.log(form.value.class_ids)
		if (isArrayMultiDimensional(form.value.class_ids)) {
			form.value.class_ids = form.value.class_ids.map((item) => String(item[1])).join(',')
		} else {
			form.value.class_ids = form.value.class_ids.join(',')
		}
		subForm = { ...form.value, ...params.value, class_ids: form.value.class_ids }
		const res = await globalPropValue.finance.recruit.orderEdit.post(subForm)
		if (res.code === 200) {
			emit('success', form.value, mode1.value)
			dialogFormVisible.value = false
			ElMessage({ type: 'success', message: '操作成功' })
		} else {
			ElMessage({ type: 'error', message: res.message })
		}
	}


}
const isArrayMultiDimensional = (arr) => {
	if (!Array.isArray(arr)) {
		return false;
	}

	for (let i = 0; i < arr.length; i++) {
		if (Array.isArray(arr[i])) {
			return true; // 数组中有元素是数组，则为多维数组
		}
	}

	return false; // 数组中所有元素都不是数组，则为普通数组
}


watch(
	() => form.value.campus_id,
	(val) => {
		console.log(val)
		// form.value.academic_id = null
		formConfig.value.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
			.filter((v) => v.parent_id === 0 && v.campus_id === val)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
watch(
	() => form.value.academic_id,
	(val) => {
		console.log(val)
		// form.value.semester_id = null
		formConfig.value.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
			.filter(
				(v) => v.parent_id !== 0 && v.parent_id === form.value.academic_id && v.campus_id === form.value.campus_id
			)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
// )
//表单注入数据
const setData = (data) => {
	Object.assign(form.value, data)

	// data.class_ids = data.class_list.map(v => v.class_id)
	form.value.class_ids = form.value.class_ids.split(',').map(v => Number(v))
	console.log(form.value)
	// form.value = { ...data }
}

defineExpose({
	dialogFormVisible,
	open,
	setData
})
</script>

<style lang="scss" scoped></style>
