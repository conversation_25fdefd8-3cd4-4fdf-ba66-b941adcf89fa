<template>
	<div>
		<ul class="myMods">
			<li v-for="mod in myMods" :key="mod.path">
				<a v-if="mod.meta.type === 'link'" :href="mod.path" target="_blank">
					<el-icon :style="{ color: mod.meta.color }">
						<component :is="mod.meta.icon || 'el-icon-menu'" />
					</el-icon>
					<p :style="{ color: mod.meta.color }">{{ mod.meta.title }}</p>
				</a>
				<router-link v-else :to="{ path: mod.path }">
					<el-icon :style="{ color: mod.meta.color }">
						<component :is="mod.meta.icon || 'el-icon-menu'" />
					</el-icon>
					<p :style="{ color: mod.meta.color }">{{ mod.meta.title }}</p>
				</router-link>
			</li>
			<li class="modItem-add" @click="addMods">
				<a href="javascript:void(0)">
					<el-icon>
						<el-icon-plus />
					</el-icon>
				</a>
			</li>
		</ul>

		<el-drawer v-model="modsDrawer" title="添加应用" size="50%" destroy-on-close>
			<div class="setMods">
				<h4>我的常用 ( {{ myModsCopy.length }} )</h4>
				<draggable v-model="myModsCopy" tag="ul" animation="200" item-key="path" group="people">
					<template #item="{ element }">
						<li>
							<el-icon :style="{ color: element.meta.color}">
								<component :is="element.meta.icon || 'el-icon-menu'" />
							</el-icon>
							<p :style="{ color: element.meta.color }">{{ element.meta.title }}</p>
						</li>
					</template>
				</draggable>
			</div>
			<div class="setMods">
				<h4>全部应用 ( {{ filterMods.length }} )</h4>
				<draggable v-model="filterMods" tag="ul" animation="200" item-key="path" :sort="false" group="people" @end="draggableEnd">
					<template #item="{ element }">
						<li>
							<el-icon :style="{ color: element.meta.color }">
								<component :is="element.meta.icon || 'el-icon-menu'" />
							</el-icon>
							<p :style="{ color: element.meta.color }">{{ element.meta.title }}</p>
						</li>
					</template>
				</draggable>
			</div>
			<template #footer>
				<el-button @click="modsDrawer = false">取消</el-button>
				<el-button type="primary" @click="saveMods">保存</el-button>
			</template>
		</el-drawer>
	</div>
</template>

<script>
import draggable from 'vuedraggable'

export default {
	components: {
		draggable
	},
	data() {
		return {
			mods: [],
			myMods: [],
			myModsCopy: [],
			myModsName: [],
			filterMods: [],
			modsDrawer: false
		}
	},
	mounted() {
		this.getMods()
	},
	methods: {
		draggableEnd(){
			if (this.myModsCopy.length>9){
				this.$message.warning('最多添加9个应用')
				this.myModsCopy=this.myModsCopy.slice(0,9)
			}
		},
		addMods() {
			this.myModsCopy = JSON.parse(JSON.stringify(this.myMods))
			this.modsDrawer = true
		},
		getMods() {
			//这里可用改为读取远程数据
			this.myModsName = this.$TOOL.data.get('my-mods') || []
			var menuTree = this.$TOOL.data.get('MENU')
			this.filterMenu(menuTree)
			this.myMods = this.mods.filter((item) => {
				return this.myModsName.includes(item.name)
			}).sort((a, b) => {
				// 根据 myModsName 的顺序排序
				return this.myModsName.indexOf(a.name) - this.myModsName.indexOf(b.name);
			});
			console.log(this.myMods)

			this.filterMods = this.mods.filter((item) => {
				return !this.myModsName.includes(item.name)
			})
		},
		filterMenu(map) {
			map.forEach((item) => {
				if (item.meta.hidden || item.meta.type === 'button') {
					return false
				}
				if (item.meta.type === 'iframe') {
					item.path = `/i/${item.name}`
				}
				if (item.children && item.children.length > 0) {
					this.filterMenu(item.children)
				} else {
					this.mods.push(item)
				}
			})
		},
		async saveMods() {
			const myModsName = this.myModsCopy.map((v) => v.name)
			console.log(myModsName)
			var res = await this.$API.user.saveMods.post({
				mods: JSON.stringify(myModsName)
			})
			if (res.code === 200) {
				this.$TOOL.data.set('my-mods', myModsName)
				this.$message.success('设置常用成功')
				this.modsDrawer = false
				this.mods=[]
				this.getMods()
			} else {
				this.$message.warning(res.message)
			}
		}
	}
}
</script>

<style scoped>
.myMods {
	min-height: 240px;
	list-style: none;
	margin: -10px;
}

.myMods li {
	display: inline-block;
	width: 100px;
	height: 100px;
	vertical-align: top;
	transition: all 0.3s ease;
	margin: 10px;
	border-radius: 10px;
}

.myMods li a {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	padding-top: 5px;
	border-radius: 10px;
}

.myMods li i {
	width: 45px;
	height: 45px;
	margin: 10px 10px 5px 10px;
	background: #fff;
	border-radius: 50%;
	font-size: 26px;
}

.myMods li p {
	font-size: 14px;
	width: 90%;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.myMods li:hover p {
	overflow: visible;
}

.modItem-add {
	border: 1px dashed #ddd;
	cursor: pointer;
}

.modItem-add i {
	margin-top: 20px !important;
	font-size: 30px;
	color: #999 !important;
}

.modItem-add:hover,
.modItem-add:hover i {
	border-color: #409eff;
	color: #409eff !important;
}

.setMods {
	padding: 0 20px;
}

.setMods h4 {
	font-size: 14px;
	font-weight: normal;
}

.setMods ul {
	margin: 20px -5px;
	min-height: 90px;
}

.setMods li {
	display: inline-block;
	width: 80px;
	height: 80px;
	text-align: center;
	margin: 5px;
	vertical-align: top;
	cursor: move;
	border-radius: 8px;
}

.setMods li i {
	width: 40px;
	height: 40px;
	margin: 10px 10px 5px 10px ;
	background: #fff;
	border-radius: 50%;
	font-size: 20px;
}

.setMods li p {
	font-size: 12px;
}

.setMods li.sortable-ghost {
	opacity: 0.3;
}
</style>
