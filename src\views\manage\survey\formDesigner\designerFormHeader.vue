<template>
    <el-header class="btn-bar">
        <!-- <el-button v-if="$attrs.uploadJson" type="text" @click="$emit('uploadJson')">
            <template #icon>
                <cusSvgIcon iconClass="upload" />
            </template>
            导入JSON
        </el-button> -->
        <el-button v-if="$attrs.clearable" type="text" @click="$emit('clearable')">
            <template #icon>
                <cusSvgIcon iconClass="clearable" />
            </template>
            清空
        </el-button>
        <el-button v-if="$attrs.preview" type="text" @click="$emit('preview')">
            <template #icon>
                <cusSvgIcon iconClass="preview" />
            </template>
            预览
        </el-button>
        <!-- <el-button v-if="$attrs.generateJson" type="text" @click="$emit('generateJson')">
            <template #icon>
                <cusSvgIcon iconClass="generate-json" />
            </template>
            生成JSON
        </el-button>
        <el-button v-if="$attrs.generateCode" type="text" @click="$emit('generateCode')">
            <template #icon>
                <cusSvgIcon iconClass="generate-code" />
            </template>
            生成代码
        </el-button> -->
        <slot></slot>
    </el-header>
</template>
<script>
import { defineComponent } from 'vue'

export default defineComponent({
    name: 'DesignerFormHeader',
    components: {
       
    },
    emits: ['uploadJson', 'clearable', 'preview', 'generateJson', 'generateCode']
})
</script>
<style lang="scss" scoped>
.btn-bar {
    text-align: right;
    height: 45px !important;
    line-height: 45px;
    font-size: 18px;
    border-bottom: 2px solid #e4e7ed;
    text-align: right;
    background-color: #fff;
    padding: 0 10px;
    display: block;
}
</style>