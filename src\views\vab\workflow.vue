<template>
	<el-container>
		<el-header>
			<el-page-header :content="data.name" @back="goBack"></el-page-header>
			<div class="do">
				<el-button type="primary" @click="exportJson">保存</el-button>
			</div>
		</el-header>
		<el-main>
			<el-alert :closable="false" title="规则:" type="warning" show-icon>
				<p>1.条件路由组只允许存在一组且只能在第一个节点判断</p>
				<p>2.抄送节点只允许存在一个且建议为最后一个节点</p>
			</el-alert>
			<sc-workflow v-model="data.nodeConfig"></sc-workflow>
		</el-main>
	</el-container>
</template>

<script>
import scWorkflow from '@/components/scWorkflow'
import cusTom from '@/utils/cusTom'
import html2canvas from 'html2canvas';
import { putObjectBig } from '@/utils/ossLib'//上传文件到oss
const { campusId, tenantId } = cusTom.getBaseQuery()

export default {
	name: 'workflow',
	components: {
		scWorkflow
	},

	data() {
		return {
			data: {}
		}
	},
	created() {
		this.getWorkflow()
	},
	mounted() { },
	methods: {
		exportJson() {
			// this.$message('返回值请查看F12控制台console.log()')
			// console.log(JSON.stringify(this.data.nodeConfig))
			let dom = document.querySelector('.box-scale')
			let that = this
			html2canvas(dom).then(function (canvas) {
				const img = canvas.toDataURL('image/png');
				var blob = that.base64ToBlob(img.split(',')[1], 'image/png');
				that.upImg(blob)
			})
		},
		async upImg(img) {
			const data = await putObjectBig(img, '.png','workflow')
			if(data){
				this.submitWorkflow(data.url)
			}
		},
		async submitWorkflow(img) {
			const { code, message } = await this.$API.approval.deploy.set.post({
				id: this.data.id,
				approval_conf: JSON.stringify(this.data.nodeConfig),
				flow_img: img,
				campus_id: campusId,
				tenant_id: tenantId
			})
			if (code === 200) {
				this.$message({ type: 'success', message: message })
				this.goBack()
			} else {
				this.$message({ type: 'error', message: message })
			}
		},
		goBack() {
			this.$router.go(-1)
		},
		async getWorkflow() {
			const { data } = await this.$API.approval.deploy.detail.get({ id: this.$route.query.id, campus_id: campusId, tenant_id: tenantId })
			this.data = {
				id: data.id,
				name: data.approval_name,
				nodeConfig: JSON.parse(data.approval_conf)
			}
		},
		base64ToBlob(base64, mimeType) {
			const byteCharacters = atob(base64);
			const byteArrays = [];
			for (let offset = 0; offset < byteCharacters.length; offset += 512) {
				const slice = byteCharacters.slice(offset, offset + 512);
				const byteNumbers = new Array(slice.length);
				for (let i = 0; i < slice.length; i++) {
					byteNumbers[i] = slice.charCodeAt(i);
				}
				const byteArray = new Uint8Array(byteNumbers);
				byteArrays.push(byteArray);
			}
			return new Blob(byteArrays, { type: mimeType });
		}
	}
}
</script>

<style></style>
