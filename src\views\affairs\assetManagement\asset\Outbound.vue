<template>
	<el-dialog v-model="visible" title="资产流转" :width="500" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :disabled="mode == 'show'" :rules="rules">
			<el-form-item label="流转操作" prop="type">
				<el-radio-group v-model="type">
					<el-radio-button label="领用" />
					<el-radio-button label="归还" />
					<el-radio-button label="报废" />
				</el-radio-group>
			</el-form-item>
			<el-form-item :label="'资产名称'" prop="assets_idObj">
				<cusSelectasset v-model="form.assets_idObj" :multiple="false"></cusSelectasset>
			</el-form-item>
			<el-form-item v-if="type == '领用'" :label="type + '人'" prop="action_userObj">
				<cusSelectTeacher v-model="form.action_userObj"></cusSelectTeacher>
			</el-form-item>
			<el-form-item :label="type + '时间'" prop="action_time">
				<el-date-picker
					v-model="form.action_time"
					type="datetime"
					placeholder="请选择日期"
					format="YYYY-MM-DD"
					value-format="YYYY-MM-DD"
				/>
			</el-form-item>

			<el-form-item :label="type + '备注'" prop="action_remark">
				<el-input
					v-model="form.action_remark"
					placeholder="请输入"
					clearable
					type="textarea"
					:autosize="{ minRows: 4, maxRows: 6 }"
				></el-input>
			</el-form-item>
			<el-form-item :label="'位置'" prop="room_idObj">
				<cusSelectField v-model="form.room_idObj" :multiple="false"></cusSelectField>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
const defaultData = () => {
	return {
		tenant_id: '',
		campus_id: '',
		action_user: null,
		action_userObj: [],
		action_time: null,

		action_remark: null,
		action_type: null,
		assets_id: null,
		assets_idObj: [],
		room_idObj: [],
		room_id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	watch: {
		type: {
			handler() {
				this.form.action_userObj = []
				this.form.action_time = null
				this.form.action_remark = null
				this.form.room_idObj = []
			}
		}
	},
	data() {
		return {
			mode: 'add',

			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			typeMap: {
				out: '耗材出库',
				refund: '耗材退库'
			},
			mMap: {
				out: '领用',
				refund: '退库'
			},
			//验证规则
			rules: {
				action_userObj: [{ required: true, message: '请输入' }],
				action_time: [{ required: true, message: '请输入' }],
				action_num: [{ required: true, message: '请输入' }],
				action_remark: [{ required: true, message: '请输入' }],
				assets_idObj: [{ required: true, message: '请选择' }],
				room_idObj: [{ required: true, message: '请选择' }]
			},
			goodName: '',
			type: '领用'
			//所需数据选项
		}
	},
	mounted() {},
	methods: {
		//添加楼层
		addFloor() {
			this.form.name.push({ value: '', key: Date.now() })
		},
		//删除楼层
		removeFloor(index) {
			this.form.name.splice(index, 1)
		},
		//显示
		open(mode, type = 1, parent_id) {
			this.mode = mode
			this.visible = true
			this.form.campus_id = this.params.campus_id
			this.form.tenant_id = this.params.tenant_id
			if (mode == 'out') {
				this.action_type = 2
			}
			if (mode == 'refund') {
				this.action_type = 3
			}
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					if (this.form.action_userObj.length) {
						this.form.action_user = this.form.action_userObj[0].id
					}
					if (this.form.assets_idObj.length) {
						this.form.assets_id = this.form.assets_idObj[0].id
					}
					if (this.form.room_idObj.length) {
						this.form.action_room_id = this.form.room_idObj[0].id
					}
					if (this.type == '领用') {
						this.form.action_type = 2
					}
					if (this.type == '归还') {
						this.form.action_type = 1
					}
					if (this.type == '报废') {
						this.form.action_type = 3
					}
					var res = await this.$API.assets.rooms.action.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
