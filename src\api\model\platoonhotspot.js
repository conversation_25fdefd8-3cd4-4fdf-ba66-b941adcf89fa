import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/manapi/platoonhotspot/list`,
		name: '获取班牌热点信息列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	info: {
		url: `${config.API_URL}/manapi/platoonhotspot/info`,
		name: '获取单个班牌热点信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	add: {
		url: `${config.API_URL}/manapi/platoonhotspot/save`,
		name: '新增班牌热点信息',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	del: {
		url: `${config.API_URL}/manapi/platoonhotspot/del`,
		name: '删除班牌热点信息',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	get_conf: {
		url: `${config.API_URL}/manapi/platoonhotspot/get_conf`,
		name: '班牌热点信息配置',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save_conf: {
		url: `${config.API_URL}/manapi/platoonhotspot/save_conf`,
		name: '更新班牌热点信息配置',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	// 模式信息-------------
	// 开放日模式信息
	get_open_mode: {
		url: `${config.API_URL}/manapi/platoonSchoolInformation/info`,
		name: '获取开放日模式信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	//开放日模式-保存学校信息
	save_open_mode: {
		url: `${config.API_URL}/manapi/platoonSchoolInformation/save`,
		name: '更新开放日模式-保存学校信息',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},

	//	开放日模式-删除学校信息
	del_open_mode: {
		url: `${config.API_URL}/manapi/platoonSchoolInformation/del`,
		name: '删除开放日模式-删除学校信息',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	// 开放日模式-新增关联热点信息
	save_related: {
		url: `${config.API_URL}/manapi/platoonSchoolInformation/save_related`,
		name: '新增关联热点信息',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	//  开放日模式-删除关联热点信息
	del_related: {
		url: `${config.API_URL}/manapi/platoonSchoolInformation/del_related`,
		name: '删除关联热点信息',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	// 霸屏模式信息-------------
	// 获取霸屏模式信息
	get_screen_mode: {
		url: `${config.API_URL}/manapi/platoonTopScreenMode/info`,
		name: '获取霸屏模式信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	// 霸屏模式信息-新增
	save_screen_mode: {
		url: `${config.API_URL}/manapi/platoonTopScreenMode/save`,
		name: '新增霸屏模式信息',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	//  霸屏模式信息-删除
	del_screen_mode: {
		url: `${config.API_URL}/manapi/platoonTopScreenMode/del`,
		name: '删除霸屏模式信息',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},

	// 领导视察模式信息-----
	// 获取领导视察模式信息
	get_leader_inspection_mode: {
		url: `${config.API_URL}/manapi/platoonLeadershipInspection/info`,
		name: '获取领导视察模式信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	// 领导视察模式信息-新增
	add_leader_inspection_mode: {
		url: `${config.API_URL}/manapi/platoonLeadershipInspection/save`,
		name: '领导视察模式信息-新增',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	// 领导视察模式信息-删除
	del_leader_inspection_mode: {
		url: `${config.API_URL}/manapi/platoonLeadershipInspection/del`,
		name: '领导视察模式信息-删除',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	// ----------------班牌背景图------------
	getBaseMap: {
		url: `${config.API_URL}/manapi/platoonhotspot/get_basemap`,
		name: '获取班牌背景图',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	saveBaseMap: {
		url: `${config.API_URL}/manapi/platoonhotspot/save_basemap`,
		name: '修改保存班牌背景图',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	syncBaseMap: {
		url: `${config.API_URL}/manapi/platoonhotspot/sync_baseMap`,
		name: '同步班牌背景图',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	// ----------------班牌tab------------
	getDeviceTab: {
		url: `${config.API_URL}/manapi/platoonhotspot/getTabInfo`,
		name: '获取班牌tab',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	saveDeviceTab: {
		url: `${config.API_URL}/manapi/platoonhotspot/saveTab`,
		name: '修改保存班牌tab',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	}
}
