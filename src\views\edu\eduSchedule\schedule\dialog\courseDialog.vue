<template>
    <el-dialog v-model="dialogVisible" title="选择禁排课程">
        <div class="course-content">
            <span>选择课程：</span>
            <ul class="course-list">
                <li v-for="item in courseList" :key="item.id" @click="selectedCourse(item)"
                    :class="selectedCourseIds.includes(item.id) ? 'selected' : ''">
                    {{ item.name }}
                </li>
            </ul>
        </div>
        <template #footer>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, semesterInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const emit = defineEmits(['success'])
const dialogVisible = ref(false)
const courseList = ref([])
const selectedCourseData = ref([])
const selectedCourseIds = ref([])
const open = (data) => {
    if (data?.length) {
        selectedCourseData.value = data
        selectedCourseIds.value = data.map(item => item.id)
    }
    dialogVisible.value = true
    getCourseList()
}
const selectedCourse = (data) => {
    if (selectedCourseIds.value.includes(data.id)) {
        selectedCourseData.value = selectedCourseData.value.filter(item => item.id !== data.id)
        selectedCourseIds.value = selectedCourseIds.value.filter(item => item !== data.id)
    } else {
        selectedCourseData.value.push(data)
        selectedCourseIds.value = selectedCourseData.value.map(item => item.id)
    }
}
const confirm = () => {
    emit('success', selectedCourseData.value)
    dialogVisible.value = false
}
const cleanCourseData= () => {
    selectedCourseData.value = []
    selectedCourseIds.value = []
}
const getCourseList = () => {
    globalPropValue.eduCourseSet.course.all.get({
        tenant_id: tenantId,
        campus_id: campusId
    }).then(res => {
        if (res.code === 200) {
            courseList.value = res.data.map(item => {
                return {
                    id: item.id,
                    name: item.course_name
                }
            })
        }
        console.log(courseList.value);
    })
}
defineExpose({
    open,
    cleanCourseData
})
</script>

<style lang="scss" scoped>
.course-content {
    display: flex;

    span {
        min-width: 80px;
    }

    .course-list {
        display: flex;
        flex-wrap: wrap;

        li {
            padding: 5px 10px;
            margin-right: 10px;
            margin-bottom: 10px;
            text-align: center;
            // padding: 5px 10px;
            background-color: var(--el-color-info-light-9);
            border-radius: 5px;
            cursor: pointer;
        }

        .selected {
            color: var(--el-color-primary);
            background-color: var(--el-color-primary-light-8);
        }
    }
}
</style>