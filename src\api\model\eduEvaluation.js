import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/eduapi/evaluation/list`,
		name: '获取评价列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/eduapi/evaluation/save`,
		name: '新增/修改评价',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/eduapi/evaluation/del`,
		name: '删除评价',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	publish: {
		url: `${config.API_URL}/eduapi/evaluation/publish`,
		name: '发布评价',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	cancel: {
		url: `${config.API_URL}/eduapi/evaluation/cancel`,
		name: '撤销评价',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	detail: {
		url: `${config.API_URL}/eduapi/evaluation/one`,
		name: '获取单个评教信息',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	user: {
		url: `${config.API_URL}/eduapi/evaluation/user`,
		name: '获取评教人员',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	delUser: {
		url: `${config.API_URL}/eduapi/evaluation/del_user`,
		name: '删除评教人员',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	data: {
		url: `${config.API_URL}/eduapi/evaluation/data`,
		name: '获取评教数据',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	analysis: {
		url: `${config.API_URL}/eduapi/evaluation/analysis`,
		name: '获取评教分析列表',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	analysisDetail: {
		url: `${config.API_URL}/eduapi/evaluation/analysis_detail`,
		name: '获取评教分析详情',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	}
}
