import config from '@/config'
import http from '@/utils/request'

export default {
	all: {
		url: `${config.API_URL}/lot/eduDevice/all`,
		name: '获取所有设备列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	list: {
		url: `${config.API_URL}/lot/eduDevice/list`,
		name: '获取设备列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	one: {
		url: `${config.API_URL}/lot/eduDevice/one`,
		name: '获取单个设备',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getByRoomId: {
		url: `${config.API_URL}/lot/eduDevice/getByRoomId`,
		name: '获取单个设备',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	power: {
		url: `${config.API_URL}/lot/eduDevice/powerCtrl`,
		name: '上下课控制',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	pcPower: {
		url: `${config.API_URL}/lot/eduDevice/pcCtrl`,
		name: '电脑开关控制',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	projectorPower: {
		url: `${config.API_URL}/lot/eduDevice/projectorCtrl`,
		name: '投影仪控制',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	projector2Power: {
		url: `${config.API_URL}/lot/eduDevice/projector2Ctrl`,
		name: '第二路投影仪控制',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	volume: {
		url: `${config.API_URL}/lot/eduDevice/volumeCtrl`,
		name: '音量控制',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	videoChannel: {
		url: `${config.API_URL}/lot/eduDevice/videoChannelCtrl`,
		name: '视频输出信号切换',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	bindRoom: {
		url: `${config.API_URL}/lot/eduDevice/bindThirdPartyClassroom`,
		name: '绑定教室',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	allPowerCtrl: {
		url: `${config.API_URL}/lot/eduDevice/allPowerCtrl`,
		name: '批量设置',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	unBindRoom: {
		url: `${config.API_URL}/lot/eduDevice/unbindThirdPartyClassroom`,
		name: '解绑教室',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	syncDevice: {
		url: `${config.API_URL}/lot/eduDevice/syncDevice`,
		name: '同步idste设备',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	refresh: {
		url: `${config.API_URL}/lot/eduDevice/refreshDevice`,
		name: '刷新设备',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	record: {
		url: `${config.API_URL}/lot/eduDevice/recordCtrl`,
		name: '录像控制',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	videoStream: {
		url: `${config.API_URL}/lot/eduDevice/getDevVideoStream`,
		name: '获取视频流',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	ReportRepair: {
		url: `${config.API_URL}/lot/eduDevice/reportRepair`,
		name: '修改故障状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	micChargeStubUnlock: {
		url: `${config.API_URL}/lot/eduDevice/micChargeStubUnlock`,
		name: '麦克风解锁',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	}
}
