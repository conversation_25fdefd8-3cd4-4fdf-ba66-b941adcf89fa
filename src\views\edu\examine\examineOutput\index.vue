<template>
    <el-container>
        <el-header>
            <el-page-header style="width: 100%" @back="goBack">
                <template #content>
                    <div class="headerContent">
                        <span class="headerTitle">{{ examineTitle }}</span>
                        <ul class="headerTabs">
                            <li class="headerTabsItem" v-for="item in options" :key="item.value"
                                :class="{ active: item.value == currentTab }" @click="selectTabs(item)">{{ item.label
                                }}</li>
                        </ul>
                    </div>
                </template>
                <template #extra></template>
            </el-page-header>
        </el-header>
        <el-main>
            <div v-if="currentTab == 1">
                <examineInfo :examineData="examineData"></examineInfo>
            </div>
            <div v-if="currentTab == 2">
                <studentInfo></studentInfo>
            </div>
            <div v-if="currentTab == 3">
                <staffInfo></staffInfo>
            </div>
            <div v-if="currentTab == 4">
                <signInfo></signInfo>
            </div>
        </el-main>
    </el-container>
</template>

<script setup>
import examineInfo from './components/examineInfo'
import studentInfo from './components/studentInfo'
import staffInfo from './components/staffInfo'
import signInfo from './components/signInfo'

import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const { go } = useRouter()
const { query } = useRoute()
const examineTitle = ref(query.name)
const goBack = () => {
    go(-1)
}
const options = ref([{
    label: '考试信息',
    value: 1
}, {
    label: '考生信息',
    value: 2
}, {
    label: '监考信息',
    value: 3
}, {
    label: '签到信息',
    value: 4
}])
const currentTab = ref(1)
const selectTabs = (item) => {
    currentTab.value = item.value
}

const examineData = ref({})
const getOne = () => {
    globalPropValue.examine.one.get({
        id: query.id,
        tenant_id: tenantId,
        campus_id: campusId
    }).then(res => {
        if (res.code === 200) {
            examineData.value = res.data
            if (examineData.value.sign == -1) {
                options.value = options.value.filter(item => item.value !== 4)
            }
        }
    })
}
getOne()

</script>

<style lang="scss" scoped>
.headerContent {
    display: flex;

    .headerTitle {
        margin-right: 100px;
        padding: 5px 15px;
    }

    .headerTabs {
        display: flex;

        .headerTabsItem {
            cursor: pointer;
            padding: 5px 15px;
            // 添加过渡效果（颜色/背景色变化）
            transition: color 0.3s ease, background-color 0.3s ease;

            &:hover {
                color: var(--el-color-primary);
            }
        }

        .active {
            color: var(--el-color-primary);
            background: var(--el-color-primary-light-8);
            border-radius: 5px;
            animation: tabActive 0.3s ease;
        }
    }
}

@keyframes tabActive {
    from {
        transform: translateY(2px);
        opacity: 0.6;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}
</style>