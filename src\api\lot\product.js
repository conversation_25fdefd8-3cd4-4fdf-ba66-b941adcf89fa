import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/lot/product/list`,
		name: '获取产品列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	all: {
		url: `${config.API_URL}/lot/product/all`,
		name: '获取产品列表(不分页)',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/lot/product/save`,
		name: '新增产品/修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/lot/product/del`,
		name: '删除产品',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	one: {
		url: `${config.API_URL}/lot/product/one`,
		name: '获取单个产品',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	changeStatus: {
		url: `${config.API_URL}/lot/product/changeStatus`,
		name: '修改产品状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	}
}
