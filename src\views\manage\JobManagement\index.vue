<template>
	<el-container>
		<el-aside width="200px">
			<el-container>
				<el-header>
					<el-input v-model="groupFilterText" placeholder="输入关键字进行过滤" clearable></el-input>
				</el-header>
				<el-main>
					<el-tree
						ref="group"
						class="menu"
						node-key="id"
						:data="groupsAdd"
						:current-node-key="''"
						:highlight-current="true"
						:filter-node-method="groupFilterNode"
						:props="defaultProps"
						:default-expanded-keys="[0]"
						@node-click="groupClick"
					></el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<div class="left-panel">
					<div class="left-panel-search">
						<!-- <el-form-item label="学校" v-if="TenantManagementList.length > 1">
							<el-select v-model="params.tenant_id" placeholder="学校">
								<el-option
									:label="item.name"
									:value="item.value"
									v-for="item in TenantManagementList"
									:key="item.code"
								></el-option>
							</el-select>
						</el-form-item> -->
						<el-form-item v-if="CampusManagementList.length > 1" label="校区">
							<el-select v-model="params.campus_id" placeholder="校区" filterable>
								<el-option
									v-for="item in CampusManagementList"
									:key="item.code"
									:label="item.name"
									:value="item.value"
								></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="职位名称">
							<el-input v-model="params.name" placeholder="请输入职位名称" clearable></el-input>
						</el-form-item>

						<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
						<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
					</div>
				</div>
				<div class="right-panel">
					<el-button type="primary" icon="el-icon-plus" @click="add">新增职位</el-button>
				</div>
			</el-header>
			<el-main>
				<scTable ref="table" row-key="id" stripe :apiObj="list.apiObj" :params="params">
					<el-table-column label="职位名称" prop="position_name" width="150"></el-table-column>
					<el-table-column label="所属部门" prop="department" width="150">
						<template #default="scope">
							<div v-if="scope.row.department">{{ scope.row.department.name }}</div>
						</template>
					</el-table-column>
					<el-table-column label="状态" prop="status" width="150">
						<template #default="scope">
							<el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
							<el-tag v-if="scope.row.status === -1" type="danger">停用</el-tag>
							<el-switch
								v-model="scope.row.status"
								style="margin-left: 10px"
								:active-value="1"
								:inactive-value="-1"
								@change="statusChange(scope.row)"
							></el-switch>
						</template>
					</el-table-column>
					<el-table-column label="备注" prop="remark"></el-table-column>
					<el-table-column label="创建时间" prop="created_at" width="250"></el-table-column>
					<el-table-column label="操作" fixed="right" align="center" width="170">
						<template #default="scope">
							<el-button-group>
								<!--								<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)"
																	>查看</el-button
																>-->
								<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)"
									>编辑
								</el-button>
								<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
									<template #reference>
										<el-button text type="danger" size="small">删除</el-button>
									</template>
								</el-popconfirm>
							</el-button-group>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		:groupData="groupData"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import saveDialog from './save'

const defaultProps = {
	children: 'children',
	label: 'department_name'
}
const { campusId, tenantId, campusInfo, tenantInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		department_id: null,
		name: null
	}
}

export default {
	name: 'JobManagement',
	data() {
		return {
			groupFilterText: '',
			groupData: [],
			defaultProps,
			list: {
				apiObj: this.$API.JobManagement.list
			},
			params: defaultParams(),
			search: {
				name: null
			},
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			JobData: [],
			dialog: {
				save: false
			}
		}
	},
	components: {
		saveDialog
	},
	watch: {
		groupFilterText(val) {
			this.$refs.group.filter(val)
		},
		'params.campus_id': {
			handler(val) {
				this.getDept()
				this.upsearch()
			}
		}
	},
	async created() {
		this.getDept()
	},
	computed: {
		groupsAdd() {
			let arr = [
				{
					id: 0,
					department_name: '全部'
				},
				...this.groupData
			]
			return arr
		}
	},
	methods: {
		//获取部门
		async getDept() {
			const { data } = await this.$API.system.dept.all.get(this.params)
			this.groupData = cusTom.arrayToTree(data)
		},

		//树过滤
		groupFilterNode(value, data) {
			if (!value) return true
			return data.department_name.indexOf(value) !== -1
		},
		//树点击事件
		groupClick(data) {
			if (data.children && data.children.length > 0) {
				return
			}
			this.params.department_id = data.id
			this.upsearch()
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//新增职位
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', this.params.department_id)
			})
		},
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.$refs.table.refresh()
			}
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit', row.department_id).setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.JobManagement.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			this.$API.JobManagement.status.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style></style>
