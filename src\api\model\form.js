import config from '@/config'
import http from '@/utils/request'

export default {
	form: {
		all: {
			url: `${config.API_URL}/sysapi/form/all`,
			name: '获取所有表单列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		list: {
			url: `${config.API_URL}/sysapi/form/list`,
			name: '获取表单列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		detail: {
			url: `${config.API_URL}/sysapi/form/one`,
			name: '获取表单信息',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add: {
			url: `${config.API_URL}/sysapi/form/save`,
			name: '新增表单',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		del: {
			url: `${config.API_URL}/sysapi/form/del`,
			name: '删除表单',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		set: {
			url: `${config.API_URL}/sysapi/form/set`,
			name: '表单配置',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		}
	}
}
