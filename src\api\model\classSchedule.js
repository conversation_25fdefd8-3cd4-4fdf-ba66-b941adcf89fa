import config from '@/config'
import http from '@/utils/request'
import { add } from 'lodash'

export default {
    getGradeSchedule: {
        url: `${config.API_URL}/eduapi/class_timetable/grade`,
        name: '获取年级课表',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    getClassSchedule: {
        url: `${config.API_URL}/eduapi/class_timetable/class`,
        name: '获取班级课表',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    getTeacherSchedule: {
        url: `${config.API_URL}/eduapi/class_timetable/teacher`,
        name: '获取教师课表',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    getClassRoomSchedule: {
        url: `${config.API_URL}/eduapi/class_timetable/room`,
        name: '获取教室课表',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    getScheduleData: {
        url: `${config.API_URL}/eduapi/class_timetable/list`,
        name: '获取课表视图数据',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    deleteSchedule: {
        url: `${config.API_URL}/eduapi/class_timetable/del`,
        name: '删除课表',
        post: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.post(this.url, params)
        }
    },
    syncSchedule: {
        url: `${config.API_URL}/eduapi/class_timetable/sync`,
        name: '同步课表',
        post: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.post(this.url, params)
        }
    },
    // 自定义排课设置（排课规则）
    getScheduleRules: {
        url: `${config.API_URL}/eduapi/class_timetable_rule/list`,
        name: '获取排课规则',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    addScheduleRules: {
        url: `${config.API_URL}/eduapi/class_timetable_rule/creat`,
        name: '添加排课规则',
        post: async function (params) {
            return await http.post(this.url, params)
        }
    },
    updateScheduleRules: {
        url: `${config.API_URL}/eduapi/class_timetable_rule/update`,
        name: '更新排课规则',
        post: async function (params) {
            return await http.post(this.url, params)
        }
    },
    deleteScheduleRules: {
        url: `${config.API_URL}/eduapi/class_timetable_rule/del`,
        name: '删除排课规则',
        post: async function (params) {
            return await http.post(this.url, params)
        }
    },
    changeScheduleRulesStatus: {
        url: `${config.API_URL}/eduapi/class_timetable_rule/changeStatus`,
        name: '修改排课规则状态',
        post: async function (params) {
            return await http.post(this.url, params)
        }
    },
    getPeriodsList: {
        url: `${config.API_URL}/eduapi/course_periods/defaultList`,
        name: '获取课程时段列表',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    getClassCourse: {
        url: `${config.API_URL}/eduapi/class/getClassCourse`,
        name: '获取班级课程',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
}