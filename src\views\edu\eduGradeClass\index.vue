<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :name="item.name" :label="item.label" :key="item.name"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main>
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script>
import classView from './classView'
import grade from './grade'
import classTeacher from './classTeacher'
import classStudent from './classStudent'
import { shallowRef } from 'vue'
export default {
	name: 'layoutTCB',
	components: {
		classView,
		grade,
		classStudent
	},
	data() {
		return {
			activeName: 'grade',
			tabs: [
				{
					label: '年级管理',
					name: 'grade',
					component: shallowRef(grade)
				},
				{
					label: '班级管理',
					name: 'class',
					component: shallowRef(classView)
				},
				/*{
					label: '任课老师管理',
					name: 'classTeacher',
					component: shallowRef(classTeacher)
				},
				{
					label: '班级学生管理',
					name: 'classStudent',
					component: shallowRef(classStudent)
				}*/
			],
			currComponent: {},
			classData: {}
		}
	},
	created() {
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
			this.resetClassD()
		},
		stu_manage(row) {
			this.classData = row
			this.activeName = 'classStudent'
			this.currComponent = this.tabs.find((item) => item.name === this.activeName)
		},
		tea_manage(row) {
			this.classData = row
			this.activeName = 'classTeacher'
			this.currComponent = this.tabs.find((item) => item.name === this.activeName)
		},
		resetClassD() {
			this.classData = {}
		}
	},
	provide() {
		return {
			stu_manage_f: this.stu_manage,
			tea_manage_f: this.tea_manage,
			classData_f: () => {
				return this.classData
			},
			resetClassD: this.resetClassD
		}
	}
}
</script>

<style></style>
