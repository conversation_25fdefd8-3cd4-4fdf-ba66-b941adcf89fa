<template>
	<el-drawer
		v-model="visible"
		title="热点信息"
		size="50%"
		destroy-on-close
		:close-on-press-escape="false"
		@close="close"
	>
		<el-header>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-setting" @click="editConfig">配置</el-button>
				<el-button type="primary" icon="el-icon-plus" @click="addHotspot">新增</el-button>
			</div>
		</el-header>
		<div>
			<el-table row-key="id" :data="hotspotInfo">
				<el-table-column label="封面图" prop="coverUrl">
					<template #default="{ row }">
						<cusImage
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="row.coverUrl"
							:preview-src-list="[row.coverUrl]"
							preview-teleported
						>
						</cusImage>
					</template>
				</el-table-column>
				<el-table-column label="热点类型" prop="hotspotType">
					<template #default="{ row }">
						{{ row.hotspotType == 1 ? '图片' : '视频' }}
					</template>
				</el-table-column>
				<el-table-column label="资源地址" prop="hotspotUrl">
					<template #default="{ row }">
						<cusImage
							v-if="row.hotspotType == 1"
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="row.hotspotUrl"
							:preview-src-list="[row.hotspotUrl]"
							preview-teleported
						>
						</cusImage>
						<div v-else>
							<el-button text type="primary" size="small" @click="videoPreview(row)">预览视频</el-button>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="100">
					<template #default="{ row }">
						<el-button text type="danger" size="small" @click="table_del(row)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</el-drawer>
	<el-dialog v-model="videoVisible" destroy-on-close title="视频预览">
		<video height="500px" width="100%" autoplay controls>
			<source :src="currentVideo.hotspotUrl" type="video/mp4" />
		</video>
	</el-dialog>
	<!-- 热点信息新增 -->
	<add ref="addVisible" @addSuccess="getHotspotInfo" />
	<!-- 配置 -->
	<el-dialog v-model="configVisible" destroy-on-close title="更新配置" width="30%">
		<el-form-item label="轮播时间间隔："> <el-input-number v-model="duration" :min="1" /> 秒 </el-form-item>
		<template #footer>
			<el-button @click="configVisible = false">取消</el-button>
			<el-button type="primary" @click="updateConfig">更新</el-button>
		</template>
	</el-dialog>
</template>
<script setup>
import cusImage from '@/components/custom/cusImage.vue'
import add from './add.vue'
import { ref, getCurrentInstance, defineExpose } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const visible = ref(false)
const hotspotInfo = ref([])
const videoVisible = ref(false)
const currentVideo = ref({})
const currentSn = ref('')
// 视频预览
const videoPreview = (row) => {
	currentVideo.value = row
	videoVisible.value = true
}
//更新配置
const configVisible = ref(false)
const duration = ref(5)
const editConfig = () => {
	getHotspotConfig()
	configVisible.value = true
	console.log('热点信息配置')
}
const updateConfig = () => {
	globalPropValue.platoonhotspot.save_conf
		.post({
			device_sn: currentSn.value,
			tenant_id: tenantId,
			campus_id: campusId,
			duration: duration.value
		})
		.then((res) => {
			if (res.code === 200) {
				ElMessage.success('更新成功')
				configVisible.value = false
				getHotspotConfig()
			}
		})
}
//新增
const addVisible = ref(null)
const addHotspot = () => {
	addVisible.value.show(currentSn.value)
	console.log('新增热点信息')
}
// 删除
const table_del = (row) => {
	ElMessageBox.confirm('确定删除该热点信息吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		globalPropValue.platoonhotspot.del
			.post({
				id: row.idString,
				tenant_id: tenantId,
				campus_id: campusId
			})
			.then((res) => {
				if (res.code === 200) {
					ElMessage.success('删除成功')
					getHotspotInfo(currentSn.value)
				}
			})
	})
}
// 获取热点信息
const getHotspotInfo = (sn) => {
	globalPropValue.platoonhotspot.info
		.get({
			device_sn: sn,
			tenant_id: tenantId,
			campus_id: campusId
		})
		.then((res) => {
			if (res.code === 200) {
				hotspotInfo.value = res.data
			}
		})
		.catch((err) => {
			console.log(`获取热点信息失败： ${err.message}`)
		})
}

//获取热点信息配置
const getHotspotConfig = () => {
	globalPropValue.platoonhotspot.get_conf
		.get({
			device_sn: currentSn.value,
			tenant_id: tenantId,
			campus_id: campusId
		})
		.then((res) => {
			if (res.code === 200) {
				duration.value = res.data.duration
			}
		})
		.catch((err) => {
			console.log(`获取热点信息配置失败： ${err.message}`)
		})
}

const show = (sn) => {
	visible.value = true
	currentSn.value = sn
	getHotspotInfo(sn)
}
const emit = defineEmits(['refresh'])
const close = () => {
	visible.value = false
	emit('refresh')
}
defineExpose({
	show,
	close
})
</script>
