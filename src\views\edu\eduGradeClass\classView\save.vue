<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<el-form ref="formref" :model="form" :rules="rules" label-width="120px">
			<el-form-item label="学期" prop="semester_id">
				<cusSelectSemester
					v-model="form.semester_id"
					:params="params"
					:show-default-value="false"
					:width="'214px'"
					clearable
					style="margin-right: 15px"
				/>
				<!--				<cusCascader v-model="form.semester_id" :options="getSemester"></cusCascader>-->
			</el-form-item>
			<el-form-item label="所属年级" prop="grade_id">
				<el-select v-model="form.grade_id" clearable>
					<el-option v-for="item in grade" :key="item.id" :label="item.grade_name" :value="item.id"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="班级名称" prop="class_name">
				<el-input v-model="form.class_name" placeholder="请输入班级名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="最多容纳学生数" prop="max_student">
				<el-input v-model="form.max_student" style="width: 150px" clearable type="number"></el-input>
			</el-form-item>
			<el-form-item label="教室" prop="room">
				<cusSelectField v-model="form.room" :multiple="false"></cusSelectField>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input v-model="form.remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'

const { semesterInfo } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		semester_id: null,
		class_name: null,
		status: 1,
		remark: null,
		tenant_id: null,
		campus_id: null,
		grade_id: null,
		academic_id: null,
		max_student: 30,
		room_id: [],
		mode: null
	}
}

export default {
	components: { cusSelectSemester },
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				class_name: [{ required: true, message: '请输入名称' }],
				semester_id: [{ required: true, message: '请选择学期' }],
				grade_id: [{ required: true, message: '请选择年级' }]
			},
			//所需数据选项
			groups: [],
			groupsProps: {
				value: 'id',
				label: 'semester_name',
				emitPath: false
			},
			gradeOptions: [],
			semesterInfo,
			grade: [],
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				labelWidth: '150px',

				formItems: [
					{
						label: '名称',
						name: 'class_name',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入班级名称',
							items: []
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '学年',
						name: 'academic_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择学年',
							items: []
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '学期',
						name: 'semester_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择学期',
							items: []
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '年级',
						name: 'grade_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择年级',
							items: []
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '最多容纳学生数',
						name: 'max_student',
						value: null,
						component: 'number',
						options: {
							placeholder: '请输入',
							controlsPosition: '1'
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '位置',
						name: 'room',
						value: null,
						component: 'cusSelectField',
						options: {
							title: '选择场室',
							tree: this.$API.fieldRoom.tree
						}
						//rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					}
				]
			}
		}
	},
	mounted() {},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.form.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	watch: {
		'form.campus_id': {
			handler(val) {
				this.getGrade()
				// this.params.academic_id = null
				this.formConfig.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
					.filter((v) => v.parent_id == 0 && v.campus_id == val)
					.map((v) => {
						return {
							label: v.name,
							value: v.value
						}
					})
			},
			immediate: true
		},
		'form.semester_id': {
			handler(val) {
				// this.params.grade_id = null
				this.getGrade()
			}
		}
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.form.mode = mode
			if (mode === 'add') {
				this.form.campus_id = this.params.campus_id
				this.form.tenant_id = this.params.tenant_id
			}
			this.visible = true
			return this
		},

		async getGrade() {
			this.params.semester_id = this.form.semester_id
			if (this.params.semester_id) {
				var res = await this.$API.eduGradeClass.grade.all.get({ ...this.params })
				this.grade = res.data
			} else {
				this.grade = []
			}
		},
		//表单提交方法
		submit() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let subForm
					console.log(this.form.room)
					if (!this.form.room || this.form.room.length <= 0) {
						subForm = { ...this.form }
						subForm.room_id = 0
					} else {
						subForm = { ...this.form, room_id: this.form.room[0].id }
					}
					subForm.max_student = parseInt(subForm.max_student)
					var res = await this.$API.eduGradeClass.class.save.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			// this.form.id = data.id
			// this.form.label = data.label
			// this.form.status = data.status
			// this.form.sort = data.sort
			// this.form.parentId = data.parentId
			// this.form.remark = data.remark

			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
			this.form.room = []
			if (data.room !== null) {
				this.form.room = [
					{
						id: data.room.id,
						label: data.room.name
					}
				]
			}

			this.getGrade()
		}
	}
}
</script>

<style></style>
