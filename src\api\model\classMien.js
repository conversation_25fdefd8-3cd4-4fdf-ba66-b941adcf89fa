import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		//url: `${config.MOCK_URL}/system/user/list`,
		url: `${config.API_URL}/eduapi/mien/list`,
		name: '获取列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/eduapi/mien/save`,
		name: '新增/修改',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},

	del: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/eduapi/mien/del`,
		name: '删除',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	}
}
