<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item label="模板名称">
						<el-input v-model="params.name" placeholder="请输入模板名称" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-CirclePlus" @click="add">新增模板</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :apiObj="list.apiObj" :params="params" :hideDo="false">
				<el-table-column label="模板名称" prop="template_name"></el-table-column>
				<el-table-column label="模板ID" prop="template_key"></el-table-column>
				<el-table-column label="场景" prop="scene" width="120"></el-table-column>
				<el-table-column label="状态" prop="status" width="120">
					<template #default="scope">
						<el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
						<el-tag v-if="scope.row.status === -1" type="danger">停用</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="描述" prop="desc" width="300" show-overflow-tooltip></el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="150"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="300">
					<template #default="scope">
						<el-button text type="success" size="small" @click="table_show(scope.row, scope.$index)"
							>物模型管理</el-button
						>
						<el-divider direction="vertical" />
						<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑 </el-button>
						<el-divider direction="vertical" />
						<el-button text type="danger" size="small" @click="table_del(scope.row, scope.$index)">删除</el-button>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		:groupData="groupData"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import saveDialog from './save'
import ScTable from '@/components/scTable/index.vue'
import { ElMessageBox } from 'element-plus'

const defaultParams = () => {
	return {
		name: null
	}
}

export default {
	name: 'templateList',
	data() {
		return {
			groupFilterText: '',
			groupData: [],
			list: {
				apiObj: this.$LotApi.template.list
			},
			params: defaultParams(),
			search: {
				name: null
			},
			dialog: {
				save: false
			}
		}
	},
	components: {
		ScTable,
		saveDialog
	},
	watch: {},
	created() {},
	computed: {},
	methods: {
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//新增职位
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.$refs.table.refresh()
			}
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			/*this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})*/
			this.$router.push({
				name: 'lotSetTemplateTsl',
				params: {
					id: row.id
				}
			})
		},
		//删除
		async table_del(row) {
			this.$c
			var reqData = { id: row.id }
			ElMessageBox.confirm('是否删除当前产品模板？删除后模板下所有物模型都会被删除！', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.template.del.post(reqData)
				if (res.code === 200) {
					this.$message.success('删除成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style></style>
