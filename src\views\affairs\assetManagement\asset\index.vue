<template>
	<el-container>
		<el-aside width="270px">
			<el-container>
				<el-header v-if="CampusManagementList.length > 1">
					<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-header>
				<el-main>
					<div class="add-lay">
						<el-button type="primary" icon="el-icon-plus" @click="addLou">新增一级类别</el-button>
					</div>
					<el-tree
						ref="group"
						class="menu"
						node-key="id"
						:data="groupsAdd"
						:highlight-current="true"
						:props="defaultProps"
						:default-expanded-keys="[0]"
						@node-click="groupClick"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node" @mouseenter="handleShowTools(data)" @mouseleave="handleShowTools(data)">
								<span>{{ node.label }}</span>
								<span v-if="data.id !== 0 && data.id === currentId">
									<a v-if="data.parent_id <= 0" @click="appendCeng(data)">
										<el-icon><el-icon-plus /></el-icon>
									</a>
									<a style="margin-left: 8px" @click.stop="edit(node, data)">
										<el-icon><el-icon-edit /></el-icon>
									</a>
									<a style="margin-left: 8px" @click.stop="remove(node, data)">
										<el-icon><el-icon-delete /></el-icon>
									</a>
								</span>
							</span>
						</template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<div class="left-panel"></div>
				<div class="right-panel">
					<el-button type="primary" plain icon="el-icon-refresh" @click="Outbound">资产流转</el-button>
					<el-button type="primary" icon="el-icon-plus" @click="Warehousing">资产入库</el-button>
				</div>
			</el-header>
			<el-main>
				<scTable ref="table" row-key="id" stripe :apiObj="list.apiObj" :params="params" @dataChange="dataChange">
					<el-table-column label="资产名称" prop="name" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="资产编号" prop="serial_number" width="150"></el-table-column>

					<el-table-column label="类别" prop="type_name" width="150" show-overflow-tooltip></el-table-column>

					<el-table-column label="位置" prop="building_name" width="200" show-overflow-tooltip>
						<template #default="scope">
							<div v-if="scope.row.room">{{ scope.row.room.name }}</div>
						</template>
					</el-table-column>
					<el-table-column label="状态" prop="status" width="100">
						<template #default="scope">
							<el-tag :type="['success', 'warning', 'danger'][scope.row.status - 1]">
								{{ formData(assetsStatusMap, scope.row.status) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="领用人" prop="borrow_user_name" width="100">
						<template #default="scope">
							<span v-if="scope.row.status === 2">{{ scope.row.borrow_user_name }}</span>
							<span v-else></span>
						</template>
					</el-table-column>
					<el-table-column label="领用时间" prop="borrow_time" width="150">
						<template #default="scope">
							<span v-if="scope.row.status === 2">{{ scope.row.borrow_time }}</span>
							<span v-else></span>
						</template>
					</el-table-column>
					<el-table-column label="报废时间" prop="scrap_time" width="150">
						<template #default="scope">
							<span v-if="scope.row.status === 3">{{ scope.row.borrow_time }}</span>
							<span v-else></span>
						</template>
					</el-table-column>
					<el-table-column label="入库时间" prop="storage_time" width="150"></el-table-column>
					<el-table-column label="金额(单位：元)" prop="amount" width="150"></el-table-column>
					<el-table-column label="备注" prop="storage_remark" width="150" show-overflow-tooltip> </el-table-column>

					<el-table-column label="操作" fixed="right" align="center" width="240">
						<template #default="scope">
							<el-button-group>
								<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)"
									>编辑</el-button
								>
								<!-- <el-button text type="primary" size="small" @click="out(scope.row, scope.$index)">二维码</el-button> -->
								<el-button
									v-if="scope.row.status === 2"
									text
									type="warning"
									size="small"
									@click="refund(scope.row, scope.$index)"
									>归还</el-button
								>
								<el-button
									v-if="scope.row.status === 1"
									text
									type="primary"
									size="small"
									@click="requisition(scope.row, scope.$index)"
									>领用</el-button
								>
								<el-button text type="primary" size="small" @click="log(scope.row, scope.$index)">流转记录</el-button>

								<el-button
									v-if="scope.row.status !== 3"
									type="danger"
									size="small"
									text
									@click="scrap(scope.row, scope.$index)"
									>报废</el-button
								>

								<el-popconfirm title="确定删除吗？" @confirm="del(scope.row, scope.$index)">
									<template #reference>
										<el-button type="danger" size="small" text>删除</el-button>
									</template>
								</el-popconfirm>
							</el-button-group>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
		<!-- {{ $refs.table }}--- -->
		<!-- {{ roomNameFilters }}  -->
	</el-container>
	<OutboundDialog
		v-if="dialog.Outbound"
		ref="OutboundDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.Outbound = false"
	></OutboundDialog>
	<log-dialog
		v-if="dialog.log"
		ref="logDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.log = false"
	>
	</log-dialog>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess1"
		@closed="dialog.save = false"
	>
	</save-dialog>
	<warehousing-dialog
		v-if="dialog.warehousing"
		ref="warehousingDialog"
		:params="params"
		:groupData="groupData"
		@success="handleSaveSuccess"
		@closed="dialog.warehousing = false"
	>
	</warehousing-dialog>
	<out-dialog
		v-if="dialog.out"
		ref="outDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.out = false"
	></out-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import OutboundDialog from './Outbound.vue'
import outDialog from './out.vue'
import saveDialog from './save.vue'
import logDialog from './log.vue'

import warehousingDialog from './Warehousing.vue'
const defaultProps = {
	children: 'child',
	label: 'name'
}
const { campusId, tenantId, campusInfo, roomTypeMap, roomCapacityMap, assetsStatusMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		type_id: null
	}
}

export default {
	name: 'assets',
	data() {
		return {
			currentId: -1,
			groupFilterText: '',
			groupData: [],
			defaultProps,
			list: {
				apiObj: this.$API.assets.rooms.list
			},
			params: defaultParams(),

			CampusManagementList: campusInfo,
			JobData: [],
			dialog: {
				save: false,
				warehousing: false,
				out: false,
				Outbound: false,
				log: false
			},

			showTools: false,
			roomNameFilters: [],
			roomTypeMap,
			roomCapacityMap,
			campusInfo,
			assetsStatusMap
		}
	},
	components: {
		OutboundDialog,
		logDialog,
		warehousingDialog,
		outDialog,
		saveDialog
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.type_id = null
				this.getLou()
				this.getRoom()
				this.$SET_campus_id(val)
			},
			immediate: true
		}
	},
	computed: {
		floorFilters() {
			let res = cusTom
				.treeToArray(this.groupData)
				.filter((v) => {
					return v.parent_id > 0
				})
				.map((v) => {
					return {
						text: v.building_name,
						value: v.building_name
					}
				})

			return cusTom.uniqueByValue(res, 'text')
		},
		groupsAdd() {
			let arr = [
				{
					id: 0,
					name: '全部'
				},
				...this.groupData
			]
			return arr
		}
	},
	async created() {},
	methods: {
		Outbound() {
			this.dialog.Outbound = true
			this.$nextTick(() => {
				this.$refs.OutboundDialog.open('out')
			})
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		handleShowTools(item) {
			this.currentId = item.id
		},
		dataChange(val) {
			if (!val.data.rows) return
			let arr = val.data.rows.map((v) => {
				return {
					text: v.room_name,
					value: v.room_name
				}
			})
			this.roomNameFilters = cusTom.uniqueByValue(arr, 'text')
		},
		filterHandler(value, row, column) {
			const property = column['property']
			return row[property] === value
		},

		//获取宿舍楼
		async getLou() {
			const res = await this.$API.assets.all.get({ ...this.params, building_type: null })
			if (!res.data) res.data = []
			let data1 = res.data.map((v) => {
				return {
					...v,
					showTools: false
				}
			})
			this.groupData = cusTom.arrayToTree(data1, 'id', 'parent_id', 'child')
		},
		//获取房间列表
		async getRoom() {
			this.$refs.table?.upData(this.params1)
		},

		refresh() {
			this.$refs.table.refresh()
		},

		//树点击事件
		groupClick(data) {
			if (data.parent_id <= 0 && data.child) {
				return
			} else {
				this.params.type_id = data.id
			}
			this.getRoom()
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		},
		//新增宿舍楼
		addLou() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},
		//楼层保存回调
		handleSaveSuccess() {
			// this.getRoom()
			this.refresh()
		},
		//房间保存回调
		handleSaveSuccess1() {
			this.getLou()
		},
		//编辑
		table_edit(row) {
			this.dialog.warehousing = true
			this.$nextTick(() => {
				this.$refs.warehousingDialog.open('edit').setData(row)
			})
		},

		showClassTable() {
			this.$message.info('正在开发中')
		},

		//添加层
		appendCeng(data) {
			// this.log(111)
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', 2, data.id)
			})
		},
		Warehousing() {
			this.dialog.warehousing = true
			this.$nextTick(() => {
				this.$refs.warehousingDialog.open().setData({
					type_id: this.params.type_id
				})
			})
		},
		//出库
		out(row, index) {
			this.dialog.out = true
			this.$nextTick(() => {
				this.$refs.outDialog.open('out').setData(row)
			})
		},
		//归还
		refund(row, index) {
			this.dialog.out = true
			this.$nextTick(() => {
				this.$refs.outDialog.open('refund').setData(row)
			})
		},
		//报废
		scrap(row) {
			this.dialog.out = true
			this.$nextTick(() => {
				this.$refs.outDialog.open('scrap').setData(row)
			})
		},
		//领用
		requisition(row) {
			this.dialog.out = true
			this.$nextTick(() => {
				this.$refs.outDialog.open('out').setData(row)
			})
		},
		//流转记录
		log(row, index) {
			this.dialog.log = true
			this.$nextTick(() => {
				this.$refs.logDialog.open().setData(row)
			})
		},
		//楼层树 编辑
		edit(node, data) {
			let obj = JSON.parse(JSON.stringify(data))
			delete obj.children
			obj.name = [{ value: obj.name }]
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit', obj.parent_id == 0 ? 1 : 2, obj.parent_id).setData(obj)
			})
		},
		// 楼层树 删除
		async del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.assets.rooms.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		remove(node, data) {
			const title = data.parent_id > 0 ? `是否删除子类${data.name}?` : `是否删除类${data.name}?`

			ElMessageBox.confirm(title, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$API.assets.del
						.post({
							...data,
							tenant_id: this.params.tenant_id,
							campus_id: this.params.campus_id
						})
						.then((res) => {
							if (res.code === 200) {
								ElMessage({
									type: 'success',
									message: '删除成功'
								})
								this.getLou()
							}
						})
				})
				.catch(() => {})
		},
		//删除房间
		async delRoom(row, index) {
			var res = await this.$API.assets.rooms.del.post({
				id: row.id,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			})
			if (res.code === 200) {
				this.getRoom()

				this.$message.success('操作成功')
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		}
	}
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;
	.el-button {
		width: 80%;
	}
}
.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;
	box-sizing: border-box;
	a {
		font-size: 18px;
	}
}
</style>
