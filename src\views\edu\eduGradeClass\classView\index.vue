<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList.length > 1">
						<el-select v-model="params.campus_id" placeholder="请选择校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="" prop="semester_id">
						<cusSelectSemester
							v-model="params.semester_id"
							:params="params"
							:show-default-value="true"
							:width="'214px'"
							clearable
							style="margin-right: 15px"
							@semesterChange="semesterChange"
						/>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="请输入班级名称" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-plus" @click="add">新增班级</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
				<el-table-column label="班级名称" prop="class_name" width="120"></el-table-column>
				<el-table-column label="年级" prop="grade_name" width="120"></el-table-column>
				<el-table-column label="最多容纳学生数" prop="max_student" width="120"></el-table-column>
				<el-table-column label="当前学生数" prop="student_num" width="120"></el-table-column>
				<el-table-column label="教室" prop="room" width="200">
					<template #default="scope">
						<span v-if="scope.row.room">{{ scope.row.room.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="学年学期" prop="academic_name">
					<template #default="scope"> {{ scope.row.academic_name }} - {{ scope.row.semester_name }} </template>
				</el-table-column>
				<el-table-column label="备注" prop="remark"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="330">
					<template #default="scope">
						<el-button-group>
							<!--							<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>-->
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-button text type="primary" size="small" @click="teaManage(scope.row, scope.$index)"
								>班级教师</el-button
							>
							<el-button text type="primary" size="small" @click="stuManage(scope.row, scope.$index)"
								>班级学生</el-button
							>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>

	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
	<tea-dialog v-if="dialog.tea" ref="teaDialog" :params="params" @closed="dialog.tea = false"></tea-dialog>
	<stu-dialog v-if="dialog.stu" ref="stuDialog" :params="params" @closed="dialog.stu = false"></stu-dialog>
</template>

<script>
import saveDialog from './save'
import teaDialog from './teacherSave.vue'
import stuDialog from './studentList.vue'
import cusTom from '@/utils/cusTom'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'
const { campusId, tenantId, campusInfo, tenantInfo, semesterInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		name: null,
		academic_id: null,
		semester_id: null,
		grade_id: null
	}
}

export default {
	name: '',
	components: {
		cusSelectSemester,
		saveDialog,
		teaDialog,
		stuDialog
	},

	data() {
		return {
			dialog: {
				save: false,
				tea: false
			},
			apiObj: this.$API.eduGradeClass.class.list,
			selection: [],
			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			semesterArr: [],
			//所需数据选项
			groupsProps: {
				value: 'id',
				label: 'semester_name',
				emitPath: false
			},
			semesterInfo,
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'name',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入班级名称',
							items: []
						}
					},
					{
						label: null,
						name: 'academic_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择学年',
							items: []
						}
					},
					{
						label: null,
						name: 'semester_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择学期',
							items: []
						}
					}
				]
			}
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.semester_id = null
			},
			immediate: true
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	async created() {},
	inject: ['stu_manage_f', 'tea_manage_f'],
	methods: {
		//添加
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
			// this.getDept()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		stuManage(row) {
			this.dialog.stu = true
			this.$nextTick(() => {
				this.$refs.stuDialog.open(row)
			})
			/*this.stu_manage_f(row)*/
		},
		teaManage(row) {
			this.dialog.tea = true
			this.$nextTick(() => {
				this.$refs.teaDialog.open(row)
			})
			//this.tea_manage_f(row)
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.eduGradeClass.class.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			this.$API.eduGradeClass.class.status.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		semesterChange(val) {
			this.params.semester_id = val
			this.$nextTick(() => {
				this.upsearch()
			})
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.$refs.table.refresh()
			}
		}
	}
}
</script>

<style lang="scss" scoped></style>
