<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script setup>
import alertList from './alertList'
import alertRule from './alertRule'
import { ref } from 'vue'
const activeName = ref('alertList')
const currComponent = ref({
	name: 'alertList',
	component: alertList
})
const tabs = [
	{
		name: 'alertList',
		label: '告警记录',
		component: alertList
	},
	{
		name: 'alertRule',
		label: '告警规则',
		component: alertRule
	}
]
const handleClick = (name) => {
	currComponent.value = tabs.find((item) => item.name === name)
}
</script>
<style lang="scss" scoped></style>
