<template>
	<el-container>
		<el-aside width="270px">
			<el-container>
				<el-header v-if="CampusManagementList.length > 1">
					<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-header>
				<el-main>
					<div class="add-lay">
						<el-button type="primary" icon="el-icon-plus" @click="addType">新增一级类别</el-button>
					</div>
					<el-tree
						ref="treeRef"
						class="menu"
						node-key="id"
						:highlight-current="true"
						:expand-on-click-node="false"
						:default-expanded-keys="[0]"
						:data="groupsAdd"
						:props="defaultProps"
						@node-click="groupClick"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node" @mouseenter="handleShowTools(data)" @mouseleave="handleShowTools(data)">
								<span>{{ node.label }}</span>
								<span v-if="data.id !== 0 && data.id === currentId">
									<a v-if="data.parent_id <= 0" @click="add(data)">
										<el-icon><el-icon-plus /></el-icon>
									</a>
									<a style="margin-left: 8px" @click.stop="edit(node, data)">
										<el-icon><el-icon-edit /></el-icon>
									</a>
									<a style="margin-left: 8px" @click.stop="remove(node, data)">
										<el-icon><el-icon-delete /></el-icon>
									</a>
								</span>
							</span>
						</template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<div class="left-panel">
					<div class="left-panel-search">
						<el-form-item label="视频名称">
							<el-input v-model="params.name" placeholder="请输入" clearable></el-input>
						</el-form-item>
						<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
						<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
					</div>
				</div>
				<div class="right-panel">
					<el-button type="primary" icon="el-icon-Plus" @click="addMaterial">新增教材</el-button>
				</div>
			</el-header>
			<el-main>
				<scTable ref="table" row-key="id" stripe :apiObj="apiObj" :params="params">
					<el-table-column label="视频封面" prop="duration" width="90" fixed="left">
						<template #default="{ row }">
							<cusImage
								:src="row.cover_url"
								showDefaultImg
								fit="contain"
								:preview-src-list="[row.cover_url]"
								preview-teleported
							></cusImage>
						</template>
					</el-table-column>
					<el-table-column label="教材名称" prop="name" width="180" fixed="left">
						<template #default="{ row }">
							<el-link
								v-if="webPreview(row.obj) || checkVideo(row.obj)"
								type="primary"
								style="font-size: 12px"
								@click="table_preview(row)"
								>{{ row.name }}
							</el-link>
							<span v-else>{{ row.name }}</span>
						</template>
					</el-table-column>
					<el-table-column label="学科名称" prop="course_name" width="120"></el-table-column>
					<el-table-column label="所属类型" prop="type_name" width="150"></el-table-column>
					<el-table-column label="教材大小" prop="size" width="100">
						<template #default="{ row }"> {{ (row.size / 1024 / 1024).toFixed(2) }}MB</template>
					</el-table-column>
					<el-table-column label="视频长度" prop="duration" width="90">
						<template #default="{ row }"> {{ row.duration.toFixed(2) + ' s ' }}</template>
					</el-table-column>

					<el-table-column label="创建人" prop="created_user_name" width="150"></el-table-column>
					<el-table-column label="点赞数量" prop="like_num" width="90">
						<template #default="{ row }">
							<el-link
								type="primary"
								:disabled="row.like_num === 0"
								:underline="false"
								@click="table_like_collect(row, 1)"
								>{{ row.like_num }}
							</el-link>
						</template>
					</el-table-column>
					<el-table-column label="收藏数量" prop="collect_num" width="90">
						<template #default="{ row }">
							<el-link
								:disabled="row.collect_num === 0"
								type="primary"
								:underline="false"
								@click="table_like_collect(row, 2)"
								>{{ row.collect_num }}
							</el-link>
						</template>
					</el-table-column>
					<el-table-column label="视频热度" prop="hot" width="90"></el-table-column>
					<el-table-column label="评论数" prop="comment_num" width="90">
						<template #default="{ row }">
							<el-link :disabled="row.comment_num === 0" type="primary" :underline="false" @click="table_remark(row)"
								>{{ row.comment_num }}
							</el-link>
						</template>
					</el-table-column>
					<!--					<el-table-column label="评论总分" prop="comment_score" width="90"></el-table-column>-->
					<el-table-column label="是否是视频" prop="is_video" width="90">
						<template #default="{ row }">
							<el-tag :type="row.is_video === -1 ? 'danger' : 'success'">{{ row.is_video === 1 ? '是' : '否' }}</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="是否推荐" prop="recommend" width="90">
						<template #default="{ row }">
							<el-tag :type="row.recommend === -1 ? 'danger' : 'success'"
								>{{ row.recommend === 1 ? '是' : '否' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="是否公开" prop="is_public" width="90">
						<template #default="{ row }">
							<el-tag :type="row.is_public === -1 ? 'danger' : 'success'"
								>{{ row.is_public === 1 ? '是' : '否' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="创建时间" prop="created_at" width="180"></el-table-column>
					<el-table-column label="最后更新时间" prop="updated_at" width="180"></el-table-column>
					<!-- <el-table-column label="教材描述" prop="description" width="300">
						<template #default="{ row }">
							<span v-html="row.description"></span>
						</template>
					</el-table-column> -->
					<el-table-column label="操作" fixed="right" align="left" width="280">
						<template #default="{ row }">
							<el-link
								type="primary"
								style="font-size: 12px; margin-right: 8px"
								:underline="false"
								@click="table_share(row)"
							>
								分享
							</el-link>
							<el-link
								type="primary"
								style="font-size: 12px; margin-right: 8px"
								:underline="false"
								@click="table_edit(row)"
							>
								编辑
							</el-link>
							<el-link
								v-if="checkVideo(row.obj)"
								type="success"
								style="font-size: 12px; margin-right: 8px"
								:underline="false"
								@click="table_slice(row)"
								>视频切片
							</el-link>
							<el-link
								type="success"
								style="font-size: 12px; margin-right: 8px"
								:underline="false"
								@click="table_attachment(row)"
								>附件管理 </el-link
							><!--
							<el-link
								type="success"
								style="font-size: 12px; margin-right: 8px"
								:underline="false"
								@click="table_remark(row)"
								>评论管理
							</el-link>-->
							<el-popconfirm title="确定删除吗？" @confirm="table_del(row)">
								<template #reference>
									<el-link type="danger" :underline="false" style="font-size: 12px; margin-right: 8px">删除</el-link>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-container>
	<DialogSave ref="saveDialog" :params="params" @success="handlerSaveSuccess"></DialogSave>
	<DrawerSave
		ref="saveDrawer"
		:disciplineOptions="disciplineOptions.list"
		:course="course.list"
		:params="params"
		:treeCurrentId="treeCurrentId"
		@success="handlerSuccess"
	></DrawerSave>
	<!--	视频播放组件-->
	<el-dialog v-model="videoVisible" destroy-on-close title="视频预览">
		<el-row :gutter="20">
			<el-col :span="sliceList?.length > 0 ? 16 : 24">
				<VideoPlay
					ref="videoPlayRef"
					:progressMarkers="progressMarkers"
					:vid="VideoPlayAtuh.videoInfo.video_vod_id"
					:playauth="VideoPlayAtuh.videoInfo.play_auth"
					:cover="VideoPlayAtuh.videoInfo.cover_url"
					height="500px"
				>
				</VideoPlay>
			</el-col>
			<el-col v-if="sliceList?.length > 0" :span="8">
				<SliceList ref="sliceRef" :slice-list="sliceList" :data="data" @changeTime="changeSliceTime"> </SliceList>
				<!--				<scTable ref="tableRef" :params="params1" :apiObj="apiObj1" hidePagination hideDo>
					<el-table-column prop="name" label="切片名称"></el-table-column>
					<el-table-column prop="description" label="切片描述" width="260"></el-table-column>
					<el-table-column prop="time_begin" label="视频开始时间">
						<template #default="{ row }">
							<el-link type="primary" @click="handlerTime(row.time_begin)"> {{ row.time_begin + 's' }}</el-link>
						</template>
					</el-table-column>
				</scTable>-->
			</el-col>
		</el-row>
	</el-dialog>
	<Drawer ref="drawer"></Drawer>
	<attachmentDrawer ref="attachment"></attachmentDrawer>
	<remarkDrawer ref="remark"></remarkDrawer>
	<videoSliceDrawer ref="videoSlice"></videoSliceDrawer>

	<el-dialog v-model="shareVisible" width="520px" destroy-on-close title="分享">
		<el-alert title="请用将小程序分享码通过微信发给学生，学生扫码即可打开" :closable="false" type="info" />
		<div v-loading="shareLoading" style="min-height: 200px" element-loading-text="分享码加载中">
			<p style="text-align: center; margin-top: 20px; margin-bottom: 20px">
				<el-image :src="shareUrl" style="width: 160px; height: 160px" />
			</p>
			<p style="text-align: center">
				<el-button type="primary" style="margin-left: 10px" @click="downloadImage(shareUrl)">下载图片</el-button>
			</p>
		</div>
	</el-dialog>
</template>
<script setup>
import VideoPlay from '@/components/videoPlay/index.vue'
import Drawer from './drawerSave.vue'
import SliceList from './sliceList.vue'
import attachmentDrawer from './components/eduAttachment'
import remarkDrawer from './components/eduRemark'
import videoSliceDrawer from './components/eduVideoSliceList'
import { ref, reactive, getCurrentInstance, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const router = useRouter()
const defaultProps = {
	children: 'child',
	label: 'type_name'
}
import DialogSave from './save.vue'
import DrawerSave from './saveList.vue'
import cusTom from '@/utils/cusTom'
import { ElMessage, ElMessageBox } from 'element-plus'

const { campusInfo, tenantId, campusId } = cusTom.getBaseQuery()
import { webPreview, checkVideo } from '@/utils/mediaLib'

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		name: '',
		type_id: null
	}
}
let data = ref({})
let videoPlayRef = ref(null)
let sliceRef = ref(null)
// 获取校区信息
const CampusManagementList = ref(campusInfo)
// params参数
const params = ref(defaultParams())
// tree数据
const treeData = ref([])
// 视频打点配置字段
const progressMarkers = ref([])
const sliceList = ref([])
// 树节点当前id
let currentId = ref(-1)
let saveDialog = ref()
let saveDrawer = ref()
let treeRef = ref(null)
let treeCurrentId = ref(0)
let drawer = ref(null)
let attachment = ref(null)
let remark = ref(null)
let videoSlice = ref(null)
let videoVisible = ref(false)
let shareVisible = ref(false)
let shareLoading = ref(false)
let shareUrl = ref('')
let table = ref()
const VideoPlayAtuh = reactive({
	videoInfo: {
		play_auth: '',
		cover_url: '',
		duration: 0,
		status: '',
		title: '',
		video_vod_id: '',
		obj: ''
	}
})
// 学科
const disciplineOptions = reactive({
	list: []
})
// 课程
const course = reactive({
	list: []
})
const groupsAdd = computed(() => {
	let arr = [
		{
			id: 0,
			type_name: '全部'
		},
		...treeData.value
	]
	return arr
})
// 获取表格列表数据
const apiObj = ref(globalPropValue.eduMaterials.material.list)
const getTreeData = async () => {
	const res = await globalPropValue.eduMaterials.material.tree.get({
		tenant_id: params.value.tenant_id,
		campus_id: params.value.campus_id
	})
	if (res.code === 200) {
		treeData.value = res.data
	}
}
onMounted(() => {
	getTreeData()
	getDisciplineOptions()
	getEduCourse()
})
// 树节点点击事件
const groupClick = (data) => {
	/*	if (data.parent_id <= 0 && data.child) {
		return
	} else {*/
	params.value.type_id = data.id
	//}
	table.value?.upData(params.value)
	treeCurrentId.value = treeRef.value.getCurrentKey()
	/*	console.log(treeRef.value.getCurrentNode())
	if (data.parent_id) {
		console.log(data.parent_id)
	}*/
}
// 鼠标移入事件
const handleShowTools = (item) => {
	// console.log(item)
	currentId.value = item.id
}
// 树添加按钮
const add = (data) => {
	// console.log('add', data)
	saveDialog.value.visible = true
	nextTick(() => {
		saveDialog.value.open('add', 2, data.id)
	})
}

// 新增类别
const addType = () => {
	saveDialog.value.visible = true
	nextTick(() => {
		saveDialog.value.open()
	})
}
// 树编辑按钮
const edit = (node, data) => {
	// console.log('edit', node, 'edit', data.id)
	// console.log(data)
	const obj = JSON.parse(JSON.stringify(data))
	delete obj.child
	// console.log(obj)
	obj.type_name = [{ value: obj.type_name }]
	saveDialog.value.visible = true
	nextTick(() => {
		saveDialog.value.open('edit', obj.parent_id === 0 ? 1 : 2, obj.parent_id)
		saveDialog.value.setData(obj)
	})
}
// 树删除按钮
const remove = (node, data) => {
	// console.log('remove', node, data)
	const title = data.parent_id > 0 ? `是否删除子类${data.type_name}?` : `是否删除类${data.type_name}?`
	ElMessageBox.confirm(title, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		globalPropValue.eduMaterials.material.tree_del
			.post({
				id: data.id,
				tenant_id: params.value.tenant_id,
				campus_id: params.value.campus_id
			})
			.then((res) => {
				if (res.code === 200) {
					getTreeData()
					ElMessage({
						type: 'success',
						message: '删除成功'
					})
					// getTreeData()
				}
			})
			.catch(() => {
				console.log(123)
			})
	})
}
// 新增教材事件
const addMaterial = () => {
	nextTick(() => {
		saveDrawer.value.visible = true
		saveDrawer.value.open('add', groupsAdd.value)
	})
}
// 搜索操作
const upsearch = () => {
	table.value.upData(params.value)
}
// 重置操作
const refresh = () => {
	params.value = defaultParams()
	table.value.upData(params.value)
}
// 编辑事件
const table_edit = (row) => {
	nextTick(() => {
		saveDrawer.value.visible = true
		saveDrawer.value.open('edit', groupsAdd.value)
		saveDrawer.value.setData(row)
	})
}

// 分享事件
const table_share = (row) => {
	shareUrl.value = null
	shareVisible.value = true
	shareLoading.value = true
	//获取分享链接
	globalPropValue.eduMaterials.material.getShareCode
		.post({
			id: row.id,
			tenant_id: row.tenant_id,
			campus_id: row.campus_id
		})
		.then((res) => {
			shareLoading.value = false
			if (res.code === 200) {
				shareUrl.value = res.data
			}
		})
		.catch(() => {
			console.log(123)
		})
}

const downloadImage = async (data) => {
	// 获取图片对象和画布对象
	const response = await fetch(data)
	const blob = await response.blob()
	// 创建下载链接
	const url = window.URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = 'shareCode_' + dayjs().unix() + '.png'
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
	// 释放 Blob URL
	window.URL.revokeObjectURL(url)
}

// 视频切片事件
const table_slice = (row) => {
	videoSlice.value.open({
		tenant_id: params.value.tenant_id,
		campus_id: params.value.campus_id,
		material_id: row.id,
		name: row.name
	})
}
// 附件管理事件
const table_attachment = (row) => {
	attachment.value.open({
		tenant_id: params.value.tenant_id,
		campus_id: params.value.campus_id,
		material_id: row.id,
		name: row.name
	})
}
// 评论管理点击事件
const table_remark = (row) => {
	remark.value.open({
		tenant_id: params.value.tenant_id,
		campus_id: params.value.campus_id,
		material_id: row.id,
		name: row.name
	})
}
// 表格点赞事件
const table_like_collect = (row, action_type) => {
	drawer.value.open(row, action_type)
}
// 预览/播放事件
const table_preview = async (row) => {
	if (row.is_video === 1) {
		// 视频播放
		data.value = row
		VideoPlayAtuh.videoInfo.obj = row.obj
		await getVideoPlayAuth(row)
		await nextTick(async () => {
			await getSliceData(row)
			videoVisible.value = true
		})
	} else if (webPreview(row.obj)) {
		// 文件预览
		const routeUrl = router.resolve({
			path: '/webofficepreview',
			query: {
				tenant_id: params.value.tenant_id,
				campus_id: params.value.campus_id,
				material_id: row.id
			}
		})
		window.open(routeUrl.href, '_blank')
	}
}
const getSliceData = async (row) => {
	const res = await globalPropValue.eduMaterials.material.get_video_slice_list.get({
		tenant_id: row.tenant_id,
		campus_id: row.campus_id,
		material_id: row.id
	})
	if (res.code === 200) {
		sliceList.value = res.data
		progressMarkers.value = res.data?.map((v) => {
			return {
				offset: v.time_begin,
				isCustomized: true,
				coverUrl: v.cover_url,
				title: v.name,
				describe: v.description
			}
		})
	}
}

// 获取视频播放凭证
const getVideoPlayAuth = async (row) => {
	const res = await globalPropValue.eduMaterials.material.get_video_play_atuh.post({
		tenant_id: tenantId,
		campus_id: campusId,
		material_id: row.id,
		password: ''
	})
	Object.assign(VideoPlayAtuh.videoInfo, res.data)
}
// 删除按钮操作
const table_del = async (row) => {
	const res = await globalPropValue.eduMaterials.material.materials_del.post({
		id: row.id,
		tenant_id: params.value.tenant_id,
		campus_id: params.value.campus_id
	})
	if (res.code === 200) {
		ElMessage({
			type: 'success',
			message: '删除成功'
		})
		// upsearch()
		table.value.refresh()
	} else {
		ElMessage({
			type: 'error',
			message: res.message
		})
	}
}
// 获取学科
const getDisciplineOptions = async () => {
	let res = await globalPropValue.eduDiscipline.discipline.all.get({ tenant_id: tenantId, campus_id: campusId })
	disciplineOptions.list = res.data
}
// 获取课程
const getEduCourse = async () => {
	let res = await globalPropValue.eduCourseSet.course.all.get({ tenant_id: tenantId, campus_id: campusId })
	course.list = res.data
}
// 自定义事件
const handlerSaveSuccess = () => {
	getTreeData()
}
const handlerSuccess = (data, mode) => {
	if (mode === 'add') {
		upsearch()
	} else {
		table.value.refresh()
	}
}
const changeSliceTime = (time) => {
	nextTick(() => {
		videoPlayRef.value.seek(time)
		videoPlayRef.value.play()
	})
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;

	.el-button {
		width: 80%;
	}
}

.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;

	a {
		font-size: 18px;
	}
}

.video_dialog {
	display: flex;
}
</style>
