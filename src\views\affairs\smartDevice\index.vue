<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList.length > 1" label="校区">
						<el-select v-model="params.campus_id" placeholder="请选择校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="设备SN">
						<el-input v-model="params.device_sn" style="width: 150px" placeholder="请输入设备SN" clearable></el-input>
					</el-form-item>
					<el-form-item label="房间名称">
						<el-input v-model="params.room_name" style="width: 150px" placeholder="请输入房间名称" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-link" @click="add">手动绑定</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<el-table-column type="selection" width="50" fixed="left" />
				<el-table-column label="设备ID" prop="device_id_str" width="180" align="center"></el-table-column>
				<el-table-column label="设备SN" prop="device_sn" width="180"></el-table-column>
				<el-table-column label="设备类型" prop="device_type" width="120">
					<template #default="scope">
						<div v-if="scope.row.device_type === 96">门锁设备</div>
						<div v-if="scope.row.device_type === 124">10寸班牌设备</div>
						<div v-if="scope.row.device_type === 123">21寸班牌设备</div>
						<div v-if="scope.row.device_type === 109">会议室预约设备</div>
						<div v-if="scope.row.device_type === 999">其他</div>
					</template>
				</el-table-column>
				<el-table-column label="绑定位置" prop="position" min-width="200">
					<template #default="scope">
						<div v-if="scope.row.position">{{ scope.row.position.name }}</div>
					</template>
				</el-table-column>
				<el-table-column label="关联空间ID" prop="tree_id_str " width="180">
					<template #default="scope">
						<div v-if="scope.row.tree_id_str > 0">{{ scope.row.tree_id_str }}</div>
						<div v-else>-</div>
					</template>
				</el-table-column>
				<el-table-column label="空间标签ID" prop="tag_id_str " width="180">
					<template #default="scope">
						<div v-if="scope.row.tag_id_str > 0">{{ scope.row.tag_id_str }}</div>
						<div v-else>-</div>
					</template>
				</el-table-column>
				<el-table-column label="关联会议室ID" prop="meeting_id_str" width="180">
					<template #default="scope">
						<div v-if="scope.row.meeting_id_str > 0">{{ scope.row.meeting_id_str }}</div>
						<div v-else>-</div>
					</template>
				</el-table-column>
				<el-table-column label="绑定时间" prop="created_at" min-width="180"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="200">
					<template #default="scope">
						<el-button plain type="primary" size="small" @click="open(scope.row, scope.$index)">远程开门</el-button>
						<el-dropdown>
							<span class="el-dropdown-link">
								<el-button plain size="small" icon="el-icon-arrow-down">更多操作</el-button>
							</span>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item v-if="scope.row.device_type === 96" @click.native="temporaryPwd(scope.row)"
										>生成临时开门密码</el-dropdown-item
									>
									<el-dropdown-item divided @click.native="unbindDialog(scope.row)"
										><span style="color: var(--el-color-danger)">解绑设备</span></el-dropdown-item
									>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</template>
				</el-table-column>
				<template v-slot:selectAction>
					<el-button size="small" :disabled="scanStatus !== true" type="success" @click="tableBatchScan"
						>生成开门二维码</el-button
					>
				</template>
			</scTable>
		</el-main>
	</el-container>
	<el-dialog v-model="dialog.unbind" title="解绑并重置" width="500" center :before-close="closeUnbind">
		<p v-if="countdown > 0" class="countdown">
			<span>{{ countdown }}</span>
		</p>
		<p v-else style="font-size: 48px; text-align: center; color: #f53f3f">
			<el-icon><el-icon-warning /></el-icon>
		</p>

		<p style="text-align: center; font-size: 14px">解绑后，数据将被清除，无法使用。确认解绑并重置？</p>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="closeUnbind">取消</el-button>
				<el-button type="danger" :disabled="countdown > 0" @click="unbind">确认</el-button>
			</div>
		</template>
	</el-dialog>
	<el-dialog v-model="dialog.pwd" title="临时密码" width="500" center>
		<h1 style="text-align: center; font-size: 48px; line-height: 60px; color: var(--el-color-danger)">
			{{ pwdInfo.pwd }}
		</h1>
		<p style="width: 90%; padding-left: 10%; font-size: 12px; line-height: 20px; color: var(--el-color-info)">
			1. 密码有效期：<span style="font-weight: bold; color: var(--el-color-danger)">{{ pwdInfo.expired }}</span>
			分钟，将在
			<span style="font-weight: bold; color: var(--el-color-danger)">{{ pwdInfo.effective_end_time }}</span>
			以后失效，<br />2. 请妥善使用，切勿随意告知给别人。
		</p>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" plain @click="copyPwd(pwdInfo)">复制密码</el-button>
			</div>
		</template>
	</el-dialog>
	<qrcode-dialog
		v-if="dialog.qrcode"
		ref="qrcodeDialog"
		:params="params"
		@closed="dialog.qrcode = false"
	></qrcode-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import { ElMessageBox } from 'element-plus'
import qrcodeDialog from './qrcode.vue'
import useClipboard from 'vue-clipboard3'

const { campusId, tenantId, campusInfo, tenantInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		device_sn: null,
		room_name: null
	}
}
export default {
	name: 'smartDevice',
	components: { qrcodeDialog },
	data() {
		return {
			unbindData: null,
			timer: null,
			countdown: 5,
			dialog: {
				save: false,
				unbind: false,
				qrcode: false,
				pwd: false
			},
			pwdInfo: {},
			unbindDisable: true,
			scanStatus: false,
			apiObj: this.$API.device.list,
			selection: [],
			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			disciplineOptions: []
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.$SET_campus_id(val)
			},
			immediate: true
		}
	},
	computed: {},
	async created() {
		clearInterval(this.timer)
		this.timer = null
	},
	unmounted() {
		clearInterval(this.timer)
		this.timer = null
	},
	methods: {
		async copyPwd(pwdInfo) {
			try {
				let encoding =
					'临时开门密码：' +
					pwdInfo.pwd +
					'；密码有效期：' +
					pwdInfo.expired +
					'分钟，过期时间：' +
					pwdInfo.effective_end_time
				const { toClipboard } = useClipboard()
				await toClipboard(encoding)
				this.$message.success('复制成功')
			} catch (error) {
				this.$message.error('复制失败')
			}
		},
		async temporaryPwd(row) {
			var reqData = {
				device_sn: row.device_sn,
				tenant_id: row.tenant_id,
				campus_id: row.campus_id
			}
			var res = await this.$API.device.temporaryPwd.post(reqData)
			if (res.code === 200) {
				this.$message.success('生成成功')
				this.dialog.pwd = true
				this.pwdInfo = res.data
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		//添加
		add() {
			ElMessageBox.prompt('请输入设备SN', '手动绑定', {
				confirmButtonText: '提交',
				cancelButtonText: '取消'
			})
				.then(async ({ value }) => {
					var res = await this.$API.device.bind.post({
						device_sn: value,
						tenant_id: this.params.tenant_id,
						campus_id: this.params.campus_id
					})
					if (res.code === 200) {
						this.$message.success('绑定成功')
						this.upsearch()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
				.catch(() => {})
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		closeUnbind() {
			clearInterval(this.timer)
			this.timer = null
			this.$nextTick(() => {
				this.dialog.unbind = false
			})
		},
		unbindDialog(row) {
			this.dialog.unbind = true
			this.unbindData = row
			//倒计时
			clearInterval(this.timer)
			this.timer = null

			this.countdown = 5
			this.timer = setInterval(() => {
				this.countdown--
				if (this.countdown <= 0) {
					clearInterval(this.timer)
					this.timer = null
				}
			}, 1000)
		},
		//删除
		async unbind() {
			if (this.countdown > 0 || this.unbindData === null) {
				return
			}
			clearInterval(this.timer)
			this.timer = null
			var reqData = {
				device_sn: this.unbindData.device_sn,
				tenant_id: this.unbindData.tenant_id,
				campus_id: this.unbindData.campus_id
			}
			var res = await this.$API.device.unbind.post(reqData)
			if (res.code === 200) {
				this.$message.success('解绑成功')
				this.dialog.unbind = false
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		async open(row) {
			//
			var reqData = {
				device_id: row.device_id_str,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			ElMessageBox.confirm('是否远程打开场室门禁？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$API.fieldRoom.openDoorByDeviceId.post(reqData)
				if (res.code === 200) {
					this.$message.success('远程开门成功')
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			this.$API.personnel.staff.status.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//批量删除
		async tableBatchScan() {
			if (this.selection.length <= 0) {
				return
			}
			var option = this.selection.map((item) => {
				return {
					device_sn: item.device_sn,
					name: item.position?.name,
					relate_id: item.relate_id,
					scan_info: `{"type":"openDoor","content":{"device_sn":"${item.device_sn}"}}`
				}
			})
			this.dialog.qrcode = true
			this.$nextTick(() => {
				this.$refs.qrcodeDialog.open(option)
			})
		},
		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},

		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
			if (selection.length > 0) {
				this.scanStatus = true
			} else {
				this.scanStatus = false
			}
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null

			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}

			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped>
.countdown {
	text-align: center;
	padding-bottom: 10px;
	span {
		width: 45px;
		height: 45px;
		border: 3px solid var(--el-color-error);
		border-radius: 50%;
		display: inline-block;
		line-height: 40px;
		font-size: 18px;
		font-weight: bold;
		color: var(--el-color-error);
		text-align: center;
	}
}
</style>
