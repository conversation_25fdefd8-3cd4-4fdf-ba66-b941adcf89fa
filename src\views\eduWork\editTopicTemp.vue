<template>
	<div>
		<el-form-item label="题目类型" prop="topic_type">
			<el-radio-group v-model="topicForm.obj.topic_type" @change="changeTopicType">
				<el-radio :label="1">判断题</el-radio>
				<el-radio :label="2">单选题</el-radio>
				<el-radio :label="3">多选题</el-radio>
			</el-radio-group>
		</el-form-item>
		<el-form-item label="分值" prop="topic_score">
			<el-input v-model="topicForm.obj.topic_score" type="number"></el-input>
		</el-form-item>
		<el-form-item label="题目" prop="topic_name">
			<textEditor v-model:value="topicForm.obj.topic_name" height="140px"></textEditor>
		</el-form-item>
		<el-form-item label="题目选项" prop="topic_desc">
			<div>
				<el-button v-if="topicForm.obj.topic_type != 1" size="default" type="primary" :icon="CirclePlus" @click="addAnswer">增加选项</el-button>
			</div>
			<el-row :gutter="10" style="width: 100%">
				<el-col :span="4" style="min-width: 70px"><span>选项编号</span></el-col>
				<el-col :span="14" style="min-width: 70px"><span>选项内容</span></el-col>
				<el-col :span="4" style="min-width: 70px"><span>正确答案</span></el-col>
				<el-col :span="2"></el-col>
			</el-row>
			<template v-if="topicForm.obj.topic_desc">
				<el-row
					v-for="(item, index) in topicForm.obj.topic_desc"
					:key="index"
					:gutter="10"
					style="width: 100%; margin-top: 10px"
				>
					<el-col :span="4" style="min-width: 70px"
						><span v-if="topicForm.obj.topic_type == 1">{{ item.serial }}</span
						><el-input v-else v-model="item.serial"></el-input
					></el-col>
					<el-col :span="14" style="min-width: 70px"
						><span v-if="topicForm.obj.topic_type == 1">{{ item.desc }}</span
						><textEditor v-else v-model:value="item.desc" height="100px"></textEditor
					></el-col>
					<el-col :span="4" style="min-width: 70px"
						><el-switch
							v-model="item.is_right"
							:active-value="1"
							:inactive-value="0"
							@change="changeRight(index, $event)"
					/></el-col>
					<el-col :span="2"
						><el-button v-if="index > 1" type="danger" :icon="Delete" circle @click="delAnswer(index)"
					/></el-col>
				</el-row>
			</template>
		</el-form-item>
	</div>
</template>

<script setup>
import { reactive, watch, defineExpose, defineProps, defineEmits } from 'vue'
import textEditor from '@/components/textEditor'
import { Delete,CirclePlus } from '@element-plus/icons-vue'
const props = defineProps({
	topicObj: {
		type: Object,
		default: () => {
			return {}
		}
	}
})
const emit = defineEmits()
const topicForm = reactive({
	obj: props.topicObj
})
let optionsNumber = [
	'A',
	'B',
	'C',
	'D',
	'E',
	'F',
	'G',
	'H',
	'I',
	'J',
	'K',
	'L',
	'M',
	'N',
	'O',
	'P',
	'Q',
	'R',
	'S',
	'T',
	'W',
	'X',
	'Y',
	'Z'
]
const changeTopicType = () => {
	if (topicForm.obj.topic_type == 1) {
		topicForm.obj.topic_desc = [
			{
				serial: 'A',
				desc: '对',
				is_right: 1
			},
			{
				serial: 'B',
				desc: '错',
				is_right: 0
			}
		]
	}
}
const addAnswer = () => {
	let index = topicForm.obj.topic_desc.length
	topicForm.obj.topic_desc.push({
		serial: optionsNumber[index],
		desc: '',
		is_right: 0
	})
}
const changeRight = (index, val) => {
	if (topicForm.obj.topic_type == 3) {
		return
	}
	if (val == 1) {
		topicForm.obj.topic_desc.forEach((v) => {
			v.is_right = 0
		})
		topicForm.obj.topic_desc[index].is_right = 1
	}
}
const delAnswer = (index) => {
	topicForm.obj.topic_desc.splice(index, 1)
}
const initData = (data) => {
	Object.keys(topicForm.obj).forEach((key) => {
		topicForm.obj[key] = data
	})
}
watch(
	() => topicForm.obj,
	(newValue, oldValue) => {
		emit('update:topicObj', newValue)
	}
)
watch(props, (val) => {
	topicForm.obj = val.topicObj
})
defineExpose({
	initData
})
</script>

<style></style>
