<template>
	<el-container class="d-form">
		<el-aside class="left-container" width="250px">
			<el-container>
				<div>
					<designerLeft></designerLeft>
				</div>
			</el-container>
		</el-aside>
		<el-container class="center-container">
			<el-main>
				<designerFormHeader v-bind="$props" @preview="() => (previewVisible = true)"
					@uploadJson="() => (uploadJsonVisible = true)" @generateJson="handleGenerateJson"
					@generateCode="handleGenerateCode" @clearable="handleClearable">
				</designerFormHeader>
				<div style="display: flex;justify-content: center;">
					<designerCenter ref="widgetFormRef" v-model:widgetForm="widgetForm"
						v-model:widgetFormSelect="widgetFormSelect">
					</designerCenter>
				</div>
			</el-main>
		</el-container>
		<el-aside class="right-container" width="300px">
			<el-container>
				<el-header>
					<div class="right-tab" :class="{ active: rightTab === 'widget' }" @click="rightTab = 'widget'">字段属性
					</div>
					<!-- <div class="right-tab" :class="{ active: rightTab === 'form' }" @click="rightTab = 'form'">表单属性
					</div> -->
				</el-header>
				<el-main class="right-content">
					<!-- <formConfig v-if="rightTab === 'form'" v-model:config="widgetForm.config"></formConfig> -->
					<widgetConfig v-if="rightTab === 'widget'" v-model:select="widgetFormSelect"
						v-model:list="widgetForm.list"></widgetConfig>
				</el-main>
			</el-container>
		</el-aside>
		<!-- 预览弹窗 -->
		<el-dialog v-model="previewVisible" title="预览" :width="500">
			<div style="min-height: 450px;max-height: 650px;overflow-y: auto;background: #f6f8f9;">
				<designerFormPreview v-if="previewVisible" ref="designerFormPreviewRef" :data="widgetForm" />
			</div>
			<!-- <template #footer>
                <el-button @click="handleReset">重置</el-button>
                <el-button type="primary" @click="handleGetData">获取数据</el-button>
            </template> -->

			<!-- <el-dialog v-model="dataJsonVisible" title="获取数据" :width="800">
                <CodeEditor :value="dataJsonTemplate" language="json" readonly />

                <template #footer>
                    <el-button @click="() => (dataJsonVisible = false)">取消</el-button>
                    <el-button type="primary" @click="handleCopyClick(dataJsonTemplate)">Copy</el-button>
                </template>
            </el-dialog> -->
		</el-dialog>
	</el-container>
</template>
<script>
import designerLeft from './designerFormLeft.vue'
import designerFormHeader from './designerFormHeader.vue'
import designerCenter from './designerFormCenter.vue'
import designerRight from './designerFormRight.vue'
import designerFormPreview from './designerFormPreview.vue'
import formConfig from './component/formConfig.vue'
import widgetConfig from './component/widgetConfig.vue'
import { defaultsDeep } from 'lodash'
import { CodeType, widgetForm } from './components'
import { defineComponent, reactive, toRefs, watch, watchEffect } from 'vue'

export default defineComponent({
	name: 'DesignerForm',
	components: {
		designerLeft,
		designerFormHeader,
		designerCenter,
		designerRight,
		designerFormPreview,
		formConfig,
		widgetConfig
	},
	props: {
		preview: {
			type: Boolean,
			default: true
		},
		generateCode: {
			type: Boolean,
			default: true
		},
		generateJson: {
			type: Boolean,
			default: true
		},
		uploadJson: {
			type: Boolean,
			default: true
		},
		clearable: {
			type: Boolean,
			default: true
		}
	},
	emits: ['saveForm'],
	setup(props, context) {
		const emit = context.emit
		const state = reactive({
			codeType: CodeType,
			widgetForm: JSON.parse(JSON.stringify(widgetForm)),
			widgetFormSelect: undefined,
			designerFormPreviewRef: null,
			rightTab: 'widget',
			previewVisible: false,
			uploadJsonVisible: false,
			dataJsonVisible: false,
			dataCodeVisible: false,
			generateJsonVisible: false,
			generateCodeVisible: false,
			generateJsonTemplate: JSON.stringify(widgetForm, null, 2),
			dataJsonTemplate: '',
			dataCodeTemplate: '',
			codeLanguage: CodeType.Vue,
			jsonEg: JSON.stringify(widgetForm, null, 2)
		})
		const handleUploadJson = () => {
			try {
				state.widgetForm.list = []
				defaultsDeep(state.widgetForm, JSON.parse(state.jsonEg))

				if (state.widgetForm.list) {
					state.widgetFormSelect = state.widgetForm.list[0]
				}

				state.uploadJsonVisible = false
				ElMessage.success('上传成功')
			} catch (error) {
				ElMessage.error('上传失败')
			}
		}

		const handleCopyClick = (text) => {
			copy(text)
			ElMessage.success('Copy成功')
		}

		const handleGetData = () => {
			state.designerFormPreviewRef.getData().then((res) => {
				state.dataJsonTemplate = JSON.stringify(res, null, 2)
				state.dataJsonVisible = true
			})
		}

		const handleGenerateJson = () => {
			state.generateJsonTemplate = JSON.stringify(state.widgetForm, null, 2)
			state.generateJsonVisible = true
		}

		const handleGenerateCode = () => {
			state.codeLanguage = CodeType.Vue
			state.dataCodeVisible = true
		}

		watchEffect(() => {
			if (state.dataCodeVisible) {
				state.dataCodeTemplate = generateCode(state.widgetForm, state.codeLanguage, PlatformType.Element)
			}
		})
		const handleClearable = () => {
			let defaultForm = {
				list: [{
					label: '头图',
					type: 'headImage',
					key: 'headImage',
					options: {
						defaultValue: '',
						type: 'upload',
					}
				},
				{
					label: '标题',
					type: 'title',
					key: 'title',
					options: {
						defaultValue: currentData.value.title,
					}
				}, {
					label: '',
					type: 'seat',
					key: 'seat',
					options: {
						defaultValue: '',
					}
				}],
				config: {
					size: 'default',
					hideRequiredAsterisk: false,
					labelWidth: 100,
					labelPosition: 'top'
				}
			}
			state.widgetForm.list = []
			defaultsDeep(state.widgetForm, JSON.parse(JSON.stringify(defaultForm)))
			state.widgetFormSelect = undefined
		}
		const handleSaveForm = () => {
			console.log(state.widgetForm, '保存的表单数据')
			emit('saveForm', state.widgetForm)
		}

		const handleReset = () => {
			state.designerFormPreviewRef.reset()
		}

		const getJson = () => {
			return state.widgetForm
		}
		const positionType = {
			1: 'left',
			2: 'right',
			3: 'top'
		}
		const currentData = ref({})
		const setJson = (data) => {
			currentData.value = data
			let jsonData
			if (data.form_config) {
				jsonData = JSON.parse(data.form_config)
				// 防止只有头图和标题时不能添加其他组件,添加占位
				jsonData.list.push({
					label: '',
					type: 'seat',
					key: 'seat',
					options: {
						defaultValue: '',
					}
				})
			} else {
				jsonData = {
					list: [{
						label: '头图',
						type: 'headImage',
						key: 'headImage',
						options: {
							defaultValue: '',
							type: 'upload',
						}
					},
					{
						label: '标题',
						type: 'title',
						key: 'title',
						options: {
							defaultValue: data.title,
						}
					}, {
						label: '',
						type: 'seat',
						key: 'seat',
						options: {
							defaultValue: '',
						}
					}],
					config: {
						size: 'default',
						hideRequiredAsterisk: false,
						labelWidth: 100,
						labelPosition: 'top'
					}
				}
			}
			state.widgetForm.list = []
			defaultsDeep(state.widgetForm, {
				list: [...jsonData.list],
				config: { ...jsonData.config }
			})
			if (jsonData.length) {
				// state.widgetFormSelect = jsonData[0]
			}
		}

		const getTemplate = (codeType) => generateCode(state.widgetForm, codeType, PlatformType.Element)

		const clear = () => handleClearable()

		return {
			...toRefs(state),
			handleUploadJson,
			handleCopyClick,
			handleGetData,
			handleGenerateJson,
			handleGenerateCode,
			handleClearable,
			handleSaveForm,
			handleReset,
			getJson,
			setJson,
			getTemplate,
			clear
		}
	}
})
</script>

<style lang="scss" scoped>
$primary-color: #2745b2;

.d-form {
	background-color: #f6f8f9;

	.left-container {
		background-color: #fff;
		border: none;
	}

	.center-container {
		// margin: 0 15px;
		padding-right: 10px;
	}

	.right-container {
		background-color: #fff;
		border: none;
	}
}

.right-tab {
	height: 41px;
	line-height: 41px;
	display: inline-block;
	width: 100%;
	text-align: center;
	font-size: 14px;
	font-weight: 500;
	position: relative;
	cursor: pointer;

	&.active {
		border-bottom: 2px solid $primary-color;
	}
}

.right-content {
	padding: 10px;
	overflow: auto;
	max-height: calc(100vh - 150px);
}
</style>

<style lang="scss">
.d-form {
	.el-dialog__body {
		padding: 15px 20px;
	}

	.el-dialog__footer {
		border-top: 1px solid #f2f6fc;
		padding: 10px 15px;
	}

	.config-content {
		.el-radio-group {
			margin-top: 10px;
			vertical-align: super;
		}

		.el-radio {
			margin-bottom: 10px;
		}
	}

	.el-rate {
		margin-top: 6px;
	}

	.el-checkbox {
		line-height: 32px;
	}
}
</style>
