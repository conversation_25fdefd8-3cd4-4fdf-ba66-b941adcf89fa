<template>
	<cusTable ref="cusTable" :apiObj="list.apiObj" :params="params" row-key="id" stripe>
		<template #searchForm>
			<cusForm ref="formref" v-model="params" :config="config" :inline="true" style="margin-right: 12px"> </cusForm>
			<el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
			<el-button type="primary" icon="el-icon-refresh" @click="reload">重置</el-button>
		</template>
		<template #searchTool>
			<div>
				<el-button type="primary" icon="el-icon-plus" @click="add">新增学校</el-button>
			</div>
		</template>
		<template #content>
			<!-- <el-table-column type="selection" width="50"></el-table-column> -->
			<el-table-column label="LOGO" prop="tenant_logo" width="80" fixed="left">
				<template #default="scope">
					<cusImage
						loading="lazy"
						:lazy="true"
						fit="contain"
						style="width: 50px; height: 50px"
						:src="scope.row.tenant_logo"
						:preview-src-list="[scope.row.tenant_logo]"
						preview-teleported
					>
					</cusImage>
				</template>
			</el-table-column>
			<el-table-column
				label="学校名称"
				prop="tenant_name"
				width="200"
				fixed="left"
				show-overflow-tooltip
			></el-table-column>
			<el-table-column label="学校编码" prop="tenant_code" width="150"> </el-table-column>
			<el-table-column label="联系人" prop="contacts" width="150" show-overflow-tooltip></el-table-column>
			<el-table-column label="联系电话" prop="phone" width="150" show-overflow-tooltip> </el-table-column>
			<el-table-column label="联系邮箱" prop="email" width="180" show-overflow-tooltip></el-table-column>
			<el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
			<el-table-column label="状态" align="center" width="140">
				<template #default="scope">
					<el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
					<el-tag v-if="scope.row.status === -1" type="danger">停用</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="操作" align="center" width="200" fixed="right">
				<template #default="scope">
					<el-button type="primary" size="small" link @click="edit(scope.row)">编辑</el-button>
					<el-button type="warning" size="small" link @click="permission(scope.row)">权限配置</el-button>
					<el-button v-if="scope.row.status === -1" type="success" link size="small" @click="statusChange(scope.row, 1)"
						>启用</el-button
					>
					<el-button v-if="scope.row.status === 1" type="danger" link size="small" @click="statusChange(scope.row, -1)"
						>停用</el-button
					>
				</template>
			</el-table-column>
		</template>
	</cusTable>
	<save-dialog ref="Dialog" @close="refeshData"></save-dialog>
	<permission-dialog
		v-if="dialog.permission"
		ref="permissionDialog"
		@closed="dialog.permission = false"
	></permission-dialog>
</template>

<script>
import saveDialog from './save'
import { ElMessageBox } from 'element-plus'
import cusImage from '@/components/custom/cusImage.vue'
import permissionDialog from './permission.vue'

const defaultData = () => {
	return {
		id: null,
		name: null,
		tenant_id: null
	}
}

const config = {
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: '学校名称',
			name: 'name',
			value: '',
			component: 'input',
			options: {
				placeholder: '请输入学校名称'
			}
		}
	]
}

export default {
	name: 'CampusManagement',
	components: {
		permissionDialog,
		cusImage,
		saveDialog
	},
	data() {
		return {
			list: {
				apiObj: this.$API.tenant.list
			},
			dialog: {
				permission: false
			},
			params: defaultData(),
			searchFormData: {},
			config
		}
	},
	created() {
		this.params.tenant_id = this.$TOOL.data.get('USER_INFO').tenant_id
	},
	methods: {
		add() {
			this.$refs.Dialog?.open()
		},
		edit(row) {
			this.$refs.Dialog?.open(row)
		},
		refeshData() {
			this.$refs.cusTable.$.refs.table.refresh()
		},
		search() {
			this.$refs.cusTable.$.refs.table.upData(this.params)
		},
		reload() {
			this.$refs.cusTable.$.refs.table.reload(defaultData())
			this.params = defaultData()
		},
		//权限设置
		permission(row) {
			this.dialog.permission = true
			this.$nextTick(() => {
				this.$refs.permissionDialog.open(row.id)
			})
		},
		//状态改变
		statusChange(row, status) {
			let query = {
				id: row.id,
				status: status,
				tenant_id: row.tenant_id
			}
			let title
			if (status === 1) {
				title = '确定对该学校启用系统吗'
			} else {
				title =
					'确定设置该学校停用系统吗？<p style="color: red"><strong>停用后该学校将不能登录使用系统的任何功能！</strong></p>'
			}
			ElMessageBox.confirm(title, '谨慎操作', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
				dangerouslyUseHTMLString: true
			}).then(async () => {
				this.$API.tenant.edit.post(query).then((res) => {
					if (res.code === 200) {
						this.$message.success('操作成功')
						this.refeshData()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
			})
		}
	}
}
</script>

<style scoped></style>
