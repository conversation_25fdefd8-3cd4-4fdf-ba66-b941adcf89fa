<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<el-select
					v-if="CampusManagementList.length > 1"
					v-model="params.campus_id"
					placeholder="校区"
					filterable
					style="margin-right: 15px"
					@change="campusChange"
				>
					<el-option
						v-for="item in CampusManagementList"
						:key="item.code"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
				<!--				<cusSelectSemester
					v-model="params.semester_id"
					:params="params"
					:width="'214px'"
					clearable
					placeholder="请选择学年"
					style="margin-right: 15px"
					@semesterChange="semesterChange"
				/>-->
				<cusSelectSemester
					v-model="params.semester_id"
					:params="params"
					:show-default-value="true"
					:width="'214px'"
					clearable
					style="margin-right: 15px"
					@semesterChange="semesterChange"
				/>
				<el-select
					v-model="params.grade_id"
					clearable
					placeholder="请选择年级"
					style="margin-right: 15px"
					@change="infoChange"
				>
					<el-option v-for="item in gradeList" :key="item.id" :label="item.grade_name" :value="item.id"></el-option>
				</el-select>
				<el-input
					v-model="params.name"
					placeholder="请输入规则名称"
					clearable
					style="width: 214px"
					@change="nameChange"
				></el-input>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-CopyDocument" @click="handleCopy">拷贝规则</el-button>
				<el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增规则</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :apiObj="apiObj" :params="params">
				<el-table-column prop="rule_name" label="规则名称" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column prop="attend_day" label="上课天数" show-overflow-tooltip></el-table-column>
				<el-table-column prop="created_at" label="创建时间" width="200"></el-table-column>
				<el-table-column prop="grade_name" label="关联年级" width="200"></el-table-column>
				<el-table-column prop="periods_name" label="关联时段" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column prop="status" label="状态" width="100">
					<template #default="scope">
						{{ scope.row.status === 1 ? '配置中' : '已配置' }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="200">
					<template #default="scope">
						<el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
						<el-button link type="primary" size="small" @click="handleSet(scope.row)">配置</el-button>
						<el-button link type="danger" size="small" @click="handleDel(scope.row)">删除</el-button>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<rulesSave ref="rulesSaveDialog" :params="params" @success="rulesSaveSuccess"></rulesSave>
	</el-container>
</template>
<script setup>
import { getCurrentInstance, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import cusTom from '@/utils/cusTom'
import { ElMessage, ElMessageBox } from 'element-plus'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'
import rulesSave from './rulesSave.vue'

const { campusInfo, tenantId, campusId } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: null,
		grade_id: null,
		name: null
	}
}
const router = useRouter()
const CampusManagementList = ref(campusInfo)
const params = ref(defaultParams())
const apiObj = ref(globalPropValue.eduSchedule.rules.list)
const table = ref(null)
const rulesSaveDialog = ref(null)
const campusChange = (val) => {
	params.value.campus_id = val
	table.value.upData(params.value)
}
const semesterChange = (val) => {
	params.value.semester_id = val
	nextTick(() => {
		getGradeData()
		table.value.upData(params.value)
	})
	
}
const infoChange = (val) => {
	params.value.grade_id = val
	table.value.upData(params.value)
}
const nameChange = (val) => {
	params.value.name = val
	table.value.upData(params.value)
}
const handleAdd = () => {
	rulesSaveDialog.value.open('add')
}

const handleCopy = () => {
	rulesSaveDialog.value.open('copy')
}
const handleEdit = (row) => {
	rulesSaveDialog.value.open('edit').setData(row)
}
const handleSet = (row) => {
	router.push({
		name: 'rulesSet',
		query: {
			id: row.id
		}
	})
	console.log(row, 'peizhi')
}
const rulesSaveSuccess = (data, mode) => {
	if (mode === 'add') {
		table.value.upData(params.value)
	} else {
		table.value.refresh()
	}
}
const handleDel = (row) => {
	ElMessageBox.confirm('确定删除该规则吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		globalPropValue.eduSchedule.rules.del
			.post({ id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id })
			.then((res) => {
				if (res.code == 200) {
					ElMessage.success('删除成功')
					// table.value.upData(params.value)
					table.value.refresh()
				} else {
					ElMessage.error(res.message)
				}
			})
	})
}
const gradeList = ref([])
const getGradeData = () => {
	globalPropValue.eduGradeClass.grade.all.get(params.value).then((res) => {
		gradeList.value = res.data
	})
}
</script>

<style lang="scss" scoped></style>
