<template>
	<el-dialog v-model="visible" title="人员绑卡" width="500" destroy-on-close>
		<div v-if="activeType === 0" class="bind">
			<div class="cardInput">
				<el-input
					ref="cardInput"
					v-model="cardNo"
					autofocus
					autocomplete="off"
					style="opacity: 0"
					@blur="onblur"
					@focus="onfocus"
					@keydown.enter="bindCard"
				/>
			</div>
			<p @click="inputFoucs">
				<el-icon style="font-size: 150px; cursor: pointer; color: var(--el-color-primary)"
					><sc-icon-bindCard
				/></el-icon>
			</p>
			<p v-if="isFoucs">
				<span style="color: var(--el-color-success)"
					><el-icon><el-icon-WarningFilled /></el-icon> 请将IC放在读卡器指定区域</span
				>
			</p>
			<p v-else>
				<span style="color: var(--el-color-danger)"
					><el-icon><el-icon-WarningFilled /></el-icon> 鼠标点击图片进入刷卡模式</span
				>
			</p>
		</div>
		<div v-if="activeType === 1" class="bind">
			<el-result v-if="bindActive === false" icon="success" title="绑定成功">
				<template #extra>
					<el-button type="primary" @click="close">关闭</el-button>
				</template>
			</el-result>
			<div v-else style="line-height: 40px; padding-top: 10px; font-size: 18px">
				<p>卡号：{{ realCardNo }}</p>
				<el-button type="primary" plain @click="unbind">解绑</el-button>
			</div>
		</div>
	</el-dialog>
</template>

<script>
export default {
	data() {
		return {
			fileurl6: '',
			activeType: 0,
			form: {
				id: null,
				tenant_id: null,
				campus_id: null
			},
			isSaveing: false,
			visible: false,
			bindActive: false,
			isFoucs: false,
			cardNo: null,
			userInfo: null,
			realCardNo: ''
		}
	},
	created() {},
	mounted() {},
	watch: {},
	methods: {
		async bindCard() {
			if (this.cardNo === '') {
				return
			}
			//绑卡操作
			var res = await this.$API.eduStudent.bindCard.post({
				student_id: this.userInfo.id,
				tenant_id: this.userInfo.tenant_id,
				campus_id: this.userInfo.campus_id,
				card_no: this.cardNo
			})
			if (res.code === 200) {
				this.$message.success('操作成功')
				this.activeType = 1
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		async unbind() {
			//绑卡操作
			var res = await this.$API.eduStudent.bindCard.post({
				student_id: this.userInfo.id,
				tenant_id: this.userInfo.tenant_id,
				campus_id: this.userInfo.campus_id,
				card_no: ''
			})
			if (res.code === 200) {
				this.$message.success('操作成功')
				this.visible = false
				this.$emit('success', this.form, this.mode)
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		onfocus() {
			this.isFoucs = true
		},
		onblur() {
			this.isFoucs = false
		},
		inputFoucs() {
			this.$nextTick(() => {
				this.cardNo = ''
				this.$refs.cardInput.focus()
			})
		},
		//显示
		open(mode = 'edit') {
			this.mode = mode
			this.visible = true
			return this
		},
		//表单注入数据
		setData(data) {
			this.userInfo = data
			if (data.moredian_card_no === '') {
				this.activeType = 0
				this.bindActive = false
			} else {
				this.activeType = 1
				this.realCardNo = data.moredian_card_no
				this.bindActive = true
			}
		},
		close() {
			this.visible = false
			this.$emit('success', this.form, this.mode)
		}
	}
}
</script>

<style scoped lang="scss">
.bind {
	text-align: center;
	line-height: 35px;
	padding-top: 10px;
	padding-bottom: 10px;
	font-size: 12px;
	img {
		margin-top: 20px;
	}
}
.cardInput {
	height: 0;
	overflow: hidden;
	line-height: 0;
	font-size: 12px;
}
.el-result {
	padding: unset !important;
}
:deep(.el-result__title) {
	margin-top: 10px !important;
}
:deep(.el-result__extra) {
	margin-top: 10px !important;
}
</style>
