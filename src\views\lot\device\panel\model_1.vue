<template>
	<div>
		<el-select v-model="channel" clearable placeholder="请选择空调通道">
			<el-option
				v-for="(item, index) in channelMap"
				:key="index"
				:label="'空调通道 - ' + item"
				:value="item"
			></el-option>
		</el-select>

		<el-card v-if="channel && this.channelPropertiesList[channel]" class="card-item" shadow="hover">
			<el-form label-position="left">
				<el-form-item label="开关">
					<el-switch
						v-model="this.channelPropertiesList[channel].PowerSwitch.value"
						active-value="1"
						inactive-value="0"
						inline-prompt
						:active-text="this.channelPropertiesList[channel].PowerSwitch.properties_info.type_spec.specs[1]"
						:inactive-text="this.channelPropertiesList[channel].PowerSwitch.properties_info.type_spec.specs[0]"
					></el-switch>
				</el-form-item>
				<el-form-item label="模式" label-position="top">
					<el-row :gutter="20">
						<el-col
							v-for="(item, index) in this.channelPropertiesList[channel].SystemMode.properties_info.type_spec.specs"
							:key="index"
							:span="6"
							class="el-col-item"
							:class="{ selected: this.channelPropertiesList[channel].SystemMode.value == item.value }"
							@click="this.channelPropertiesList[channel].SystemMode.value = item.value"
						>
							<p><cusSvgIcon :iconClass="'mode_' + item.value" /></p>
							<p>{{ item.name }}</p>
						</el-col>
						<!--						<el-col :span="6">
							<cusSvgIcon class="icon_item" iconClass="mode_2" />
						</el-col>
						<el-col :span="6">
							<cusSvgIcon class="icon_item" iconClass="mode_3" />
						</el-col>
						<el-col :span="6">
							<cusSvgIcon class="icon_item" iconClass="mode_4" />
						</el-col>-->
					</el-row>
				</el-form-item>
				<el-form-item label="风速" label-position="top">
					<el-row :gutter="20">
						<el-col
							v-for="(item, index) in this.channelPropertiesList[channel].FanMode.properties_info.type_spec.specs"
							:key="index"
							:span="6"
							class="el-col-item"
							:class="{ selected: this.channelPropertiesList[channel].FanMode.value == item.value }"
							@click="this.channelPropertiesList[channel].FanMode.value = item.value"
						>
							<p><cusSvgIcon :iconClass="'fan_' + item.value" /></p>
							<p>{{ item.name }}</p>
						</el-col>
						<!--						<el-col :span="6">
							<cusSvgIcon class="icon_item" iconClass="mode_2" />
						</el-col>
						<el-col :span="6">
							<cusSvgIcon class="icon_item" iconClass="mode_3" />
						</el-col>
						<el-col :span="6">
							<cusSvgIcon class="icon_item" iconClass="mode_4" />
						</el-col>-->
					</el-row>
				</el-form-item>
				<el-form-item label="设定温度" label-position="top">
					<el-input-number
						v-model="this.channelPropertiesList[channel].TargetTemperature.value"
						:step="1"
						step-strictly
					/>
				</el-form-item>
				<el-form-item style="border-bottom: unset" label="回风温度" label-position="left">
					{{ this.channelPropertiesList[channel].CurrentTemperature.value }}
					<span
						v-if="
							['int', 'float'].includes(
								this.channelPropertiesList[channel].CurrentTemperature.properties_info.type_spec.type
							)
						"
					>
						&nbsp;{{
							this.channelPropertiesList[channel].CurrentTemperature.properties_info.type_spec.specs.unit_symbol
						}}</span
					></el-form-item
				>
			</el-form>
			<template #footer>
				<el-button type="primary" style="width: 100%" plain round @click="setDeviceAttr">设置</el-button>
			</template>
		</el-card>

		<el-card v-else class="card-item" shadow="never">
			<el-empty description="请先选择空调通道"></el-empty>
		</el-card>
	</div>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
	name: 'index',
	props: {
		deviceInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			channelMap: [],
			propertiesList: [],
			channelPropertiesList: {},
			channel: null
		}
	},
	created() {
		this.channelMap = this.deviceInfo.channel.split(',')
		if (this.channelMap.length === 1) {
			this.channel = this.channelMap[0]
		}
		this.channelMap.forEach((item) => {
			this.channelPropertiesList[item] = {}
		})
		console.log(this.channelPropertiesList)
		this.getPropertiesInfo()
	},
	methods: {
		// 属性设置
		setDeviceAttr() {
			/*if (this.writerTslChecked.length === 0) {
				ElMessage.error('请选择要设置的属性！')
				return
			}*/
			if (this.channel === null || !this.channelPropertiesList[this.channel]) {
				ElMessage.error('请选择要设置的通道！')
				return
			}
			console.log()
			let property = []
			Object.keys(this.channelPropertiesList[this.channel]).forEach((key) => {
				let item = this.channelPropertiesList[this.channel][key]
				if (
					item &&
					item.value !== '' &&
					(item.properties_info.access_mode === 2 || item.properties_info.access_mode === 3)
				) {
					property.push({
						tsl_id: item.properties_info.tsl_id,
						value: item.value.toString()
					})
				}
			})
			if (property.length === 0) {
				ElMessage.error('请选择要设置的属性和设置值！')
				return
			}
			let reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id,
				property: property
			}
			this.$LotApi.device.writeDevProperty.post(reqData).then((res) => {
				if (res.code === 200) {
					ElMessage.success('执行成功！')
				}
			})
		},
		getPropertiesInfo() {
			this.loading = true
			// 获取设备属性信息
			this.$LotApi.device.getDeviceTslWithLastValue
				.get({
					id: this.deviceInfo.id,
					tenant_id: this.deviceInfo.tenant_id,
					campus_id: this.deviceInfo.campus_id
				})
				.then((res) => {
					this.loading = false
					if (res.code === 200) {
						this.propertiesList = res.data?.map((item) => {
							if (item.properties_info.type_spec.specs) {
								item.properties_info.type_spec.specs = JSON.parse(item.properties_info.type_spec.specs)
							}
							/*if (item.properties_info.type_spec.type === 'bool' && item.last_value && item.last_value.value) {
								item.last_value.value = item.properties_info.type_spec.specs[item.last_value.value * 1]
							}
							if (item.properties_info.type_spec.type === 'enum' && item.last_value && item.last_value.value) {
								item.last_value.value = item.properties_info.type_spec.specs.find(
									(specsItem) => specsItem.value === item.last_value.value
								)?.name
							}*/
							item.value = item.last_value?.value
							return item
						})
						if (this.deviceInfo.product_info?.panel_type === 1 && this.deviceInfo.channel !== '') {
							let channelMap = this.deviceInfo.channel.split(',')
							if (channelMap.length > 0) {
								let panelCode = []
								this.propertiesList.map((item) => {
									let codeAry = item.properties_info.code.split('_')
									if (channelMap.includes(codeAry[1])) {
										panelCode.push(item)
										this.channelPropertiesList[codeAry[1]][codeAry[0]] = item
									}
								})
								console.log(this.channelPropertiesList)
								this.propertiesList = panelCode
							}
						}
					}
				})
		}
	}
}
</script>

<style scoped lang="scss">
:deep(.el-form-item) {
	border-bottom: 1px solid #eaeaea;
	padding: 10px 0;
}

:deep(.card-item) {
	width: 350px;
	margin-top: 15px;

	.el-card__footer {
		padding: 10px 20px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.el-image {
	color: #ccc;
}

.el-col-item {
	text-align: center;
	line-height: 20px;
	cursor: pointer;
	.svg-icon {
		font-size: 42px;
	}
}

.selected {
	color: var(--el-color-primary);
}
</style>
