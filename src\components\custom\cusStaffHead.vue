<template>
	<div class="custom-image">
		<el-image
			:src="src"
			:alt="alt"
			:fit="fit"
			:infinite="infinite"
			:preview-src-list="previewSrcList"
			:lazy="lazy"
			:preview-teleported="previewTeleported"
			:initial-index="initialIndex"
			:loading="loading"
			:scroll-container="scrollContainer"
			style="width: 100%; height: 100%; border-radius: 50%"
		>
			<template #error>
				<div class="image-slot">
					<img style="width: 50px;height: 50px" class="logo" src="@/assets/img/staff_head.png" />
				</div>
			</template>
		</el-image>
	</div>
</template>

<script>
import { Avatar } from '@element-plus/icons-vue'

export default {
	name: 'CustomHead',
	components: {
		Avatar
	},
	data() {
		return {}
	},
	props: {
		// 图片地址
		src: {
			type: String,
			required: true
		},
		// 图片描述
		alt: {
			type: String,
			default: ''
		},
		// 图片填充模式
		fit: {
			type: String,
			default: 'cover'
		},
		// 图片加载状态
		loading: {
			type: String,
			default: 'lazy' //'lazy'|| 'eager'
		},
		// 是否无限循环预览
		infinite: {
			type: Boolean,
			default: false
		},
		// 预览图片列表
		previewSrcList: {
			type: Array,
			default: () => []
		},
		// 预览图片是否插入至 body 元素
		previewTeleported: {
			type: Boolean,
			default: true
		},
		// 预览图片初始索引
		initialIndex: {
			type: Number,
			default: 0
		},
		// 是否懒加载
		lazy: {
			type: Boolean,
			default: false
		},
		// 滚动容器选择器
		scrollContainer: {
			type: String,
			default: ''
		}
	},
	methods: {
		handleClick() {
			// 处理点击事件，可根据需要自定义逻辑
		}
	}
}
</script>

<style scoped>
.custom-image {
	/* 添加自定义样式，可根据需要进行修改 */
	text-align: center;
}

.image-slot {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	background: var(--el-fill-color-dark);
	color: var(--el-text-color-secondary);
	font-size: 20px;
}
</style>
