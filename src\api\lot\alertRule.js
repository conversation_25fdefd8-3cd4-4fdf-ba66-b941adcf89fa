import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/lot/alertRule/list`,
		name: '获取告警规则列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/lot/alertRule/save`,
		name: '新增或修改告警规则',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/lot/alertRule/del`,
		name: '删除',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	one: {
		url: `${config.API_URL}/lot/alertRule/one`,
		name: '获取告警规则信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	changeStatus: {
		url: `${config.API_URL}/lot/alertRule/changeStatus`,
		name: '修改状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	setRule: {
		url: `${config.API_URL}/lot/alertRule/setRule`,
		name: '规则条件触发条件设置',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	treatedAlert: {
		url: `${config.API_URL}/lot/alertRule/treatedAlert`,
		name: '处理告警',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	batchTreatedAlert: {
		url: `${config.API_URL}/lot/alertRule/batchTreatedAlert`,
		name: '批量处理告警',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	getLog: {
		url: `${config.API_URL}/lot/alertRule/getLog`,
		name: '获取告警日志',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	plate: {
		url: `${config.API_URL}/lot/alertRule/plate`,
		name: '获取7日内告警统计',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	}
}
