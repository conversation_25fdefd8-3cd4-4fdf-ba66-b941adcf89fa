<template>
	<el-dialog v-model="visible" :title="title" width="500" destroy-on-close @close="close">
		<el-form ref="dialogForm" :model="formData" :rules="rules" label-width="auto">
			<el-form-item label="考试日期" prop="examine_date">
				<el-date-picker
					v-model="formData.examine_date"
					type="date"
					placeholder="选择考试日期"
					value-format="YYYY-MM-DD"
				></el-date-picker>
			</el-form-item>
			<el-form-item label="签到时间" prop="sign_time">
				<el-time-picker v-model="formData.sign_time" placeholder="请选择签到时间" format="HH:mm" value-format="HH:mm" />
			</el-form-item>
			<el-form-item label="考试时间" prop="time">
				<el-time-picker
					v-model="formData.time"
					is-range
					range-separator="至"
					start-placeholder="选择考试开始时间"
					end-placeholder="选择考试结束时间"
					format="HH:mm"
					value-format="HH:mm"
				></el-time-picker>
			</el-form-item>
			<el-form-item label="考试科目" prop="course_id">
				<el-select v-model="formData.course_id" placeholder="请选择考试科目">
					<el-option v-for="item in courseList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
				</el-select>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="close">取 消</el-button>
				<el-button type="primary" @click="save">确 定</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const { query } = useRoute()

const props = defineProps({
	courseList: {
		type: Array,
		default: () => []
	}
})
const emit = defineEmits(['saveSuccess'])

const defaultData = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		examine_id: Number(query.id),
		course_id: '',
		examine_date: '',
		time: [],
		begin_time: '',
		end_time: '',
		sign_time: '',
		duration: ''
	}
}

const dialogForm = ref()
const save = () => {
	dialogForm.value.validate((valid) => {
		if (valid) {
			formData.value.begin_time = formData.value.time[0]
			formData.value.end_time = formData.value.time[1]
			// 计算时长
			const startTime = dayjs(`2000-01-01 ${formData.value.begin_time}`) // 补充虚拟日期
			const endTime = dayjs(`2000-01-01 ${formData.value.end_time}`)
			formData.value.duration = endTime.diff(startTime, 'minute') // 单位为minute
			globalPropValue.examine.timeSave.post(formData.value).then((res) => {
				if (res.code === 200) {
					ElMessage({ type: 'success', message: '保存成功' })
					close()
					emit('saveSuccess')
				}
			})
		}
	})
}

const visible = ref(false)
const title = ref('新增时段')
const formData = ref(defaultData())
const rules = ref({
	examine_date: [{ required: true, message: '请选择考试日期', trigger: 'change' }],
	time: [{ required: true, message: '请选择考试时间', trigger: 'change' }],
	course_id: [{ required: true, message: '请选择考试科目', trigger: 'change' }]
})
const open = (type, data) => {
	title.value = type === 'add' ? '新增时段' : '编辑时段'
	formData.value = defaultData()
	if (type === 'edit') {
		console.log(data)
		formData.value = data
		formData.value.time = [data.begin_time, data.end_time]
	}
	visible.value = true
}
const close = () => {
	visible.value = false
}

defineExpose({
	open,
	close
})
</script>
