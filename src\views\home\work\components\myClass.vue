<template>
	<el-card shadow="hover">
		<template #header>
			<div class="card-header">
				<span>我的班级</span>
				<div v-if="total" class="pagination">
					<el-pagination
						layout="prev, pager, next"
						:total="total"
						:current-page="currentPage"
						:page-size="pageSize"
						@current-change="handlePageChange"
					/>
				</div>
			</div>
		</template>
		<div class="my-class">
			<div v-if="paginatedArray?.length" class="class-list">
				<div v-for="item in paginatedArray" :key="item.id" class="class-item" @click="classItemClick(item)">
					<div class="class-title">
						<cusSvgIcon iconClass="class"></cusSvgIcon>
						{{ item.grade_name + item.class_name }}
					</div>
					<div class="class-item-info">
						<el-tag type="danger" size="small">{{ item.course_name }}</el-tag>
						<div class="class-item-info-item">
							<span>{{ item.student_num }}</span
							>人
						</div>
					</div>
				</div>
			</div>
			<el-empty v-else :image-size="150" />
		</div>
	</el-card>
</template>
<script>
import cusSelectSemester from '@/components/custom/cusSelectSemester'
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, semesterInfo, studentVacateTypeMap } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: null
	}
}
export default {
	components: {
		cusSelectSemester
	},
	data() {
		return {
			CampusManagementList: campusInfo,
			params: defaultParams(),
			semesterInfo,
			treeData: [],
			classList: [],
			paginatedArray: [],
			total: 0,
			currentPage: 1,
			pageSize: 4
		}
	},
	created() {
		this.getSemester()
	},
	mounted() {},
	methods: {
		getSemester() {
			this.treeData = cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.params.campus_id),
				'value',
				'parent_id'
			)
			let value = this.findSelectedChildren(this.treeData)
			if (value.length > 1) {
				this.params.semester_id = value[1].value
				this.getClassList()
			}
		},
		findSelectedChildren(tree) {
			let selectedChildren = []
			function recurse(nodes) {
				for (const node of nodes) {
					if (node.selected === 1) {
						selectedChildren.push(node)
					}
					if (node.children && node.children.length > 0) {
						recurse(node.children)
					}
				}
			}
			recurse(tree)
			return selectedChildren
		},
		classItemClick(item) {
			this.$router.push({
				name: 'classInfo',
				query: {
					id: item.class_id
				}
			})
		},
		getClassList() {
			this.$API.class.class.get(this.params).then((res) => {
				if (res.code == 200) {
					if (!res.data) {
						return
					}
					this.classList = res.data
					this.paginateArray(this.classList)
					this.total = res.data.length
				} else {
					ElMessage.error(res.message)
				}
			})
		},
		paginateArray(arry) {
			if (!arry?.length) {
				return
			}
			const start = (this.currentPage - 1) * this.pageSize
			const end = start + this.pageSize
			this.paginatedArray = arry.slice(start, end)
		},
		handlePageChange(currentPage) {
			this.currentPage = currentPage
			this.paginateArray(this.classList)
		}
	}
}
</script>

<style lang="scss" scoped>
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.my-class {
	min-height: 210px;
}

.class-list {
	display: flex;
	flex-wrap: wrap;

	.class-item {
		width: 48%;
		border: 1px solid var(--el-border-color-light);
		margin-right: 10px;
		margin-bottom: 11px;
		border-radius: 5px;
		padding: 8px;
		cursor: pointer;

		&:hover {
			box-shadow: 0 0 9px 2px var(--el-border-color-light);
		}

		.class-title {
			font-size: 14px;
			font-weight: bold;
			display: flex;
			align-items: center;
			margin-bottom: 10px;

			.svg-icon {
				font-size: 30px;
				margin-right: 15px;
			}
		}

		.class-item-info {
			padding: 8px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: var(--el-bg-color-overlay);
			border-radius: 5px;

			.class-item-info-item {
				font-size: 12px;
				font-style: italic;

				span {
					font-size: 14px;
				}
			}
		}
	}
}

// .pagination {
//     position: absolute;
//     bottom: 0;
//     left: 50%;
//     transform: translateX(-50%);
// }
</style>
