<template>
	<el-container>
		<el-header>
			<div class="plate">
				<div class="chart">
					<scEcharts class="scEcharts" :option="chartData" width="100%" height="150px"></scEcharts>
				</div>
				<div class="chartData">
					<div class="chartData-item">
						<el-statistic :value="alertTotal">
							<template #title>
								<div style="display: inline-flex; align-items: center">告警数（7日）</div>
							</template>
						</el-statistic>
					</div>
					<div class="chartData-item">
						<el-row>
							<el-col :span="6">
								<el-statistic :value="chartDataMap[1]">
									<template #title>
										<div style="display: inline-flex; align-items: center; color: var(--el-color-danger)">
											紧急 {{ alertTotal > 0 ? ((chartDataMap[1] / alertTotal) * 100).toFixed(2) : 0 }}%
										</div>
									</template>
								</el-statistic>
							</el-col>
							<el-col :span="6">
								<el-statistic :value="chartDataMap[2]">
									<template #title>
										<div style="display: inline-flex; align-items: center; color: var(--el-color-warning)">
											重要 {{ alertTotal > 0 ? ((chartDataMap[2] / alertTotal) * 100).toFixed(2) : 0 }}%
										</div>
									</template>
								</el-statistic>
							</el-col>
							<el-col :span="6">
								<el-statistic :value="chartDataMap[3]">
									<template #title>
										<div style="display: inline-flex; align-items: center; color: var(--el-color-primary)">
											次要 {{ alertTotal > 0 ? ((chartDataMap[3] / alertTotal) * 100).toFixed(2) : 0 }}%
										</div>
									</template>
								</el-statistic>
							</el-col>
							<el-col :span="6">
								<el-statistic :value="chartDataMap[4]">
									<template #title>
										<div style="display: inline-flex; align-items: center; color: var(--el-color-info)">
											提示 {{ alertTotal > 0 ? ((chartDataMap[4] / alertTotal) * 100).toFixed(2) : 0 }}%
										</div>
									</template>
								</el-statistic>
							</el-col>
						</el-row>
					</div>
				</div>
			</div>
			<div class="left-panel" style="width: 100%">
				<div class="left-panel-search">
					<el-form-item label="">
						<el-select
							v-if="CampusManagementList.length > 1"
							v-model="params.campus_id"
							placeholder="请选择校区"
							filterable
							style="width: 150px"
							@change="getChartData"
						>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.alert_level" style="width: 100px" placeholder="告警级别" clearable>
							<el-option
								v-for="(item, index) in enumConfig.alertLevelMap"
								:key="index"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.status" style="width: 100px" placeholder="告警状态" clearable>
							<el-option label="未处理" :value="-1"></el-option>
							<el-option label="已处理" :value="1"></el-option>
							<el-option label="已忽略" :value="2"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="请输入告警内容搜索" clearable></el-input>
					</el-form-item>
					<el-form-item label="">
						<el-date-picker
							v-model="params.time"
							type="datetimerange"
							range-separator="至"
							start-placeholder="开始时间"
							end-placeholder="结束时间"
							value-format="YYYY-MM-DD HH:mm:ss"
						/>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<!--<div class="right-panel">
						<el-button type="primary" icon="el-icon-CirclePlus" @click="add">新增告警规则</el-button>
			</div>-->
		</el-header>
		<el-main>
			<scTable
				ref="table"
				size="small"
				row-key="id"
				:page-size="10"
				:pageSizes="[10, 20, 30, 50, 100]"
				:apiObj="list.apiObj"
				:params="params"
				:hideDo="false"
				:renderDataFunc="renderDataFunc"
				@selection-change="selectionChange"
			>
				<el-table-column type="selection" width="50" fixed="left" />
				<el-table-column label="告警ID" prop="alert_id" width="140" fixed="left"></el-table-column>
				<el-table-column label="触发时间" prop="trigger_time" width="150"></el-table-column>
				<el-table-column label="告警规则" prop="alert_name" width="180">
					<template #default="scope">
						<span v-if="scope.row.alert_rule_info">{{ scope.row.alert_rule_info.alert_name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="告警等级" prop="alert_level" width="100">
					<template #default="scope">
						<span v-if="scope.row.alert_rule_info">
							<el-tag :type="alertLevelStatus[scope.row.alert_rule_info.alert_level]">{{
								enumConfig.alertLevelMap.find((item) => item.value === scope.row.alert_rule_info.alert_level)?.name
							}}</el-tag>
						</span>
					</template>
				</el-table-column>
				<el-table-column label="告警场室" prop="room" width="180" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.room_info">{{ scope.row.room_info.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="告警内容" prop="alert_content" min-width="300">
					<template #default="scope">
						<el-tooltip :raw-content="true">
							<template #content>
								<p>设备：{{ scope.row.alert_content.deviceName }}({{ scope.row.alert_content.deviceKey }})</p>
								<p>触发告警：{{ scope.row.alert_content.alertName }}</p>
								<template v-if="scope.row.alert_content.triggerMode === 1">
									<p>触发属性：{{ scope.row.alert_content.tslName }}({{ scope.row.alert_content.tslCode }})</p>
									<p>属性值：{{ scope.row.alert_content.valueFormate }}</p>
								</template>
								<template v-if="scope.row.alert_content.triggerMode === 2">
									<p>触发事件：{{ scope.row.alert_content.tslName }}({{ scope.row.alert_content.tslCode }})</p>
									<p>触发参数：{{ scope.row.alert_content.valueFormate }}</p>
								</template>
								<template v-if="scope.row.alert_content.triggerMode === 3">
									<p>触发原因：{{ scope.row.alert_content.statusDesc }}</p>
								</template>
							</template>
							<div class="overflowTips">
								设备：{{ scope.row.alert_content.deviceName }}({{ scope.row.alert_content.deviceKey }})；触发告警：{{
									scope.row.alert_content.alertName
								}}；<template v-if="scope.row.alert_content.triggerMode === 1">
									触发属性：{{ scope.row.alert_content.tslName }}({{ scope.row.alert_content.tslCode }})； 属性值：{{
										scope.row.alert_content.valueFormate
									}}；
								</template>
								<template v-if="scope.row.alert_content.triggerMode === 2">
									触发事件：{{ scope.row.alert_content.tslName }}({{ scope.row.alert_content.tslCode }})；
								</template>
								<template v-if="scope.row.alert_content.triggerMode === 3">
									触发原因：{{ scope.row.alert_content.statusDesc }}；
								</template>
							</div>
						</el-tooltip>
					</template>
				</el-table-column>
				<el-table-column label="告警状态" prop="status" width="120">
					<template #default="scope">
						<span v-if="scope.row.status === -1">
							<sc-status-indicator type="primary"> </sc-status-indicator> 待处理
						</span>
						<span v-if="scope.row.status === 1">
							<sc-status-indicator type="success"> </sc-status-indicator> 已处理
						</span>
						<span v-if="scope.row.status === 2"> <sc-status-indicator type="info"> </sc-status-indicator> 已忽略 </span>
					</template>
				</el-table-column>
				<el-table-column label="处理结果" prop="treated_remark" width="150" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.treated_remark">{{ scope.row.treated_remark }}</span>
						<span v-else>-</span>
					</template>
				</el-table-column>
				<el-table-column label="处理时间" prop="treated_time" width="150">
					<template #default="scope">
						<span v-if="scope.row.treated_time">{{ scope.row.treated_time }}</span>
						<span v-else>-</span>
					</template>
				</el-table-column>
				<el-table-column label="处理人" prop="treated_user" width="120">
					<template #default="scope">
						<span v-if="scope.row.treated_user_info">{{ scope.row.treated_user_info.name }}</span>
						<span v-else>-</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="120">
					<template #default="scope">
						<el-button
							v-if="scope.row.status !== 1"
							text
							type="primary"
							size="small"
							@click="table_edit(scope.row, scope.$index)"
							>处理</el-button
						>
						<el-button
							v-if="scope.row.status === -1"
							text
							type="danger"
							size="small"
							@click="table_del(scope.row, scope.$index)"
							>忽略</el-button
						>
					</template>
				</el-table-column>
				<template v-slot:selectAction>
					<el-popconfirm title="确定批量将选择的告警设置为已处理吗？" @confirm="tableBatchTreated(1)">
						<template #reference>
							<el-button size="small" :disabled="treatedStatus !== true" type="primary">批量处理</el-button>
						</template>
					</el-popconfirm>
					<el-popconfirm title="确定批量将选择的告警忽略吗？" @confirm="tableBatchTreated(-1)">
						<template #reference>
							<el-button size="small" :disabled="treatedStatus !== true" type="danger">批量忽略</el-button>
						</template>
					</el-popconfirm>
				</template>
			</scTable>
		</el-main>
	</el-container>
</template>

<script>
import ScTable from '@/components/scTable/index.vue'
import { ElMessageBox } from 'element-plus'
import cusTom from '@/utils/cusTom'
import ScStatusIndicator from '@/components/scMini/scStatusIndicator.vue'
import dayjs from 'dayjs'
import scEcharts from '@/components/scEcharts/index.vue'
const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		campus_id: campusId,
		alert_level: null,
		status: null,
		time: null,
		begin_time: null,
		end_time: null
	}
}

export default {
	name: 'alertList',
	data() {
		return {
			groupFilterText: '',
			groupData: [],
			treatedStatus: false,
			CampusManagementList: [],
			selection: [],
			enumConfig: [],
			alertLevelStatus: {
				1: 'danger',
				2: 'warning',
				3: 'primary',
				4: 'info'
			},
			alertLevelMap: {
				1: '紧急',
				2: '重要',
				3: '次要',
				4: '提示'
			},
			list: {
				apiObj: this.$LotApi.alertRule.getLog
			},
			alertTotal: 0,
			chartDataMap: {
				1: 0,
				2: 0,
				3: 0,
				4: 0
			},
			chartData: {
				color: ['#F56C6C', '#FF7D00', '#165DFF', '#909399'],
				title: {
					text: '',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						name: '',
						type: 'pie',
						radius: ['50%', '80%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						},
						data: []
					}
				],
				legend: {
					show: false,
					bottom: 0
				}
			},
			params: defaultParams(),
			search: {
				name: null
			},
			dialog: {
				save: false,
				ruleSet: false
			}
		}
	},
	components: {
		scEcharts,
		ScStatusIndicator,
		ScTable
	},
	watch: {},
	created() {
		/*	this.params.time = [dayjs().add(-7, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
		this.params.begin_time = this.params.time ? this.params.time[0] : null
		this.params.end_time = this.params.time ? this.params.time[1] : null*/
		this.CampusManagementList = campusInfo
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
		this.getChartData()
	},
	computed: {},
	methods: {
		renderDataFunc(data) {
			data.map((v) => {
				v.alert_content = JSON.parse(v.alert_content)
			})
			return data
		},
		async tableBatchTreated(type) {
			if (this.selection.length <= 0) {
				return
			}
			if (type === 1) {
				ElMessageBox.prompt('处理结果', '处理', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					inputPlaceholder: '请输入处理结果',
					inputType: 'textarea'
				})
					.then(async ({ value }) => {
						var reqData = {
							alert_id: this.selection.map((v) => v.id),
							tenant_id: this.params.tenant_id,
							campus_id: this.params.campus_id,
							status: 1,
							remark: value
						}
						var res = await this.$LotApi.alertRule.batchTreatedAlert.post(reqData)
						if (res.code === 200) {
							this.$message.success('操作成功')
							this.$refs.table.refresh()
						} else {
							this.$alert(res.message, '提示', { type: 'error' })
						}
					})
					.catch(() => {})
			} else {
				var reqData = {
					alert_id: this.selection.map((v) => v.id),
					tenant_id: this.params.tenant_id,
					campus_id: this.params.campus_id,
					status: 2
				}
				var res = await this.$LotApi.alertRule.batchTreatedAlert.post(reqData)
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.$refs.table.refresh()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			}
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
			if (selection.length > 0) {
				this.treatedStatus = true
			} else {
				this.treatedStatus = false
			}
		},
		async getChartData() {
			this.alertTotal = 0
			this.chartData.series[0].data = []
			this.chartDataMap = {
				1: 0,
				2: 0,
				3: 0,
				4: 0
			}
			let reqData = {
				tenant_id: tenantId,
				campus_id: this.params.campus_id
			}
			var res = await this.$LotApi.alertRule.plate.get(reqData)
			if (res.code === 200) {
				let chartData = [
					{
						name: '紧急',
						value: 0
					},
					{
						name: '重要',
						value: 0
					},
					{
						name: '次要',
						value: 0
					},
					{
						name: '提示',
						value: 0
					}
				]
				if (!res.data) {
					return
				}
				res.data.forEach((v) => {
					if (chartData[v.alert_level - 1]) {
						chartData[v.alert_level - 1].value = v.count
					}
				})
				this.alertTotal = 0
				this.chartData.series[0].data = chartData
				res.data.forEach((item) => {
					this.chartDataMap[item.alert_level] = item.count
					this.alertTotal += item.count
				})
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//搜索
		upsearch() {
			this.params.begin_time = this.params.time ? this.params.time[0] : null
			this.params.end_time = this.params.time ? this.params.time[1] : null
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			//this.params.time = [dayjs().add(-7, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
			this.params.begin_time = this.params.time ? this.params.time[0] : null
			this.params.end_time = this.params.time ? this.params.time[1] : null
			this.upsearch()
			this.getChartData()
		},
		//新增职位
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', this.params.campus_id)
			})
		},
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.$refs.table.refresh()
			}
		},
		//编辑
		table_edit(row) {
			ElMessageBox.prompt('处理结果', '处理', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputPlaceholder: '请输入处理结果',
				inputType: 'textarea'
			})
				.then(async ({ value }) => {
					var reqData = {
						alert_id: row.id,
						tenant_id: row.tenant_id,
						campus_id: row.campus_id,
						status: 1,
						remark: value
					}
					var res = await this.$LotApi.alertRule.treatedAlert.post(reqData)
					if (res.code === 200) {
						this.$message.success('操作成功')
						this.$refs.table.refresh()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
				.catch(() => {})
		},
		table_rule(row) {
			this.dialog.ruleSet = true
			this.$nextTick(() => {
				this.$refs.ruleSetDialog.open(row)
			})
		},
		//查看
		table_show(row) {
			/*this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})*/
			this.$router.push({
				name: 'lotSetTemplateTsl',
				params: {
					id: row.id
				}
			})
		},
		//删除
		async table_del(row) {
			var reqData = { alert_id: row.id, tenant_id: row.tenant_id, campus_id: row.campus_id, status: 2 }
			ElMessageBox.confirm('确定要忽略此告警吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.alertRule.treatedAlert.post(reqData)
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.$refs.table.refresh()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.el-header {
	display: unset;
	.plate {
		margin-bottom: 10px;
		width: 100%;
		height: 150px;
		display: flex;
	}
	.chart {
		width: 220px;
		padding-left: 10px;
	}
	.chartData {
		padding-top: 20px;
		width: 500px;
	}
	.chartData-item {
		padding-bottom: 15px;
	}
}
.left-panel {
}
.overflowTips {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
</style>
