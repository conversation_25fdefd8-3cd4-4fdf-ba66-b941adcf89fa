{"name": "Gins-Admin", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build --mode development", "build:prod": "vite build --mode production", "build:test": "vite build --mode test", "preview": "vite preview", "prettier": "prettier --write ."}, "dependencies": {"@codemirror/lang-javascript": "^6.2.2", "@element-plus/icons-vue": "2.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ali-oss": "^6.16.0", "axios": "1.6.0", "codemirror": "^5.65.18", "codemirror-editor-vue3": "^2.8.0", "core-js": "3.24.1", "cropperjs": "1.5.12", "crypto-js": "4.2.0", "dayjs": "^1.11.11", "echarts": "5.3.3", "element-plus": "2.8.4", "fast-glob": "^3.3.2", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "modern-screenshot": "^4.5.1", "nprogress": "0.2.0", "path": "^0.12.7", "pikaz-excel-js": "^1.0.5", "pinia": "^2.1.7", "pinyin-match": "^1.2.2", "qrcodejs2": "0.0.2", "sortablejs": "1.15.0", "tinymce": "6.7.3", "uuid": "^10.0.0", "v-preview-image": "^3.1.0", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.4.15", "vue-clipboard3": "^2.0.0", "vue-draggable-resizable": "^3.0.0", "vue-i18n": "9.2.2", "vue-router": "4.1.3", "vuedraggable": "4.0.3", "xgplayer": "2.31.7", "xgplayer-hls": "2.5.2", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.1.0", "@vue/eslint-config-prettier": "^7.0.0", "add": "^2.0.6", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.3.0", "prettier": "^2.5.1", "sass": "1.59.3", "terser": "^5.15.0", "unplugin-auto-import": "^19.1.2", "vite": "^4.2.0", "xlsx-style-vite": "^0.0.2", "yarn": "^1.22.22"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}