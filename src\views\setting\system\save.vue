<template>
	<el-dialog v-model="showForm" :title="title" direction="rtl" width="500" @close="closeForm">
		<el-form ref="formref" :model="form" label-width="90px" label-position="right" :rules="formRules">
			<el-form-item label="开始时间" prop="begin_time">
				<el-date-picker
					v-model="form.begin_time"
					type="datetime"
					placeholder="请选择开始时间"
					value-format="YYYY-MM-DD HH:mm:ss"
				/>
			</el-form-item>
			<el-form-item label="结束时间" prop="end_time">
				<el-date-picker
					v-model="form.end_time"
					type="datetime"
					value-format="YYYY-MM-DD HH:mm:ss"
					placeholder="请选择结束时间"
				/>
			</el-form-item>
			<el-form-item label="状态" prop="status">
				<el-switch
					v-model="form.status"
					:active-value="1"
					:inactive-value="-1"
					@change="handleSwitchChange"
				></el-switch>
			</el-form-item>
			<el-form-item label="备注" prop="reason">
				<el-input v-model="form.reason" placeholder="请输入备注" type="textarea" rows="3" />
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="cancel">取消</el-button>
			<el-button type="primary" @click="publish">保存</el-button>
		</template>
	</el-dialog>
</template>
<script>
import { cloneDeep } from 'lodash'
import cusTom from '@/utils/cusTom'
const { tenantId } = cusTom.getBaseQuery()

export default {
	data() {
		return {
			showForm: false,
			title: '',
			form: {
				id: 0,
				tenant_id: tenantId,
				begin_time: '',
				end_time: '',
				status: -1,
				reason: ''
			},
			formRules: {
				begin_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
				end_time: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
			}
		}
	},
	methods: {
		open(type) {
			this.showForm = true
			this.title = type === 'edit' ? '编辑' : '新增'
			this.$nextTick(() => {
				if (type !== 'edit') {
					this.form = {
						id: 0,
						begin_time: '',
						end_time: '',
						status: -1,
						reason: ''
					}
				}
			})
		},
		submitForm(status) {
			this.$refs.formref.validate((valid) => {
				if (valid) {
					this.$emit('success', this.form)
					this.showForm = false
				}
			})
		},
		cancel() {
			this.showForm = false
		},
		publish() {
			this.submitForm(1)
		},
		handleSwitchChange(item) {
			this.form.status = item
		},
		closeForm() {
			this.form = {
				id: 0,
				begin_time: '',
				end_time: '',
				status: -1,
				reason: ''
			}
		},
		setData(data) {
			let formData = cloneDeep(data)
			this.form = { ...formData }
		}
	}
}
</script>
<style lang="scss" scoped></style>
