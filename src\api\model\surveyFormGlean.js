import config from '@/config'
import http from '@/utils/request'

export default {
	formInfo: {
		url: `${config.API_URL}/manapi/survey/codeOne`,
		name: '获取问卷详情',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	status: {
		url: `${config.API_URL}/manapi/survey/submitStatus`,
		name: '获取问卷提交状态',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	submit: {
		url: `${config.API_URL}/manapi/survey/submit`,
		name: '提交问卷',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	}
}
