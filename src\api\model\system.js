import config from '@/config'
import http from '@/utils/request'

export default {
	menu: {
		myMenus: {
			//url: `${config.MOCK_URL}/system/menu/my/1.6.1`,
			url: `${config.API_URL}/sysapi/user/permissions`,
			name: '获取我的菜单',
			get: async function () {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url)
			}
		},
		list: {
			//url: `${config.MOCK_URL}/system/menu/list`,
			url: `${config.API_URL}/sysapi/menu/getAllMenus`,
			name: '获取所有菜单',
			get: async function (type = 1) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, { type })
			}
		},
		save: {
			//url: `${config.MOCK_URL}/system/menu/list`,
			url: `${config.API_URL}/sysapi/menu/saveMenu`,
			name: '新增菜单/修改',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del: {
			//url: `${config.MOCK_URL}/system/menu/list`,
			url: `${config.API_URL}/sysapi/menu/delMenu`,
			name: '删除菜单',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		}
	},
	dic: {
		tree: {
			url: `${config.MOCK_URL}/system/dic/tree`,
			name: '获取字典树',
			get: async function () {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url)
			}
		},
		list: {
			url: `${config.MOCK_URL}/system/dic/list`,
			name: '字典明细',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		get: {
			url: `${config.MOCK_URL}/system/dic/get`,
			name: '获取字典数据',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		}
	},
	dept: {
		list: {
			url: `${config.API_URL}/manapi/department/list`,
			name: '获取部门列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/manapi/department/all`,
			name: '获取部门列表树形',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/manapi/department/save`,
			name: '新增部门/修改',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/manapi/department/del`,
			name: '删除部门',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		status: {
			url: `${config.API_URL}/manapi/department/changeStatus`,
			name: '修改部门状态',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		}
	},
	app: {
		list: {
			url: `${config.MOCK_URL}/system/app/list`,
			name: '应用列表',
			get: async function () {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url)
			}
		}
	},
	log: {
		list: {
			url: `${config.MOCK_URL}/system/log/list`,
			name: '日志列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		}
	},
	table: {
		list: {
			url: `${config.MOCK_URL}/system/table/list`,
			name: '表格列管理列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		info: {
			url: `${config.MOCK_URL}/system/table/info`,
			name: '表格列管理详情',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		}
	},
	tasks: {
		list: {
			url: `${config.MOCK_URL}/system/tasks/list`,
			name: '系统任务管理',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		}
	}
}
