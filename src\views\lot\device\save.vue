<template>
	<el-drawer v-model="visible" :title="titleMap[mode]" :size="size" destroy-on-close @closed="$emit('closed')">
		<el-form
			ref="dialogForm"
			:model="form"
			:rules="rules"
			:disabled="mode === 'show'"
			label-width="85"
			label-position="top"
		>
			<el-form-item label="设备名称" prop="device_name">
				<el-input v-model="form.device_name" placeholder="请输入设备名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="所属产品" prop="product_id">
				<el-select
					v-model="form.product_id"
					style="width: 100%; max-width: unset"
					placeholder="请选择产品品类"
					clearable
					@change="selectProduct"
				>
					<el-option v-for="(item, index) in productList" :key="index" :label="item.product_name" :value="item.id">
						<span style="float: left">{{ item.product_name }}</span>
						<span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
							{{ enumConfig.nodeTypeMap.find((i) => i.value === item.node_type)?.name }}
						</span>
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item v-if="showGateway" label="上级网关" prop="parent_id">
				<el-select
					v-model="form.parent_id"
					style="width: 100%; max-width: unset"
					placeholder="请选择上级网关"
					filterable
					clearable
				>
					<el-option v-for="(item, index) in gatewayDeviceList" :key="index" :label="item.device_name" :value="item.id">
						<span style="float: left">{{ item.device_name }}</span>
						<span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
							{{ item.device_key }}
						</span>
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="mac地址" prop="device_mac">
				<el-input v-model="form.device_mac" placeholder="请填写设备mac地址" clearable></el-input>
			</el-form-item>
			<el-form-item label="设备图片" prop="device_img">
				<scUpload v-model="form.device_img" fileTypeTag="lotDevice"></scUpload>
			</el-form-item>
			<el-form-item label="设备位置" prop="room_id">
				<cusSelectField
					v-model="form.room"
					width="100%"
					:multiple="false"
					placeholder="请选择设备位置"
				></cusSelectField>
			</el-form-item>
			<el-form-item label="安装位置" prop="install_location">
				<el-input
					v-model="form.install_location"
					placeholder="请填写设备安装位置，例如：场室天花板东南角"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item label="设备描述" prop="desc">
				<el-input v-model="form.desc" clearable rows="3" type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
	<select-template
		v-if="templateSelectVisible"
		ref="templateSelect"
		@success="submitTemplate"
		@closed="closedTemplateSelect"
	></select-template>
</template>

<script>
import cusTom from '@/utils/cusTom'
import SelectTemplate from '@/views/lot/product/selectTemplate.vue'

const { tenantId, campusId } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		id: null,
		tenant_id: tenantId,
		campus_id: campusId,
		device_name: null,
		product_id: null,
		parent_id: null,
		device_img: null,
		device_mac: null,
		room_id: null,
		room: null,
		install_location: null,
		desc: null
	}
}

export default {
	components: { SelectTemplate },
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},

	data() {
		return {
			CampusManagementList: [],
			productList: [],
			gatewayDeviceList: [],
			mode: 'add',
			titleMap: {
				add: '创建产品',
				edit: '编辑产品',
				show: '查看'
			},
			visible: false,
			templateSelectVisible: false,
			isSaveing: false,
			showGateway: false,
			//表单数据
			form: defaultData(),
			enumConfig: [],
			//验证规则
			rules: {
				campus_id: [{ required: true, message: '请选择校区' }],
				device_name: [{ required: true, message: '请输入设备名称' }],
				product_id: [{ required: true, message: '请选择产品品类' }],
				parent_id: [{ required: true, message: '请选择上级网关' }]
			}
		}
	},
	mounted() {},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	methods: {
		getGatewayDevice() {
			this.$LotApi.device.getGatewayDevice
				.get({
					tenant_id: tenantId,
					campus_id: this.form.campus_id
				})
				.then((res) => {
					if (res.code === 200) {
						this.gatewayDeviceList = res.data
					}
				})
		},
		selectProduct(val) {
			let node_type = this.productList.find((i) => i.id === val)?.node_type
			if (node_type === 2) {
				this.showGateway = true
			} else {
				this.showGateway = false
			}
		},
		submitTemplate(info) {
			this.form.template_id = info.id
			this.form.template_info.name = info.template_name
			this.form.template_info.id = info.id
			this.templateSelectVisible = false
		},
		closedTemplateSelect() {
			this.templateSelectVisible = false
		},
		selectTemplate() {
			this.templateSelectVisible = true
			this.$nextTick(() => {
				this.$refs.templateSelect.open()
			})
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let subForm
					if (this.mode === 'add') {
						subForm = { ...this.params, ...this.form }
					} else if (this.mode === 'edit') {
						subForm = { ...this.form }
					}
					if (this.form.room && this.form.room.length > 0) {
						subForm.room_id = this.form.room[0].id
					} else {
						subForm.room_id = 0
					}
					var res = await this.$LotApi.device.save.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		getProductList() {
			this.$LotApi.product.all
				.get({
					tenant_id: tenantId,
					status: 1
				})
				.then((res) => {
					if (res.code === 200) {
						this.productList = res.data
					}
				})
		},
		//表单注入数据
		setData(data, campus_id) {
			this.getProductList()
			this.campus_id = campus_id
			this.form.campus_id = campus_id
			Object.assign(this.form, data)
			if (data && data.room_info) {
				this.form.room = [{ label: data.room_info.name, id: data.room_info.id }]
			}
			if (data && data.product_info && data.product_info.node_type === 2) {
				this.showGateway = true
			}
			this.getGatewayDevice()
		}
	}
}
</script>

<style scoped lang="scss">
.selectTemplate {
	cursor: pointer;
}
</style>
