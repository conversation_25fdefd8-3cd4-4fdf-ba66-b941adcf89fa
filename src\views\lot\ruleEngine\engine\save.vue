<template>
	<el-drawer v-model="visible" :title="titleMap[mode]" :size="500" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-position="top">
			<el-form-item label="规则名称" prop="engine_name">
				<el-input v-model="form.engine_name" placeholder="请输入规则名称" clearable></el-input>
			</el-form-item>

			<el-form-item label="启用状态" prop="status">
				<el-switch
					v-model="form.status"
					:active-value="1"
					:inactive-value="-1"
					active-text="开"
					inline-prompt
					style="--el-switch-on-color: #00b42a"
					inactive-text="关"
				></el-switch>
			</el-form-item>
			<el-form-item label="规则描述" prop="engine_desc">
				<el-input v-model="form.engine_desc" rows="4" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import cusTom from '@/utils/cusTom'

const { tenantId, campusId } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		engine_name: '',
		engine_desc: '',
		status: 1
	}
}

export default {
	emits: ['success', 'closed'],
	props: {},

	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增规则',
				edit: '编辑规则',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			enumConfig: [],
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				engine_name: [{ required: true, message: '请输入规则名称' }],
				status: [{ required: true, message: '请选择告警启用状态' }]
			}
		}
	},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	mounted() {},
	methods: {
		//显示
		open(mode = 'add', campus_id) {
			this.form.campus_id = campus_id
			this.mode = mode
			this.visible = true
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let subForm
					if (this.mode === 'add') {
						subForm = { ...this.params, ...this.form }
					} else if (this.mode === 'edit') {
						subForm = { ...this.form }
					}
					var res = await this.$LotApi.ruleEngine.save.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			console.log(data)
			Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
