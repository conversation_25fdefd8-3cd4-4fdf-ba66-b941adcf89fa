<template>
	<div v-if="openStatus === false" class="notOpen">
		<el-empty description="暂未开通名师工作室">
			<el-button v-if="userId === 0" type="primary" @click="open">开通名师工作室</el-button>
		</el-empty>
	</div>
	<el-container v-else style="margin-top: 10px">
		<el-aside style="width: 350px" :class="{ asideIn: userId > 0 }">
			<el-card shadow="hover" header="基本信息">
				<div>
					<el-row :gutter="10">
						<el-col v-if="info.teacher_info" :span="10">
							<el-image
								:src="info.teacher_info.user_head"
								fit="cover"
								style="width: 100%; height: 100%; max-height: 150px"
							>
								<template #error>
									<div class="image-slot">
										<img style="width: 100%" class="logo" src="@/assets/img/staff_head.png" />
									</div>
								</template>
							</el-image>
						</el-col>
						<el-col v-if="info.teacher_info" :span="14" class="info">
							<h2>{{ info.teacher_info.name }}</h2>
							<p>性别：{{ formData(sexMap, info.teacher_info.sex) }}</p>
							<p>学科：{{ info.teacher_info.course_name }}</p>
							<p>职称：{{ formData(professionalTypeMap, info.teacher_info.professional_type) }}</p>
							<p>工作单位：{{ info.campus_name }}</p>
						</el-col>
					</el-row>
				</div>
			</el-card>
			<el-card shadow="hover" class="introduction">
				<template #header>
					<div class="card-header">
						<span>个人简介</span>
						<el-button v-if="userId === 0" type="text" @click="editIntroduction">编辑</el-button>
					</div>
				</template>
				{{ info.introduction }}
			</el-card>
		</el-aside>
		<el-container>
			<el-main style="padding: 0; background: unset; overflow-x: hidden">
				<el-row :gutter="10">
					<el-col :span="6">
						<el-card shadow="hover">
							<el-statistic title="" :value="info.article_num">
								<template #title>
									<el-icon>
										<el-icon-Document />
									</el-icon>
									文章数
								</template>
								<template #suffix>篇</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="6">
						<el-card shadow="hover">
							<el-statistic :value="info.video_num">
								<template #title>
									<el-icon>
										<el-icon-VideoCamera />
									</el-icon>
									视频数
								</template>
								<template #suffix>个</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="6">
						<el-card shadow="hover">
							<el-statistic :value="info.like_num">
								<template #title>
									<el-icon>
										<el-icon-star />
									</el-icon>
									点赞数
								</template>
								<template #suffix>次</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="6">
						<el-card shadow="hover">
							<el-statistic :value="info.collect_num">
								<template #title>
									<el-icon>
										<el-icon-Collection />
									</el-icon>
									收藏数
								</template>
								<template #suffix>次</template>
							</el-statistic>
						</el-card>
					</el-col>
				</el-row>
				<el-row :gutter="10">
					<el-col :span="12">
						<el-card shadow="hover" class="doc">
							<template #header>
								<div class="card-header">
									<span>我的文章</span>
									<el-button type="text" @click="showMore('article')">更多</el-button>
								</div>
							</template>
							<div class="article">
								<ul v-if="info.article_list.length > 0">
									<li v-for="(item, index) in info.article_list" :key="index">
										<el-image :src="item.cover_url" fit="cover">
											<template #error>
												<div class="image-slot">
													<img
														style="width: 90px; height: 90px; object-fit: cover"
														class="logo"
														src="@/assets/img/material_cover.png"
													/>
												</div>
											</template>
										</el-image>
										<div style="display: flex; height: 90px; flex-direction: column; flex: 1">
											<div class="title" @click="showArticle(item)">
												<el-tag v-if="item.is_public === 1" type="success" size="small">公开</el-tag> {{ item.title }}
											</div>
											<div class="desc">
												<span class="time">{{ item.created_at }}</span>
												<span class="read">点赞：{{ item.like_num }}</span>
												<span class="read">收藏：{{ item.collect_num }}</span>
												<span class="read">评论：{{ item.comment_num }}</span>
											</div>
										</div>
									</li>
								</ul>
								<el-empty v-else description="暂无文章" />
							</div>
						</el-card>
					</el-col>
					<el-col :span="12">
						<el-card shadow="hover" class="doc">
							<template #header>
								<div class="card-header">
									<span>我的视频</span>
									<el-button type="text" @click="showMore('video')">更多</el-button>
								</div>
							</template>
							<div class="article">
								<ul v-if="info.video_list.length > 0">
									<li v-for="(item, index) in info.video_list" :key="index">
										<el-image :src="item.cover_url" fit="cover">
											<template #error>
												<div class="image-slot">
													<img
														style="width: 90px; height: 90px; object-fit: cover"
														class="logo"
														src="@/assets/img/material_cover.png"
													/>
												</div>
											</template>
										</el-image>
										<div style="display: flex; height: 90px; flex-direction: column; flex: 1">
											<div class="title" @click="showArticle(item)">
												<el-tag v-if="item.is_public === 1" type="success" size="small">公开</el-tag> {{ item.title }}
											</div>
											<div class="desc">
												<span class="time">{{ item.created_at }}</span>
												<span class="read">点赞：{{ item.like_num }}</span>
												<span class="read">收藏：{{ item.collect_num }}</span>
												<span class="read">评论：{{ item.comment_num }}</span>
											</div>
										</div>
									</li>
								</ul>
								<el-empty v-else description="暂无视频" />
							</div>
						</el-card>
					</el-col>
				</el-row>
			</el-main>
		</el-container>
	</el-container>
	<el-dialog v-model="dialogIntroductionVisible" title="个人简介" width="500">
		<el-input v-model="form.introduction" type="textarea" rows="10" autocomplete="off" />
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="dialogIntroductionVisible = false">取消</el-button>
				<el-button type="primary" @click="submitIntroduction"> 保存 </el-button>
			</div>
		</template>
	</el-dialog>
	<el-drawer
		v-model="showDetailDialog"
		:title="currentData.title"
		direction="rtl"
		size="50%"
		class="articleDetail"
		append-to-body
		destroy-on-close
		:close-on-press-escape="false"
	>
		<Show v-model="currentData" :only-show="userId>0"></Show>
	</el-drawer>
</template>
<script>
import cusTom from '@/utils/cusTom'
import { deepCopy } from 'ali-oss/lib/common/utils/deepCopy'
import Show from '@/views/home/<USER>/compoments/show.vue'

const { sexMap, professionalTypeMap } = cusTom.getBaseQuery()
export default {
	name: 'homeComponent',
	components: { Show },
	inject: ['userId'],
	data() {
		return {
			src: {},
			openStatus: true,
			dialogIntroductionVisible: false,
			showDetailDialog: false,
			currentData: {},
			info: {
				video_list: [],
				article_list: [],
				teacher_info: null
			},
			form: {
				introduction: ''
			},
			sexMap,
			professionalTypeMap
		}
	},
	created() {
		console.log(this.userId)
		this.getInfo()
	},
	methods: {
		getInfo() {
			//获取个人空间信息
			this.$API.famous.my
				.get({
					userId: this.userId
				})
				.then((res) => {
					if (res.code === 200) {
						this.info = res.data
					} else {
						this.openStatus = false
					}
				})
		},
		open() {
			if (this.openStatus) {
				return
			}
			this.$alert('开通名师空间，是否继续？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$API.famous.open.post().then((res) => {
					if (res.code === 200) {
						this.$message.success('开通成功')
						this.openStatus = true
						this.getInfo()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
			})
		},
		showArticle(item) {
			this.currentData = item
			this.showDetailDialog = true
		},
		showMore(type) {
			this.$emit('handleTab', type)
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value === val)?.name || '-'
		},
		editIntroduction() {
			this.form.introduction = deepCopy(this.info.introduction)
			this.dialogIntroductionVisible = true
		},
		submitIntroduction() {
			this.$API.famous.introductionEdit.post(this.form).then((res) => {
				if (res.code === 200) {
					this.$message.success('修改成功')
					this.dialogIntroductionVisible = false
					this.info.introduction = this.form.introduction
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
.asideIn {
	margin-right: 10px;
	border-right: unset;
}
:deep(.el-statistic) {
	.el-statistic__number {
		font-size: 26px;
	}

	.el-statistic__head {
		font-size: 16px;
		font-weight: bold;
		vertical-align: middle;
	}

	.el-statistic__head > .el-icon {
		font-size: 26px;
		vertical-align: middle;
	}

	.el-statistic__content {
		text-align: center;
		line-height: 30px;
	}

	.el-statistic__suffix {
		color: var(--el-text-color-secondary);
		font-size: 14px;
	}
}

.info {
	line-height: 25px;
}

.info h2 {
	padding-bottom: 15px;
}

.introduction {
	margin-top: 15px;
}

:deep(.introduction) {
	.el-card__body {
		line-height: 25px;
		font-size: 14px;
		color: var(--el-text-color-regular);
	}

	.card-header {
		vertical-align: middle;
		align-items: center;
		display: flex;
		justify-content: space-between;
	}
}

:deep(.doc) {
	.card-header {
		vertical-align: middle;
		align-items: center;
		display: flex;
		justify-content: space-between;
	}
}

.notOpen {
	width: 500px;
	margin: 0 auto;
}

.article {
	li {
		display: flex;
		border: 1px solid var(--el-border-color-light);
		padding-right: 15px;
		margin-bottom: 15px;
		.el-image {
			width: 90px;
			height: 90px;
			min-width: 90px;
			margin-right: 10px;
		}

		.title {
			flex: 1;
			padding-top: 10px;
			overflow: hidden;
			font-size: 12px;
			display: -webkit-box;
			text-overflow: ellipsis; //属性规定当文本溢出包含元素时发生的事情  text-overflow: clip|ellipsis|string; (修剪/省略号/指定字符串)
			-webkit-line-clamp: 2;
			/*要显示的行数*/
			/* autoprefixer: off */
			-webkit-box-orient: vertical; //属性规定框的子元素应该被水平或垂直排列
			/* autoprefixer: on */
			font-weight: bold;
			word-break: break-all;
			cursor: pointer;
		}
		.desc {
			color: var(--el-text-color-regular);
			font-size: 12px;
			line-height: 35px;
			span {
				margin-right: 10px;
			}
		}
	}
}
</style>
