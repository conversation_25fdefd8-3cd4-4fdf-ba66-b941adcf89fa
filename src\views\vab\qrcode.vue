<!--
 * @Author: Xu<PERSON><PERSON><PERSON>
 * @Date: 2023-02-28 14:12:15
 * @LastEditors: Xujianchen
 * @LastEditTime: 2023-03-19 12:02:11
 * @Description:
-->
<template>
	<el-main>
		<el-row :gutter="15">
			<el-col :lg="8">
				<el-card shadow="never" header="常用">
					<sc-qr-code text="gin-admin"></sc-qr-code>
				</el-card>
			</el-col>
			<el-col :lg="8">
				<el-card shadow="never" header="带Logo">
					<sc-qr-code text="gin-admin" :logo="loadImageUrl('logo')"></sc-qr-code>
				</el-card>
			</el-col>
			<el-col :lg="8">
				<el-card shadow="never" header="自定义颜色大小">
					<sc-qr-code text="gin-admin" :size="100" colorDark="#088200" colorLight="#fff"></sc-qr-code>
				</el-card>
			</el-col>
			<el-col :lg="8">
				<el-card shadow="never" header="动态">
					<sc-qr-code :text="qrcode"></sc-qr-code>
					<el-input v-model="qrcode" placeholder="Please input" style="margin-top: 20px" />
				</el-card>
			</el-col>
		</el-row>
	</el-main>
</template>

<script>
import { loadImageUrl } from '@/utils/load'

export default {
	name: 'qrcode',
	data() {
		return {
			qrcode: 'gin-admin'
		}
	},
	mounted() {},
	methods: { loadImageUrl }
}
</script>

<style></style>
