<template>
    <div class="staffContent">
        <div class="staffList">
            <div class="staffType">
                <div class="staffTypeTitle">主考人员</div>
                <cusSelectTeacher v-model="staffData.chief" multiple :isShowTag="false" width="100%"
                    placeholder="请选择主考人员">
                </cusSelectTeacher>
            </div>
            <div class="staffType">
                <div class="staffTypeTitle">副主考</div>
                <cusSelectTeacher v-model="staffData.deputy" multiple :isShowTag="false" width="100%"
                    placeholder="请选择副主考人员">
                </cusSelectTeacher>
            </div>
            <div class="staffType">
                <div class="staffTypeTitle">巡考</div>
                <cusSelectTeacher v-model="staffData.patrol" multiple :isShowTag="false" width="100%"
                    placeholder="请选择巡考人员">
                </cusSelectTeacher>
            </div>
            <div class="staffType">
                <div class="staffTypeTitle">机动</div>
                <cusSelectTeacher v-model="staffData.flexible" multiple :isShowTag="false" width="100%"
                    placeholder="请选择机动人员">
                </cusSelectTeacher>
            </div>
            <div class="staffType">
                <div class="staffTypeTitle">监考</div>
                <cusSelectTeacher v-model="staffData.invigilate" multiple inputType="textarea" :rows="5" width="100%"
                    placeholder="请选择监考人员">
                </cusSelectTeacher>
            </div>
            <div class="count">
                共{{ invigilateCount }}名监考员
            </div>
            <div class="staffBtn">
                <el-button type="primary" style="width: 50%;" @click="save">保存</el-button>
            </div>
        </div>
    </div>
</template>
<script setup>
import cusSelectTeacher from '@/components/custom/cusSelectTeacher'
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()

const typeMap = reactive({
    1: 'chief',
    2: 'deputy',
    3: 'patrol',
    4: 'flexible',
    5: 'invigilate'
})

const save = () => {
    const staffList = [];
    Object.entries(typeMap).forEach(([userType, key]) => {
        const staffs = staffData.value[key];
        if (staffs && staffs.length) {
            const mapped = staffs.map(item => ({
                staff_id: item.id,
                user_type: parseInt(userType, 10)
            }));
            staffList.push(...mapped);
        }
    });
    console.log(staffList)
    globalPropValue.examine.staffSave.post({
        tenant_id: tenantId,
        campus_id: campusId,
        examine_id: Number(query.id),
        staff_list: staffList
    }).then(res => {
        if (res.code === 200) {
            ElMessage.success('保存成功')
        }
    })
}

const staffData = ref({})
const getStaffList = () => {
    globalPropValue.examine.staffList.get({
        tenant_id: tenantId,
        campus_id: campusId,
        examine_id: query.id
    }).then(res => {
        if (res.code === 200) {
            // 使用 reduce 分组，并通过 typeMap 映射 key
            const groupedData = res.data.reduce((acc, item) => {
                const userType = item.user_type; // 获取当前 user_type
                // 检查 user_type 是否存在于 typeMap 中
                if (typeMap.hasOwnProperty(userType)) {
                    const key = typeMap[userType]; // 映射为value称作为键
                    const staffInfoValue = { label: item.staff_info.name, id: item.staff_info.id };
                    acc[key] = [...(acc[key] || []), staffInfoValue]; // 合并到对应的键数组中
                }
                return acc;
            }, {});
            staffData.value = groupedData
        }
        console.log(staffData.value)
    })
}

onMounted(() => {
    getStaffList()
})
// 计算
const invigilateCount = computed(() => {
    return staffData.value.invigilate?.length || 0
})
</script>

<style lang="scss" scoped>
.staffContent {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .staffList {
        width: 30%;
        padding: 15px;
        background: #f6f8f9;
        border-radius: 10px;

        .staffType {
            margin-bottom: 15px;

            &Title {
                font-size: 16px;
                margin-bottom: 10px;
            }
        }
        .count{
            margin: 15px 0;
            text-align: center;
            font-size: 16px;
            color: var(--el-color-warning);
        }
    }

    .staffBtn {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>