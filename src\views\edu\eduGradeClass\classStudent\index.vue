<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
					<el-form-item label="" style="margin-left: 15px">
						<cusCascader v-model="params.semester_id" placeholder="请选择学期" :options="getSemester" ></cusCascader>
					</el-form-item>
					<el-form-item>
						<el-cascader
							v-model="params.class_id"
							:options="classList"
							placeholder="请选择班级"
							:props="{ multiple: false, label: 'name', value: 'id', children: 'class_list' }"
							clearable
						></el-cascader>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-plus" @click="add">新增班级学员</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<el-table-column label="学生姓名" prop="student_name" width="110"></el-table-column>
				<el-table-column label="学号" prop="serial_number" width="150"></el-table-column>
				<el-table-column label="头像" prop="user_head" width="100">
					<template #default="scope">
						<cusHead
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.user_head"
							:preview-src-list="[scope.row.user_head]"
							preview-teleported
						>
						</cusHead>
					</template>
				</el-table-column>
				<el-table-column label="性别" prop="sex" width="100">
					<template #default="scope">
						{{ $formatDictionary(sexMap, scope.row.sex) }}
					</template>
				</el-table-column>
				<el-table-column label="学年学期" prop="academic_name" width="200">
					<template #default="scope"> {{ scope.row.academic_name }} - {{ scope.row.semester_name }} </template>
				</el-table-column>
				<el-table-column label="年级班级" prop="grade_name" width="200">
					<template #default="scope"> {{ scope.row.grade_name }} - {{ scope.row.class_name }} </template>
				</el-table-column>
				<el-table-column label="职务" prop="position" width="150"></el-table-column>
				<el-table-column label="备注" prop="remark"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="210">
					<template #default="scope">
						<el-button-group>
<!--							<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>-->
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm title="确定移除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">移出</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
	<add-dialog
		v-if="dialog.add"
		ref="addDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.add = false"
	></add-dialog>
</template>

<script>
import saveDialog from './save'
import addDialog from './add'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, tenantInfo, attendanceStatusMap, semesterInfo, sexMap } = cusTom.getBaseQuery()
import cusHead from '@/components/custom/cusStaffHead.vue'

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null,
		grade_id: null,
		class_id: [],
		begin_time: null,
		end_time: null,
		date: [],
		name: null
	}
}
export default {
	name: '',
	components: { cusHead, saveDialog, addDialog },
	data() {
		return {
			dialog: {
				save: false,
				add: false
			},
			apiObj: this.$API.eduGradeClass.classStu.list,
			selection: [],

			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			semesterInfo,
			treeData: null,
			entryExitMap: [
				{ name: '进', value: 1 },
				{ name: '出', value: 2 }
			],
			userTypeMap: [
				{ name: '学员', value: 1 },
				{ name: '教职工', value: 2 }
			],
			attendanceStatusMap,
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'name',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入学生姓名或学号'
						}
					}

					/*					{
						label: null,
						name: 'class_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择班级',
							items: []
						}
					}*/
				]
			},
			grade: [],
			class: [],
			classList: [],
			sexMap
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.getClassData()
			},
			immediate: true
		},
		'params.semester_id':{
			handler(val) {
				console.log(val)
				// this.params.semester_id
				this.getClassData()
			},
		},
		classData_f: {
			handler(val) {
				console.log(val, 'vvv')
			},
			deep: true
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	inject: ['classData_f', 'resetClassD'],
	async created() {
		const classD = this.classData_f()
		if (classD.id) {
			this.params.class_id = classD.id
		}
	},
	methods: {
		async getClassData() {
			let res = await this.$API.eduGradeClass.class.all.get(this.params)
			this.classList = res.data
		},

		//添加
		add() {
			this.dialog.add = true
			this.$nextTick(() => {
				console.log(this.params.class_id, 'this.params.class_id')
				this.$refs.addDialog.open('add').setData({
					class_id: this.params.class_id
				})
			})
			// this.upsearch()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = {
				id: row.id,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				class_id: row.class_id
			}
			var res = await this.$API.eduGradeClass.classStu.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('移出成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},

		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		//搜索
		upsearch() {
			console.log(this.params.class_id)
			/*this.params.begin_time = this.params.date ? this.params.date[0] : null
			this.params.end_time = this.params.date ? this.params.date[1] : null*/
			if (this.params.class_id!=null&&this.params.class_id.length>1) {
				this.params.class_id = this.params.class_id[this.params.class_id.length - 1]
			}
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.resetClassD()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped></style>
