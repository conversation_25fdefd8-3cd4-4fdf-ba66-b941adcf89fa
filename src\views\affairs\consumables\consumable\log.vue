<template>
	<el-dialog v-model="visible" :width="500" destroy-on-close @closed="$emit('closed')">
		<template #header="{ close, titleId, titleClass }">
			<div class="my-header">
				<h4 :id="titleId" :class="titleClass">流转记录</h4>
			</div>
		</template>
		<div style="max-height: 600px; overflow: auto; padding: 10px; box-sizing: border-box">
			<el-timeline style="max-width: 600px">
				<el-timeline-item :color="'#0bbd87'" v-for="item in logList">
					<div class="logItem">
						<div class="logItem__left">
							<div v-if="item.log_type == 1">入库</div>
							<div v-if="item.log_type == 2">出库</div>
							<div v-if="item.log_type == 3">退库</div>
							<div>{{ item.created_at }}</div>
						</div>
						<div class="logItem__right">操作人：{{ item.action_user_name }}</div>
					</div>
					<div class="logContent">
						<template v-if="item.log_type == 1">
							<p>数量：{{ item.action_num }}(剩余库存{{ item.storage_num }})</p>
							<!-- <p>金额：{{  }}</p> -->
							<p>入库备注：{{ item.log_remark }}</p>
						</template>
						<template v-if="item.log_type == 2">
							<p>领用人：{{ item.log_user_name }}</p>
							<p>数量：{{ item.action_num }}(剩余库存{{ item.storage_num }})</p>
							<p>领用备注：{{ item.log_remark }}</p>
						</template>
						<template v-if="item.log_type == 3">
							<p>退库人：{{ item.log_user_name }}</p>
							<p>数量：{{ item.action_num }}(剩余库存{{ item.storage_num }})</p>
							<p>退库备注：{{ item.log_remark }}</p>
						</template>
					</div>
				</el-timeline-item>
			</el-timeline>
		</div>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		groupData: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			mode: 'add',

			visible: false,
			isSaveing: false,
			logList: [],
			consumablesId: null
		}
	},
	mounted() {},
	created() {},
	computed: {},
	watch: {},
	methods: {
		changebuild() {
			this.form.floor_id = ''
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			return this
		},
		async getLog() {
			var res = await this.$API.consumables.rooms.log.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				consumable_id: this.consumablesId
			})
			this.logList = res.data
		},
		//表单注入数据
		setData(data) {
			this.consumablesId = data.id
			this.getLog()
		}
	}
}
</script>

<style lang="scss" scoped>
.logItem {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
	&__left {
		& > div {
			&:nth-child(2) {
				font-size: 13px;
				color: #999;
			}
		}
	}
}
.logContent {
	padding: 10px;
	background-color: #f6f8fa;
	border-radius: 3px;
	line-height: 20px;
}
</style>
