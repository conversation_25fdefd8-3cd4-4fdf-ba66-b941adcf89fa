<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" @selection-change="selectionChange" :apiObj="apiObj">
				<!-- <el-table-column type="selection" width="50"></el-table-column> -->
				<el-table-column label="登录用户" prop="nickname" width="150"></el-table-column>
				<el-table-column label="登录时间" prop="created_at" width="150"></el-table-column>
				<el-table-column label="登录状态" prop="status" width="100">
					<template #default="scope">
						<el-tag :type="['success', 'warning'][scope.row.status - 1]">
							{{ $formatDictionary(statusMap, scope.row.status) }}
						</el-tag>
					</template>
				</el-table-column>

				<el-table-column label="登录方式" prop="type" width="100">
					<template #default="scope">
						{{ $formatDictionary(typeMap, scope.row.type) }}
					</template>
				</el-table-column>
				<el-table-column label="IP地址" prop="ip" width="180"></el-table-column>
				<el-table-column label="备注" prop="remark" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="user_agent" prop="user_agent" show-overflow-tooltip>
					<template #default="scope">
						<div v-html="scope.row.user_agent"></div>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
const {
	campusId,
	tenantId,
	campusInfo,
	tenantInfo,
	attendanceStatusMap,
	semesterInfo,
	attendanceTypeMap,
	attendanceModeMap
} = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: null,
		begin_time: null,
		end_time: null,
		date: [],
		name: null
	}
}
export default {
	name: 'dept',
	components: {},
	data() {
		return {
			dialog: {
				save: false
			},
			apiObj: this.$API.log.login,
			selection: [],

			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			statusMap: [
				{ name: '成功', value: 1 },
				{ name: '失败', value: 2 }
			],
			typeMap: [
				{ name: 'PC', value: 1 },
				{ name: '手机', value: 2 }
			],
			attendanceStatusMap,
			attendanceTypeMap,
			attendanceModeMap,
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					/*{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},*/

					{
						label: null,
						name: 'date',
						value: null,
						component: 'cusDate',
						options: {
							placeholder: '请选择校区',
							type: 'daterange'
						}
					}
				]
			}
		}
	},
	watch: {},
	computed: {},
	async created() {},
	methods: {
		//添加
		add(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', row)
			})
			// this.upsearch()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.buildingRooms.AccommodationArrangements.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},

		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		//搜索
		upsearch() {
			this.params.begin_time = this.params.date ? this.params.date[0] : null
			this.params.end_time = this.params.date ? this.params.date[1] : null
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped>
.roomItem {
	& + & {
		margin-top: 5px;
	}
}
</style>
