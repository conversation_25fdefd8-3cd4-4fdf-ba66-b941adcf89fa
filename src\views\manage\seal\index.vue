<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList.length > 1" label="">
						<el-select
							v-model="params.campus_id"
							placeholder="请选择校区"
							filterable
							style="width: 180px"
							@change="upsearch"
						>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input
							v-model="params.name"
							placeholder="请输入公章名称或落款文字搜索"
							clearable
							style="width: 240px"
							@input="upsearch"
						></el-input>
					</el-form-item>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-CirclePlus" @click="add">新增公章</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
				<el-table-column label="公章名称" prop="name" width="250" fixed="left"></el-table-column>
				<el-table-column label="部门" prop="department_name" width="120"></el-table-column>
				<el-table-column label="落款文字" prop="signed" min-width="200"> </el-table-column>
				<el-table-column label="颁发单位" prop="issue" min-width="200"> </el-table-column>
				<el-table-column label="公章图片" prop="seal_img" width="150">
					<template #default="scope">
						<cusImage
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.seal_img"
							:preview-src-list="[scope.row.seal_img]"
							preview-teleported
						>
						</cusImage>
					</template>
				</el-table-column>
				<el-table-column label="状态" prop="status" width="120">
					<template #default="scope">
						<el-switch
							v-model="scope.row.status"
							inline-prompt
							active-color="#00B42A"
							style="--el-switch-on-color: #00B42A;"
							:active-value="1"
							:inactive-value="-1"
							@change="
								(val) => {
									changeStatus(val, scope.row)
								}
							"
						/>
					</template>
				</el-table-column>
<!--				<el-table-column label="使用次数" prop="cert_count" width="180"></el-table-column>-->
				<el-table-column label="操作" fixed="right" align="center" width="120">
					<template #default="scope">
						<el-button-group>
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>

	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:disciplineOptions="disciplineOptions"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import saveDialog from './save.vue'
import cusTom from '@/utils/cusTom'
import cusImage from '@/components/custom/cusImage.vue'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, campusInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		name: null
	}
}
export default {
	name: '',
	components: {
		cusImage,
		saveDialog
	},
	data() {
		return {
			dialog: {
				save: false
			},
			apiObj: this.$API.seal.list,
			selection: [],
			params: defaultParams(),
			CampusManagementList: campusInfo,
			treeData: null,
			disciplineOptions: []
		}
	},
	watch: {},
	computed: {},
	async created() {},
	methods: {
		//添加
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add')
			})
			// this.upsearch()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		changeStatus(val, item) {
			item.status = val
			this.$API.seal.save.post(item).then((res) => {
				if (res.code === 200) {
					ElMessage({ type: 'success', message: '操作成功！' })
				} else {
					ElMessage({ type: 'error', message: res.message })
				}
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.seal.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else {
				this.$refs.table.refresh()
			}
		}
	}
}
</script>

<style lang="scss" scoped></style>
