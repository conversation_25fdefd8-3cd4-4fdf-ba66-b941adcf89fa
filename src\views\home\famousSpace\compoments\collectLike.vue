<template>
	<div>
		<el-table v-loading="loading" :data="data.rows" element-loading-text="数据加载中">
			<el-table-column label="操作人姓名" prop="action_user_name"></el-table-column>
			<el-table-column label="用户类型" prop="user_type">
				<template #default="{ row }">
					<el-tag v-if="row.user_type === 1" type="success">学员</el-tag>
					<el-tag v-if="row.user_type === 2" type="primary">教职工</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="操作类型" prop="action_type">
				<template #default="{ row }">
					<el-tag v-if="row.action_type === 1" type="success">点赞</el-tag>
					<el-tag v-else type="warning">收藏</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="操作时间" prop="created_at"></el-table-column>
		</el-table>
	</div>
</template>

<script>
export default {
	name: 'collectLike',
	inject: ['resourcesData',"onlyShow"],
	data() {
		return {
			loading: false,
			data: {},
			params: {
				page: 1,
				pageSize: 10,
				resources_id: this.resourcesData.id,
				tenant_id: this.resourcesData.tenant_id,
				campus_id: this.resourcesData.campus_id,
				action_type: 0
			}
		}
	},
	created() {
		this.getList()
	},
	methods: {
		getList() {
			this.loading = true
			this.$API.famous.resources.getCollectLikeList.get(this.params).then((res) => {
				this.loading = false
				if (res.code === 200) {
					this.data = res.data
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
:deep(.content) {
	font-size: 14px;
	line-height: 25px;
	padding: 20px;

	img {
		max-width: 100% !important;
	}
}
.files {
	font-size: 14px;
	line-height: 25px;
	padding: 0px 20px 20px 20px;
}
</style>
