<script>
import { ElMessage } from 'element-plus'

export default {
	name: 'schedule',
	data() {
		return {
			visible: false,
			title: '课表',
			scheduleList: {},
			roomData: {},
			classType: [
				{
					label: '行政班',
					value: 1
				},
				// {
				//     label: '走班',
				//     value: 2
				// },
				{
					label: '班会',
					value: 3
				},
				{
					label: '自习',
					value: 4
				},
				{
					label: '活动',
					value: 5
				},
				{
					label: '劳动',
					value: 6
				}
			]
		}
	},
	methods: {
		open(roomData) {
			this.title = "课表："+ roomData.building_name+'-'+roomData.floor_name+'-'+roomData.room_name
			console.log(roomData)
			this.roomData = roomData
			this.visible = true
			this.getMySchedule()
			return this
		},
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.label || val
		},
		getMySchedule() {
			this.$API.fieldRoom.rooms.schedule
				.get({
					tenant_id: this.roomData.tenant_id,
					campus_id: this.roomData.campus_id,
					id: this.roomData.id
				})
				.then((res) => {
					if (res.code === 200) {
						this.scheduleList = res.data
						if (res.data.periods_item) {
							this.scheduleList.periods_item = this.upData(res.data.periods_item)
						}
					} else {
						ElMessage.error(res.msg)
					}
					console.log(this.scheduleList)
				})
		},
		upData(arry) {
			return arry.map((period) => {
				const item = {
					item_name: period.item_name,
					begin_time: period.begin_time,
					end_time: period.end_time,
					value: []
				}
				this.scheduleList.attend_day.forEach((day) => {
					const scheduleItem = this.scheduleList.class_schedule_list.find(
						(item) => item.periods_item_id === period.periods_item_id && item.attend_day === day
					)
					item.value.push(scheduleItem ? scheduleItem : '')
				})
				return item
			})
		}
	}
}
</script>

<template>
	<el-drawer v-model="visible" :title="title" size="70%" destroy-on-close @closed="$emit('closed')">
		<div v-if="scheduleList.class_schedule_list" class="schedule-content">
			<ul class="table th">
				<li class="row">
					<div class="row-data">
						<span class="cell">时间 / 日期</span>
						<span v-for="item in scheduleList.attend_day" :key="item" class="cell">{{ item }}</span>
					</div>
				</li>
			</ul>
			<ul class="table td">
				<li v-for="item in scheduleList.periods_item" class="row">
					<div v-if="item.value" class="row-data">
						<span class="cell">{{ item.item_name }}<br />{{ item.begin_time + '~' + item.end_time }}</span>
						<span v-for="items in item.value" :key="items" class="cell">
							<div v-if="items && items.classroom_type === 1 && items.course_name" class="cell-info">
								<p>
									{{ items.course_name?.name }} <span>({{ items.teacher_name?.name }})</span>
								</p>
								<p>{{ items.grade_name?.name }}/{{ items.class_name?.name }}</p>
							</div>
							<div
								v-else-if="items && items.classroom_type !== 1"
								:class="['cell-info', 'color' + items.classroom_type]"
							>
								<p>{{ formData(classType, items.classroom_type) }}</p>
							</div>
						</span>
					</div>
				</li>
			</ul>
		</div>
		<div v-else class="schedule-content">
			<el-empty :image-size="200" description="当前房间暂无课程安排"></el-empty>
		</div>
	</el-drawer>
</template>

<style lang="scss" scoped>
.bg {
	background-color: var(--el-color-info-light-9);
}

.schedule-content {
	// min-height: 350px;

	.table {
		width: 100%;
		display: flex;

		.row {
			border-left: 1px solid var(--el-border-color-light);
			border-right: 1px solid var(--el-border-color-light);
			border-bottom: 1px solid var(--el-border-color-light);

			.row-data {
				display: flex;
				text-align: center;

				.bg1.selected {
					color: var(--el-color-primary);
					background-color: var(--el-color-primary-light-8);
				}

				.bg3.selected {
					color: var(--el-color-success);
					background-color: var(--el-color-success-light-8);
				}

				.bg4.selected {
					color: var(--el-color-warning);
					background-color: var(--el-color-warning-light-8);
				}

				.bg5.selected {
					color: var(--el-color-info);
					background-color: var(--el-color-info-light-8);
				}

				.bg6.selected {
					color: var(--el-color-danger);
					background-color: var(--el-color-danger-light-8);
				}
			}

			.cell {
				flex: 1;
				text-align: center;
				min-width: 100px;
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				padding: 10px;
				border-right: 1px solid var(--el-border-color-light);
				&:first-child {
					flex: 0;
					min-width: 90px;
				}

				&:last-child {
					border-right: none;
				}
			}
		}
	}

	.th {
		.row {
			width: 100%;
			border-top: 1px solid var(--el-border-color-light);
			background-color: var(--el-border-color-extra-light);
		}

		.row-data {
			width: 100%;
			height: 50px;

			.cell {
				font-weight: bold;
			}
		}
	}

	.td {
		flex-direction: column;

		.row {
			.row-data {
				min-height: 70px;

				.cell {
					p span {
						font-size: 12px;
					}
					&:first-child {
						font-weight: bold;
						background-color: var(--el-border-color-extra-light);
					}

					.cell-info {
						p:first-child {
							font-size: 14px;
							font-weight: bold;
							padding-bottom: 5px;
						}
					}
				}
			}
		}
	}
}
.color1 {
	color: var(--el-color-primary);
}

.color3 {
	color: var(--el-color-success);
}

.color4 {
	color: var(--el-color-warning);
}

.color5 {
	color: var(--el-color-info);
}

.color6 {
	color: var(--el-color-danger);
}
</style>
