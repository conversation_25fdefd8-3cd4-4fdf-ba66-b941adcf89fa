<template>
	<div class="adminui-topbar" :class="layout">
		<div class="left-panel">
			<el-breadcrumb separator-icon="el-icon-arrow-right" class="hidden-sm-and-down">
				<transition-group name="breadcrumb">
					<template v-for="item in breadList" :key="item.title">
						<el-breadcrumb-item v-if="item.path !== '/' && !item.meta.hiddenBreadcrumb" :key="item.meta.title">
							<el-icon v-if="item.meta.icon" class="icon" :style="{ color: item.meta.color }">
								<component :is="item.meta.icon" />
							</el-icon>
							<span :style="{ color: item.meta.color }">{{ item.meta.title }}</span>
						</el-breadcrumb-item>
					</template>
				</transition-group>
			</el-breadcrumb>
		</div>
		<div class="center-panel"></div>
		<div class="right-panel">
			<slot></slot>
		</div>
	</div>
</template>

<script>
export default {
	props: ['layout'],
	data() {
		return {
			breadList: []
		}
	},
	created() {
		this.getBreadcrumb()
	},
	watch: {
		$route() {
			this.getBreadcrumb()
		}
	},
	methods: {
		getBreadcrumb() {
			this.breadList = this.$route.meta.breadcrumb
		},
		goBack() {
			this.$router.go(-1)
		}
	}
}
</script>

<style scoped>
.panel-item {
	padding: 0 10px;
	cursor: pointer;
	height: 100%;
	display: flex;
	align-items: center;
}

.panel-item i {
	font-size: 16px;
}

.panel-item:hover {
	background: rgba(0, 0, 0, 0.1);
}
.el-breadcrumb {
	margin-left: 15px;
}

.adminui-topbar-menu .el-breadcrumb {
	margin-left: 0px;
}

.el-breadcrumb .el-breadcrumb__inner .icon {
	font-size: 14px;
	margin-right: 5px;
	float: left;
}

.breadcrumb-enter-active,
.breadcrumb-leave-active {
	transition: all 0.3s;
}

.breadcrumb-enter-from,
.breadcrumb-leave-active {
	opacity: 0;
	transform: translateX(20px);
}

.breadcrumb-leave-active {
	position: absolute;
}
</style>
