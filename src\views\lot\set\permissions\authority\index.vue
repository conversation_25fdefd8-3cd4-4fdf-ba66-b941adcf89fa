<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item label="">
						<el-select
							v-if="CampusManagementList.length > 1"
							v-model="params.campus_id"
							placeholder="请选择校区"
							filterable
							style="width: 150px"
						>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="请输入人员姓名搜索" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-CirclePlus" @click="add">新增场室权限</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :apiObj="list.apiObj" :params="params" :hideDo="false">
				<el-table-column label="人员" prop="user_info" width="150" fixed="left">
					<template #default="scope">
						<span v-if="scope.row.user_info">{{ scope.row.user_info.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="场室范围" prop="room_list">
					<template #default="scope">
						<template v-if="scope.row.authority_type === 2">
							<el-tooltip v-if="scope.row.room_info.length > 0" class="tableTips" :raw-content="true" effect="light">
								<template #content>
									<div style="max-width: 800px">
										<el-tag v-for="(item, index) in scope.row.room_info" :key="index" style="margin-bottom: 8px">{{
											item.name
										}}</el-tag>
									</div>
								</template>
								<div class="overflowTips">
									<template v-for="item in scope.row.room_info" :key="item.id">
										<span>{{ item.name }} </span>
										<el-divider direction="vertical" />
									</template>
								</div>
							</el-tooltip>
						</template>
						<template v-else>
							<el-tag type="success">全部场室</el-tag>
						</template>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="145"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="145">
					<template #default="scope">
						<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)"
							>编辑权限</el-button
						>
						<el-divider direction="vertical" />
						<el-button text type="danger" size="small" @click="table_del(scope.row, scope.$index)">删除</el-button>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import saveDialog from './save'
import ScTable from '@/components/scTable/index.vue'
import { ElMessageBox } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		campus_id: campusId,
		engine_type: null,
		status: null
	}
}

export default {
	name: 'alertRule',
	data() {
		return {
			groupFilterText: '',
			statusLoading: false,
			groupData: [],
			CampusManagementList: [],
			enumConfig: [],
			alertLevelStatus: {
				1: 'danger',
				2: 'warning',
				3: 'primary',
				4: 'info'
			},
			list: {
				apiObj: this.$LotApi.authority.list
			},
			params: defaultParams(),
			search: {
				name: null
			},
			dialog: {
				save: false,
				ruleSet: false
			}
		}
	},
	components: {
		ScTable,
		saveDialog
	},
	watch: {},
	created() {
		this.CampusManagementList = campusInfo
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	computed: {},
	methods: {
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//新增职位
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', this.params.campus_id)
			})
		},
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.$refs.table.refresh()
			}
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit', row.campus_id).setData(row)
			})
		},
		table_rule(row) {
			this.dialog.ruleSet = true
			this.$nextTick(() => {
				this.$refs.ruleSetDialog.open(row)
			})
		},
		//查看
		table_show(row) {
			/*this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})*/
			this.$router.push({
				name: 'lotSetTemplateTsl',
				params: {
					id: row.id
				}
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: row.tenant_id, campus_id: row.campus_id }
			ElMessageBox.confirm('确定要删除此场景吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.authority.del.post(reqData)
				if (res.code === 200) {
					this.$message.success('删除成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
.overflowTips {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
</style>
