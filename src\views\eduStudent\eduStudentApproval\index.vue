<template>
	<el-container>
		<el-header>
			<div class="search-panel">
				<el-select
					v-if="CampusManagementList.length > 1"
					v-model="params.campus_id"
					placeholder="校区"
					filterable
					style="margin-right: 15px"
				>
					<el-option
						v-for="item in CampusManagementList"
						:key="item.code"
						:label="item.name"
						:value="item.value"
					></el-option> </el-select
				><!--
				<cusSelectSemester
					v-model="params.semester_id"
					clearable
					placeholder="请选择学期"
					style="margin-right: 15px"
					width="200px"
					:params="params"
					@semesterChange="semesterChange"
				/>-->
				<cusSelectSemester
					v-model="params.semester_id"
					:params="params"
					:show-default-value="true"
					:width="'214px'"
					clearable
					style="margin-right: 15px"
				/>
				<cusSelectClass
					v-model="params.class_id"
					clearable
					placeholder="请选择班级"
					style="margin-right: 15px"
					width="200px"
					@classChange="classChange"
				/>
				<el-select v-model="params.status" placeholder="请选择状态" clearable @change="statusChange">
					<el-option v-for="item in statusMap" :key="item.value" :label="item.name" :value="item.value" />
				</el-select>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
				<el-table-column prop="vacate_type" label="请假类型" width="100">
					<template #default="scope">
						{{ formData(studentVacateTypeMap, scope.row.vacate_type) }}
					</template>
				</el-table-column>
				<el-table-column prop="student_name" label="申请人" width="100">
					<template #default="scope">
						{{ scope.row.student.name }}
					</template>
				</el-table-column>
				<el-table-column prop="semester" label="学期" width="180">
					<template #default="scope">
						{{ scope.row.academic.name + '-' + scope.row.semester.name }}
					</template>
				</el-table-column>
				<el-table-column prop="class" label="班级" width="120">
					<template #default="scope">
						{{ scope.row.grade.name + scope.row.class.name }}
					</template>
				</el-table-column>
				<el-table-column prop="begin_time" label="请假时间" width="180">
					<template #default="scope"> {{ scope.row.begin_time }} 至 {{ scope.row.end_time }} </template>
				</el-table-column>
				<el-table-column
					prop="reason"
					label="申请事由"
					max-width="300"
					min-width="200"
					show-overflow-tooltip
				></el-table-column>
				<el-table-column prop="img" label="附件图片" width="300">
					<template #default="{ row }">
						<div v-if="row.img !== ''">
							<el-image
								v-for="(item, index) in row.img.split(',')"
								:src="item"
								:preview-src-list="row.img.split(',')"
								z-index="1000000000"
								:initial-index="index"
								preview-teleported
								style="width: 50px; height: 50px; margin: 0px 5px"
								fit="cover"
							>
							</el-image>
						</div>
					</template>
				</el-table-column>
				<el-table-column prop="status" label="状态" width="100">
					<template #default="scope">
						<el-tag v-if="scope.row.status === 1" type="success">已审批</el-tag>
						<el-tag v-if="scope.row.status === -1">审核中</el-tag>
						<el-tag v-if="scope.row.status === 2" type="success">已销假</el-tag>
						<el-tag v-if="scope.row.status === -2" type="warning">待销假</el-tag>
						<el-tag v-if="scope.row.status === -3" type="danger">已驳回</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="180">
					<template #default="scope">
						<el-button-group>
							<el-button
								v-if="scope.row.status === -1 || scope.row.status === -2"
								text
								type="primary"
								size="small"
								@click="table_pass(scope.row, scope.$index)"
								>通过</el-button
							>
							<el-button
								v-if="scope.row.status === -1 || scope.row.status === -2"
								text
								type="danger"
								size="small"
								@click="table_reject(scope.row, scope.$index)"
								>驳回</el-button
							>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<save ref="saveDialog" @success="saveSuccess"></save>
	</el-container>
</template>

<script>
import cusSelectClass from '@/components/custom/cusSelectClass.vue'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'
import save from './save.vue'
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, studentVacateTypeMap } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: null,
		class_id: null,
		status: null
	}
}
export default {
	components: {
		cusSelectClass,
		cusSelectSemester,
		save
	},
	data() {
		return {
			params: defaultParams(),
			CampusManagementList: campusInfo,
			apiObj: this.$API.applyLeave.list,
			studentVacateTypeMap,
			statusMap: [
				{
					name: '审批中',
					value: -1
				},
				{
					name: '已审批',
					value: 1
				},
				{
					name: '待销假',
					value: -2
				},
				{
					name: '已销假',
					value: 2
				},
				{
					name: '已驳回',
					value: -3
				}
			],
			applyId: ''
		}
	},
	methods: {
		semesterChange(val) {
			this.params.semester_id = val
			console.log(val, 'semesterChange')
			this.$refs.table.upData(this.params)
		},
		classChange(val) {
			this.params.class_id = val
			this.$refs.table.upData(this.params)
		},
		statusChange(val) {
			this.params.status = val
			this.$refs.table.upData(this.params)
		},
		saveSuccess(data) {
			let params = {
				id: this.applyId,
				tenant_id: tenantId,
				campus_id: campusId,
				audit_action: data.action,
				audit_remark: data.audit_remark
			}
			this.$API.applyLeave.audit.post(params).then((res) => {
				if (res.code == 200) {
					ElMessage.success(res.message)
				} else {
					ElMessage.error(res.message)
				}
				// this.$refs.table.upData(this.params)
				this.$refs.table.refresh()
			})
			console.log(data, 'saveSuccess')
		},
		table_pass(row, index) {
			this.applyId = row.id
			this.$refs.saveDialog.open(1)
			console.log(row, index, '通过')
		},
		table_reject(row, index) {
			this.applyId = row.id
			this.$refs.saveDialog.open(-1)
			console.log(row, index, '驳回')
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || '-'
		}
	}
}
</script>

<style lang="scss" scoped></style>
