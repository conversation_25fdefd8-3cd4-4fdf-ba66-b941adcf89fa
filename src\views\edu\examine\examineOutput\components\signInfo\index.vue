<template>
    <el-table :data="monitorTeacherList" style="width: 100%" max-height="600" @cell-click="handleCellClick">
        <el-table-column prop="room_info" label="考场/时间" align="center">
            <template #default="{ row }">
                <div>{{ row.room_info?.name }}</div>
                <!-- <div v-if="row.room_id !== 0">({{ row.teacher_num }})</div> -->
            </template>
        </el-table-column>
        <el-table-column v-for="(item, index) in dateList" :key="index" :label="item.date" align="center">
            <el-table-column v-for="items in item.course" :key="items.id" :prop="items.time"
                :label="`${items.course_name}`" :index="items.time_id" align="center">
                <template #header="{ column }">
                    <div>{{ column.label }}</div>
                    <div>{{ column.property }}</div>
                </template>
                <template #default="{ row }">
                    <!-- <template v-for="tea in row.teachers" :key="tea.id">
                        <div v-if="items.id == tea.time_id" class="font-center">
                            <span>{{ tea.staff_info?.name }}</span>
                        </div>
                    </template> -->
                    <el-button type="primary" text>
                        查看
                    </el-button>
                </template>
            </el-table-column>
        </el-table-column>
    </el-table>
    <infoDialog ref="infoDialogRef"></infoDialog>
</template>
<script setup>
import infoDialog from './infoDialog.vue'
import { excelExport } from 'pikaz-excel-js'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()

const defaultParams = () => {
    return {
        tenant_id: tenantId,
        campus_id: campusId,
        examine_id: query.id
    }
}
const params = ref(defaultParams())

// 单元个点击
const infoDialogRef = ref()
const handleCellClick = (row, column) => {
    if (!column.rawColumnKey) return
    params.value.room_id = row.room_id
    params.value.time_id = column.rawColumnKey
    infoDialogRef.value.open(params.value)
}

// 导出
const exportExcel = async () => {
    let merges = ['1-2:1-5', '2-2:2-5']
    const headers = [
        { label: '序号', key: 'index' },
        { label: '考场', key: 'room' }
    ]
    let startColumn = 2 // 从第4列开始（前2列是固定的：序号、考场）
    dateList.value.forEach((date, index) => {
        // 计算当前日期的课程占用的总列数（每个课程占2列：地点和座位号）
        const coursesColumns = date.course.length
        console.log(date.course.length, 'coursesColumns')
        // 合并日期行，从startColumn列开始，跨coursesColumns列
        merges.push(`${startColumn - 2 + 1 + 2}-2:${startColumn + coursesColumns}-2`)

        date.course.forEach((course, courseIndex) => {
            //计算当前课程的起始列
            const courseStartColumn = startColumn + courseIndex
            // 合并课程行，每个课程合并2列（地点和座位号）
            merges.push(`${courseStartColumn + 1}-3:${courseStartColumn + 1}-5`)

            headers.push({
                label: `${course.course_name}\r\n${course.time}`,
                key: `course_${course.id}`,
                date: courseIndex == 0 ? date.date : ''
            })
        })
        // 更新下一个日期的起始列
        startColumn += coursesColumns
    })
    const tableData = monitorTeacherList.value.map((item, index) => {
        let data = {
            index: index + 1,
            room: item.room_info?.name
        }
        dateList.value.forEach((date) => {
            date.course.forEach((course) => {
                // 查找对应考试时段的教师信息
                let hasTea = item.teachers.filter((teacher) => teacher.time_id === course.id)
                data[`course_${course.id}`] = hasTea.map((teacher) => teacher.staff_info?.name).join('\r\n')
            })
        })
        return data
    })
    await excelExport({
        sheet: [
            {
                title: '监考总表',
                globalStyle: { font: { sz: 14 }, alignment: { wrapText: true } },
                cellStyle: [{ cell: 'A1', font: { bold: true, sz: 18 } }],
                sheetName: '监考总表',
                merges: merges,
                keys: headers.map((h) => h.key),
                table: [
                    Object.fromEntries(headers.map((h, i) => [h.key, i < 2 ? h.label : h.date || ''])),
                    Object.fromEntries(headers.map((h, i) => [h.key, i > 1 && h.key.includes('course') ? h.label : ''])),
                    Object.fromEntries(headers.map((h, i) => [h.key, i < 2 ? h.label : ''])),
                    Object.fromEntries(headers.map((h, i) => [h.key, i < 2 ? h.label : ''])),
                    ...tableData
                ]
            }
        ],
        filename: query.name + '-监考总表'
    })
}

const monitorTeacherList = ref([])
const dateList = ref([])
const timeList = ref([])
const roomList = ref([])
const staffList = ref([])
const getMonitorList = () => {
    globalPropValue.examine.getMonitorTeacherList.get(params.value).then((res) => {
        if (res.code === 200) {
            // 按room_id对数据进行分组
            const groupedData = res.data.reduce((acc, curr) => {
                // 去掉巡考人员数据
                if (curr.room_id == 0) return acc
                if (!acc[curr.room_id]) {
                    acc[curr.room_id] = {
                        room_id: curr.room_id,
                        room_info: null,
                        teachers: []
                    }
                }
                acc[curr.room_id].teachers.push(curr)
                return acc
            }, {})
            monitorTeacherList.value = Object.values(groupedData)
            getTimeData()
            getStaffList()
        }
    })
}

//获取监考人员数据
const getStaffList = () => {
    globalPropValue.examine.staffList
        .get({
            tenant_id: tenantId,
            campus_id: campusId,
            examine_id: query.id
        })
        .then((res) => {
            if (res.code === 200) {
                staffList.value = res.data.filter((item) => item.user_type != 1 && item.user_type != 2 && item.user_type != 4)
            }
        })
}
// 获取时间段列表
const getTimeData = () => {
    globalPropValue.examine.timeList.get(params.value).then((res) => {
        if (res.code === 200) {
            timeList.value = res.data
            const groupedData = {}
            res.data.forEach((item) => {
                if (!groupedData[item.examine_date]) {
                    groupedData[item.examine_date] = {
                        date: item.examine_date,
                        course: []
                    }
                }
                groupedData[item.examine_date].course.push({
                    id: item.id,
                    course_name: item.course_name,
                    time: item.begin_time + '-' + item.end_time,
                })
            })
            dateList.value = Object.values(groupedData)
            console.log(dateList.value)
            getRoomList()
        }
    })
}
// 获取考场列表
const getRoomList = () => {
    globalPropValue.examine.roomList.get(params.value).then((res) => {
        if (res.code === 200) {
            roomList.value = [...res.data]
            monitorTeacherList.value.forEach((item) => {
                const roomData = roomList.value.find((room) => room.room_id === item.room_id)
                if (roomData) {
                    item.room_info = roomData.room_info
                    item.student_num = roomData.student_num
                    item.teacher_num = roomData.teacher_num
                }
            })
            console.log(monitorTeacherList.value)
        }
    })
}

onMounted(() => {
    getMonitorList()
})
</script>

<style scoped lang="scss">
.font-center {
    font-size: 15px;
}
</style>
