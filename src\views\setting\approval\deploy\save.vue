<template>
	<el-dialog v-model="dialogVisible" :title="title" width="500">
		<cusForm ref="formref" v-model="form" :config="formConfig"></cusForm>
		<template #footer>
			<el-button @click="dialogVisible = false">取消</el-button>
			<el-button type="primary" @click="confirm">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import cusTom from '@/utils/cusTom'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { tenantId, campusId, approvalModelMap } = cusTom.getBaseQuery()
const emit = defineEmits(['success'])
const dialogVisible = ref(false)
const defaultData = () => {
	return {
		id: null,
		tenant_id: tenantId,
		campus_id: campusId,
		approval_name: '',
		approval_send: [],
		mode: null,
		form_id: null,
		scope: null,
		icon: null,
		icon_color: null,
		remark: ''
	}
}
let form = ref(defaultData())
let formref = ref(null)
const title = ref('新增')
const formConfig = ref({
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: '审批名称',
			name: 'approval_name',
			value: null,
			component: 'input',
			options: {
				placeholder: '请输入审批名称'
			}
		},
		{
			label: '所属模块',
			name: 'mode',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择模块',
				items: approvalModelMap.map((item) => {
					item.label = item.name
					return item
				})
			}
		},
		{
			label: '关联表单',
			name: 'form_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择关联表单',
				items: []
			}
		},
		{
			label: '范围',
			name: 'scope',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择范围',
				items: [
					{ label: '全部', value: 0 },
					{ label: '教职工', value: 1 },
					{ label: '学生', value: 2 }
				]
			}
		},
		/*{
			label: '抄送人员',
			name: 'approval_send',
			value: null,
			multiple: true,
			readonly: false,
			component: 'cusSelectTeacher'
		},*/
		{
			label: '图标',
			name: 'icon',
			value: null,
			component: 'upload',
			options: {
				items: [
					{
						name: 'icon_img',
						label: '图标',
						type: 'icon'
					}
				]
			}
		},
		// {
		//     label: '图标颜色',
		//     name: 'icon_color',
		//     value: null,
		//     component: 'color',
		//     options: {
		//         placeholder: '请选择图标颜色'
		//     }
		// },
		{
			label: '描述',
			name: 'remark',
			value: null,
			component: 'textarea',
			options: {
				placeholder: '请输入描述'
			}
		}
	]
})
const open = (data) => {
	dialogVisible.value = true
	if (data) {
		data.approval_send = data.approval_send_list?.map((item) => {
			return {
				label: item.name,
				id: item.id
			}
		})
		form.value = data
		title.value = '编辑'
	} else {
		setTimeout(() => {
			formref.value.resetFields()
			form.value = defaultData()
			title.value = '新增'
			console.log(form.value, 'form.value')
		}, 0)
	}
	getFormList()
}
const confirm = () => {
	emit('success', form.value)
	dialogVisible.value = false
}

const getFormList = async () => {
	const { data } = await globalPropValue.form.form.all.get({ tenant_id: tenantId, campus_id: campusId, form_type: 2 })
	if (data) {
		formConfig.value.formItems[2].options.items = data.map((item) => {
			return {
				label: item.title,
				value: item.id
			}
		})
	}
	// console.log(data, '获取表单列表')
}
defineExpose({
	dialogVisible,
	open
})
</script>
