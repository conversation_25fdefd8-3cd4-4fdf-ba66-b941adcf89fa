<template>
	<el-drawer
		v-model="visible"
		title="任课老师管理"
		size="60%"
		:close-on-press-escape="false"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<div>
			<el-table v-loading="loading" element-loading-text="数据加载中" :data="data" empty-text="请配置关联年级学科">
				<el-table-column prop="date" label="学科/班主任">
					<template #default="scope">
						<span v-if="scope.row.course">
							<sc-status-indicator v-if="scope.row.is_head === 1" type="danger"></sc-status-indicator>
							{{ scope.row.course.name }}
						</span>
					</template>
				</el-table-column>
				<el-table-column prop="name" label="教师" width="200">
					<template #default="scope">
						<cusSelectTeacher v-model="scope.row.teacherObj" width="150" placeholder="请选择教师"></cusSelectTeacher>
					</template>
				</el-table-column>
				<el-table-column prop="address" label="周课时数" width="200">
					<template #default="scope">
						<el-input-number
							v-if="scope.row.is_head !== 1"
							v-model="scope.row.week_num"
							:min="0"
							:max="100"
							placeholder="周课时数"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="address" label="上课教室"  width="250">
					<template #default="scope">
						<cusSelectField
							v-if="scope.row.is_head !== 1"
							v-model="scope.row.roomObj"
							width="200"
							:multiple="false"
						></cusSelectField>
					</template>
				</el-table-column>
				<el-table-column prop="address" label="职务" width="150">
					<template #default="scope">
						<el-input v-if="scope.row.is_head !== 1" v-model="scope.row.position" placeholder="职务" />
					</template>
				</el-table-column>
			</el-table>
		</div>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import CusSelectTeacher from '@/components/custom/cusSelectTeacher.vue'
import { cloneDeep } from 'lodash'

const defaultData = () => {
	return {
		tenant_id: null,
		campus_id: null,
		class_id: null
	}
}
export default {
	components: { CusSelectTeacher },
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			data: [],
			visible: false,
			loading: false,
			isSaveing: false,
			//表单数据
			form: defaultData()
		}
	},
	mounted() {},
	computed: {},
	methods: {
		//显示
		open(data) {
			this.form.tenant_id = data.tenant_id
			this.form.campus_id = data.campus_id
			this.form.class_id = data.id
			this.getTeacher()
			this.visible = true
			return this
		},

		async getTeacher() {
			this.loading = true
			var res = await this.$API.class.teacherData.get(this.form)
			this.loading = false
			res.data.map((v) => {
				if (v.teacher) {
					v.teacherObj = [
						{
							id: v.teacher.id,
							label: v.teacher.name
						}
					]
				}else{
					v.teacherObj=null
				}
				if (v.room) {
					v.roomObj = [
						{
							id: v.room.id,
							label: v.room.name
						}
					]
				}else{
					v.roomObj=null
				}
				return v
			})
			console.log(res.data)
			this.data = res.data
		},
		//表单提交方法
		async submit() {
			let formData = cloneDeep(this.data)
			formData.map((v) => {
				if (v.teacherObj && v.teacherObj.length > 0) {
					v.staff_id = v.teacherObj[0].id
				} else {
					v.staff_id = 0
				}
				if (v.roomObj && v.roomObj.length > 0) {
					v.room_id = v.roomObj[0].id
				} else {
					v.room_id = 0
				}
			})
			this.isSaveing = true
			var res = await this.$API.class.teacherSave.post({
				data: formData,
				...this.form
			})
			this.isSaveing = false
			if (res.code === 200) {
				this.$message.success('操作成功')
				this.getTeacher()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		}
	}
}
</script>

<style></style>
