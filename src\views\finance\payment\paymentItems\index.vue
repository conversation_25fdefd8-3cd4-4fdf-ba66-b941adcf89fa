<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" @click="add_items">新增缴费项</el-button>
				<el-button type="primary" :disabled="!selectedIdLength > 0" @click="add_receipt">创建缴费单</el-button>
				<el-button type="primary" @click="type">缴费项类型管理</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable
				ref="table"
				row-key="id"
				:params="params"
				:apiObj="apiObj"
				@selection-change="selectionChange"
				@select="selected"
			>
				<el-table-column fixed="left" :reserve-selection="true" type="selection" width="55" />
				<el-table-column label="学年学期" prop="academic_name" width="200" show-overflow-tooltip>
					<template #default="{ row }"> {{ row.academic_name }} - {{ row.semester_name }} </template>
				</el-table-column>
				<el-table-column label="缴费项名称" prop="item_name" width="150"></el-table-column>
				<el-table-column label="缴费项类型" prop="type_name" width="150"></el-table-column>
				<el-table-column label="数量" prop="num" width="100">
					<template #default="{ row }"> {{ row.num }} </template>
				</el-table-column>
				<el-table-column label="单位" prop="unit" width="100"></el-table-column>
				<el-table-column label="金额" prop="amount" width="150"></el-table-column>
				<el-table-column label="状态" prop="status" width="150">
					<template #default="scope">
						<el-tag v-if="scope.row.status === 1" type="success">正常</el-tag>
						<el-tag v-if="scope.row.status === -1" type="danger">作废</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="备注" prop="remark"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="180">
					<template #default="scope">
						<el-button-group>
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm title="确定作废吗？" @confirm="table_cancel(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="info" size="small">作废</el-button>
								</template>
							</el-popconfirm>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<!-- 新增缴费项 -->
		<saveDialog ref="dialog" :params="params" @success="handleSaveSuccess"></saveDialog>
		<!-- 设置缴费项类别 -->
		<typeDialog ref="dialogType" :params="params"></typeDialog>
		<!-- 创建缴费单 -->
		<orderDialog ref="dialogOrder" :params="params" :order_items="selected_id"></orderDialog>
	</el-container>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick, watch, computed } from 'vue'
import cusTom from '@/utils/cusTom'
import saveDialog from './save.vue'
import typeDialog from './type.vue'
import orderDialog from './saveOrder.vue'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null
	}
}
let apiObj = ref(globalPropValue.finance.recruit.list)
let params = ref(defaultParams())
console.log(params.value.tenant_id)
let searchConfig = ref({
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: null,
			name: 'campus_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择校区',
				noClearable: true,
				items: campusInfo.map((v) => {
					return {
						label: v.name,
						value: v.value
					}
				})
			}
		},
		{
			label: null,
			name: 'name',
			value: null,
			component: 'input',
			options: {
				placeholder: '请输入缴费项名称',
				items: []
			}
		},
		{
			label: null,
			name: 'academic_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学年',
				items: []
			}
		},
		{
			label: null,
			name: 'semester_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学期',
				items: []
			}
		}
	]
})
const handleSaveSuccess = (data, mode) => {
	if (mode === 'add') {
		upsearch()
	} else {
		table.value.refresh()
	}
}
const selectedIdLength = computed(() => {
	return selected_id.value.length
})

watch(
	() => params.value.campus_id,
	(val) => {
		params.value.academic_id = null
		searchConfig.value.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
			.filter((v) => v.parent_id === 0 && v.campus_id === val)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
watch(
	() => params.value.academic_id,
	() => {
		params.value.semester_id = null
		searchConfig.value.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
			.filter(
				(v) => v.parent_id !== 0 && v.parent_id === params.value.academic_id && v.campus_id === params.value.campus_id
			)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
let dialog = ref()
let dialogType = ref()
let dialogOrder = ref()
let table = ref()
const selectedItems = ref([])
let selected_id = ref([])
// let CampusManagementList = ref(campusInfo)
// 搜索按钮回调
const upsearch = () => {
	table.value.upData(params.value)
}
// 重置按钮回调
const refresh = () => {
	params.value = defaultParams()
	upsearch()
}
// 新增缴费项
const add_items = () => {
	nextTick(() => {
		dialog.value.open('add')
	})
}
// 创建缴费单
const add_receipt = () => {
	nextTick(() => {
		dialogOrder.value.open('add')
	})
}
// 缴费类型管理
const type = () => {
	nextTick(() => {
		dialogType.value.open()
	})
}
// 选择项操作变化
const selectionChange = (selection) => {
	selectedItems.value = selection
	if (selection) {
		selected_id.value = selection.map((v) => v.id)
	}
	console.log(selected_id.value, '选择项')
}
// 选择事件
const selected = (selection, row) => {
	selectedItems.value = selection
	console.log(JSON.stringify(selection[0]), JSON.stringify(row), '选择项')
}
// 编辑操作
const table_edit = (row) => {
	dialog.value.dialogFormVisible = true
	nextTick(() => {
		dialog.value.open('edit')
		dialog.value.setData(row)
	})
}
// 作废操作
const table_cancel = async (row) => {
	const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.finance.recruit.cancel.post(reqData)
	if (res.code === 200) {
		// upsearch()
		table.value.refresh()
		ElMessage({ type: 'success', message: '作废成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
// 删除操作
const table_del = async (row) => {
	const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.finance.recruit.del.post(reqData)
	if (res.code === 200) {
		// upsearch()
		table.value.refresh()
		ElMessage({ type: 'success', message: '删除成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
</script>

<style lang="scss" scoped></style>
