<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item label="学期/学年">
						<el-input v-model="params.name" placeholder="学期/学年名称" clearable></el-input>
					</el-form-item>

					<el-form-item v-if="CampusManagementList.length > 1" label="校区">
						<el-select v-model="params.campus_id" placeholder="校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">

				<el-button type="primary" plain icon="el-icon-link" style="margin-right: 20px" @click="syncCalendar">生成班牌校历</el-button>

				<el-button type="primary" icon="el-icon-plus" @click="add({ id: 0 })">新增学年</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable
				ref="table"
				row-key="id"
				:params="params"
				hidePagination
				:data="treeData"
				@selection-change="selectionChange"
			>
				<!-- <el-table-column type="selection" width="50"></el-table-column> -->
				<el-table-column label="名称" prop="semester_name" width="250">
					<template #default="scope">
						{{ scope.row.semester_name }}
						<el-tag v-if="scope.row.is_default === 1">默认</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="状态" prop="status" width="150">
					<template #default="scope">
						<el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
						<el-tag v-if="scope.row.status === -1" type="danger">停用</el-tag>
						<el-switch
							v-model="scope.row.status"
							style="margin-left: 10px"
							:active-value="1"
							:inactive-value="-1"
							@change="statusChange(scope.row)"
						></el-switch>
					</template>
				</el-table-column>
				<el-table-column label="开始日期" prop="semester_begin_date" width="250"></el-table-column>
				<el-table-column label="结束日期" prop="semester_end_date" width="250"></el-table-column>
				<el-table-column label="序号" prop="order" width="250">
					<template #default="scope">
						<div v-if="scope.row.parent_id !== 0">{{ scope.row.order }}</div>
						<div v-else></div>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="180"></el-table-column>
				<el-table-column label="备注" prop="remark" min-width="300"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="240">
					<template #default="scope">
						<el-button-group>
							<el-button v-if="scope.row.parent_id == 0" text type="primary" size="small" @click="add(scope.row)"
								>新增学期</el-button
							>
							<el-button
								v-if="scope.row.parent_id !== 0 && scope.row.is_default === 0"
								text
								type="primary"
								size="small"
								@click="setDefault(scope.row)"
								>默认学期</el-button
							>
							<!--							<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>-->
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>

	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import saveDialog from './save'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, tenantInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		name: null
	}
}
export default {
	name: 'dept',
	components: {
		saveDialog
	},
	data() {
		return {
			dialog: {
				save: false
			},
			// apiObj: this.$API.eduYearSemester.eduYear.all,
			selection: [],

			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null
		}
	},
	watch: {},
	computed: {},
	async created() {
		await this.getDept()
	},
	methods: {
		//获取部门
		async getDept() {
			const { data } = await this.$API.eduYearSemester.eduYear.all.get(this.params)
			this.treeData = cusTom.arrayToTree(data)
		},
		async syncCalendar() {
			this.$confirm(`确定将当前校区的学期配置同步到班牌校历？`, '提示', {
				type: 'warning'
			})
				.then(() => {
					this.$API.eduYearSemester.eduYear.syncCalendar.post(this.params).then((res) => {
						if (res.code === 200) {
							this.$message.success('同步成功')
						} else {
							this.$alert(res.message, '提示', { type: 'error' })
						}
					})
				})
				.catch(() => {})
		},
		//添加
		add(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', row)
			})
			// this.getDept()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.eduYearSemester.eduYear.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.getDept()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			this.$API.eduYearSemester.eduYear.status.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.getDept()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//设置默认学年
		setDefault(row) {
			this.$confirm(`确定将${row.semester_name}设置为默认学期吗？`, '提示', {
				type: 'warning'
			})
				.then(() => {
					let query = {
						id: row.id,
						tenant_id: this.params.tenant_id,
						campus_id: this.params.campus_id
					}
					this.$API.eduYearSemester.eduYear.setDefault.post(query).then((res) => {
						if (res.code === 200) {
							this.$message.success('操作成功')
							this.getDept()
						} else {
							this.$alert(res.message, '提示', { type: 'error' })
						}
					})
				})
				.catch(() => {})
		},
		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		//搜索
		upsearch() {
			this.getDept()
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.getDept()
		}
	}
}
</script>

<style lang="scss" scoped></style>
