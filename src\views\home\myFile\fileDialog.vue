<template>
	<el-dialog v-model="visable" :title="type[model]" destroy-on-close width="25%">
		<el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
			<el-form-item label="文件名称" prop="name">
				<el-input v-model="form.name" clearable placeholder="请输入"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button size="small" @click="visable = false">取消</el-button>
			<el-button type="primary" :loading="isSaveing" size="small" @click="save">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import { cloneDeep } from 'lodash'


const defaultForm = () => {
	return {
		id: null,
		tenant_id: null,
		campus_id: null,
		model: null,
		name: '',
		type_id: null,
		pid: null,
		file_type: null,
		suffix: '',
		suffix_type: 0,
		size: null,
		url: '',
		objkey: ''
	}
}
import { reactive, ref, useAttrs, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const attrs = useAttrs()
const emit = defineEmits(['success'])

let visable = ref(false)
let model = ref('add')
const type = ref({ add: '新增', edit: '编辑' })
let isSaveing = ref(false)
let formRef = ref(null)
const form = ref(defaultForm())
const rules = reactive({
	name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
})

const open = (mode = 'add', typeVal) => {
	form.value = defaultForm()
	model.value = mode
	form.value.id = typeVal?.id
	form.value.name = typeVal?.name
	form.value.tenant_id = attrs.params.tenant_id
	form.value.campus_id = attrs.params.campus_id
	form.value.suffix_type = attrs.params.suffix_type
	form.value.pid = attrs.params.pid
	form.value.type_id = attrs.params.type_id
	form.value.file_type = typeVal
	form.value.model = attrs.params.model
	visable.value = true
}
const save = async () => {
	await formRef.value.validate()
	if (model.value === 'add') {
		isSaveing.value = true
		const res = await globalPropValue.fileManagement.file.add_file.post(form.value)
		isSaveing.value = false
		if (res.code === 200) {
			ElMessage.success('添加成功')
			emit('success', form.value, model.value)
		} else {
			ElMessage.error(res.msg)
		}
	}
	if (model.value === 'edit') {
		isSaveing.value = true
		const res = await globalPropValue.fileManagement.file.edit_file.post(form.value)
		isSaveing.value = false
		if (res.code === 200) {
			ElMessage.success('修改成功')
			emit('success', form.value, model.value)
		} else {
			ElMessage.error(res.msg)
		}
	}
	visable.value = false
}
// 表单数据注入
const setForm = (data) => {
	form.value = cloneDeep(data)
}

defineExpose({
	open,
	setForm
})
</script>

<style scoped></style>
