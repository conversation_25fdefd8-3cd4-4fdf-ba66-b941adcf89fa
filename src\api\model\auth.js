import config from '@/config'
import http from '@/utils/request'

export default {
	login: {
		url: `${config.API_URL}/api/user/login`,
		name: '登录',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	loginForCode: {
		url: `${config.API_URL}/api/user/loginForCode`,
		name: '登录',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	logout: {
		url: `${config.API_URL}/sysapi/user/logout`,
		name: '退出登录',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	studentLogin:{
		url: `${config.API_URL}/stuapi/user/login`,
		name: '学生登录',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	}
}
