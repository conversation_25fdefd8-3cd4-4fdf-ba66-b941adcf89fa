<template>
	<el-drawer v-model="visible" title="耗材入库" destroy-on-close @closed="$emit('closed')">
		<cusForm ref="formref" v-model="form" :config="formConfig"> </cusForm>

		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import CusCascader from '@/components/custom/cusCascader.vue'
const defaultData = () => {
	return {
		tenant_id: null,
		campus_id: null,
		name: null,
		serial_number: null,
		type_id: null,
		storage_time: null,
		num: 1,
		unit: null,
		room_id: [],
		img: [],
		amount: null,
		storage_remark: null,
		type: '新耗材入库',
		haocai: [],
		id: 0
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		groupData: {
			type: Array,
			default: () => {
				return []
			}
		}
	},
	data() {
		return {
			mode: 'add',
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			titleMap: {
				edit: '离校学生-'
			},

			//验证规则
			rules: {
				name: [{ required: true, message: '请输入' }],
				serial_number: [{ required: true, message: '请输入' }],
				type_id: [{ required: true, message: '请选择' }],

				num: [{ required: true, message: '请输入' }],
				unit: [{ required: true, message: '请输入' }],
				room_id: [{ required: true, message: '请选择' }]
			},
			//所需数据选项
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				labelWidth: '130px',
				formItems: [
					{
						label: '入库类别',
						name: 'type',
						value: null,
						component: 'radio',
						options: {
							placeholder: '请选择',
							items: [
								{
									value: '新耗材入库',
									label: '新耗材入库'
								},
								{
									value: '已有耗材增加库存',
									label: '已有耗材增加库存'
								}
							]
						},
						rules: [{ required: true, message: '请选择状态', trigger: 'blur' }],
						hideHandle: '$.mode == "edit"'
					},
					{
						label: '耗材类别',
						name: 'type_id',
						value: null,
						component: 'cascader',
						options: {
							placeholder: '请选择耗材类别',
							prop: {
								emitPath: false,
								value: 'id',
								label: 'name',

								children: 'child'
							},
							items: []
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }],
						hideHandle: '$.type == "已有耗材增加库存"'
					},
					{
						label: '耗材名称',
						name: 'name',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入'
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }],
						hideHandle: '$.type == "已有耗材增加库存"'
					},
					{
						label: '耗材编号',
						name: 'serial_number',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入'
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }],
						hideHandle: '$.type == "已有耗材增加库存"'
					},

					{
						label: '耗材',
						name: 'haocai',
						value: null,
						component: 'cusSelectConsumables',
						/*options: {
							title: '选择耗材',
							tree: this.$API.consumables.tree
						},*/
						rules: [{ required: true, message: '请选择', trigger: 'blur' }],
						hideHandle: '$.type != "已有耗材增加库存"'
					},
					{
						label: '入库时间',
						name: 'storage_time',
						value: null,
						component: 'cusDate',
						options: {
							type: 'datetime'
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					},
					{
						label: '入库数量',
						name: 'num',
						value: null,
						component: 'number',
						options: {
							placeholder: '请输入',
							controlsPosition: '1'
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					},
					{
						label: '计量单位',
						name: 'unit',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入'
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '位置',
						name: 'room_id',
						value: null,
						component: 'cusSelectTree',
						options: {
							title: '选择场室',
							tree: this.$API.fieldRoom.tree
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					},
					{
						label: '耗材图片',
						component: 'uploadMultiple',
						options: {
							items: [
								{
									name: 'img',
									label: '耗材图片',
									type: 'consumables'
								}
							]
						}
					},
					{
						label: '总金额(单位：元)',
						name: 'amount',
						value: null,
						component: 'number',
						options: {
							placeholder: '请输入',
							controlsPosition: '1'
						}
					},
					{
						label: '入库备注',
						name: 'storage_remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入',
							controlsPosition: '1'
						}
					}
				]
			}
		}
	},
	watch: {
		groupData: {
			handler(val) {
				let cur = this.formConfig.formItems.find((v) => v.name == 'type_id')
				if (cur) {
					cur.options.items = val
				}
			},
			deep: true,
			immediate: true
		}
	},

	mounted() {},
	methods: {
		gethaocai(id) {},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.form.mode = mode
			this.visible = true
			this.form.campus_id = this.params.campus_id
			this.form.tenant_id = this.params.tenant_id
			return this
		},
		//表单提交方法
		submit() {
			console.log(this.form)
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true

					let formCopy = JSON.parse(JSON.stringify(this.form))
					if (formCopy.img) {
						formCopy.img = formCopy.img.map((v) => v.url).toString()
					}

					formCopy.room_id = formCopy.room_id.map((v) => v.id).toString()
					formCopy.room_id = Number(formCopy.room_id)
					formCopy.amount = Number(formCopy.amount)
					if (formCopy.id !== 0 && this.mode === 'edit') {
						var res = await this.$API.consumables.rooms.update.post(formCopy)
						this.isSaveing = false
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode)
							this.visible = false
							this.$message.success('操作成功')
						} else {
							this.$alert(res.message, '提示', { type: 'error' })
						}

						return false
					}
					formCopy.id = formCopy.haocai.map((v) => v.id).toString()
					formCopy.id = Number(formCopy.id)
					var res
					if (this.form.type == '新耗材入库') {
						res = await this.$API.consumables.rooms.create.post(formCopy)
					} else {
						res = await this.$API.consumables.rooms.incr.post(formCopy)
					}
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			console.log(this.form)
			if (data.room) {
				this.form.room_id = [
					{
						id: data.room.id,
						label: data.room.name
					}
				]
			}
			if (data.img) {
				this.form.img = data.img.split(',').map((v) => {
					return {
						url: v
					}
				})
			}
		}
	},
	components: { CusCascader }
}
</script>

<style scoped lang="scss"></style>
