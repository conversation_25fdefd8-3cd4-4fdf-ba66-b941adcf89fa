<template>
    <el-select v-model="value" :multiple="multiple" :multiple-limit="multipleLimit" :collapseTags="collapseTags" :placeholder="placeholder"
        :clearable="clearable" :disabled="disabled" :style="{ width: width }" @change="infoChange">
        <el-option v-for="item of options" :key="item.value" :value="item.value" :label="item.name" />
    </el-select>
</template>

<script>
import cusTom from '@/utils/cusTom'
const {
    campusId,
    tenantId,
    campusInfo,
    semesterInfo1, //学期 默认列表
    academicYearInfo //学年列表
} = cusTom.getBaseQuery()
export default {
    props: {
        type: {
            type: String,
            default: ''
        },
        multiple: {
            type: Boolean,
            default: false
        },
        multipleLimit:{
            type:Number,
            default: 0
        },
        collapseTags: {
            type: Boolean,
            default: true
        },
        placeholder: {
            type: String,
            default: '请选择'
        },
        clearable: {
            type: Boolean,
            default: true
        },
        disabled: {
            type: <PERSON>olean,
            default: false
        },
        width: {
            type: String,
            default: '200px'
        },
        modelValue: null
    },
    data() {
        return {
            typeData: '',
            value: null,
            options: [],
            params: {
                campus_id: campusId,
                tenant_id: tenantId
            }
        }
    },
    watch: {
        modelValue: {
            handler(val) {
                this.value = val
            },
            immediate: true
        },
        type: {
            handler(val) {
                this.typeData = val
                this.getData()
            },
            deep: true,
            immediate: true
        },
        value() {
            this.$emit('update:modelValue', this.value)
        }
    },
    created() {

    },
    mounted() {

    },
    methods: {
        infoChange(val) {
            this.$emit('infoChange', val)
        },
        getData() {
            if (this.typeData === 'select-campus') {
                this.options = campusInfo
            } else if (this.typeData === 'select-semester') {
                this.options = semesterInfo1
            } else if (this.typeData === 'select-academic') {
                this.options = academicYearInfo
            } else if (this.typeData === 'select-class') {
                this.loadData('class')
            } else if (this.typeData === 'select-grade') {
                this.loadData('grade')
            }
        },
        loadData(type){
            this.$API.eduGradeClass[type].all.get(this.params).then(res => {
                this.options = res.data.map(item => {
                    return {
                        value: item.id,
                        name: `${type === 'grade' ? item.grade_name : item.class_name}`
                    }
                })
            })
        },
    },
}
</script>
