<template>
	<el-container>
		<el-header>
			<el-page-header
				:content="`${productData?.product_name} （${productData?.product_key}）`"
				@back="goBack"
			></el-page-header>
		</el-header>
		<div v-if="productData" class="productInfo">
			<div class="productInfo-img">
				<el-image
					style="width: 150px; height: 150px"
					fit="contain"
					:src="productData.product_img"
					:preview-src-list="[productData.product_img]"
				>
					<template #error>
						<div class="image-slot">
							<el-icon>
								<sc-icon-psyResource />
							</el-icon>
						</div>
					</template>
				</el-image>
			</div>
			<div class="productInfo-content">
				<h2>
					{{ productData.product_name }}
					<el-button
						size="small"
						style="margin-left: 30px"
						type="primary"
						plain
						icon="el-icon-edit"
						@click="editProduct"
					>
						修改信息</el-button
					>
				</h2>
				<el-row>
					<el-col :span="12">
						<el-form-item label="产品编号：">{{ productData.product_key }}</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="产品状态：">
							<el-button
								v-if="productData.status === 1"
								type="success"
								plain
								size="small"
								icon="el-icon-CircleCheck"
								@click="statusChange(productData.id, -1)"
								>已发布</el-button
							>
							<el-button
								v-else
								size="small"
								type="info"
								plain
								icon="el-icon-CircleClose"
								@click="statusChange(productData.id, 1)"
								>未发布</el-button
							>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="所属品类：">
							<span v-if="productData.template_id > 0">{{ productData.template_info?.name }}</span>
							<span v-else>自定义品类</span>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="节点类型：">
							{{ enumConfig.nodeTypeMap.find((item) => item.value === productData.node_type)?.name }}
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="网络类型：">
							{{ enumConfig.netTypeMap.find((item) => item.value === productData.net_type)?.name }}
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="接入协议：">
							{{ enumConfig.protocolMap.find((item) => item.value === productData.protocol)?.name }}
						</el-form-item>
					</el-col>

					<el-col :span="12">
						<el-form-item label="工厂/品牌：">{{ productData.factory }}</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="产品描述：">{{ productData.desc }}</el-form-item>
					</el-col>
				</el-row>
			</div>
		</div>
		<el-header style="margin-top: 15px">
			<div class="pannel">
				<div class="left-panel">
					<div class="left-panel-search">
						<el-form-item>
							<el-select v-model="params.tsl_type" placeholder="请选择功能类型" clearable style="width: 150px">
								<el-option
									v-for="item in allTslType"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								></el-option>
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-input v-model="params.name" placeholder="请输入功能名称/标识符搜索" clearable></el-input>
						</el-form-item>
						<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
						<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
					</div>
				</div>
				<div class="right-panel">
					<el-button type="primary" plain icon="el-icon-Download" @click="showImport">导入</el-button>
					<el-button type="primary" plain icon="el-icon-Upload" @click="showExport">导出</el-button>
					<el-button type="primary" icon="el-icon-CirclePlus" @click="add">添加物模型</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable
				ref="table"
				size="small"
				row-key="id"
				border
				:pageSize="10"
				:apiObj="apiObj"
				:params="params"
				:hideDo="false"
			>
				<el-table-column label="功能类型" prop="tsl_type" width="100">
					<template #default="scope">
						<span v-if="scope.row.tsl_type === 1">属性类型</span>
						<span v-if="scope.row.tsl_type === 2">事件类型</span>
						<span v-if="scope.row.tsl_type === 3">服务类型</span>
					</template>
				</el-table-column>
				<el-table-column label="功能名称" prop="name" width="120"></el-table-column>
				<el-table-column label="标识符" prop="code" width="150"></el-table-column>
				<el-table-column label="数据类型" prop="type" width="150">
					<template #default="scope">
						<span v-if="scope.row.tsl_type === 1">
							{{ scope.row.type_spec.type }}（{{ typeConfig[scope.row.type_spec.type] }}）
						</span>
						<span v-else>-</span>
					</template>
				</el-table-column>
				<el-table-column label="读写类型" prop="access_mode" width="80">
					<template #default="scope">
						<div v-if="scope.row.tsl_type === 1">
							<span v-if="scope.row.access_mode === 1">只读</span>
							<span v-if="scope.row.access_mode === 2">读写</span>
							<span v-if="scope.row.access_mode === 3">只写</span>
						</div>
						<div v-else>-</div>
					</template>
				</el-table-column>
				<el-table-column label="数据定义" prop="specs" width="450">
					<template #default="scope">
						<span v-if="scope.row.tsl_type === 1">
							<showSpecs
								:specsData="JSON.parse(scope.row.type_spec.specs)"
								:type="scope.row.type_spec.type"
							></showSpecs>
						</span>
						<span v-else>-</span>
					</template>
				</el-table-column>
				<el-table-column label="描述" prop="description"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="250">
					<template #default="scope">
						<el-button text type="success" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>
						<el-divider direction="vertical" />
						<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑 </el-button>
						<el-divider direction="vertical" />
						<el-button text type="danger" size="small" @click="table_del(scope.row, scope.$index)">删除</el-button>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<ProductSaveDialog
		v-if="dialog.productSave"
		ref="productSaveDialog"
		@success="getProductInfo"
		@closed="dialog.productSave = false"
	></ProductSaveDialog>
	<TslSaveDialog
		v-if="dialog.tslSave"
		ref="tslSaveDialog"
		@success="handleSaveSuccess"
		@closed="dialog.tslSave = false"
	></TslSaveDialog>
	<TslImportExportDialog
		v-if="dialog.tslImportExportSave"
		ref="tslImportExportDialog"
		@success="handleImportSuccess"
		@closed="dialog.tslImportExportSave = false"
	></TslImportExportDialog>
</template>

<script>
import ProductSaveDialog from '../save'
import TslSaveDialog from './tslSave'
import TslImportExportDialog from './tslImportExport'
import ScTable from '@/components/scTable/index.vue'
import { ElMessageBox } from 'element-plus'
import ShowSpecs from '@/components/thingsModel/showSpecs.vue'
import tool from '@/utils/tool'
import cusTom from '@/utils/cusTom'
const { tenantId } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		name: null,
		tsl_type: 0,
		product_id: 0,
		tenant_id: tenantId
	}
}

export default {
	name: 'templateList',
	data() {
		return {
			template_id: null,
			typeConfig: {
				int: '整数型',
				bool: '布尔型',
				float: '浮点型',
				text: '字符串',
				enum: '枚举型',
				date: '时间型',
				struct: '结构体',
				array: '数组'
			},
			allTslType: [
				{
					value: 0,
					label: '功能类型(全部)'
				},
				{
					value: 1,
					label: '属性类型'
				},
				{
					value: 2,
					label: '服务类型'
				},
				{
					value: 3,
					label: '事件类型'
				}
			],
			enumConfig: {},
			groupFilterText: '',
			productData: null,
			apiObj: this.$LotApi.productTsl.listPage,
			params: defaultParams(),
			search: {
				name: null
			},
			dialog: {
				productSave: false,
				tslImportExportSave: false,
				tslSave: false
			}
		}
	},
	components: {
		ShowSpecs,
		ScTable,
		ProductSaveDialog,
		TslImportExportDialog,
		TslSaveDialog
	},
	watch: {},
	created() {
		this.product_id = this.$route.query.id
		this.params.product_id = this.product_id
		this.getProductInfo()
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	computed: {},
	methods: {
		showExport() {
			this.dialog.tslImportExportSave = true
			this.$nextTick(() => {
				this.$refs.tslImportExportDialog.open(this.product_id, 'export').setData(this.productData)
			})
		},
		showImport() {
			this.dialog.tslImportExportSave = true
			this.$nextTick(() => {
				this.$refs.tslImportExportDialog.open(this.product_id, 'import').setData(this.productData)
			})
		},
		editProduct() {
			this.dialog.productSave = true
			this.$nextTick(() => {
				this.$refs.productSaveDialog.open('edit').setData(this.productData)
			})
		},
		getProductInfo() {
			this.$LotApi.product.one
				.get({
					id: this.product_id,
					tenant_id: tenantId
				})
				.then((res) => {
					if (res.code === 200) {
						this.productData = res.data
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
		},
		goBack() {
			this.$router.back()
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.params.product_id = this.product_id
			this.upsearch()
		},
		//新增职位
		add() {
			this.dialog.tslSave = true
			this.$nextTick(() => {
				this.$refs.tslSaveDialog.open(this.product_id)
			})
		},
		handleSaveSuccess(mode) {
			if (mode === 'add') {
				this.upsearch()
			} else {
				this.$refs.table.refresh()
			}
		},
		handleImportSuccess(mode) {
			if (mode === 'import') {
				this.upsearch()
			}
		},
		//编辑
		table_edit(row) {
			this.dialog.tslSave = true
			var info = tool.objCopy(row)
			if (info.tsl_type === 1 && typeof info.type_spec.specs === 'string') {
				info.type_spec.specs = JSON.parse(info.type_spec.specs)
			}
			this.$nextTick(() => {
				this.$refs.tslSaveDialog.open(this.product_id, 'edit').setData(info)
			})
		},
		//查看
		table_show(row) {
			this.dialog.tslSave = true
			var info = tool.objCopy(row)
			if (info.tsl_type === 1 && typeof info.type_spec.specs === 'string') {
				info.type_spec.specs = JSON.parse(info.type_spec.specs)
			}
			this.$nextTick(() => {
				this.$refs.tslSaveDialog.open(this.product_id, 'show').setData(info)
			})
		},
		//状态改变
		statusChange(id, status) {
			let query = {
				id: id,
				status: status,
				tenant_id: tenantId
			}
			let text
			if (status > 0) {
				text = '是否确认发布当前产？'
			} else {
				text = '是否确认取消发布？'
			}
			ElMessageBox.confirm(text, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				this.$LotApi.product.changeStatus.post(query).then((res) => {
					if (res.code === 200) {
						this.$message.success('操作成功')
						this.getProductInfo()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
			})
		},
		//删除
		async table_del(row) {
			this.$c
			var reqData = { id: row.id, tenant_id: row.tenant_id, product_id: row.product_id }
			ElMessageBox.confirm('是否删除当前物模型？删除后相应功能会被删除！', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.productTsl.del.post(reqData)
				if (res.code === 200) {
					this.$message.success('删除成功')
					this.$refs.table.refresh()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
.pannel {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.el-header {
	display: block;
	.el-divider--horizontal {
		margin: 10px 0;
	}
}
.productInfo {
	margin-top: 15px;
	background: var(--el-bg-color-overlay);
	padding: 15px;
	border-radius: 6px;
	display: flex;
}

.image-slot {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	background: var(--el-color-primary-light-9);
	color: var(--el-color-primary-light-2);
	border-radius: 6px;
	font-size: 30px;
}
.image-slot .el-icon {
	font-size: 30px;
}
.productInfo-img {
	width: 150px;
}
.productInfo-content {
	padding-left: 20px;
	flex: 1;
	h2 {
		font-size: 18px;
		margin-bottom: 8px;
	}
}
:deep(.el-form-item--default) {
	margin-bottom: unset !important;
	.el-form-item__content {
		word-break: break-all !important;
		color: var(--el-text-color-secondary);
	}
}
/*:deep(.el-descriptions__label:not(.is-bordered-label)) {
	margin-right: unset !important;
}*/
</style>
