import config from '@/config'

const rules = {
	trigger: 'blur', //触发的事件
	enum: '', // 数组，指定字段值必须是该数组中的一个选项。
	len: undefined, // 字符串，指定字段值的长度。
	max: undefined, // 字符串，指定字段值的最大长度。
	min: undefined, // 字符串，指定字段值的最小长度。
	message: '', // 验证失败时显示的提示信息
	pattern: '', // 正则表达式，指定字段值的格式。
	required: false, // 布尔值，指定字段是否必填。
	type: 'any' // 字符串，指定字段值的类型。
}
export const widgetForm = {
	list: [],
	config: {
		size: 'default',
		hideRequiredAsterisk: false,
		labelWidth: 100,
		labelPosition: 'right'
	}
}
export const CodeType = {
	Vue: 'vue',
	Html: 'html'
}

export const basicComponents = [
	{
		label: '单行文本',
		type: 'input',
		options: {
			width: '100%',
			defaultValue: '',
			placeholder: '请输入',
			minlength: null,
			maxlength: null,
			showWordLimit: false,
			// prefix: '',
			// suffix: '',
			// prepend: '',
			// append: '',
			disabled: false,
			clearable: false,
			readonly: false,
			rules
		}
	},
	{
		label: '密码框',
		type: 'password',
		options: {
			width: '100%',
			defaultValue: '',
			placeholder: '请输入',
			minlength: null,
			maxlength: null,
			// prefix: '',
			// suffix: '',
			// prepend: '',
			// append: '',
			showPassword: true,
			disabled: false,
			clearable: false,
			readonly: false,
			rules
		}
	},
	{
		label: '多行文本',
		type: 'textarea',
		options: {
			width: '100%',
			defaultValue: '',
			placeholder: '请输入',
			minlength: null,
			maxlength: null,
			rows: 2,
			autosize: false,
			showWordLimit: false,
			disabled: false,
			clearable: false,
			readonly: false,
			rules
		}
	},
	{
		label: '计数器',
		type: 'number',
		options: {
			width: '',
			defaultValue: 0,
			min: 0,
			max: 100,
			step: 1,
			disabled: false,
			rules
		}
	},
	{
		label: '单选框组',
		type: 'radio',
		options: {
			defaultValue: '',
			width: '',
			inline: true,
			remote: false,
			showLabel: false,
			remoteFunc: '',
			options: [
				{
					value: '选项1',
					label: '选项1'
				},
				{
					value: '选项2',
					label: '选项2'
				},
				{
					value: '选项3',
					label: '选项3'
				}
			],
			remoteOptions: [],
			props: {
				value: 'value',
				label: 'label'
			},
			disabled: false,
			rules
		}
	},
	{
		label: '多选框组',
		type: 'checkbox',
		options: {
			defaultValue: [],
			width: '',
			inline: true,
			remote: false,
			showLabel: false,
			remoteFunc: '',
			options: [
				{
					label: '选项1',
					value: '选项1'
				},
				{
					label: '选项2',
					value: '选项2'
				},
				{
					label: '选项3',
					value: '选项3'
				}
			],
			remoteOptions: [],
			props: {
				value: 'value',
				label: 'label'
			},
			disabled: false,
			rules
		}
	},
	{
		label: '时间选择器',
		type: 'time',
		options: {
			defaultValue: '',
			width: '',
			placeholder: '请选择时间',
			format: 'HH:mm:ss',
			valueFormat: 'HH:mm:ss',
			readonly: false,
			editable: true,
			clearable: true,
			disabled: false,
			rules
		}
	},
	{
		label: '时间范围选择',
		type: 'timerange',
		options: {
			isRange: true,
			rangeSeparator: '至',
			startPlaceholder: '开始时间',
			endPlaceholder: '结束时间',
			defaultValue: '',
			width: '',
			placeholder: '请选择时间范围',
			format: 'HH:mm:ss',
			valueFormat: 'HH:mm:ss',
			readonly: false,
			editable: true,
			clearable: true,
			disabled: false,
			rules
		}
	},
	{
		label: '日期选择器',
		type: 'date',
		options: {
			defaultValue: '',
			width: '',
			placeholder: '请选择日期',
			type: 'date',
			format: 'YYYY-MM-DD',
			valueFormat:'YYYY-MM-DD',
			readonly: false,
			editable: true,
			clearable: true,
			disabled: false,
			rules
		}
	},
	{
		label: '日期范围选择',
		type: 'daterange',
		options: {
			type: 'daterange',
			rangeSeparator: '至',
			startPlaceholder: '开始日期',
			endPlaceholder: '结束日期',
			defaultValue: '',
			width: '',
			placeholder: '请选择日期范围',
			type: 'daterange',
			format: 'YYYY-MM-DD',
			valueFormat: 'YYYY-MM-DD',
			readonly: false,
			editable: true,
			clearable: true,
			disabled: false,
			rules
		}
	},
	{
		label: '评分',
		type: 'rate',
		options: {
			defaultValue: 0,
			max: 5,
			allowHalf: false,
			disabled: false,
			rules
		}
	},
	{
		label: '下拉选择框',
		type: 'select',
		options: {
			defaultValue: '',
			width: '100%',
			multiple: false,
			collapseTags: true,
			placeholder: '请选择',
			multipleLimit: 0,
			remote: false,
			showLabel: false,
			filterable: false,
			clearable: false,
			disabled: false,
			props: {
				label: 'label',
				value: 'value'
			},
			options: [
				{
					label: '选项1',
					value: '选项1'
				},
				{
					label: '选项2',
					value: '选项2'
				},
				{
					label: '选项3',
					value: '选项3'
				}
			],
			remoteOptions: [],
			remoteFunc: '',
			rules
		}
	},
	{
		label: '开关',
		type: 'switch',
		options: {
			defaultValue: false,
			disabled: false,
			activeText: '',
			inactiveText: '',
			rules
		}
	},
	{
		label: '滑块',
		type: 'slider',
		options: {
			defaultValue: 0,
			width: '',
			min: 0,
			max: 100,
			step: 1,
			disabled: false,
			range: false,
			rules
		}
	},
	{
		label: '文字',
		type: 'text',
		options: {
			// defaultValue: '',
			htmlContent: '<p>请在右侧字段属性输入内容</p>',
			placeholder: '请输入内容',
		}
	}
]

export const advanceComponents = [
	{
		label: '图片上传',
		type: 'img-upload',
		options: {
			defaultValue: [],
			name: 'image',
			action: `${config.API_URL}/api/common/upload`,
			method: 'post',
			listType: 'picture-card',
			accept: 'image/*',
			limit: 3,
			multiple: false,
			disabled: false
		}
	},
	{
		label: '文件上传',
		type: 'file-upload',
		options: {
			defaultValue: [],
			name: 'file',
			action: 'http://example.com/upload',
			method: 'post',
			listType: '',
			accept: 'file/*',
			limit: 3,
			multiple: false,
			disabled: false
		}
	},
	{
		label: '分割线',
		type: 'divider',
		options: {
			direction: 'horizontal',
			borderStyle: 'solid',
			content: '',
			contentPosition: 'left'
		}
	},
	{
		label: '富文本编辑器',
		type: 'richtext-editor',
		options: {
			value: '',
			width: '',
			readOnly: false,
			mode: 'default'
		}
	}
	// {
	// 	label: '级联选择器',
	// 	type: 'cascader',
	// 	options: {
	// 		defaultValue: [],
	// 		width: '100%',
	// 		placeholder: '',
	// 		disabled: false,
	// 		clearable: false,
	// 		filterable: false,
	// 		remote: true,
	// 		remoteOptions: [],
	// 		props: {
	// 			label: 'label',
	// 			value: 'value',
	// 			children: 'children'
	// 		},
	// 		remoteFunc: '',
	// 		rules
	// 	}
	// }
]

export const layoutComponents = [
	{
		label: '栅格布局',
		type: 'grid',
		columns: [
			{
				span: 12,
				list: []
			},
			{
				span: 12,
				list: []
			}
		],
		options: {
			gutter: 0,
			justify: 'start',
			align: 'top'
		}
	}
]

export const systemComponents = [
	{
		label: '资产选择',
		type: 'select-asset',
		options: {
			defaultValue: '',
			placeholder: '请选择资产',
			disabled: false,
			clearable: true,
			width: '100%',
			multiple: false,
			rules
		}
	},
	{
		label: '耗材选择',
		type: 'select-consumables',
		options: {
			defaultValue: '',
			placeholder: '请选择耗材',
			disabled: false,
			clearable: true,
			width: '100%',
			multiple: false,
			rules
		}
	},
	{
		label: '宿舍选择',
		type: 'select-room',
		options: {
			defaultValue: '',
			placeholder: '请选择宿舍',
			disabled: false,
			clearable: true,
			width: '100%',
			multiple: false,
			rules
		}
	},
	{
		label: '场地选择',
		type: 'select-field',
		options: {
			defaultValue: '',
			placeholder: '请选择场地',
			disabled: false,
			clearable: true,
			width: '100%',
			multiple: false,
			rules
		}
	},
	{
		label: '部门选择',
		type: 'select-department',
		options: {
			defaultValue: '',
			placeholder: '请选择部门',
			disabled: false,
			clearable: true,
			multiple: false,
			width: '100%',
			rules
		}
	},
	{
		label: '校区选择',
		type: 'select-campus',
		options: {
			defaultValue: '',
			placeholder: '请选择校区',
			disabled: false,
			clearable: true,
			width: '100%',
			multiple: false,
			multipleLimit: 0,
			rules
		}
	},
	{
		label: '学期选择',
		type: 'select-semester',
		options: {
			defaultValue: '',
			placeholder: '请选择学期',
			disabled: false,
			clearable: true,
			width: '100%',
			multiple: false,
			multipleLimit: 0,
			rules
		}
	},
	{
		label: '年级选择',
		type: 'select-grade',
		options: {
			defaultValue: '',
			placeholder: '请选择年级',
			disabled: false,
			clearable: true,
			width: '100%',
			multiple: false,
			multipleLimit: 0,
			rules
		}
	},
	{
		label: '班级选择',
		type: 'select-class',
		options: {
			defaultValue: '',
			placeholder: '请选择班级',
			disabled: false,
			clearable: true,
			width: '100%',
			multiple: false,
			multipleLimit: 0,
			rules
		}
	},
	{
		label: '教职工选择',
		type: 'select-teacher',
		options: {
			defaultValue: '',
			placeholder: '请选择教职工',
			disabled: false,
			clearable: true,
			width: '100%',
			multiple: false,
			multipleLimit: 0,
			rules
		}
	},
	{
		label: '学生选择',
		type: 'select-student',
		options: {
			defaultValue: '',
			placeholder: '请选择学生',
			disabled: false,
			clearable: true,
			width: '100%',
			multiple: false,
			multipleLimit: 0,
			rules
		}
	}
]
