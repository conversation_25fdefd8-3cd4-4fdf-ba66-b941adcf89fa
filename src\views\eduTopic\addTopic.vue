<template>
	<el-drawer v-model="drawer" :title="titleMap[mode]" size="50%">
		<el-form
			ref="topicFormRef"
			:model="topicForm.obj"
			:rules="rules"
			label-position="left"
			label-suffix="："
			label-width="100px"
		>
			<el-form-item label="所属学科" prop="course_id">
				<el-select v-model="topicForm.obj.course_id" placeholder="请选择" filterable clearable style="width: 100%">
					<el-option v-for="item in getCourse" :key="item.id" :label="item.course_name" :value="item.id"></el-option>
				</el-select>
			</el-form-item>
			<!--      <el-form-item label="课程" prop="course_id">
        <el-select v-model="topicForm.obj.course_id" placeholder="请选择" filterable clearable style="width: 100%">
          <el-option :label="item.course_name" :value="item.id" v-for="item in getCourse" :key="item.id"></el-option>
        </el-select>
      </el-form-item>-->
			<el-form-item label="年级" prop="grade_id">
				<el-select v-model="topicForm.obj.grade_id" placeholder="请选择年级" filterable clearable style="width: 100%">
					<el-option v-for="item in getGrade" :key="item.id" :label="item.grade_name" :value="item.id" />
				</el-select>
			</el-form-item>
			<editTopicTemp ref="editTopicTempRef" v-model:topicObj="topicForm.obj"></editTopicTemp>
			<el-form-item label="附件">
				<scUploadFile v-model="topicForm.obj.topic_file" accept="*" fileTypeTag="eduTopic"></scUploadFile>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="drawer = false">取消</el-button>
				<el-button v-loading="isSaveing" type="primary" @click="save">保存</el-button>
			</div>
		</template>
	</el-drawer>
</template>

<script setup>
import {
	reactive,
	unref,
	ref,
	computed,
	watch,
	getCurrentInstance,
	onMounted,
	defineEmits,
	defineExpose,
	nextTick
} from 'vue'
const props = defineProps({
	course: {
		type: Array,
		default: () => {
			return []
		}
	}
})
import cusTom from '@/utils/cusTom'
import textEditor from '@/components/textEditor'
import { Delete } from '@element-plus/icons-vue'
import editTopicTemp from '@/views/eduWork/editTopicTemp.vue'
import { ElMessage } from 'element-plus'
const { proxy } = getCurrentInstance()
const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
const emit = defineEmits(['suc'])
let drawer = ref(false)
let mode = ref('add')
const titleMap = reactive({
	add: '新增题目',
	edit: '编辑题目',
	show: '查看题目'
})
const getCourse = computed(() => {
	return props.course.filter((v) => {
		return v.campus_id === topicForm.obj.campus_id
	})
})
let isSaveing = ref(false)
const topicFormRef = ref(null)
const editTopicTempRef = ref(null)
const defaultForm = () => {
	return {
		id: 0,
		tenant_id: tenantId,
		campus_id: campusId,
		discipline_id: null,
		course_id: null,
		grade_id: null,
		topic_type: 1,
		topic_name: '',
		topic_desc: [
			{
				serial: 'A',
				desc: '对',
				is_right: 1
			},
			{
				serial: 'B',
				desc: '错',
				is_right: 0
			}
		],
		topic_desc_json: [
			{
				serial: 'A',
				desc: '对',
				is_right: 1
			},
			{
				serial: 'B',
				desc: '错',
				is_right: 0
			}
		],
		topic_file: '',
		topic_score: ''
	}
}
const topicForm = reactive({
	obj: defaultForm()
})
const validateDesc = (rule, value, callback) => {
	let flag = true
	if (topicForm.obj.topic_type == 1) {
		callback()
		return
	}
	topicForm.obj.topic_desc.forEach((item) => {
		let desc = item.desc.replaceAll('<p><br></p>', '')
		if (!item.serial || !desc) {
			flag = false
		}
	})
	if (flag) {
		callback()
	} else {
		callback(new Error('请完善题目选项'))
	}
}
const validateName = (rule, value, callback) => {
	let flag = true
	let topic_name = topicForm.obj.topic_name.replaceAll('<p><br></p>', '')
	if (!topic_name) {
		flag = false
	}
	if (flag) {
		callback()
	} else {
		callback(new Error('请输入题目'))
	}
}
const rules = reactive({
	discipline_id: [{ required: true, message: '请选择所属学科', trigger: 'change' }],
	course_id: [{ required: true, message: '请选择课程', trigger: 'change' }],
	grade_id: [{ required: true, message: '请选择年级', trigger: 'change' }],
	topic_score: [{ required: true, message: '请输入分值', trigger: 'blur' }],
	topic_type: [{ required: true, message: '请选择题目类型', trigger: 'change' }],
	topic_name: [{ required: true, validator: validateName, trigger: 'change' }],
	topic_desc: [{ required: true, validator: validateDesc, trigger: 'change' }]
})
const myGrade = reactive({
	list: []
})
const disciplineOptions = reactive({
	list: []
})
const course = reactive({
	list: []
})
const show = (type, item) => {
	mode.value = type
	drawer.value = true
	nextTick(() => {
		topicFormRef.value.resetFields()
	})
	if (type == 'add') {
		topicForm.obj = defaultForm()
	} else {
		getStoreOne(item.id)
	}
}
const getGradeData = async () => {
	let params = {
		tenant_id: topicForm.obj.tenant_id,
		campus_id: topicForm.obj.campus_id
	}
	let res = await proxy.$API.eduGradeClass.grade.all.get(params)
	myGrade.list = res.data
}
const getDisciplineOptions = async () => {
	let { data } = await proxy.$API.eduDiscipline.discipline.all.get({ tenant_id: tenantId, campus_id: campusId })
	disciplineOptions.list = data
}
const getEduCourse = async () => {
	let { data } = await proxy.$API.eduCourseSet.course.all.get({ tenant_id: tenantId, campus_id: campusId })
	course.list = data
}
const getStoreOne = async (id) => {
	let { data } = await proxy.$API.eduWork.storeOne.get({ tenant_id: tenantId, campus_id: campusId, id })
	Object.keys(topicForm.obj).forEach((key) => {
		if (key == 'topic_desc') {
			topicForm.obj[key] = JSON.parse(data[key])
		} else {
			topicForm.obj[key] = data[key]
		}
	})
}
const save = async () => {
	const form = unref(topicFormRef)
	if (!form) {
		return
	}
	form.validate(async (valid) => {
		if (valid) {
			topicForm.obj.topic_score = Number(topicForm.obj.topic_score)
			isSaveing.value = true
			try {
				let res = await proxy.$API.eduWork.saveStore.post(topicForm.obj)
				isSaveing.value = false
				if (res.code === 200) {
					mode.value == 'add' ? ElMessage.success('新增成功') : ElMessage.success('修改成功')
					drawer.value = false
					emit('suc')
				}
			} catch (err) {
				isSaveing.value = false
			}
		}
	})
}
let getGrade = computed(() => {
	return myGrade.list.filter((v) => v.parent_id != 0 && v.campus_id == topicForm.obj.campus_id)
})
/*let getCourse = computed(() => {
	return course.list.filter((item) => {
		return item.campus_id == topicForm.obj.campus_id && item.discipline_id == topicForm.obj.discipline_id
	})
})*/
watch(
	() => topicForm.obj.grade_id,
	(val) => {
		topicForm.obj.class_id = null
	}
)
onMounted(() => {
	getGradeData()
	getDisciplineOptions()
	getEduCourse()
})
defineExpose({
	show
})
</script>

<style lang="scss" scoped>
.workPreview {
	padding: 20px;
	border: 1px solid var(--el-border-color);
	> div {
		display: flex;
	}
	.topicDesc {
		display: flex;
	}
}
</style>
