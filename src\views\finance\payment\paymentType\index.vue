<template>
	<el-container>
		<el-aside width="200px">
			<el-container>
				<el-header>
					<el-input v-model="groupFilterText" placeholder="输入关键字进行过滤" clearable></el-input>
				</el-header>
				<el-main>
					<el-tree
						ref="group"
						class="menu"
						node-key="id"
						:data="groupsAdd"
						:current-node-key="''"
						:highlight-current="true"
						:filter-node-method="groupFilterNode"
						@node-click="groupClick"
						:props="defaultProps"
						:default-expanded-keys="[0]"
					></el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<div class="left-panel">
					<div class="left-panel-search">
						<!-- <el-form-item label="学校" v-if="TenantManagementList.length > 1">
							<el-select v-model="params.tenant_id" placeholder="学校">
								<el-option
									:label="item.name"
									:value="item.value"
									v-for="item in TenantManagementList"
									:key="item.code"
								></el-option>
							</el-select>
						</el-form-item> -->
						<el-form-item label="校区" v-if="CampusManagementList.length > 1">
							<el-select v-model="params.campus_id" placeholder="校区" filterable>
								<el-option
									:label="item.name"
									:value="item.value"
									v-for="item in CampusManagementList"
									:key="item.code"
								></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="缴费名称">
							<el-input v-model="params.name" placeholder="请输入缴费名称" clearable></el-input>
						</el-form-item>

						<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
						<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
					</div>
				</div>
				<div class="right-panel">
					<el-button type="primary" icon="el-icon-plus" @click="add">新增缴费</el-button>
				</div>
			</el-header>
			<el-main>
				<scTable ref="table" row-key="id" stripe :apiObj="list.apiObj" :params="params">
					<el-table-column label="缴费名称" prop="item_name" width="150"></el-table-column>
					<el-table-column label="缴费项类型" prop="type_name" width="150"></el-table-column>
					<el-table-column label="金额" prop="amount" width="150"></el-table-column>
					<el-table-column label="状态" prop="status" width="150">
						<template #default="scope">
							<el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
							<el-tag v-if="scope.row.status === -1" type="danger">停用</el-tag>
						</template>
					</el-table-column>
                    <el-table-column label="创建时间" prop="created_at" width="250"></el-table-column>
					<el-table-column label="备注" prop="remark"></el-table-column>
					<el-table-column label="操作" fixed="right" align="center" width="170">
						<template #default="scope">
							<el-button-group>
								<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)"
									>查看</el-button
								>
								<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)"
									>编辑</el-button
								>
								<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
									<template #reference>
										<el-button text type="danger" size="small">删除</el-button>
									</template>
								</el-popconfirm>
							</el-button-group>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-container>
	<!-- <save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
		:params="params"
		:groupData="groupData"
	></save-dialog> -->
</template>

<script>
import cusTom from '@/utils/cusTom'
// import saveDialog from './save'

const defaultProps = {
	children: 'children',
	label: 'item_name'
}
const { campusId, tenantId, campusInfo, tenantInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		department_id: null,
		name: null
	}
}

export default {
	name: 'payment',
	data() {
		return {
			groupFilterText: '',
			groupData: [],
			defaultProps,
			list: {
				apiObj: this.$API.finance.recruit.list
			},
			params: defaultParams(),
			search: {
				name: null
			},
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			JobData: [],
			dialog: {
				save: false
			}
		}
	},
	components: {
		// saveDialog
	},
	watch: {
		groupFilterText(val) {
			this.$refs.group.filter(val)
		},
		'params.campus_id': {
			handler(val) {
				this.getDept()
				this.upsearch()
			}
		}
	},
	async created() {
		this.getDept()
	},
	computed: {
		groupsAdd() {
			let arr = [
				// {
				// 	id: 0,
				// 	item_name: '全部'
				// },
				...this.groupData
			]
			return arr
		}
	},
	methods: {
		//获取部门
		async getDept() {
			const { data } = await this.$API.finance.recruit.typeList.get(this.params)
			this.groupData = cusTom.arrayToTree(data)
		},

		//树过滤
		groupFilterNode(value, data) {
			if (!value) return true
			return data.item_name.indexOf(value) !== -1
		},
		//树点击事件
		groupClick(data) {
			if (data.children && data.children.length > 0) {
				return
			}
			this.params.name = data.item_name
			this.upsearch()
		},
		//搜索
		upsearch() {
            console.log(this.params)
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//新增缴费
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				// this.$refs.saveDialog.open()
			})
		},
		handleSaveSuccess() {
			this.upsearch()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				// this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				// this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.JobManagement.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
	}
}
</script>

<style></style>
