<template>
	<div v-if="data" v-loading="loading" element-loading-text="数据加载中">
		<div v-if="data.is_video === 1 && data.url" class="video">
			<VideoPlay ref="videoRef" width="100%" :source="data.url"></VideoPlay>
		</div>
		<div class="content" v-html="data.description"></div>
		<div v-if="data.files && data.files.length > 0" class="files">
			<h3>附件：</h3>
			<div v-for="(item, index) in data.files" :key="index">
				<el-link type="primary" :href="item.url" target="_blank">{{ item.name }}</el-link>
			</div>
		</div>
	</div>
</template>

<script>
import VideoPlay from '@/components/videoPlay/index.vue'

export default {
	name: 'detail',
	components: { VideoPlay },
	inject: ['resourcesData'],
	data() {
		return {
			loading: false,
			data: null
		}
	},
	created() {
		this.getDetail()
	},
	methods: {
		getDetail() {
			this.loading = true
			this.$API.famous.resources.one
				.get({
					id: this.resourcesData.id,
					tenant_id: this.resourcesData.tenant_id,
					campus_id: this.resourcesData.campus_id
				})
				.then((res) => {
					this.loading = false
					if (res.code === 200) {
						this.data = res.data
						if (res.data.files) {
							this.data.files = JSON.parse(res.data.files)
						}
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
		}
	}
}
</script>

<style scoped lang="scss">
.video{
	padding: 5px 20px;
}
:deep(.content) {
	font-size: 14px;
	line-height: 25px;
	padding: 10px 20px;
	min-height: 350px;
	img {
		max-width: 100% !important;
	}
}
.files {
	font-size: 14px;
	line-height: 25px;
	padding: 10px 20px 20px 20px;
	border-top: 1px solid var(--el-border-color-light);
}
</style>
