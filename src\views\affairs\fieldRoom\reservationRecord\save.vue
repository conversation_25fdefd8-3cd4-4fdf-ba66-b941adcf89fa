<template>
    <el-dialog v-model="visible" title="修改预约" :width="500" destroy-on-close>
        <cusForm ref="formref" v-model="form" :config="formConfig">
            <template #customItem>
                <el-col>
                    <el-form-item label="预约日期">
                        <el-date-picker v-model="form.date" type="date" placeholder="请选择日期" value-format="YYYY-MM-DD"
                            style="width: 100%;"></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col>
                    <el-form-item label="预约时间段">
                        <el-time-select v-model="form.startTime" :max-time="form.endTime" placeholder="开始时间"
                            start="08:30" step="00:30" end="18:30" style="width: 47%;margin-right: 6%;" />
                        <el-time-select v-model="form.endTime" :min-time="form.startTime" placeholder="结束时间"
                            start="08:30" step="00:30" end="18:30" style="width: 47%" />
                    </el-form-item>
                </el-col>
                <el-col>
                    <el-form-item label="备注">
                        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" show-word-limit
                            autosize style="width: 100%;"></el-input>
                    </el-form-item>
                </el-col>
            </template>
        </cusForm>
        <template #footer>
            <el-button @click="visible = false">取 消</el-button>
            <el-button type="primary" @click="submit">确 定</el-button>
        </template>
    </el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { campusId, tenantId } = cusTom.getBaseQuery()

export default {
    emits: ['success'],
    data() {
        return {
            visible: false,
            //表单数据
            form: {
                date: null,
                startTime: null,
                endTime: null,
            },
            formConfig: {
                labelPosition: 'right',
                size: 'medium',
                formItems: [
                    {
                        label: '预约主题',
                        name: 'title',
                        value: null,
                        component: 'input',
                        options: {
                            placeholder: '请输入预约主题'
                        }
                    },
                    {
                        label: '房间',
                        name: 'room_id',
                        value: null,
                        component: 'cascader',
                        options: {
                            width: '100%',
                            all: false,
                            placeholder: '请选择开放房间',
                            prop: { value:'id' },
                            items: [],
                        }
                    },
                    {
                        label: '参与老师',
                        name: 'teachers',
                        value: null,
                        component: 'cusSelectTeacher',
                        multiple: true,
                        disabled: false,
                        options: {
                            placeholder: '请选择参与老师',
                        }
                    },
                    {
                        label: '参与学生',
                        name: 'students',
                        value: null,
                        component: 'cusSelectStudent',
                        multiple: true,
                        disabled: false,
                        options: {
                            placeholder: '请选择参与学生'
                        }
                    }
                ]
            },
        }
    },
    created() {
        this.getTreeRoom()
    },
    mounted() {

    },
    methods: {
        //显示
        open(data) {
            this.visible = true
            if (data) {
                this.setData(data)
            }
        },
        //表单提交方法
        submit() {
            this.$emit('success', this.form)
        },
        //表单注入数据
        setData(data) {
            console.log(data, 'setData')
            let dateb = data.begin_time.split(" ")
            let datee = data.end_time.split(" ")
            data.date = dateb[0]
            data.startTime = dateb[1]
            data.endTime = datee[1]
            data.teachers = data.teachers_list.map(item => {
                return {
                    label: item.name,
                    id: item.id
                }
            })
            data.students = data.students_list.map(item => {
                return {
                    label: item.name,
                    id: item.id
                }
            })
            Object.assign(this.form, data)
        },
        async getTreeRoom() {
            const res = await this.$API.fieldReservation.roomtree.get({
                tenant_id: tenantId,
                campus_id: campusId
            })
            this.formConfig.formItems[1].options.items = res.data
            console.log(res, 'getTreeRoom')
        },
    }
}
</script>

<style></style>
