<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" @selection-change="selectionChange" :apiObj="apiObj">
				<!-- <el-table-column type="selection" width="50"></el-table-column> -->
				<el-table-column label="考勤类型" prop="attendance_type" width="100" fixed="left">
					<template #default="scope">
						<el-tag :type="['success', 'warning'][scope.row.attendance_type - 1]">
							{{ $formatDictionary(attendanceTypeMap, scope.row.attendance_type) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="考勤时间" prop="created_at" width="150"></el-table-column>
				<el-table-column label="考勤状态" prop="attendance_status" width="100">
					<template #default="scope">
						{{ $formatDictionary(attendanceStatusMap, scope.row.attendance_status) }}
					</template>
				</el-table-column>
				<el-table-column label="教职工姓名" prop="staff_name" width="110" fixed="left"></el-table-column>
				<el-table-column label="教职工手机号码" prop="staff_phone" width="150"></el-table-column>
				<el-table-column label="部门" prop="department_name" width="150"></el-table-column>

				<el-table-column label="考勤方式" prop="attendance_mode" width="100">
					<template #default="scope">
						{{ $formatDictionary(attendanceModeMap, scope.row.attendance_mode) }}
					</template>
				</el-table-column>
				<el-table-column label="考勤地点" prop="address" width="250"></el-table-column>
				<el-table-column label="体温" prop="temperature" width="250"></el-table-column>
				<el-table-column label="体温状态" prop="temperature_status" width="180"></el-table-column>
				<el-table-column label="备注" prop="remark" ></el-table-column>
			</scTable>
		</el-main>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
const {
	campusId,
	tenantId,
	campusInfo,
	tenantInfo,
	attendanceStatusMap,
	semesterInfo,
	attendanceTypeMap,
	attendanceModeMap
} = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null,
		grade_id: null,
		class_id: null,
		begin_time: null,
		end_time: null,
		date: [],
		name: null
	}
}
export default {
	name: 'dept',
	components: {},
	data() {
		return {
			dialog: {
				save: false
			},
			apiObj: this.$API.personnel.attendance.list,
			selection: [],

			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			entryExitMap: [
				{ name: '进', value: 1 },
				{ name: '出', value: 2 }
			],
			userTypeMap: [
				{ name: '学员', value: 1 },
				{ name: '教职工', value: 2 }
			],
			attendanceStatusMap,
			attendanceTypeMap,
			attendanceModeMap,
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'academic_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择学年',
							items: []
						}
					},
					{
						label: null,
						name: 'semester_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择学期',
							items: []
						}
					},
					{
						label: null,
						name: 'date',
						value: null,
						component: 'cusDate',
						options: {
							placeholder: '请选择校区',
							type: 'daterange'
						}
					}
				]
			}
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.academic_id = null
				this.searchConfig.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
					.filter((v) => v.parent_id == 0 && v.campus_id == val)
					.map((v) => {
						return {
							label: v.name,
							value: v.value
						}
					})
			},
			immediate: true
		},
		'params.academic_id': {
			handler(val) {
				this.params.semester_id = null
				this.searchConfig.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
					.filter(
						(v) => v.parent_id != 0 && v.parent_id == this.params.academic_id && v.campus_id == this.params.campus_id
					)
					.map((v) => {
						return {
							label: v.name,
							value: v.value
						}
					})
			}
		}
	},
	computed: {},
	async created() {},
	methods: {
		//添加
		add(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', row)
			})
			// this.upsearch()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.buildingRooms.AccommodationArrangements.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},

		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		//搜索
		upsearch() {
			this.params.begin_time = this.params.date ? this.params.date[0] : null
			this.params.end_time = this.params.date ? this.params.date[1] : null
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped>
.roomItem {
	& + & {
		margin-top: 5px;
	}
}
</style>
