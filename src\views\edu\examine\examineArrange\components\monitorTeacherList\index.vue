<template>
	<div class="monitorContent">
		<div class="monitorContent-left">
			<div class="monitorContent-left-top">
				<el-button type="primary" plain @click="arrange">一键安排</el-button>
				<el-button type="primary" @click="save">保存</el-button>
			</div>
			<table width="100%" border="0" cellspacing="1" cellpadding="4" bgcolor="#ffffff" align="center"
				class="table-header" style="position: sticky; top: 50px; z-index: 5">
				<thead>
					<tr class="table-header">
						<th width="10%" class="" rowspan="2">考场/日期</th>
						<template v-for="(date, index) in dateList" :key="index">
							<th :colspan="date.subjects.length" class="">{{ date.date }}</th>
						</template>
					</tr>
					<tr class="table-header">
						<template v-for="(date, index) in dateList" :key="index">
							<template v-for="subject in date.subjects" :key="subject.id">
								<th class="">
									<p>{{ subject.course_name }}</p>
									<p>{{ subject.time }}</p>
								</th>
							</template>
						</template>
					</tr>
				</thead>
			</table>
			<table width="100%" border="0" cellspacing="1" cellpadding="4" bgcolor="#ffffff" align="center" @mouseleave="
				() => {
					visibleContent = ''
					focusData = { room_id: '', subject_id: '' }
				}
			">
				<tbody>
					<template v-for="room in roomList" :key="room.room_id">
						<tr>
							<td width="10%" class="font-center">
								<div>{{ room.room_info.name }}</div>
								<div v-if="room.room_id !== 0">（{{ room.teacher_num }}）</div>
							</td>
							<template v-for="(date, dateIndex) in dateList" :key="dateIndex">
								<template v-for="subject in date.subjects" :key="subject.id">
									<el-popover v-if="visibleContent"
										:visible="room.room_id == focusData.room_id && subject.id == focusData.subject_id"
										:content="visibleContent" placement="right">
										<template #reference>
											<td class="font-center" :class="getTeachersByRoom(room.room_id, subject.id).teacherIds.includes(currentStaff.staff_info?.id)
												? 'lite'
												: ''
												" v-on:mouseenter="mouseFocus(room.room_id, subject.id)" @click="mouseClick(room.room_id, subject.id)">
												<div class="tdContent">
													<p v-for="tea in getTeachersByRoom(room.room_id, subject.id).teachers"
														:key="tea.id">
														<span>{{ tea.name }}</span>
														<el-icon color="#F53F3F" size="15px" class="closeIcon"
															@click.stop="delMonitorTeacher(tea.id)">
															<el-icon-circleClose />
														</el-icon>
													</p>
												</div>
											</td>
										</template>
									</el-popover>
									<td v-else class="font-center" :class="getTeachersByRoom(room.room_id, subject.id)?.teacherIds.includes(currentStaff.staff_info?.id)
										? 'lite'
										: ''
										" v-on:mouseenter="mouseFocus(room.room_id, subject.id)" @click="mouseClick(room.room_id, subject.id)">
										<div class="tdContent">
											<p v-for="tea in getTeachersByRoom(room.room_id, subject.id)?.teachers"
												:key="tea.id">
												<span>{{ tea.name }}</span>
												<el-icon color="#F53F3F" size="15px" class="closeIcon"
													@click.stop="delMonitorTeacher(tea.id)">
													<el-icon-circleClose />
												</el-icon>
											</p>
										</div>
									</td>
								</template>
							</template>
						</tr>
					</template>
				</tbody>
			</table>
		</div>
		<div class="monitorContent-right">
			<div class="monitorContent-right-title">
				<span>人员</span>
				<el-input v-model="staffName" style="width: 200px" clearable placeholder="输入人员名称搜索"
					prefix-icon="el-icon-search" />
			</div>
			<div v-if="staffList && staffList.length" class="monitorContent-right-content">
				<div v-for="item in filteredStaffList" :key="item.staff_id" class="monitorContent-right-item"
					:class="{ active: item.staff_id == currentStaff.staff_info?.id }" :data-user-type="item.user_type"
					@click="staffClick(item)">
					<div class="monitorContent-right-item-title">
						<span>{{ item.staff_info?.name }}</span>
						<el-divider direction="vertical" />
						<el-tag :type="item.user_type == 3 ? 'warning' : 'primary'" size="small">{{
							filterType(item.user_type)
						}}</el-tag>
						<!-- <span style="opacity:0.7">{{ filterType(item.user_type) }}</span> -->
					</div>
					<div style="opacity: 0.7; font-size: 15px">段数：{{ getTeacherCount(item.staff_id) }}</div>
				</div>
			</div>
			<div v-else class="monitorContent-right-content">
				<el-empty description="暂无监考老师" :image-size="100"></el-empty>
			</div>
		</div>
	</div>
	<warnDialog ref="warnRef" @confirm="submitList"></warnDialog>
</template>

<script setup>
import warnDialog from './warnDialog.vue'

import { ElMessage, ElMessageBox } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()

const params = ref({
	tenant_id: tenantId,
	campus_id: campusId,
	examine_id: Number(query.id)
})

const filterType = (type) => {
	const map = { 1: '主考人员', 2: '副主考', 3: '巡考', 4: '机动', 5: '监考' }
	return map[type]
}
const getTeacherCount = (staffId) => {
	if (!teacherList.value) {
		return 0
	} else {
		return teacherList.value.filter((item) => item.staff_id === staffId).length
	}
}

// 人员点击
const currentStaff = ref({})
const staffClick = (item) => {
	if (currentStaff.value.staff_info?.id === item.staff_id) {
		currentStaff.value = {}
	} else {
		currentStaff.value = { staff_info: item.staff_info, user_type: item.user_type }
	}
	console.log(item)
}

// visible
const visibleContent = ref('')
const focusData = ref({
	room_id: '',
	subject_id: ''
})
// 鼠标聚焦
const mouseFocus = (room_id, subject_id) => {
	if (teacherList.value && !teacherList.value.length) {
		return
	}

	const warnings = []

	// 检查考场是否已满员
	const currentRoom = roomList.value.find((room) => room.room_id === room_id)
	const currentTeachers = getTeachersByRoom(room_id, subject_id)?.teachers
	if (currentRoom.teacher_num && currentTeachers && currentTeachers.length >= currentRoom.teacher_num) {
		warnings.push('人员已满')
	}

	// 检查当前选中的老师在同一时间段是否有冲突
	if (currentStaff.value.staff_info?.id && teacherList.value && teacherList.value.length) {
		const hasConflict = teacherList.value.some(
			(teacher) => teacher.staff_id === currentStaff.value.staff_info.id && teacher.time_id === subject_id
		)
		if (hasConflict) {
			warnings.push('时间冲突')
		}
	}

	visibleContent.value = warnings.join('、')
	focusData.value = {
		room_id,
		subject_id
	}
}
// 鼠标点击
const mouseClick = (room_id, subject_id) => {
	// 检查当前选中的老师在同一时间段是否有冲突
	if (currentStaff.value.staff_info?.id && teacherList.value && teacherList.value.length) {
		const conflictTeacher = teacherList.value.find(
			(teacher) => teacher.staff_id === currentStaff.value.staff_info.id && teacher.time_id === subject_id
		)
		if (conflictTeacher) {
			// 获取冲突的考场和科目信息
			const conflictRoom = roomList.value.find((room) => room.room_id === conflictTeacher.room_id)
			const conflictDate = dateList.value.find((date) => date.subjects.some((sub) => sub.id === subject_id))
			const conflictSubject = conflictDate?.subjects.find((sub) => sub.id === subject_id)
			let msg = ''
			if (conflictRoom && conflictRoom.room_id == 0) {
				msg = `时间冲突：该教师已安排了${conflictSubject?.course_name || ''}考试${conflictRoom?.room_info?.name || ''}`
			} else {
				msg = `时间冲突：该教师已在${conflictRoom?.room_info?.name || ''}安排了${conflictSubject?.course_name || ''
					}监考`
			}
			ElMessage.error(msg)
			return
		}
		// room_id: 0 表示巡考,只能添加巡考教师user_type为3的
		if (room_id == 0 && currentStaff.value.user_type != 3) {
			ElMessage.error('巡考只能添加巡考人员')
			return
		}
		// room_id:!0 表示考场,只能添加考场人员user_type为5的
		if (room_id != 0 && currentStaff.value.user_type != 5) {
			ElMessage.error('考场只能添加监考人员')
			return
		}

		// 如果已经满员了，则提示，没有继续添加
		const currentTeachers = getTeachersByRoom(room_id, subject_id)?.teachers
		const currentRoom = roomList.value.find((room) => room.room_id === room_id)
		if (currentRoom.teacher_num && currentTeachers.length >= currentRoom.teacher_num) {
			ElMessage.warning('人员已满')
			return
		}
		teacherList.value.push({
			staff_id: currentStaff.value.staff_info.id,
			staff_info: currentStaff.value.staff_info,
			user_type: currentStaff.value.user_type,
			room_id,
			time_id: subject_id
		})
	} else {
		// room_id: 0 表示巡考,只能添加巡考教师user_type为3的
		if (room_id == 0 && currentStaff.value.user_type != 3) {
			ElMessage.error('巡考只能添加巡考人员')
			return
		}
		// room_id:!0 表示考场,只能添加考场人员user_type为5的
		if (room_id != 0 && currentStaff.value.user_type != 5) {
			ElMessage.error('考场只能添加监考人员')
			return
		}
		teacherList.value.push({
			staff_id: currentStaff.value.staff_info.id,
			staff_info: currentStaff.value.staff_info,
			user_type: currentStaff.value.user_type,
			room_id,
			time_id: subject_id
		})
	}
}

// 删除监考老师
const delMonitorTeacher = (id) => {
	const currentRoom = focusData.value.room_id
	const currentSubject = focusData.value.subject_id
	// 添加延时处理，避免与mouseClick事件冲突
	setTimeout(() => {
		teacherList.value =
			teacherList.value.filter(
				(item) => !(item.staff_id === id && item.room_id === currentRoom && item.time_id === currentSubject)
			) || []
	}, 100)
}
// 一键安排
const arrange = () => {
	globalPropValue.examine.arrangeTeacher.post(params.value).then((res) => {
		if (res.code === 200) {
			ElMessage.success('一键安排成功')
			teacherList.value = res.data || []
		}
	})
}
const saveParams = ref({})
const warnRef = ref()
// 保存
const save = () => {
	saveParams.value = {
		examine_id: Number(query.id),
		tenant_id: tenantId,
		campus_id: campusId,
		staff_list: teacherList.value.map((item) => {
			return {
				staff_id: item.staff_id,
				room_id: item.room_id,
				time_id: item.time_id,
				mod: item.mod ? item.mod : item.user_type == 5 ? 1 : 2
			}
		})
	}
	console.log(saveParams.value)
	// 检查排考()
	ElMessageBox.confirm('确定保存吗？若存在已安排的监考安排，保存后会覆盖之前的监考安排！', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		globalPropValue.examine.checkExam.post(saveParams.value).then((res) => {
		if (res.code === 200) {
			if (!res.data) {
				submitList()
			} else {
				warnRef.value.open(res.data)
			}
		}
	})
	})

	
}

const submitList = () => {
	globalPropValue.examine.saveMonitorTeacher.post(saveParams.value).then((res) => {
		if (res.code === 200) {
			ElMessage.success('保存成功')
			getMonitorTeacherData()
		}
	})
}

const teacherList = ref([])
const dateList = ref([])
const roomList = ref([])
const staffList = ref([])
const staffName = ref('')
const filteredStaffList = computed(() => {
	let list = staffName.value
		? staffList.value.filter((item) => item.staff_info?.name.toLowerCase().includes(staffName.value.toLowerCase()))
		: staffList.value

	// 将巡考人员（user_type为3）排在前面
	return list.sort((a, b) => {
		if (a.user_type === 3 && b.user_type !== 3) return -1
		if (a.user_type !== 3 && b.user_type === 3) return 1
		return 0
	})
})
// 获取监考老师数据
const getMonitorTeacherData = () => {
	globalPropValue.examine.getMonitorTeacherList.get(params.value).then((res) => {
		if (res.code === 200) {
			teacherList.value = res.data || []
			getTimeData()
			getStaffList()
		}
	})
}
//获取监考人员数据
const getStaffList = () => {
	globalPropValue.examine.staffList
		.get({
			tenant_id: tenantId,
			campus_id: campusId,
			examine_id: query.id
		})
		.then((res) => {
			if (res.code === 200) {
				staffList.value = res.data.filter((item) => item.user_type != 1 && item.user_type != 2 && item.user_type != 4)
			}
		})
}
// 获取时间段列表
const getTimeData = () => {
	globalPropValue.examine.timeList.get(params.value).then((res) => {
		if (res.code === 200) {
			const groupedData = {}
			res.data.forEach((item) => {
				if (!groupedData[item.examine_date]) {
					groupedData[item.examine_date] = {
						date: item.examine_date,
						subjects: []
					}
				}
				groupedData[item.examine_date].subjects.push({
					id: item.id,
					course_name: item.course_name,
					time: item.begin_time + '-' + item.end_time
				})
			})
			dateList.value = Object.values(groupedData)
			getRoomList()
		}
	})
}

// 获取考场列表
const getRoomList = () => {
	globalPropValue.examine.roomList.get(params.value).then((res) => {
		if (res.code === 200) {
			let patrol = { room_id: 0, room_info: { name: '巡考', id: 0 } }
			roomList.value = [patrol, ...res.data]
		}
	})
}

// 根据考场和时间段获取监考老师
const getTeachersByRoom = (roomId, timeId) => {
	if (!teacherList.value) {
		return null
	}
	const teachers = teacherList.value.filter((item) => item.room_id === roomId && item.time_id === timeId)
	return {
		teachers: teachers.map((teacher) => teacher.staff_info),
		teacherIds: teachers.map((teacher) => teacher.staff_id)
	}
}

onMounted(() => {
	getMonitorTeacherData()
})
</script>

<style lang="scss" scoped>
.monitorContent {
	height: calc(100vh - 210px);
	display: flex;
	justify-content: space-between;

	.monitorContent-left {
		width: 75%;
		height: 100%;
		padding-right: 10px;
		overflow: auto;
		position: relative;

		&-top {
			position: sticky;
			top: 0;
			z-index: 2;
			background: #fff;
			padding: 10px 0;
			text-align: right;
		}

		.table-header {}

		th {
			height: 50px;
			border: 1px solid #dcdfe6;
			background-color: #f5f7fa;
		}

		td {
			border: 1px solid #dcdfe6;
			padding: 8px;
			height: 100px;
			cursor: pointer;
		}

		table {
			border-collapse: collapse;
			border: 1px solid #dcdfe6;
			table-layout: fixed; //宽度固定，不随内容自动改变
		}

		.font-center {
			text-align: center;
			max-width: 100%;

			.tdContent {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				font-size: 15px;

				p {
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}

			&:hover {
				.closeIcon {
					display: inline-block;
				}
			}
		}

		.closeIcon {
			display: none;
			margin-left: 3px;
		}
	}

	.monitorContent-right {
		width: 25%;
		height: calc(100% - 50px);
		border-left: 1px solid #e5e5e5;

		&-title {
			padding: 0 15px 8px;
			margin-bottom: 10px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1px solid #dcdfe6;

			span {
				font-size: 13px;
			}
		}

		&-content {
			height: 100%;
			overflow-y: auto;
			padding: 10px;
		}

		&-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px 15px;
			margin-bottom: 10px;
			background: #f6f8f9;
			border-radius: 5px;
			cursor: pointer;

			// 巡考人员（user_type为3）与其他人员之间添加间隔
			&:not(:last-child) {
				&[data-user-type='3']+div:not([data-user-type='3']) {
					margin-top: 20px;
					position: relative;

					&::before {
						content: '';
						position: absolute;
						top: -10px;
						left: 0;
						right: 0;
						height: 1px;
						background-color: #dcdfe6;
					}
				}
			}

			&:last-child {
				margin-bottom: 0;
			}

			&-title {
				font-size: 15px;
			}
		}

		.active {
			background: var(--el-color-primary-light-7);
		}
	}
}

.lite {
	background: var(--el-color-warning-light-3);

	&:hover {
		background: var(--el-color-primary-light-7);
	}
}
</style>
