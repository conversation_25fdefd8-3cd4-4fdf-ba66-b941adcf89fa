<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-page-header v-if="breadcrumb.length > 1" title="" @back="breadcrumb_back">
						<template #content>
							<el-breadcrumb separator="/">
								<el-breadcrumb-item v-for="item in breadcrumb"
									><b>{{ item.name }}</b></el-breadcrumb-item
								>
							</el-breadcrumb>
						</template>
					</el-page-header>
					<el-breadcrumb v-else separator="/">
						<el-breadcrumb-item v-for="item in breadcrumb"
							><b>{{ item.name }}</b></el-breadcrumb-item
						>
					</el-breadcrumb>
				</div>
			</div>
			<div class="right-panel">
				<el-form-item style="margin-bottom: 0">
					<el-select v-model="params.suffix_type" clearable @change="upsearch">
						<el-option
							v-for="item in fileSuffixType"
							:key="item.value"
							:value="item.value"
							:label="item.name"
						></el-option>
					</el-select>
					<el-button style="margin-left: 10px" type="primary" @click="addUploadFile(1)">上传文件 </el-button>
					<el-button type="success" @click="addDirFile(2)">新增文件夹</el-button>
				</el-form-item>
			</div>
		</el-header>
		<el-main>
			<scTable
				ref="table"
				row-key="id"
				postion
				:apiObj="apiObj"
				:params="params"
				@select-all="onSelectAll"
				@select-change="onSelectChange"
			>
				<el-table-column type="selection"></el-table-column>
				<el-table-column label="文件名称" prop="name">
					<template #default="{ row }">
						<div style="display: flex">
							<SvgIcon :iconName="suffix_type[row.suffix_type]" /> &nbsp;&nbsp;
							<el-link v-if="webPreview(row.url)" type="primary" :underline="false" @click="table_preview(row)">{{
								row.name
							}}</el-link>
							<el-link v-else-if="checkVideo(row.url)" type="primary" :underline="false" @click="table_play(row)">{{
								row.name
							}}</el-link>
							<el-link v-else-if="imageSuffix(row.url)" type="primary" :underline="false" @click="table_image(row)">
								{{ row.name }}
							</el-link>
							<el-link v-else-if="row.file_type === 2" type="primary" :underline="false" @click="dir_preview(row)">{{
								row.name
							}}</el-link>
							<span v-else style="font-size: 14px">{{ row.name }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="文件大小" prop="size" width="150">
					<template #default="{ row }">
						<span v-if="row.file_type === 1">{{ row.size_format }}</span>
						<span v-if="row.file_type === 2"> </span>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="200"></el-table-column>
				<el-table-column label="操作" width="260">
					<template #default="{ row }">
						<el-button-group>
							<el-button type="primary" text size="small" @click="table_edit(row)">编辑</el-button>
							<el-button v-if="row.file_type === 1" type="success" text size="small" @click="table_download(row)"
								>下载</el-button
							>
							<el-button type="success" text size="small" @click="table_share(row)">分享</el-button>
							<el-button type="success" text size="small" @click="table_move(row)">移动到</el-button>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
			<div class="footer-check-btn">
				<div class="checkout">
					<div>已选 {{ currentCheckedLength }}/{{ checkedTotal }}</div>
				</div>
				<div class="btn">
					<el-button :disabled="currentCheckedLength === 0" size="small" type="primary" @click="Onshare"
						>分享</el-button
					>
					<el-popconfirm title="确定要删除吗?" @confirm="onDel">
						<template #reference>
							<el-button :disabled="currentCheckedLength === 0" size="small" type="danger">删除</el-button>
						</template>
					</el-popconfirm>
				</div>
			</div>
		</el-main>
	</el-container>
	<FileDialog ref="fileDialogRef" :params="params" @success="DirfileSuccess"></FileDialog>
	<FileUpload ref="fileUploadRef" :params="params" @success="fileSuccess"></FileUpload>
	<FileShare ref="fileShareRef" :params="params"></FileShare>
	<el-dialog v-model="visible" title="视频播放" destroy-on-close>
		<VideoPlay :source="source"></VideoPlay>
	</el-dialog>
	<el-image-viewer
		v-if="isShowImage"
		:url-list="imageUrl"
		:zIndex="1000"
		style="width: 100px; height: 100px"
		@close="onClose"
	></el-image-viewer>
	<MoveDialog ref="moveDialogRef" @success="onSuccess"></MoveDialog>
</template>

<script setup>
// 表格列表字段
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		model: 5,
		type_id: -1,
		suffix_type: 0,
		pid: null
	}
}
import cusTom from '@/utils/cusTom'
import { webPreview, checkVideo, imageSuffix } from '@/utils/mediaLib'
import FileUpload from '../fileUpload.vue'
import FileDialog from '../fileDialog.vue'
import FileShare from '../fileShare.vue'
import MoveDialog from './moveDialog.vue'
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
import VideoPlay from '@/components/videoPlay/index.vue'
const { tenantId, campusId, fileSuffixTypeMap } = cusTom.getBaseQuery()
import { ref, getCurrentInstance, computed, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const props = defineProps(['Tableparams'])
const params = ref(defaultParams())
let visible = ref(false)
let isShowImage = ref(false)
let imageUrl = ref([])
const table = ref(null)
let fileDialogRef = ref(null)
let fileUploadRef = ref(null)
let fileShareRef = ref(null)
let moveDialogRef = ref(null)
let source = ref('')
let checkedTotal = ref(0)
let currentCheckedLength = ref(0)
let currentRow = ref([])
const breadcrumb = ref([{ id: -1, root: 1, name: '默认文件夹' }])
const apiObj = ref(globalPropValue.fileManagement.file.file_list)
const suffix_type = reactive({
	0: 'icon-wenjianjia',
	1: 'icon-word',
	2: 'icon-excel',
	3: 'icon-PPT',
	4: 'icon-PDF',
	5: 'icon-tupian1',
	6: 'icon-shipin1',
	7: 'icon-yinle',
	8: 'icon-yasuowenjian',
	99: 'icon-qita1'
})

// 文件类型
const fileSuffixType = computed(() => {
	return [{ name: '全部', value: 0 }, ...fileSuffixTypeMap]
})
// 下拉框搜索事件
const upsearch = () => {
	table.value?.upData(params.value)
}
// 新增文件夹事件
const addDirFile = (type) => {
	fileDialogRef.value.open('add', type)
}
// 上传问价事件
const addUploadFile = (type) => {
	fileUploadRef.value.open(type)
}

// 表格编辑事件
const table_edit = (data) => {
	fileDialogRef.value.open('edit', data)
	fileDialogRef.value.setForm(data)
}
// 文件删除事件
const onDel = async () => {
	const ids = currentRow.value.map((v) => v.id)
	const res = await globalPropValue.fileManagement.file.del_file.post({
		id: ids,
		campus_id: params.value.campus_id,
		tenant_id: params.value.tenant_id
	})
	if (res.code === 200) {
		ElMessage({
			type: 'success',
			message: '删除成功'
		})
		// table.value.upData(params.value)
		table.value.refresh()
	} else {
		ElMessage({
			type: 'error',
			message: res.msg
		})
	}
}
// 文件分享事件
const table_share = (row) => {
	fileShareRef.value.open(row.id)
}
// 文件批量分享事件
const Onshare = () => {
	const ids = currentRow.value.map((v) => v.id)
	fileShareRef.value.open(ids)
}
// 文件下载事件
const table_download = (data) => {
	let tagA = document.createElement('a')
	tagA.href = data.url
	tagA.download = data.name || '文件下载'
	tagA.click()
	tagA.remove()
}
// 表格预览点击事件
const router = useRouter()
const table_preview = (row) => {
	const routeUrl = router.resolve({
		path: '/filepreview',
		query: {
			tenant_id: params.value.tenant_id,
			campus_id: params.value.campus_id,
			file_id: row.id
		}
	})
	window.open(routeUrl.href, '_blank')
}
// 表格视频播放事件
const table_play = (row) => {
	visible.value = true
	source.value = row.url
}
// 表格图片预览事件
const table_image = (row) => {
	isShowImage.value = true
	imageUrl.value = [row.url]
}
// 点击x按钮触发事件
const onClose = () => {
	isShowImage.value = false
}
// 点击表格文件夹事件
const dir_preview = (row) => {
	params.value.pid = row.id
	breadcrumb.value.push({ id: row.id, root: 0, name: row.name })
	table.value.upData(params.value)
}
// 表头返回点击事件
const breadcrumb_back = () => {
	breadcrumb.value.pop()
	const last = breadcrumb.value[breadcrumb.value.length - 1]
	if (last.root === 1) {
		params.value.type_id = last.id
		params.value.pid = null
	} else {
		params.value.pid = last.id
	}
	table.value.upData(params.value)
}
// 表格移动事件
const table_move = (row) => {
	moveDialogRef.value.open(params.value, row.id)
}

// 自定义事件
const upadteTable = () => {
	params.value.type_id = props.Tableparams.type_id
	params.value.pid = null
	breadcrumb.value = [{ id: props.Tableparams.treeList.id, root: 1, name: props.Tableparams.treeList.name }]
	table.value.upData(params.value)
	setTimeout(() => {
		checkedTotal.value = table.value.total
	}, 100)
}
const onSuccess = () => {
	table.value.refresh()
	// table.value.upData(params.value)
}
const DirfileSuccess = () => {
	table.value.refresh()
	// table.value.upData(params.value)
}
const fileSuccess = () => {
	table.value.refresh()
	// table.value.upData(params.value)
}
const onSelectChange = (val) => {
	currentCheckedLength.value = val.length
	currentRow.value = val
}
const onSelectAll = (val) => {
	currentCheckedLength.value = val.length
}
onMounted(() => {
	console.log(table.value)
	setTimeout(() => {
		checkedTotal.value = table.value.total
	}, 200)
})
defineExpose({
	upadteTable
})
</script>

<style scoped lang="scss">
.footer-check-btn {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-top: -40px;
	overflow: hidden;

	.checkout {
		margin-right: 10px;
	}
}
</style>
