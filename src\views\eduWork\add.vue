<template>
	<el-container>
		<el-scrollbar style="height: 100%">
			<el-card style="width: 100%" :shadow="false">
				<el-form
					ref="workFormRef"
					:model="workForm"
					:rules="rules"
					label-position="left"
					label-suffix="："
					label-width="100px"
				>
					<el-collapse v-model="activeNames">
						<el-collapse-item title="基本信息" name="1">
							<el-row :gutter="20">
								<el-col :span="6">
									<el-form-item label="作业类型" prop="work_type">
										<el-radio-group v-model="workForm.work_type">
											<el-radio :label="1">线上</el-radio>
											<el-radio :label="2">线下</el-radio>
										</el-radio-group>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="作业名称" prop="work_name">
										<el-input v-model="workForm.work_name"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="所属学科" prop="discipline_id">
										<el-select v-model="workForm.discipline_id" placeholder="请选择" filterable clearable>
											<el-option
												v-for="item in disciplineOptions.list"
												:key="item.id"
												:label="item.discipline_name"
												:value="item.id"
											></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="课程" prop="course_id">
										<el-select v-model="workForm.course_id" placeholder="请选择" filterable clearable>
											<el-option
												v-for="item in getCourse"
												:key="item.id"
												:label="item.course_name"
												:value="item.id"
											></el-option>
										</el-select>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="20">
								<el-col :span="6">
									<el-form-item label="学年" prop="academic_id">
										<el-select v-model="workForm.academic_id" placeholder="请选择学年" filterable clearable>
											<el-option
												v-for="item in getAcademic_year"
												:key="item.code"
												:label="item.name"
												:value="item.value"
											/>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="学期" prop="semester_id">
										<el-select v-model="workForm.semester_id" placeholder="请选择学期" filterable clearable>
											<el-option v-for="item in getSemester" :key="item.code" :label="item.name" :value="item.value" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="年级" prop="grade_id">
										<el-select v-model="workForm.grade_id" placeholder="请选择年级" filterable clearable>
											<el-option v-for="item in getGrade" :key="item.id" :label="item.grade_name" :value="item.id" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="6">
									<el-form-item label="班级" prop="class_id">
										<el-select v-model="workForm.class_id" placeholder="请选择班级" filterable clearable>
											<el-option v-for="item in getClass" :key="item.id" :label="item.class_name" :value="item.id" />
										</el-select>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="20">
								<el-col :span="6"><el-form-item label="开启评论" prop="allow_comments"> </el-form-item></el-col>
								<el-col :span="6"><el-form-item label="评分类型" prop="scoring_type"> </el-form-item></el-col>
								<el-col :span="6"><el-form-item label="通过分数" prop="pass_score"> </el-form-item></el-col>
								<el-col :span="6"><el-form-item label="三级评分制" prop="score_level"> </el-form-item></el-col>
							</el-row>
						</el-collapse-item>
						<el-collapse-item title="作业试题" name="2">
							<el-row :gutter="20">
								<el-col :span="10">
									<div class="workPreview"></div>
								</el-col>
								<el-col :span="14">
									<el-form label-position="left" label-suffix="：">
										<el-form-item label="题目类型">
											<el-radio-group v-model="questiontype">
												<el-radio :label="1">单选题</el-radio>
												<el-radio :label="2">多选题</el-radio>
												<el-radio :label="3">判断题</el-radio>
											</el-radio-group>
										</el-form-item>
										<el-form-item label="题目">
											<textEditor></textEditor>
										</el-form-item>
										<el-form-item label="题目选项">
											<el-row :gutter="10" style="width: 100%">
												<el-col :span="4"><span>选项编号</span></el-col>
												<el-col :span="16"><span>选项内容</span></el-col>
												<el-col :span="4"><span>是否正确答案</span></el-col>
											</el-row>
											<template v-if="questiontype == 3">
												<el-row :gutter="10" style="width: 100%">
													<el-col :span="4">A</el-col>
													<el-col :span="16">对</el-col>
													<el-col :span="4"
														><el-switch v-model="judgeVal" active-value="A" inactive-value="B"
													/></el-col>
												</el-row>
												<el-row :gutter="10" style="width: 100%">
													<el-col :span="4">B</el-col>
													<el-col :span="16">错</el-col>
													<el-col :span="4"
														><el-switch v-model="judgeVal" active-value="B" inactive-value="A"
													/></el-col>
												</el-row>
											</template>
										</el-form-item>
									</el-form>
								</el-col>
							</el-row>
						</el-collapse-item>
					</el-collapse>
					<el-button type="primary" style="margin-top: 15px">保存</el-button>
				</el-form>
			</el-card>
		</el-scrollbar>
	</el-container>
</template>

<script setup>
import { reactive, ref, computed, watch, getCurrentInstance, onMounted, nextTick } from 'vue'
import cusTom from '@/utils/cusTom'
import textEditor from '@/components/textEditor'
const { proxy } = getCurrentInstance()
const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
let activeNames = reactive(['1'])
const workFormRef = ref(null)
const workForm = reactive({
	id: '',
	tenant_id: tenantId,
	campus_id: campusId,
	work_type: 1,
	work_name: '',
	discipline_id: '',
	course_id: '',
	academic_id: '',
	semester_id: '',
	grade_id: '',
	class_id: '',
	scoring_type: '',
	pass_score: '',
	score_level: '',
	allow_comments: '',
	work_file: ''
})
const rules = reactive({
	work_type: [{ required: true, message: '请选择作业类型', trigger: 'change' }],
	work_name: [{ required: true, message: '请输入作业名称', trigger: 'blur' }],
	discipline_id: [{ required: true, message: '请选择所属学科', trigger: 'change' }],
	course_id: [{ required: true, message: '请选择课程', trigger: 'change' }],
	academic_id: [{ required: true, message: '请选择学年', trigger: 'change' }],
	semester_id: [{ required: true, message: '请选择学期', trigger: 'change' }],
	grade_id: [{ required: true, message: '请选择年级', trigger: 'change' }],
	class_id: [{ required: true, message: '请选择班级', trigger: 'change' }],
	scoring_type: [{ required: true, message: '请选择评分类型', trigger: 'change' }]
})
const myGrade = reactive({
	list: []
})
const myClass = reactive({
	list: []
})
const disciplineOptions = reactive({
	list: []
})
const course = reactive({
	list: []
})
let questiontype = ref(1)
let judgeVal = ref('A')
let getAcademic_year = computed(() => {
	return semesterInfo.filter((v) => v.parent_id == 0 && v.campus_id == workForm.campus_id)
})
let getSemester = computed(() => {
	return semesterInfo.filter(
		(v) => v.parent_id != 0 && v.parent_id == workForm.academic_id && v.campus_id == workForm.campus_id
	)
})
let getGrade = computed(() => {
	return myGrade.list.filter(
		(v) => v.parent_id != 0 && v.semester_id == workForm.semester_id && v.campus_id == workForm.campus_id
	)
})
let getClass = computed(() => {
	return myClass.list.filter(
		(v) => v.parent_id != 0 && v.grade_id == workForm.grade_id && v.campus_id == workForm.campus_id
	)
})
let getCourse = computed(() => {
	return course.list.filter((item) => {
		return item.campus_id == workForm.campus_id && item.discipline_id == workForm.discipline_id
	})
})
watch(
	() => workForm.academic_id,
	(val) => {
		workForm.semester_id = null
	}
)
watch(
	() => workForm.semester_id,
	(val) => {
		workForm.grade_id = null
	}
)
watch(
	() => workForm.grade_id,
	(val) => {
		workForm.class_id = null
	}
)
const getGradeData = async () => {
	let params = {
		tenant_id: workForm.tenant_id,
		campus_id: workForm.campus_id,
		semester_id: workForm.semester_id
	}
	let res = await proxy.$API.eduGradeClass.grade.all.get(params)
	myGrade.list = res.data
}
const getClassData = async () => {
	let params = {
		tenant_id: workForm.tenant_id,
		campus_id: workForm.campus_id,
		semester_id: workForm.semester_id,
		grade_id: workForm.grade_id
	}
	let { data } = await proxy.$API.eduGradeClass.class.all.get(params)
	myClass.list = data
}
const getDisciplineOptions = async () => {
	let { data } = await proxy.$API.eduDiscipline.discipline.all.get({ tenant_id: tenantId, campus_id: campusId })
	disciplineOptions.list = data
}
const getEduCourse = async () => {
	let { data } = await proxy.$API.eduCourseSet.course.all.get({ tenant_id: tenantId, campus_id: campusId })
	course.list = data
}
onMounted(() => {
	getGradeData()
	getClassData()
	getDisciplineOptions()
	getEduCourse()
})
</script>

<style lang="scss" scoped>
.workPreview {
	padding: 20px;
	border: 1px solid var(--el-border-color);
}
</style>
