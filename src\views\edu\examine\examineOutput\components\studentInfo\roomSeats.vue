<template>
	<div class="date-box">
		<div v-for="(item, index) in dateList" :key="index" class="date-item"
			:style="`flex:${item.course.length} 1 auto`">
			<div class="date-item-header">{{ item.date }}</div>
			<div class="date-item-course">
				<div v-for="(course, index) in item.course" :key="index" class="course-item"
					:class="{ active: currentId === course.id }" @click="selectCourse(item, course)">
					<p>{{ course.course_name }}</p>
					<p>{{ course.time }}</p>
				</div>
			</div>
		</div>
	</div>
	<div>
		<div class="add-btn">
			<p></p>
			<div>
				<el-button type="primary" @click="exportExcel">导出</el-button>
			</div>
		</div>
		<el-table :data="paginatedSeatList" stripe style="width: 100%" min-height="400">
			<el-table-column prop="room_info" label="考场">
				<template #default="{ row }">
					<div>{{ row.room_info?.name }}</div>
				</template>
			</el-table-column>
			<el-table-column prop="seat_number" label="座位号"></el-table-column>
			<el-table-column prop="serial_number" label="学号/考号"></el-table-column>
			<el-table-column prop="student_name" label="姓名"></el-table-column>
			<el-table-column prop="grade" label="原班级">
				<template #default="{ row }">
					<div>{{ row.grade_info?.name || '' }} {{ row.class_info?.name || '' }}</div>
				</template>
			</el-table-column>
		</el-table>
		<el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" size="small" background
			:page-sizes="[10, 20, 30, 50]" :total="seatList.length" layout="total, sizes, prev, pager, next, jumper"
			class="pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
	</div>
</template>
<script setup>
import { excelExport } from 'pikaz-excel-js'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		examine_id: query.id,
		course_id: null
	}
}
const params = ref(defaultParams())

const currentId = ref('')
const currentCoursse = ref({})
const selectCourse = (date, course) => {
	currentCoursse.value = {
		date: date.date,
		course_name: course.course_name,
		time: course.time
	}
	if (currentId.value === course.id) {
		if (query.seat_type == 2) {
			return
		}
		currentId.value = ''
		params.value.course_id = ''
	} else {
		currentId.value = course.id
		params.value.course_id = course.id
	}
	getSeats()
}

// 导出
const exportExcel = async () => {
	// 整理数据
	const data = seatList.value.map((item, index) => {
		return {
			type: index + 1,
			room: item.room_info?.name,
			seat: item.seat_number,
			num: item.serial_number,
			student_name: item.student_name,
			grade: item.grade_info?.name + (item.class_info?.name || '')
		}
	})
	// 按教室分组学生
	const groupedByRoom = data.reduce((acc, student) => {
		const room = student.room
		if (!acc.find((item) => item.room === room)) {
			acc.push({
				room: room,
				studentList: [student]
			})
		} else {
			acc.find((item) => item.room === room).studentList.push(student)
		}
		return acc
	}, [])
	// 计算学生数
	const studentNum = data.reduce((acc, item) => {
		if (!acc.includes(item.student_name)) {
			acc.push(item.student_name)
		}
		return acc
	}, [])
	let courseData = [
		{
			type: '考试时间',
			room: currentCoursse.value.date + ' ' + currentCoursse.value.time,
			seat: '',
			num: '',
			student_name: '',
			grade: ''
		},
		{ type: '考试科目', room: currentCoursse.value.course_name, seat: '', num: '', student_name: '', grade: '' }
	]
	let merges = ['2-2:6-2', '2-3:6-3', '2-4:6-4']
	if (!currentId.value) {
		courseData = []
		merges = ['2-2:6-2']
	}

	// 考场表格数据
	let roomSheet = []
	groupedByRoom.forEach((item) => {
		// 对studentList对半分为leftList和rightList
		const half = Math.ceil(item.studentList.length / 2)
		const leftList = item.studentList.slice(0, half)
		const rightList = item.studentList.slice(half)
		const maxLen = Math.max(leftList.length, rightList.length)
		roomSheet.push({
			title: query.name,
			globalStyle: { font: { sz: 14 } },
			keys: ['seat', 'num', 'student_name', 'empty', 'seat2', 'num2', 'student_name2'],
			table: [
				{ seat: item.room, num: '', student_name: '', empty: '', seat2: '', num2: '', student_name2: '' },
				{
					seat: '座位号',
					num: '学号/考号',
					student_name: '姓名',
					empty: '',
					seat2: '座位号',
					num2: '学号/考号',
					student_name2: '姓名'
				},
				...Array.from({ length: maxLen }).map((_, idx) => ({
					seat: leftList[idx] ? leftList[idx].seat : '',
					num: leftList[idx] ? leftList[idx].num : '',
					student_name: leftList[idx] ? leftList[idx].student_name : '',
					empty: '',
					seat2: rightList[idx] ? rightList[idx].seat : '',
					num2: rightList[idx] ? rightList[idx].num : '',
					student_name2: rightList[idx] ? rightList[idx].student_name : ''
				}))
			],
			merges: ['1-2:7-2'],
			colWidth: [10, 15, 15, 15, 10, 15, 15],
			cellStyle: [{ cell: 'A1', font: { bold: true, sz: 18 } }],
			sheetName: item.room
		})
	})
	await excelExport({
		sheet: [
			{
				// 表格标题
				title: '考场座位表',
				globalStyle: {
					font: {
						sz: 14
					}
				},
				keys: ['type', 'room', 'seat', 'num', 'student_name', 'grade'],
				table: [
					...courseData,
					{
						type: '考场信息',
						room: `${studentNum.length}个考生，${groupedByRoom.length}个考场`,
						seat: '',
						num: '',
						student_name: '',
						grade: ''
					},
					{ type: '序号', room: '考场', seat: '座位号', num: '考号', student_name: '姓名', grade: '原班级' },
					...data
				],
				colWidth: [15, 30, 10, 15, 15, 30],
				merges: merges,
				cellStyle: [{ cell: 'A1', font: { bold: true, sz: 18 } }],
				sheetName: '考场座位表'
			},
			...roomSheet
		],
		// 文件名称
		filename: query.name + '-考场座位表'
	})
}

// 获取考场座位表
const seatList = ref([])
const getSeats = () => {
	globalPropValue.examine.roomStudentList.get(params.value).then((res) => {
		if (res.code === 200) {
			seatList.value = res.data || []
			console.log(res.data)
		}
	})
}
// 获取时段
const dateList = ref([])
const getTime = () => {
	globalPropValue.examine.timeList.get(params.value).then((res) => {
		if (res.code === 200) {
			const groupedData = {}
			res.data.forEach((item) => {
				if (!groupedData[item.examine_date]) {
					groupedData[item.examine_date] = {
						date: item.examine_date,
						course: []
					}
				}
				groupedData[item.examine_date].course.push({
					id: item.course_id,
					course_name: item.course_name,
					time: item.begin_time + '-' + item.end_time
				})
			})
			dateList.value = Object.values(groupedData)
			currentId.value = dateList.value[0].course[0].id
			currentCoursse.value = {
				date: dateList.value[0].date,
				course_name: dateList.value[0].course[0].course_name,
				time: dateList.value[0].course[0].time
			}
			if (query.seat_type == 2) {
				params.value.course_id = currentId.value
			}
			getSeats()
			console.log(dateList.value)
		}
	})
}

onMounted(() => {
	// getSeats()
	getTime()
})
const currentPage = ref(1)
const pageSize = ref(10)

const paginatedSeatList = computed(() => {
	const start = (currentPage.value - 1) * pageSize.value
	const end = start + pageSize.value
	return seatList.value.slice(start, end)
})

const handleCurrentChange = (val) => {
	currentPage.value = val
}

const handleSizeChange = (val) => {
	pageSize.value = val
	currentPage.value = 1
}
</script>

<style lang="scss" scoped>
.date-box {
	display: flex;
	border: 1px solid var(--el-border-color-light);
	margin-bottom: 20px;

	.date-item {
		border-right: 1px solid var(--el-border-color-light);

		&:nth-child(even) {
			.date-item-header {
				background-color: var(--el-fill-color-light);
			}
		}

		&:nth-child(odd) {
			.date-item-header {
				background-color: var(--el-fill-color-lighter);
			}
		}

		&:last-child {
			border-right: none;
		}

		.date-item-header {
			height: 50px;
			line-height: 50px;
			text-align: center;
			font-size: 15px;
			border-bottom: 1px solid var(--el-border-color-light);
		}

		.date-item-course {
			display: flex;
			justify-content: space-around;
			padding: 0 10px;
			min-width: 120px;

			.course-item {
				padding: 0 10px;
				height: 50px;
				margin: 10px;
				font-size: 16px;
				border-radius: 5px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				background: var(--el-fill-color-light);
				cursor: pointer;

				p {
					&:last-child {
						font-size: 14px;
					}
				}
			}

			.active {
				background: var(--el-color-primary-light-8);
				color: var(--el-color-primary);
			}
		}
	}
}

.add-btn {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
}

.pagination {
	margin-top: 20px;
	display: flex;
	justify-content: flex-end;
}
</style>
