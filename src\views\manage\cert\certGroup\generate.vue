<template>
	<el-dialog
		v-model="visible"
		class="preview"
		title="证书生成"
		width="500"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		append-to-body
		:show-close="false"
		destroy-on-close
		top="35vh"
	>
		<div style="padding: 15px">
			<div v-if="completed < total && total !== 0">
				<el-progress
					:percentage="total > 0 ? ((completed / total) * 100).toFixed(2) : 0"
					color="#00b42a"
					:stroke-width="20"
					striped
					striped-flow
					:duration="10"
				>
					<template #default="{ percentage }">
						<span style="font-size: 24px; padding-left: 10px">
							<span style="color: #00b42a">{{ completed }}</span> / {{ total }}
						</span>
					</template>
				</el-progress>
				<div style="padding: 20px 0; font-size: 14px">证书生成中，生成速度可能受设备、网络影响，请耐心等候。</div>
			</div>
			<div v-if="completed === total && total !== 0">
				<el-result icon="success" title="" :sub-title="'证书生成完成，生成成功：' + success + '，失败：' + error">
					<template #extra>
						<el-button type="primary" @click="closeVisible">关闭</el-button>
					</template>
				</el-result>
			</div>

			<div v-if="previewStatus" :key="generateKey" style="position: absolute; left: -99999999px">
				<div
					class="certCanvas"
					:style="{
						position: 'relative',
						zIndex: 99999,
						width: certTemplateConfig.canvasConfig.width + 'px',
						height: certTemplateConfig.canvasConfig.height + 'px'
					}"
				>
					<template v-for="(item, index) in certTemplateConfig.list" :key="index">
						<div
							:style="{
								position: 'absolute',
								top: item.y + 'px',
								left: item.x + 'px',
								width: item.w + 'px',
								height: item.h + 'px'
							}"
						>
							<div v-if="item.type === 'image' && item.field !== 'qrcode'" class="drag-img">
								<img :src="item.content" alt="" />
							</div>
							<div v-else-if="item.type === 'image' && item.field === 'qrcode'" class="qrcode">
								<img :src="item.content" alt="" />
							</div>
							<div v-else class="drag-item">
								<div v-html="item.content"></div>
							</div>
						</div>
					</template>
				</div>
			</div>
		</div>
	</el-dialog>
	<!--	-->
</template>

<script>
import { cloneDeep } from 'lodash'
import tool from '@/utils/tool'
import html2canvas from 'html2canvas'
import { domToPng, domToBlob } from 'modern-screenshot'

import { putObjectBig } from '@/utils/ossLib' //上传文件到oss
//import centerComponent from '@/views/manage/cert/certTemplateConfig/components/center.vue'
import QRcode from '@/components/scQrCode/qrcode'

export default {
	name: 'generate',
	//components: { centerComponent },
	data() {
		return {
			visible: false,
			previewDialog: false,
			previewStatus: false,
			total: 0,
			percentage: 0,
			completed: 0,
			success: 0,
			error: 0,
			generateKey: 0,
			certTemplateConfig: {},
			certTemplate: {},
			generateList: [],
			originalList: {},
			time: null,
			qrcode: null,
			form: {
				academic_name: '',
				semester_name: '',
				teacher_name: '',
				grade_name: '',
				class_name: '',
				student_name: '',
				department_name: '',
				course_name: '',
				award_name: '',
				award_ranking: '',
				award_desc: '',
				award_date: '',
				cert_number: '',
				signed: ''
			}
		}
	},
	created() {
	},
	watch: {},
	methods: {
		closeVisible() {
			this.visible = false
			this.previewStatus = false
			this.$emit('closed')
		},
		open(certTemplateConfig, certTemplate, list = []) {
			if (!list || list.length === 0 || !certTemplateConfig || !certTemplate) {
				return false
			}
			this.total = list.length
			this.generateList = list
			this.certTemplateConfig = cloneDeep(certTemplateConfig)
			this.certTemplate = cloneDeep(certTemplate)
			this.form.signed = certTemplate.seal?.signed
			this.certTemplateConfig.list.forEach((item) => {
				item.active = false
				return item
			})
			this.originalList = cloneDeep(this.certTemplateConfig.list)
			this.$nextTick(() => {
				this.visible = true
			})
			this.completed = 0
			this.generate_cert(this.completed)
		},
		async generate_cert(index) {
			let row = this.generateList[index]
			if (index >= this.total) {
				return false
			}
			this.previewStatus = false
			this.form = row
			switch (row.object) {
				case 1:
					row.student_name = row.objecter?.name
					break
				case 2:
					row.teacher_name = row.objecter?.name
					break
				case 3:
					row.department_name = row.objecter?.name
					break
				case 4:
					row.grade_name = row.objecter?.name
					break
				case 5:
					row.class_name = row.objecter?.name
					break
				case 6:
					row.course_name = row.objecter?.name
			}
			let list = cloneDeep(this.originalList)
			row.signed = this.certTemplate.seal?.signed
			await this.createQrCode('{"type":"cert","content":{"cert_number":"' + row.cert_number + '"}}')
			this.certTemplateConfig.list.map((item) => {
				if (item.field === 'seal_img') {
					item.content = this.certTemplate.seal?.seal_img
				}
				return item
			})
			list.forEach((item) => {
				item.content = this.compiler(item.content, this.form)
				if (item.field === 'qrcode') {
					item.content = this.qrcode
				}
			})
			this.certTemplateConfig.list = list
			this.generateKey = index
			this.previewStatus = true
			this.$nextTick(() => {
				this.html2img()
			})
			/*this.time = setTimeout(async () => {
				await this.html2img()
			}, 100)*/
		},
		/*generateAction() {
			console.log('渲染完成', this.generateKey)
			this.time = setTimeout(async () => {
				await this.html2img()
			}, 300)
		},*/
		compiler(template, data) {
			let reg = /\{\{(.+?)\}\}/g
			template = template.replace(reg, function(_, g) {
				let key = g.trim()
				let value = data[key]
				return value
			})
			return template
		},
		//创建原始二维码DOM
		async createQrCode(text) {
			return new Promise((resolve) => {
				var element = document.createElement('div')
				new QRcode(element, {
					text: text,
					width: 150,
					height: 150,
					colorDark: '#000000',
					colorLight: '#ffffff',
					correctLevel: 2
				})
				if (element.getElementsByTagName('canvas')[0]) {
					this.qrcode = element.getElementsByTagName('canvas')[0].toDataURL('image/png')
					resolve()
				}
			})
		},
		async html2img() {
			let _that = this
			let dom = document.querySelector('.certCanvas')
			var scale = window.devicePixelRatio
			console.log(this.certTemplateConfig.canvasConfig)
			await domToBlob(dom, {
				type: 'image/png',
				width: this.certTemplateConfig.canvasConfig.width * scale,
				height: this.certTemplateConfig.canvasConfig.height * scale,
				quality: 1,
				scale: 2
			}).then((dataUrl) => {
				console.log(dataUrl)
				/*var blob = _that.base64ToBlob(dataUrl.split(',')[1], 'image/png')*/
				_that.upImg(dataUrl)
			})
			/*await html2canvas(dom, {
				backgroundColor: '#ffffff',
				allowTaint: true, //开启跨域
				useCORS: true,
				height: this.certTemplateConfig.canvasConfig.height,
				width: this.certTemplateConfig.canvasConfig.width,
				scrollY: 0,
				scrollX: 0
			}).then(function (canvas) {
				const img = canvas.toDataURL('image/png')
				console.log(_that.generateKey)
				console.log(img)
				var blob = _that.base64ToBlob(img.split(',')[1], 'image/png')
				_that.upImg(blob)
			})*/
		},
		base64ToBlob(base64, mimeType) {
			const byteCharacters = atob(base64)
			const byteArrays = []
			for (let offset = 0; offset < byteCharacters.length; offset += 512) {
				const slice = byteCharacters.slice(offset, offset + 512)
				const byteNumbers = new Array(slice.length)
				for (let i = 0; i < slice.length; i++) {
					byteNumbers[i] = slice.charCodeAt(i)
				}
				const byteArray = new Uint8Array(byteNumbers)
				byteArrays.push(byteArray)
			}
			return new Blob(byteArrays, { type: mimeType })
		},
		async upImg(img) {
			const data = await putObjectBig(img, '.png', 'cert')
			if (data) {
				this.submitCert(data.url)
			}
		},
		async submitCert(img) {
			const { code } = await this.$API.cert.cert.generate.post({
				cert_id: this.form.id,
				cert_file_url: img,
				campus_id: this.form.campus_id,
				tenant_id: this.form.tenant_id
			})
			if (code === 200) {
				this.success++
			} else {
				this.error++
			}
			this.completed++
			this.generate_cert(this.completed)
		}
	}
}
</script>

<style scoped lang="scss">
.el-result {
	padding: 0;
}

.drag-item {
	font-size: 14px;
}

.qrcode {
	padding: 5px;
	background: #fff;
	width: 100%;
	height: 100%;

	img {
		width: 100%;
		height: 100%;
	}
}

.drag-img {
	background-repeat: no-repeat;
	background-size: 100% 100%;
	width: 100%;
	height: 100%;

	img {
		width: 100%;
		height: 100%;
	}
}
</style>
