<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script>
import certGroup from './certGroup'
import certAudit from './certAudit'
import certRecord from './certRecord'
import certTemplate from './certTemplate'
import { useRoute } from 'vue-router'
import { shallowRef } from 'vue'
export default {
	name: 'Cert',
	components: {
		certGroup,
		certAudit,
		certRecord,
		certTemplate
	},
	data() {
		return {
			activeName: 'certGroup',
			tabs: [
				{
					label: '证书组',
					name: 'certGroup',
					component: shallowRef(certGroup)
				},
				{
					label: '证书审核',
					name: 'certAudit',
					component: shallowRef(certAudit)
				},
				{
					label: '证书列表',
					name: 'certRecord',
					component: shallowRef(certRecord)
				},
				{
					label: '证书模板',
					name: 'certTemplate',
					component: shallowRef(certTemplate)
				}
			],
			currComponent: {}
		}
	},
	created() {
		const route = useRoute()
		const query = route.query
		if (query && query.type) {
			this.activeName = query.type
		}
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
		}
	}
}
</script>

<style></style>
