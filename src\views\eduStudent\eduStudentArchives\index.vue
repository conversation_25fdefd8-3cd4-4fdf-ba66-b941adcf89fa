<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search" style="padding-right: 0px">
					<el-form-item v-if="CampusManagementList.length > 1" label="">
						<el-select v-model="params.campus_id" placeholder="请选择校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input
							v-model="params.name"
							placeholder="学员姓名/手机号码/IC卡号"
							clearable
							style="width: 180px"
						></el-input>
					</el-form-item>
					<el-form-item label="" prop="entrance_year">
						<el-date-picker
							v-model="params.entrance_year"
							placeholder="入学年份"
							type="year"
							style="width: 120px"
							format="YYYY"
							value-format="YYYY"
						/>
					</el-form-item>
					<el-form-item label="">
						<cusSelectSemester
							v-model="params.semester_id"
							:params="params"
							:show-default-value="false"
							:width="'214px'"
							clearable
							style="margin-right: 15px"
							@semesterChange="semesterChange"
						/>
					</el-form-item>
					<el-form-item>
						<el-cascader
							v-model="params.class_id"
							:options="classList"
							style="width: 150px"
							placeholder="请选择班级"
							:props="{ multiple: false, label: 'name', value: 'id', children: 'class_list', emitPath: false }"
							clearable
						></el-cascader>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.bind_face" placeholder="绑定人脸" clearable>
							<el-option :value="1" label="是"></el-option>
							<el-option :value="-1" label="否"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.bind_card" placeholder="是否绑卡" clearable>
							<el-option :value="1" label="是"></el-option>
							<el-option :value="-1" label="否"></el-option>
						</el-select>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
		</el-header>
		<el-main>

			<scTable
				ref="table"
				row-key="id"
				:params="params"
				:customColumnShow="true"
				:apiObj="apiObj"
				:show-action="true"
				:hide-do="true"
				@selection-change="selectionChange"
			>
			<template #action>
					<div style="text-align: right;">
						<el-button type="success" icon="el-icon-plus" @click="add">新增学员</el-button>
						<el-button icon="el-icon-sort" @click="importExport">导入导出数据</el-button>
						<el-button icon="el-icon-switch" @click="sync">同步学生数据</el-button>
					</div>
			</template>
				<el-table-column type="selection" width="50" fixed="left" />
				<el-table-column label="姓名" prop="student_name" width="100" fixed="left"></el-table-column>
				<el-table-column label="头像" prop="user_head" width="100" align="center">
					<template #default="scope">
						<cusHead
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.user_head"
							:preview-src-list="[scope.row.user_head]"
							preview-teleported
						>
						</cusHead>
					</template>
				</el-table-column>
				<el-table-column label="人脸识别" prop="face_img" width="75" align="center">
					<template #default="scope">
						<el-icon
							v-if="scope.row.face_img === ''"
							style="font-size: 28px; cursor: pointer; color: var(--el-color-info)"
							@click="setFace(scope.row)"
							><sc-icon-face
						/></el-icon>
						<el-icon
							v-if="scope.row.face_img !== ''"
							style="font-size: 28px; cursor: pointer; color: var(--el-color-success)"
							@click="setFace(scope.row)"
							><sc-icon-face
						/></el-icon>
					</template>
				</el-table-column>
				<el-table-column label="IC卡" prop="face_img" width="75" align="center">
					<template #default="scope">
						<el-icon
							v-if="scope.row.moredian_card_no === ''"
							style="font-size: 24px; cursor: pointer; color: var(--el-color-info)"
							@click="setCard(scope.row)"
						>
							<el-icon-Postcard />
						</el-icon>
						<el-icon
							v-if="scope.row.moredian_card_no !== ''"
							style="font-size: 24px; cursor: pointer; color: var(--el-color-success)"
							@click="setCard(scope.row)"
						>
							<el-icon-Postcard />
						</el-icon>
					</template>
				</el-table-column>
				<el-table-column label="性别" prop="sex" width="70">
					<template #default="scope">
						{{ formData(sexMap, scope.row.sex) }}
					</template>
				</el-table-column>
				<el-table-column label="身份证号" prop="idcard" width="170"></el-table-column>
				<el-table-column label="学号" prop="serial_number" width="120"></el-table-column>
				<el-table-column label="手机号" prop="phone" width="120"></el-table-column>
				<el-table-column label="入学学年" prop="entrance_year" width="100"></el-table-column>
				<el-table-column label="学年学期" prop="academic_name" width="170">
					<template #default="scope"> {{ scope.row.academic_name }} - {{ scope.row.semester_name }} </template>
				</el-table-column>
				<el-table-column label="年级班级" prop="grade_name" width="170">
					<template #default="scope"> {{ scope.row.grade_name }} - {{ scope.row.class_name }} </template>
				</el-table-column>
				<el-table-column label="来源" prop="source" width="120">
					<template #default="scope">
						{{ formData(studentSourceMap, scope.row.source) }}
					</template>
				</el-table-column>
				<el-table-column label="状态" prop="source" width="120">
					<template #default="scope">
						{{ formData(studentStatusMap, scope.row.status) }}
						<el-tag v-if="scope.row.status === 5" type="info">
							{{ formData(leaveReasonMap, scope.row.leave_reason) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="邮箱" prop="email" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="政治面貌" prop="political" width="120">
					<template #default="scope">
						{{ formData(politicalMap, scope.row.political) }}
					</template>
				</el-table-column>
				<el-table-column label="民族" prop="nation" width="100">
					<template #default="scope">
						{{ formData(nationMap, scope.row.nation) }}
					</template>
				</el-table-column>
				<el-table-column label="地址" prop="address" width="150" show-overflow-tooltip></el-table-column>

				<el-table-column label="出生日期" prop="birthdate" width="120"></el-table-column>

				<el-table-column label="创建时间" prop="created_at" width="180"></el-table-column>
				<el-table-column label="备注" prop="remark" min-width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="210">
					<template #default="scope">
						<el-button-group>
							<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm
								v-if="scope.row.status === 1"
								title="确定删除吗？"
								@confirm="table_del(scope.row, scope.$index)"
							>
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
							<el-button
								v-if="scope.row.status !== 5"
								text
								type="primary"
								size="small"
								@click="leave(scope.row, scope.$index)"
								>离校</el-button
							>
						</el-button-group>
					</template>
				</el-table-column>
				<template v-slot:selectAction>
					<el-popconfirm title="确定批量将选择的学生离校操作吗？" @confirm="tableBatchLeave">
						<template #reference>
							<el-button size="small" :disabled="delStatus !== true" type="warning">批量离校</el-button>
						</template>
					</el-popconfirm>
				</template>
			</scTable>
		</el-main>
	</el-container>

	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		:disciplineOptions="disciplineOptions"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
	<leaveDialog
		v-if="dialog.leave"
		ref="leaveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.leave = false"
	></leaveDialog>
	<face-dialog
		v-if="dialog.face"
		ref="faceDialog"
		:params="params"
		:disciplineOptions="disciplineOptions"
		@success="handleSaveSuccess"
		@closed="dialog.face = false"
	></face-dialog>
	<import-export-dialog
		v-if="dialog.importExport"
		ref="importExportDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.importExport = false"
	></import-export-dialog>
	<card-dialog
		v-if="dialog.card"
		ref="cardDialog"
		@success="handleSaveSuccess"
		@closed="dialog.card = false"
	></card-dialog>
</template>

<script>
import cusHead from '@/components/custom/cusStudentHead.vue'
import saveDialog from './save'
import importExportDialog from './importExport'
import faceDialog from './face'
import leaveDialog from './leave'
import cusTom from '@/utils/cusTom'
import cardDialog from './card.vue'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'
import { ElMessageBox } from 'element-plus'

const {
	campusId,
	tenantId,
	campusInfo,
	tenantInfo,
	sexMap,
	politicalMap,
	identityMap,
	workStatusMap,
	staffTypeMap,
	academicTypeMap,
	professionalTypeMap,
	compileTypeMap,
	nationMap,
	semesterInfo,
	leaveReasonMap,
	studentSourceMap,
	studentStatusMap
} = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: null,
		academic_id: null,
		grade_id: null,
		class_id: null,
		name: null,
		bind_face: null,
		bind_card: null,
		entrance_year: null
	}
}
export default {
	name: 'dept',
	components: {
		cusSelectSemester,
		cardDialog,
		saveDialog,
		leaveDialog,
		cusHead,
		importExportDialog,
		faceDialog
	},
	data() {
		return {
			dialog: {
				save: false,
				leave: false,
				importExport: false,
				face: false,
				card: false
			},
			classList: [],
			apiObj: this.$API.eduStudent.list,
			selection: [],
			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			delStatus: false,
			disciplineOptions: [],
			sexMap,
			politicalMap,
			identityMap,
			workStatusMap,
			leaveReasonMap,
			staffTypeMap,
			academicTypeMap,
			professionalTypeMap,
			compileTypeMap,
			nationMap,
			semesterInfo,
			studentSourceMap,
			studentStatusMap,
			grade: [],
			class: []
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.semester_id = null
				this.getClassData()
			},
			immediate: true
		},
		'params.semester_id': {
			handler(val) {
				console.log(val)
				this.params.class_id = null
				this.getClassData()
			}
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.params.campus_id),
				'value',
				'parent_id'
			)
		},
		getClass() {
			return this.class.filter(
				(v) => v.parent_id != 0 && v.grade_id == this.params.grade_id && v.campus_id == this.params.campus_id
			)
		}
	},
	async created() {},
	methods: {
		setCard(row) {
			this.dialog.card = true
			this.$nextTick(() => {
				this.$refs.cardDialog.open('edit').setData(row)
			})
		},
		setFace(row) {
			this.dialog.face = true
			this.$nextTick(() => {
				this.$refs.faceDialog.open('edit').setData(row)
			})
		},
		async getClassData() {
			let res = await this.$API.eduGradeClass.class.all.get(this.params)
			this.classList = res.data
		},
		async getGradeData() {
			var res = await this.$API.eduGradeClass.grade.all.get(this.params)
			this.grade = res.data
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || '-'
		},
		//添加
		add(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', row)
			})
			// this.upsearch()
		},
		sync() {
			ElMessageBox.confirm('确认将所有学生信息数据同步到智能设备，用于刷卡刷脸签到、开门等功能', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
			var reqData = { tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			this.$API.CampusManagement.syncStudent.post(reqData).then((res) => {
					if (res.code === 200) {
						this.$message.success('提交同步任务成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
			})
		},
		importExport() {
			this.dialog.importExport = true
			this.$nextTick(() => {
				this.$refs.importExportDialog.open('export')
			})
			// this.upsearch()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//离校
		leave(row) {
			let rowData = {
				id: [row.id],
				student_name: row.student_name,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			this.dialog.leave = true
			this.$nextTick(() => {
				this.$refs.leaveDialog.open('edit').setData(rowData)
			})
		},
		tableBatchLeave() {
			if (this.selection.length <= 0) {
				return
			}
			let row = {
				id: this.selection.map((v) => v.id),
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			this.dialog.leave = true
			this.$nextTick(() => {
				this.$refs.leaveDialog.open('edit').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.eduStudent.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				// this.upsearch()
				this.$refs.table.refresh()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},

		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status
			}
			this.$API.eduStudent.status.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
			if (selection.length > 0) {
				this.delStatus = true
			} else {
				this.delStatus = false
			}
		},
		//搜索
		upsearch() {
			if (this.params.class_id != null && this.params.class_id.length > 1) {
				this.params.class_id = this.params.class_id[this.params.class_id.length - 1]
			}
			this.$refs.table.upData(this.params)
		},
		semesterChange(val) {
			this.params.semester_id = val
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.dialog.save = false
			this.dialog.card = false
			this.dialog.face = false
			this.dialog.importExport = false
			if (mode === 'add') {
				this.upsearch()
			} else {
				this.$refs.table.refresh()
			}
		}
	}
}
</script>

<style lang="scss" scoped></style>
