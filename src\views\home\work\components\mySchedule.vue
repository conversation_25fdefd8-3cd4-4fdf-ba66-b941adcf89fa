<template>
	<div v-if="scheduleList && scheduleList.periods_item && scheduleList.class_schedule_list" class="schedule-content">
		<ul class="table th">
			<li class="row">
				<div class="row-data">
					<span class="cell">时间 / 日期</span>
					<span v-for="item in scheduleList.attend_day" :key="item" class="cell">{{ item }}</span>
				</div>
			</li>
		</ul>
		<ul class="table td">
			<li v-for="item in scheduleList.periods_item" class="row">
				<div v-if="item.value" class="row-data">
					<span class="cell">{{ item.item_name }}<br />{{ item.begin_time + '~' + item.end_time }}</span>
					<span v-for="items in item.value" :key="items" class="cell">
						<div v-if="items" class="cell-info" :class="items ? 'bg' : ''">
							<p>{{ items.grade_name?.name + items.class_name?.name || '' }}</p>
							<p>{{ items.course_name?.name }}</p>
							<p>{{ items.room?.name }}</p>
						</div>
					</span>
				</div>
			</li>
		</ul>
	</div>
	<div v-else class="schedule-content">
		<el-empty :image-size="150" description="暂无课表数据"></el-empty>
	</div>
</template>
<script>
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId } = cusTom.getBaseQuery()
export default {
	name: 'schedule',
	data() {
		return {
			scheduleList: {}
		}
	},
	created() {
		this.getMySchedule()
	},
	methods: {
		getMySchedule() {
			console.log('获取我的课表', this)
			this.$API.eduSchedule.schedule.myschedule
				.get({
					tenant_id: tenantId,
					campus_id: campusId
				})
				.then((res) => {
					if (res.code == 200) {
						if (!res.data) {
							return
						}
						this.scheduleList = res.data
						this.scheduleList.periods_item = this.upData(res.data.periods_item)
					} else {
						ElMessage.error(res.msg)
					}
					console.log(this.scheduleList)
				})
		},
		upData(arry) {
			return arry.map((period) => {
				const item = {
					item_name: period.item_name,
					begin_time: period.begin_time,
					end_time: period.end_time,
					value: []
				}
				this.scheduleList.attend_day.forEach((day) => {
					const scheduleItem = this.scheduleList.class_schedule_list.find(
						(item) => item.periods_item_id === period.periods_item_id && item.attend_day === day
					)
					item.value.push(scheduleItem ? scheduleItem : '')
				})
				return item
			})
		}
	}
}
</script>
<style lang="scss" scoped>
.bg {
	background-color: var(--el-color-info-light-9);
}

.schedule-content {
	min-height: 350px;
	.table {
		width: 100%;
		display: flex;

		.row {
			// border-left: 1px solid var(--el-border-color-light);
			// border-right: 1px solid var(--el-border-color-light);
			border-bottom: 1px solid var(--el-border-color-light);

			.row-data {
				display: flex;

				.bg1.selected {
					color: var(--el-color-primary);
					background-color: var(--el-color-primary-light-8);
				}

				.bg3.selected {
					color: var(--el-color-success);
					background-color: var(--el-color-success-light-8);
				}

				.bg4.selected {
					color: var(--el-color-warning);
					background-color: var(--el-color-warning-light-8);
				}

				.bg5.selected {
					color: var(--el-color-info);
					background-color: var(--el-color-info-light-8);
				}

				.bg6.selected {
					color: var(--el-color-danger);
					background-color: var(--el-color-danger-light-8);
				}
			}

			.cell {
				flex: 1;
				min-width: 100px;
				cursor: pointer;
				// border-right: 1px solid var(--el-border-color-light);

				&:first-child {
					flex: 0;
					min-width: 90px;
				}

				&:last-child {
					border-right: none;
				}
			}
		}
	}

	.th {
		.row {
			width: 100%;
			border-top: 1px solid var(--el-border-color-light);
			background-color: var(--el-border-color-extra-light);
		}

		.row-data {
			width: 100%;
			height: 50px;

			.cell {
				font-weight: bold;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}

	.td {
		flex-direction: column;

		.row {
			.row-data {
				min-height: 90px;

				.cell {
					p {
						margin-bottom: 5px;
						font-size: 12px;

						&:first-child {
							font-size: 14px;
							font-weight: bold;
						}
					}

					&:not(:first-child) {
						padding: 5px;
					}

					&:first-child {
						font-weight: bold;
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
						background-color: var(--el-border-color-extra-light);
					}

					.cell-info {
						height: 100%;
						padding: 10px;
					}
				}
			}
		}
	}
}
</style>
