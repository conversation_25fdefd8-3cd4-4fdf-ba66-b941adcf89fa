<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script setup>
import consumable from './consumable'
import record from './record'
import approve from './approve'
import { ref } from 'vue'
const activeName = ref('consumable')
const currComponent = ref({
	name: 'consumable',
	component: consumable
})
const tabs = [
	{
		name: 'consumable',
		label: '耗材清单',
		component: consumable
	},
	{
		name: 'record',
		label: '出入库记录',
		component: record
	},
	{
		name: 'approve',
		label: '领用审批',
		component: approve
	}
]
const handleClick = (name) => {
	currComponent.value = tabs.find((item) => item.name === name)
}
</script>
<style lang="scss" scoped></style>
