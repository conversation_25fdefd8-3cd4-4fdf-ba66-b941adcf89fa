<template>
	<el-dialog v-model="visible" width="500" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-width="90">

			<el-form-item label="模板名称" prop="name">
				<el-input v-model="form.name" placeholder="请输入名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="证书类型" prop="type">
				<el-select v-model="form.type" placeholder="请选择证书类型" clearable>
					<el-option v-for="item in certTypeMap" :key="item.value" :label="item.name" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="证书对象" prop="object">
				<el-select v-model="form.object" placeholder="请选择证书对象" clearable>
					<el-option v-for="item in certObjectMap" :key="item.value" :label="item.name" :value="item.value" />
				</el-select>
			</el-form-item>

			<el-form-item label="使用公章" prop="seal_id">
				<el-select v-model="form.seal_id" placeholder="请选择电子公章" clearable>
					<el-option v-for="item in sealMap" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="状态" prop="status">
				<el-switch
					v-model="form.status"
					inline-prompt
					style="--el-switch-on-color: #00b42a"
					:active-value="1"
					:inactive-value="-1"
				/>
			</el-form-item>
			<el-form-item label="复制配置" prop="copy">
				<el-radio-group v-model="form.copy">
					<el-radio :value="0">否</el-radio>
					<el-radio :value="1">是</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.copy === 1" label="选择模板" prop="copy_from">
				<el-select v-model="form.copy_from" placeholder="请选择已有模板" clearable>
					<el-option v-for="item in certMap" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { certTypeMap, certObjectMap } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		name: null,
		type: null,
		object: null,
		seal_id: null,
		status: 1,
		copy: 0,
		copy_from: null,
		id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			certTypeMap,
			certObjectMap,
			mode: 'add',
			sealMap: [],
			certMap: [],
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				name: [{ required: true, message: '请输入模板名称' }],
				type: [{ required: true, message: '请选择证书类型' }],
				object: [{ required: true, message: '请选择证书对象' }],
				seal_id: [{ required: true, message: '请选择使用公章' }]
			},

			course: [],
			discipline: [],
			treeData: [],
			position: []
		}
	},
	computed: {},
	mounted() {},
	created() {
		this.getSeal()
		this.getCert()
	},
	methods: {
		async getSeal() {
			const { data } = await this.$API.seal.all.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			})
			this.sealMap = data
		},
		async getCert() {
			const { data } = await this.$API.cert.certTemplate.all.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			})
			this.certMap = data
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.cert.certTemplate.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
		}
	}
}
</script>
<style scoped lang="scss">
.el-select{
	width: 100%;
	max-width: unset;
}
</style>
