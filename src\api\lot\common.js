import config from '@/config'
import http from '@/utils/request'

export default {
	enumList: {
		url: `${config.API_URL}/lot/common/enumList`,
		name: '获取枚举数据',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	unitList: {
		url: `${config.API_URL}/lot/common/unitList`,
		name: '获取单位数据',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	statistics: {
		url: `${config.API_URL}/lot/common/statistics`,
		name: '获取统计数据',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	}
}
