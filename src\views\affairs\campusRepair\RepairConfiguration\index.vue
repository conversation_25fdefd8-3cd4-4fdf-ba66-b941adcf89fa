<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-plus" @click="addType">新增</el-button>
			</div>
		</el-header>
		<el-main>
			<el-table :data="list" row-key="id" :tree-props="{ children: 'child' }" default-expand-all>
				<el-table-column label="类别名称" prop="name" align="left" > </el-table-column>
				<el-table-column label="类型" prop="type" width="150">
					<template #default="scope">
						{{ $formatDictionary(repairTypeMap, scope.row.type) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="170">
					<template #default="scope">
						<el-button-group>
							<!--
							<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>
-->
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</el-table>
		</el-main>
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, repairTypeMap } = cusTom.getBaseQuery()
import saveDialog from './save.vue'
const defaultData = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		type: 1
	}
}

export default {
	name: '',
	components: { saveDialog },
	data() {
		return {
			params: defaultData(),
			apiObj: this.$API.campusRepair.configAll,
			dialog: {
				save: false
			},
			repairTypeMap,
			list: [],
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'type',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择报修类别',
							noClearable: false,
							items: repairTypeMap.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					}
				]
			}
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.campus_id = val
				this.getList()
			}
		},
		'params.type': {
			handler(val) {
				this.params.type = val
				this.getList()
			}
		}
	},
	created() {
		this.getList()
	},
	methods: {
		async getList() {
			var res = await this.$API.campusRepair.configAll.get(this.params)
			if (res.code === 200) {
				this.list = res.data
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		addType() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},
		upsearch() {
			this.getList()
			//this.$refs.table.upData(this.params)
		},
		refresh() {
			this.params = defaultData()
			this.upsearch()
		},
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.campusRepair.configDel.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		handleSaveSuccess() {
			this.upsearch()
		}
	}
}
</script>

<style scoped></style>
