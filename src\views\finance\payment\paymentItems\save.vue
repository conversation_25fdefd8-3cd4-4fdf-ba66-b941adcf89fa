<template>
    <el-dialog v-if="dialogFormVisible" v-model="dialogFormVisible" :title="titleMap[mode1]" width="500">
        <cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode1">
        </cusForm>
        <template #footer>
            <el-button @click="dialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="confirm">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance, nextTick, onMounted } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, tenantInfo, campusInfo, semesterInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params'])
const emit = defineEmits(['success'])
let mode1 = ref('add')
const dialogFormVisible = ref(false)
const defaultData = () => {
    return {
        id: null,
        tenant_id: tenantId,
        campus_id: campusId,
        item_name: '',
        item_type: '',
        num: null,
        amount: null,
        remark: '',
        semester_id: -1,
        academic_id: '',
        mode: null
    }
}
let form = ref(defaultData())
let formConfig = ref({
    labelWidth: '150px',
    labelPosition: 'right',
    size: 'medium',
    formItems: [
        {
            label: '缴费项名称',
            name: 'item_name',
            value: null,
            component: 'input',
            options: {
                placeholder: '请输入缴费项名称',
                items: []
            },
            rules: [{ required: true, message: '请输入缴费项名称', trigger: 'blur' }]
        },
        {
            label: '缴费项类型',
            name: 'item_type',
            value: null,
            component: 'select',
            options: {
                placeholder: '请选择缴费项类型',
                items: []
            },
            rules: [{ required: true, message: '请选择缴费类型', trigger: 'blur' }]
        },
        {
            label: '数量',
            name: 'num',
            value: null,
            component: 'number',
            options: {
                placeholder: '请输入数量',
                items: []
            },
            rules: [{ required: true, message: '请输入数量', trigger: 'blur' }]
        },
        {
            label: '金额（单位：元）',
            name: 'amount',
            value: null,
            component: 'number',
            options: {
                placeholder: '请输入金额',
                items: []
            },
            rules: [{ required: true, message: '请输入金额', trigger: 'blur' }]
        },
        {
            label: '学年',
            name: 'academic_id',
            value: null,
            component: 'select',
            options: {
                placeholder: '请选择学年',
                items: semesterInfo.map((v) => {
                    return {
                        label: v.name,
                        value: v.value
                    }
                })
            },
            rules: [{ required: true, message: '请选择学年', trigger: 'blur' }]
        },
        {
            label: '学期',
            name: 'semester_id',
            value: null,
            component: 'select',
            options: {
                placeholder: '请选择学期',
                items: []
            },
            rules: [{ required: true, message: '请选择学期', trigger: 'blur' }]
        },
        {
            label: '备注',
            name: 'remark',
            value: null,
            component: 'textarea',
            options: {
                placeholder: '请输入...',
                items: []
            },
        }
    ]
})
const defaultParams = () => {
    return {
        tenant_id: tenantId,
        campus_id: campusId,
    }
}
let params = ref(defaultParams())
let formref = ref(null)
let titleMap = ref({ add: '新增', edit: '编辑' })
onMounted(async () => {
    const { data } = await globalPropValue.finance.recruit.typeList.get(params.value)
    formConfig.value.formItems[1].options.items = data.map((v) => {
        return {
            label: v.item_name,
            value: v.id
        }
    })
})

const open = async (mode = 'add') => {
    dialogFormVisible.value = true
    mode1.value = mode
    form.value.mode = mode
    if (mode === 'add') {
        form.value = defaultData()
        form.value.campus_id = props.params.campus_id
        form.value.tenant_id = props.params.tenant_id
        setTimeout(() => {
            formref.value.resetFields()
        }, 0)
    }
    if (mode === 'edit') {
        nextTick(() => {
            formref.value.resetFields()
        })
    }
}
const confirm = async () => {
    await formref.value.validate()
    let subForm = { ...form.value, ...params.value}
    const handleSuccess = (data) => {
        emit('success', form.value, mode1.value);
        dialogFormVisible.value = false;
        ElMessage({ type: 'success', message: '操作成功' });
    };
    const handleError = (message) => {
        ElMessage({ type: 'error', message });
    };
    if (mode1.value === 'add') {
        subForm = { ...form.value, ...params.value, academic_id: form.value.academic_id }
        const res = await globalPropValue.finance.recruit.add.post(subForm)
        if (res.code === 200) {
            handleSuccess(res)
        } else {
            handleError(res.message);
        }
    } else if (mode1.value === 'edit') {
        const res = await globalPropValue.finance.recruit.edit.post(subForm)
        if (res.code === 200) {
            handleSuccess(res)
        } else {
            handleError(res.message);
        }
    }
}
watch(
    () => form.value.campus_id,
    (val) => {
        console.log(val)
        form.value.academic_id = null
        formConfig.value.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
            .filter((v) => v.parent_id === 0 && v.campus_id === val)
            .map((v) => {
                return {
                    label: v.name,
                    value: v.value
                }
            })
    },
    { immediate: true }
)
watch(
    () => form.value.academic_id,
    (val) => {
        console.log(val)
        form.value.semester_id = null
        formConfig.value.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
            .filter(
                (v) => v.parent_id !== 0 && v.parent_id === form.value.academic_id && v.campus_id === form.value.campus_id
            )
            .map((v) => {
                return {
                    label: v.name,
                    value: v.value
                }
            })
    },
    { immediate: true }
)
//表单注入数据
const setData = (data) => {
    console.log(data,'edit///')
    // form.value = { ...data }
    Object.assign(form.value, data)
}

defineExpose({
    dialogFormVisible,
    open,
    setData
})
</script>

<style lang="scss" scoped></style>