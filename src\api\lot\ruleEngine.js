import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/lot/ruleEngine/list`,
		name: '获取规则列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/lot/ruleEngine/save`,
		name: '新增或修改规则',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/lot/ruleEngine/del`,
		name: '删除',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	one: {
		url: `${config.API_URL}/lot/ruleEngine/one`,
		name: '获取规则信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	changeStatus: {
		url: `${config.API_URL}/lot/ruleEngine/changeStatus`,
		name: '修改状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	setConfig: {
		url: `${config.API_URL}/lot/ruleEngine/setConfig`,
		name: '规则设置',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	getLog: {
		url: `${config.API_URL}/lot/ruleEngine/getLog`,
		name: '获取规则日志',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	}
}
