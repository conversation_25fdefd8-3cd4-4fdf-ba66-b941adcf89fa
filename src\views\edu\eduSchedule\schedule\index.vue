<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script setup>
import rules from './courseRules'
import schedule from './courseSchedule'
import { ref } from 'vue'
const activeName = ref('schedule')
const currComponent = ref({
	name: 'schedule',
	component: schedule
})
const tabs = [
	{
		name: 'schedule',
		label: '排课',
		component: schedule
	},
	{
		name: 'rules',
		label: '排课规则',
		component: rules
	}
]
const handleClick = (name) => {
	currComponent.value = tabs.find((item) => item.name === name)
}
</script>
<style lang="scss" scoped></style>
