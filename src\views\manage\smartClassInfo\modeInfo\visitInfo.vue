<template>
    <el-drawer v-model="visible" title="领导视察信息" size="50%" destroy-on-close :close-on-press-escape="false" @close="close">
        <el-header>
            <div class="right-panel">
                <el-button type="primary" icon="el-icon-plus" @click="add">新增</el-button>
            </div>
        </el-header>
        <el-table row-key="id" :data="visitInfo">
            <el-table-column label="封面图" prop="coverUrl">
                <template #default="{ row }">
                    <cusImage loading="lazy" :lazy="true" fit="contain" style="width: 50px; height: 50px"
                        :src="row.coverUrl" :preview-src-list="[row.coverUrl]" preview-teleported>
                    </cusImage>
                </template>
            </el-table-column>
            <el-table-column label="信息类型" prop="displayType">
                <template #default="{ row }">
                    {{ row.displayType == 1 ? '图片' : '视频' }}
                </template>
            </el-table-column>
            <el-table-column label="资源地址" prop="hotspotUrl">
                <template #default="{ row }">
                    <cusImage loading="lazy" :lazy="true" fit="contain" style="width: 50px; height: 50px"
                        :src="row.hotspotUrl" :preview-src-list="[row.hotspotUrl]" preview-teleported
                        v-if="row.displayType == 1">
                    </cusImage>
                    <div v-else>
                        <el-button text type="primary" size="small" @click="videoPreview(row)">预览视频</el-button>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
                <template #default="{ row }">
                    <el-button text type="danger" size="small" @click="table_del(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </el-drawer>
    <el-dialog v-model="videoVisible" destroy-on-close title="视频预览">
        <video height="500px" width="100%" autoplay controls>
            <source :src="currentVideo.hotspotUrl" type="video/mp4">
        </video>
    </el-dialog>
    <addVisit ref="addVisitRef" @addSuccess="getModeInfo" />
</template>
<script setup>
import addVisit from './addVisit.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const visible = ref(false)
const visitInfo = ref([])
const videoVisible = ref(false)
const currentVideo = ref({})
const currentSn = ref('')
// 视频预览
const videoPreview = (row) => {
    currentVideo.value = row
    videoVisible.value = true
}
// 
const addVisitRef = ref()
const add = () => {
    addVisitRef.value.show(currentSn.value)
}
// 删除
const table_del = (row) => {
    ElMessageBox.confirm('确定删除该班牌信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        globalPropValue.platoonhotspot.del_leader_inspection_mode.post({
            id: row.idString,
            tenant_id: tenantId,
            campus_id: campusId
        }).then((res) => {
            if (res.code === 200) {
                ElMessage.success('删除成功')
                getModeInfo(currentSn.value)
            }
        })
    })
}
// 获取模式信息
const getModeInfo = (sn) => {
    globalPropValue.platoonhotspot.get_leader_inspection_mode.get({
        device_sn: sn,
        tenant_id: tenantId,
        campus_id: campusId
    }).then((res) => {
        if (res.code === 200) {
            visitInfo.value = res.data
        }
    }).catch((err) => {
        console.log(`获取热点信息失败： ${err.message}`)
    });
}

const show = (sn) => {
    visible.value = true
    currentSn.value = sn
    getModeInfo(sn)
}
const emit = defineEmits(['refresh'])
const close = () => {
    visible.value = false
    emit('refresh')
}
defineExpose({
    show,
    close
})
</script>