<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script setup>
import change from './change'
import management from './management'
import log from './log'
import { ref } from 'vue'
const activeName = ref('change')
const currComponent = ref({
	name: 'change',
	component: change
})
const tabs = [
	{
		name: 'change',
		label: '场景切换',
		component: change
	},
	{
		name: 'management',
		label: '场景管理',
		component: management
	},
	{
		name: 'log',
		label: '场景操作日志',
		component: log
	}
]
const handleClick = (name) => {
	currComponent.value = tabs.find((item) => item.name === name)
}
</script>
<style lang="scss" scoped></style>
