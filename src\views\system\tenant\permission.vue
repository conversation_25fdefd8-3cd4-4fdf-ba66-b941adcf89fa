<template>
	<el-drawer v-model="visible" title="学校菜单设置" size="50%" destroy-on-close @closed="$emit('closed')">
		<el-tabs tab-position="top">
			<el-tab-pane label="PC菜单权限">
				<div class="desc">
					权限类型：
					<strong><sc-status-indicator type="success"></sc-status-indicator> 菜单</strong>
					<strong><sc-status-indicator type="warning"></sc-status-indicator> 内嵌网页</strong>
					<strong><sc-status-indicator type="danger"></sc-status-indicator> 外链网页</strong>
					<strong><sc-status-indicator type="info"></sc-status-indicator> 按钮/接口</strong>
				</div>
				<div class="treeMain">
					<el-tree
						ref="menu"
						node-key="id"
						:data="menu.list"
						:props="menu.props"
						show-checkbox
						:default-expand-all="true"
						highlight-current
						:render-after-expand="true"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span class="label">
									<sc-status-indicator v-if="data.meta.type === 'menu'" type="success"></sc-status-indicator>
									<sc-status-indicator v-if="data.meta.type === 'iframe'" type="warning"></sc-status-indicator>
									<sc-status-indicator v-if="data.meta.type === 'link'" type="danger"></sc-status-indicator>
									<sc-status-indicator v-if="data.meta.type === 'button'" type="info"></sc-status-indicator>
									{{ node.label }}
								</span>
							</span>
						</template>
					</el-tree>
				</div>
			</el-tab-pane>
			<el-tab-pane label="教师手机端权限">
				<el-button type="primary" size="small" @click="showMark=!showMark"><span v-if="!showMark">显示</span><span v-if="showMark">关闭</span>菜单标记</el-button>
				<div class="treeMain" style="margin-top:15px">
					<el-tree
						ref="teacherPhoneMenu"
						node-key="mark"
						:data="teacherPageList"
						show-checkbox
						:default-expand-all="true"
						highlight-current
						:render-after-expand="true"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span class="label">
									<el-image
										v-if="data.icon"
										:src="'https://educdn.xjzredu.cn/scms/system/front/h5-teacher' + data.icon"
										style="width: 15px; height: 15px"
									></el-image>
									{{ data.label }} <span v-if="showMark">( {{ data.mark }} )</span>
								</span>
							</span>
						</template>
					</el-tree>
				</div>
			</el-tab-pane>
			<el-tab-pane label="学生手机端权限">
				<el-button type="primary" size="small" @click="showMark=!showMark"><span v-if="!showMark">显示</span><span v-if="showMark">关闭</span>菜单标记</el-button>
				<div class="treeMain" style="margin-top:15px">
					<el-tree
						ref="studentPhoneMenu"
						node-key="mark"
						:data="studentPageList"
						show-checkbox
						:default-expand-all="true"
						highlight-current
						:render-after-expand="true"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span class="label">
									<el-image
										v-if="data.icon"
										:src="'https://educdn.xjzredu.cn/scms/system/front/h5' + data.icon"
										style="width: 15px; height: 15px"
									></el-image>
									{{ data.label }} <span v-if="showMark">( {{ data.mark }} )</span>
								</span>
							</span>
						</template>
					</el-tree>
				</div>
			</el-tab-pane>
		</el-tabs>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import phoneMenu from '@/config/phoneMenu'

export default {
	emits: ['success', 'closed'],
	data() {
		return {
			tenantId: 0,
			visible: false,
			showMark: false,
			isSaveing: false,
			teacherPageList: phoneMenu.teacher,
			teacherChecked: [],
			studentPageList: phoneMenu.student,
			studentChecked: [],
			menu: {
				list: [],
				checked: [],
				props: {
					label: (data) => {
						return data.meta.title
					},
					class: (data, node) => {
						let isPenultimate = true
						for (const key in data.children) {
							if (data.children[key]?.children?.length ?? 0 > 0) {
								isPenultimate = false
								break
							}
						}
						return data.children?.length > 0 && isPenultimate ? `is-penultimate level${node.level}` : ''
					}
				}
			},
		}
	},
	mounted() {},
	methods: {
		open(tenantId) {
			this.tenantId = tenantId
			this.getInit()
			this.visible = true
		},
		async submit() {
			this.isSaveing = true
			//选中的和半选的合并后传值接口
			var checkedKeys = this.$refs.menu.getCheckedKeys().concat(this.$refs.menu.getHalfCheckedKeys())
			var phoneTeaMenus = this.$refs.teacherPhoneMenu.getCheckedKeys().concat(this.$refs.teacherPhoneMenu.getHalfCheckedKeys())
			var phoneStuMenus = this.$refs.studentPhoneMenu.getCheckedKeys().concat(this.$refs.studentPhoneMenu.getHalfCheckedKeys())

			//var checkedKeys_dept = this.$refs.dept.getCheckedKeys().concat(this.$refs.dept.getHalfCheckedKeys())

			//var checkedKeys_dashboard = this.$refs.grid.getCheckedKeys().concat(this.$refs.grid.getHalfCheckedKeys())
			var res = await this.$API.tenant.permissions.post({
				tenantId: this.tenantId,
				menuChecked: checkedKeys,
				phoneTeaMenus: phoneTeaMenus,
				phoneStuMenus: phoneStuMenus
				//dashboardChecked: checkedKeys_dashboard
			})
			if (res.code === 200) {
				this.isSaveing = false
				this.visible = false
				this.$message.success('操作成功')
				this.$emit('success')
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		async getInit() {
			var res = await this.$API.tenant.permissions.get(this.tenantId)
			//var res = await this.$API.system.menu.list.get()
			this.menu.list = res.data.menus
			//获取接口返回的之前选中的和半选的合并，处理过滤掉有叶子节点的key
			this.menu.checked = res.data.menuChecked
			this.teacherChecked= res.data.teacherChecked
			this.studentChecked = res.data.studentChecked
			
			this.$nextTick(() => {
				let filterKeys = this.menu.checked.filter((key) => this.$refs.menu.getNode(key)?.isLeaf)
				this.$refs.menu.setCheckedKeys(filterKeys, true)
			})
			this.$nextTick(() => {
				let filterKeys = this.teacherChecked.filter((key) => this.$refs.teacherPhoneMenu.getNode(key)?.isLeaf)
				this.$refs.teacherPhoneMenu.setCheckedKeys(filterKeys, true)
			})
			this.$nextTick(() => {
				let filterKeys = this.studentChecked.filter((key) => this.$refs.studentPhoneMenu.getNode(key)?.isLeaf)
				this.$refs.studentPhoneMenu.setCheckedKeys(filterKeys, true)
			})
		}
	}
}
</script>

<style scoped lang="scss">
:deep(.el-dialog__body) {
	padding: 5px 10px;
}
:deep(.is-penultimate) {
	.el-tree-node__children {
		padding-left: 65px;
		white-space: pre-wrap;
		line-height: 100%;

		.el-tree-node {
			display: inline-block;
		}

		.el-tree-node__content {
			padding-left: 12px !important;
			padding-right: 12px;
			width: 180px;
			.el-tree-node__expand-icon.is-leaf {
				display: none;
			}
		}
	}
	&.level1 {
		.el-tree-node__children {
			padding-left: 36px;
		}
	}
	&.level2 {
		.el-tree-node__children {
			padding-left: 54x;
		}
	}
	&.level3 {
		.el-tree-node__children {
			padding-left: 72px;
		}
	}
	&.level4 {
		.el-tree-node__children {
			padding-left: 90px;
		}
	}
}
.treeMain {
	min-height: 300px;
	overflow: auto;
	border: 1px solid #dcdfe6;
	margin-bottom: 10px;
}

.desc {
	padding-bottom: 10px;
}
.desc strong {
	padding-right: 15px;
	font-weight: unset;
}
</style>
