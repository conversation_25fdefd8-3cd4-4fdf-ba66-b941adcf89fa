<template>
	<el-container class="d-form">
		<el-aside class="left-container" width="250px">
			<el-container>
				<div>
					<designerLeft></designerLeft>
				</div>
			</el-container>
		</el-aside>
		<el-container class="center-container">
			<el-main>
				<designerFormHeader v-bind="$props" @preview="() => (previewVisible = true)"
					@uploadJson="() => (uploadJsonVisible = true)" @generateJson="handleGenerateJson"
					@generateCode="handleGenerateCode" @clearable="handleClearable">
				</designerFormHeader>
				<designerCenter ref="widgetFormRef" v-model:widgetForm="widgetForm"
					v-model:widgetFormSelect="widgetFormSelect">
				</designerCenter>
			</el-main>
		</el-container>
		<el-aside class="right-container" width="300px">
			<el-container>
				<el-header>
					<div class="right-tab" :class="{ active: rightTab === 'widget' }" @click="rightTab = 'widget'">字段属性
					</div>
					<div class="right-tab" :class="{ active: rightTab === 'form' }" @click="rightTab = 'form'">表单属性
					</div>
				</el-header>
				<el-main class="right-content">
					<formConfig v-show="rightTab === 'form'" v-model:config="widgetForm.config"></formConfig>
					<widgetConfig v-show="rightTab === 'widget'" v-model:select="widgetFormSelect"></widgetConfig>
				</el-main>
			</el-container>
		</el-aside>
		<!-- 预览弹窗 -->
		<el-dialog v-model="previewVisible" title="预览" :width="800">
			<designerFormPreview v-if="previewVisible" ref="designerFormPreviewRef" :data="widgetForm" />
			<!-- <template #footer>
                <el-button @click="handleReset">重置</el-button>
                <el-button type="primary" @click="handleGetData">获取数据</el-button>
            </template> -->

			<!-- <el-dialog v-model="dataJsonVisible" title="获取数据" :width="800">
                <CodeEditor :value="dataJsonTemplate" language="json" readonly />

                <template #footer>
                    <el-button @click="() => (dataJsonVisible = false)">取消</el-button>
                    <el-button type="primary" @click="handleCopyClick(dataJsonTemplate)">Copy</el-button>
                </template>
            </el-dialog> -->
		</el-dialog>
	</el-container>
</template>
<script>
import designerLeft from './designerFormLeft.vue'
import designerFormHeader from './designerFormHeader.vue'
import designerCenter from './designerFormCenter.vue'
import designerRight from './designerFormRight.vue'
import designerFormPreview from './designerFormPreview.vue'
import formConfig from './component/formConfig.vue'
import widgetConfig from './component/widgetConfig.vue'
import { defaultsDeep } from 'lodash'
import { CodeType, widgetForm } from '@/utils/components'
import { defineComponent, reactive, toRefs, watch, watchEffect } from 'vue'

export default defineComponent({
	name: 'DesignerForm',
	components: {
		designerLeft,
		designerFormHeader,
		designerCenter,
		designerRight,
		designerFormPreview,
		formConfig,
		widgetConfig
	},
	props: {
		preview: {
			type: Boolean,
			default: true
		},
		generateCode: {
			type: Boolean,
			default: true
		},
		generateJson: {
			type: Boolean,
			default: true
		},
		uploadJson: {
			type: Boolean,
			default: true
		},
		clearable: {
			type: Boolean,
			default: true
		}
	},
	emits: ['saveForm'],
	setup(props, context) {
		const emit = context.emit
		const state = reactive({
			codeType: CodeType,
			widgetForm: JSON.parse(JSON.stringify(widgetForm)),
			widgetFormSelect: undefined,
			designerFormPreviewRef: null,
			rightTab: 'widget',
			previewVisible: false,
			uploadJsonVisible: false,
			dataJsonVisible: false,
			dataCodeVisible: false,
			generateJsonVisible: false,
			generateCodeVisible: false,
			generateJsonTemplate: JSON.stringify(widgetForm, null, 2),
			dataJsonTemplate: '',
			dataCodeTemplate: '',
			codeLanguage: CodeType.Vue,
			jsonEg: JSON.stringify(widgetForm, null, 2)
		})
		const handleUploadJson = () => {
			try {
				state.widgetForm.list = []
				defaultsDeep(state.widgetForm, JSON.parse(state.jsonEg))

				if (state.widgetForm.list) {
					state.widgetFormSelect = state.widgetForm.list[0]
				}

				state.uploadJsonVisible = false
				ElMessage.success('上传成功')
			} catch (error) {
				ElMessage.error('上传失败')
			}
		}

		const handleCopyClick = (text) => {
			copy(text)
			ElMessage.success('Copy成功')
		}

		const handleGetData = () => {
			state.designerFormPreviewRef.getData().then((res) => {
				state.dataJsonTemplate = JSON.stringify(res, null, 2)
				state.dataJsonVisible = true
			})
		}

		const handleGenerateJson = () => {
			state.generateJsonTemplate = JSON.stringify(state.widgetForm, null, 2)
			state.generateJsonVisible = true
		}

		const handleGenerateCode = () => {
			state.codeLanguage = CodeType.Vue
			state.dataCodeVisible = true
		}

		watchEffect(() => {
			if (state.dataCodeVisible) {
				state.dataCodeTemplate = generateCode(state.widgetForm, state.codeLanguage, PlatformType.Element)
			}
		})
		const handleClearable = () => {
			state.widgetForm.list = []
			defaultsDeep(state.widgetForm, JSON.parse(JSON.stringify(widgetForm)))
			state.widgetFormSelect = undefined
		}
		const handleSaveForm = () => {
			console.log(state.widgetForm, '保存的表单数据')
			emit('saveForm', state.widgetForm)
		}

		const handleReset = () => {
			state.designerFormPreviewRef.reset()
		}

		const getJson = () => {
			return state.widgetForm
		}
		const positionType = {
			1: 'left',
			2: 'right',
			3: 'top'
		}
		const setJson = (data) => {
			let jsonData = JSON.parse(data.form_content)
			state.widgetForm.list = []
			console.log(jsonData,'999999999999')
			defaultsDeep(state.widgetForm, {
				list: jsonData,
				config: { labelWidth: data.lable_width, labelPosition: positionType[data.label_position], size: data.size }
			})
			if (jsonData.length) {
				// state.widgetFormSelect = jsonData[0]
			}
		}

		const getTemplate = (codeType) => generateCode(state.widgetForm, codeType, PlatformType.Element)

		const clear = () => handleClearable()

		return {
			...toRefs(state),
			handleUploadJson,
			handleCopyClick,
			handleGetData,
			handleGenerateJson,
			handleGenerateCode,
			handleClearable,
			handleSaveForm,
			handleReset,
			getJson,
			setJson,
			getTemplate,
			clear
		}
	}
})
</script>

<style lang="scss" scoped>
$primary-color: #2745b2;

.d-form {
	background-color: #f6f8f9;

	.left-container {
		background-color: #fff;
		border: none;
	}

	.center-container {
		// margin: 0 15px;
		padding-right: 10px;
	}

	.right-container {
		background-color: #fff;
		border: none;
	}
}

.right-tab {
	height: 41px;
	line-height: 41px;
	display: inline-block;
	width: 145px;
	text-align: center;
	font-size: 14px;
	font-weight: 500;
	position: relative;
	cursor: pointer;

	&.active {
		border-bottom: 2px solid $primary-color;
	}
}

.right-content {
	padding: 10px;
	overflow: auto;
	max-height: calc(100vh - 150px);
}
</style>

<style lang="scss">
.d-form {
	.el-dialog__body {
		padding: 15px 20px;
	}

	.el-dialog__footer {
		border-top: 1px solid #f2f6fc;
		padding: 10px 15px;
	}

	.config-content {
		.el-radio-group {
			margin-top: 10px;
			vertical-align: super;
		}

		.el-radio {
			margin-bottom: 10px;
		}
	}

	.el-rate {
		margin-top: 6px;
	}

	.el-checkbox {
		line-height: 32px;
	}
}
</style>
