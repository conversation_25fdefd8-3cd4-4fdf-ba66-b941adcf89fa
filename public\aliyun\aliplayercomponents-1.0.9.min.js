/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 6);
/******/ })
/************************************************************************/
/******/ ([
/* 0 */
/***/ (function(module, exports, __webpack_require__) {

  "use strict";


  /*
    MIT License http://www.opensource.org/licenses/mit-license.php
    Author Tobias Koppers @sokra
  */
  // css base code, injected by the css-loader
  module.exports = function () {
    var list = [];
  
    // return the list of modules as css string
    list.toString = function toString() {
      var result = [];
      for (var i = 0; i < this.length; i++) {
        var item = this[i];
        if (item[2]) {
          result.push("@media " + item[2] + "{" + item[1] + "}");
        } else {
          result.push(item[1]);
        }
      }
      return result.join("");
    };
  
    // import a list of modules into the list
    list.i = function (modules, mediaQuery) {
      if (typeof modules === "string") modules = [[null, modules, ""]];
      var alreadyImportedModules = {};
      for (var i = 0; i < this.length; i++) {
        var id = this[i][0];
        if (typeof id === "number") alreadyImportedModules[id] = true;
      }
      for (i = 0; i < modules.length; i++) {
        var item = modules[i];
        // skip already imported module
        // this implementation is not 100% perfect for weird media query combinations
        //  when a module is imported multiple times with different media queries.
        //  I hope this will never occur (Hey this way we have smaller bundles)
        if (typeof item[0] !== "number" || !alreadyImportedModules[item[0]]) {
          if (mediaQuery && !item[2]) {
            item[2] = mediaQuery;
          } else if (mediaQuery) {
            item[2] = "(" + item[2] + ") and (" + mediaQuery + ")";
          }
          list.push(item);
        }
      }
    };
    return list;
  };
  
  /***/ }),
  /* 1 */
  /***/ (function(module, exports) {
  
  /*
    MIT License http://www.opensource.org/licenses/mit-license.php
    Author Tobias Koppers @sokra
  */
  var stylesInDom = {},
    memoize = function(fn) {
      var memo;
      return function () {
        if (typeof memo === "undefined") memo = fn.apply(this, arguments);
        return memo;
      };
    },
    isOldIE = memoize(function() {
      return /msie [6-9]\b/.test(self.navigator.userAgent.toLowerCase());
    }),
    getHeadElement = memoize(function () {
      return document.head || document.getElementsByTagName("head")[0];
    }),
    singletonElement = null,
    singletonCounter = 0,
    styleElementsInsertedAtTop = [];
  
  module.exports = function(list, options) {
    if(typeof DEBUG !== "undefined" && DEBUG) {
      if(typeof document !== "object") throw new Error("The style-loader cannot be used in a non-browser environment");
    }
  
    options = options || {};
    // Force single-tag solution on IE6-9, which has a hard limit on the # of <style>
    // tags it will allow on a page
    if (typeof options.singleton === "undefined") options.singleton = isOldIE();
  
    // By default, add <style> tags to the bottom of <head>.
    if (typeof options.insertAt === "undefined") options.insertAt = "bottom";
  
    var styles = listToStyles(list);
    addStylesToDom(styles, options);
  
    return function update(newList) {
      var mayRemove = [];
      for(var i = 0; i < styles.length; i++) {
        var item = styles[i];
        var domStyle = stylesInDom[item.id];
        domStyle.refs--;
        mayRemove.push(domStyle);
      }
      if(newList) {
        var newStyles = listToStyles(newList);
        addStylesToDom(newStyles, options);
      }
      for(var i = 0; i < mayRemove.length; i++) {
        var domStyle = mayRemove[i];
        if(domStyle.refs === 0) {
          for(var j = 0; j < domStyle.parts.length; j++)
            domStyle.parts[j]();
          delete stylesInDom[domStyle.id];
        }
      }
    };
  }
  
  function addStylesToDom(styles, options) {
    for(var i = 0; i < styles.length; i++) {
      var item = styles[i];
      var domStyle = stylesInDom[item.id];
      if(domStyle) {
        domStyle.refs++;
        for(var j = 0; j < domStyle.parts.length; j++) {
          domStyle.parts[j](item.parts[j]);
        }
        for(; j < item.parts.length; j++) {
          domStyle.parts.push(addStyle(item.parts[j], options));
        }
      } else {
        var parts = [];
        for(var j = 0; j < item.parts.length; j++) {
          parts.push(addStyle(item.parts[j], options));
        }
        stylesInDom[item.id] = {id: item.id, refs: 1, parts: parts};
      }
    }
  }
  
  function listToStyles(list) {
    var styles = [];
    var newStyles = {};
    for(var i = 0; i < list.length; i++) {
      var item = list[i];
      var id = item[0];
      var css = item[1];
      var media = item[2];
      var sourceMap = item[3];
      var part = {css: css, media: media, sourceMap: sourceMap};
      if(!newStyles[id])
        styles.push(newStyles[id] = {id: id, parts: [part]});
      else
        newStyles[id].parts.push(part);
    }
    return styles;
  }
  
  function insertStyleElement(options, styleElement) {
    var head = getHeadElement();
    var lastStyleElementInsertedAtTop = styleElementsInsertedAtTop[styleElementsInsertedAtTop.length - 1];
    if (options.insertAt === "top") {
      if(!lastStyleElementInsertedAtTop) {
        head.insertBefore(styleElement, head.firstChild);
      } else if(lastStyleElementInsertedAtTop.nextSibling) {
        head.insertBefore(styleElement, lastStyleElementInsertedAtTop.nextSibling);
      } else {
        head.appendChild(styleElement);
      }
      styleElementsInsertedAtTop.push(styleElement);
    } else if (options.insertAt === "bottom") {
      head.appendChild(styleElement);
    } else {
      throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");
    }
  }
  
  function removeStyleElement(styleElement) {
    styleElement.parentNode.removeChild(styleElement);
    var idx = styleElementsInsertedAtTop.indexOf(styleElement);
    if(idx >= 0) {
      styleElementsInsertedAtTop.splice(idx, 1);
    }
  }
  
  function createStyleElement(options) {
    var styleElement = document.createElement("style");
    styleElement.type = "text/css";
    insertStyleElement(options, styleElement);
    return styleElement;
  }
  
  function createLinkElement(options) {
    var linkElement = document.createElement("link");
    linkElement.rel = "stylesheet";
    insertStyleElement(options, linkElement);
    return linkElement;
  }
  
  function addStyle(obj, options) {
    var styleElement, update, remove;
  
    if (options.singleton) {
      var styleIndex = singletonCounter++;
      styleElement = singletonElement || (singletonElement = createStyleElement(options));
      update = applyToSingletonTag.bind(null, styleElement, styleIndex, false);
      remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true);
    } else if(obj.sourceMap &&
      typeof URL === "function" &&
      typeof URL.createObjectURL === "function" &&
      typeof URL.revokeObjectURL === "function" &&
      typeof Blob === "function" &&
      typeof btoa === "function") {
      styleElement = createLinkElement(options);
      update = updateLink.bind(null, styleElement);
      remove = function() {
        removeStyleElement(styleElement);
        if(styleElement.href)
          URL.revokeObjectURL(styleElement.href);
      };
    } else {
      styleElement = createStyleElement(options);
      update = applyToTag.bind(null, styleElement);
      remove = function() {
        removeStyleElement(styleElement);
      };
    }
  
    update(obj);
  
    return function updateStyle(newObj) {
      if(newObj) {
        if(newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap)
          return;
        update(obj = newObj);
      } else {
        remove();
      }
    };
  }
  
  var replaceText = (function () {
    var textStore = [];
  
    return function (index, replacement) {
      textStore[index] = replacement;
      return textStore.filter(Boolean).join('\n');
    };
  })();
  
  function applyToSingletonTag(styleElement, index, remove, obj) {
    var css = remove ? "" : obj.css;
  
    if (styleElement.styleSheet) {
      styleElement.styleSheet.cssText = replaceText(index, css);
    } else {
      var cssNode = document.createTextNode(css);
      var childNodes = styleElement.childNodes;
      if (childNodes[index]) styleElement.removeChild(childNodes[index]);
      if (childNodes.length) {
        styleElement.insertBefore(cssNode, childNodes[index]);
      } else {
        styleElement.appendChild(cssNode);
      }
    }
  }
  
  function applyToTag(styleElement, obj) {
    var css = obj.css;
    var media = obj.media;
  
    if(media) {
      styleElement.setAttribute("media", media)
    }
  
    if(styleElement.styleSheet) {
      styleElement.styleSheet.cssText = css;
    } else {
      while(styleElement.firstChild) {
        styleElement.removeChild(styleElement.firstChild);
      }
      styleElement.appendChild(document.createTextNode(css));
    }
  }
  
  function updateLink(linkElement, obj) {
    var css = obj.css;
    var sourceMap = obj.sourceMap;
  
    if(sourceMap) {
      // http://stackoverflow.com/a/26603875
      css += "\n/*# sourceMappingURL=data:application/json;base64," + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + " */";
    }
  
    var blob = new Blob([css], { type: "text/css" });
  
    var oldSrc = linkElement.href;
  
    linkElement.href = URL.createObjectURL(blob);
  
    if(oldSrc)
      URL.revokeObjectURL(oldSrc);
  }
  
  
  /***/ }),
  /* 2 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.parseDom = parseDom;
  function parseDom(html) {
    var ele = document.createElement('div');
    ele.innerHTML = html;
    return ele.childNodes[0];
  }
  
  module.exports.cookieSet = function (cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
    var expires = 'expires=' + d.toGMTString();
    document.cookie = cname + '=' + escape(cvalue) + '; ' + expires;
  };
  
  /***/ }),
  /* 3 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _typeof2 = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; };
  
  var _typeof = typeof Symbol === "function" && _typeof2(Symbol.iterator) === "symbol" ? function (obj) {
    return typeof obj === "undefined" ? "undefined" : _typeof2(obj);
  } : function (obj) {
    return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj === "undefined" ? "undefined" : _typeof2(obj);
  };
  
  // Save the previous value of the device variable.
  var previousDevice = window.device;
  
  var device = {};
  
  var changeOrientationList = [];
  
  // Add device as a global object.
  window.device = device;
  
  // The <html> element.
  var documentElement = window.document.documentElement;
  
  // The client user agent string.
  // Lowercase, so we can use the more efficient indexOf(), instead of Regex
  var userAgent = window.navigator.userAgent.toLowerCase();
  
  // Detectable television devices.
  var television = ['googletv', 'viera', 'smarttv', 'internet.tv', 'netcast', 'nettv', 'appletv', 'boxee', 'kylo', 'roku', 'dlnadoc', 'roku', 'pov_tv', 'hbbtv', 'ce-html'];
  
  // Main functions
  // --------------
  
  device.macos = function () {
    return find('mac');
  };
  
  device.ios = function () {
    return device.iphone() || device.ipod() || device.ipad();
  };
  
  device.iphone = function () {
    return !device.windows() && find('iphone');
  };
  
  device.ipod = function () {
    return find('ipod');
  };
  
  device.ipad = function () {
    return find('ipad');
  };
  
  device.android = function () {
    return !device.windows() && find('android');
  };
  
  device.androidPhone = function () {
    return device.android() && find('mobile');
  };
  
  device.androidTablet = function () {
    return device.android() && !find('mobile');
  };
  
  device.blackberry = function () {
    return find('blackberry') || find('bb10') || find('rim');
  };
  
  device.blackberryPhone = function () {
    return device.blackberry() && !find('tablet');
  };
  
  device.blackberryTablet = function () {
    return device.blackberry() && find('tablet');
  };
  
  device.windows = function () {
    return find('windows');
  };
  
  device.windowsPhone = function () {
    return device.windows() && find('phone');
  };
  
  device.windowsTablet = function () {
    return device.windows() && find('touch') && !device.windowsPhone();
  };
  
  device.fxos = function () {
    return (find('(mobile') || find('(tablet')) && find(' rv:');
  };
  
  device.fxosPhone = function () {
    return device.fxos() && find('mobile');
  };
  
  device.fxosTablet = function () {
    return device.fxos() && find('tablet');
  };
  
  device.meego = function () {
    return find('meego');
  };
  
  device.cordova = function () {
    return window.cordova && location.protocol === 'file:';
  };
  
  device.nodeWebkit = function () {
    return _typeof(window.process) === 'object';
  };
  
  device.mobile = function () {
    return device.androidPhone() || device.iphone() || device.ipod() || device.windowsPhone() || device.blackberryPhone() || device.fxosPhone() || device.meego();
  };
  
  device.tablet = function () {
    return device.ipad() || device.androidTablet() || device.blackberryTablet() || device.windowsTablet() || device.fxosTablet();
  };
  
  device.desktop = function () {
    return !device.tablet() && !device.mobile();
  };
  
  device.television = function () {
    var i = 0;
    while (i < television.length) {
      if (find(television[i])) {
        return true;
      }
      i++;
    }
    return false;
  };
  
  device.portrait = function () {
    if (screen.orientation && Object.prototype.hasOwnProperty.call(window, 'onorientationchange')) {
      return screen.orientation.type.includes('portrait');
    }
    return window.innerHeight / window.innerWidth > 1;
  };
  
  device.landscape = function () {
    if (screen.orientation && Object.prototype.hasOwnProperty.call(window, 'onorientationchange')) {
      return screen.orientation.type.includes('landscape');
    }
    return window.innerHeight / window.innerWidth < 1;
  };
  
  // Public Utility Functions
  // ------------------------
  
  // Run device.js in noConflict mode,
  // returning the device variable to its previous owner.
  device.noConflict = function () {
    window.device = previousDevice;
    return this;
  };
  
  // Private Utility Functions
  // -------------------------
  
  // Simple UA string search
  function find(needle) {
    return userAgent.indexOf(needle) !== -1;
  }
  
  // Check if documentElement already has a given class.
  function hasClass(className) {
    return documentElement.className.match(new RegExp(className, 'i'));
  }
  
  // Add one or more CSS classes to the <html> element.
  function addClass(className) {
    var currentClassNames = null;
    if (!hasClass(className)) {
      currentClassNames = documentElement.className.replace(/^\s+|\s+$/g, '');
      documentElement.className = currentClassNames + ' ' + className;
    }
  }
  
  // Remove single CSS class from the <html> element.
  function removeClass(className) {
    if (hasClass(className)) {
      documentElement.className = documentElement.className.replace(' ' + className, '');
    }
  }
  
  // HTML Element Handling
  // ---------------------
  
  // Insert the appropriate CSS class based on the _user_agent.
  
  if (device.ios()) {
    if (device.ipad()) {
      addClass('ios ipad tablet');
    } else if (device.iphone()) {
      addClass('ios iphone mobile');
    } else if (device.ipod()) {
      addClass('ios ipod mobile');
    }
  } else if (device.macos()) {
    addClass('macos desktop');
  } else if (device.android()) {
    if (device.androidTablet()) {
      addClass('android tablet');
    } else {
      addClass('android mobile');
    }
  } else if (device.blackberry()) {
    if (device.blackberryTablet()) {
      addClass('blackberry tablet');
    } else {
      addClass('blackberry mobile');
    }
  } else if (device.windows()) {
    if (device.windowsTablet()) {
      addClass('windows tablet');
    } else if (device.windowsPhone()) {
      addClass('windows mobile');
    } else {
      addClass('windows desktop');
    }
  } else if (device.fxos()) {
    if (device.fxosTablet()) {
      addClass('fxos tablet');
    } else {
      addClass('fxos mobile');
    }
  } else if (device.meego()) {
    addClass('meego mobile');
  } else if (device.nodeWebkit()) {
    addClass('node-webkit');
  } else if (device.television()) {
    addClass('television');
  } else if (device.desktop()) {
    addClass('desktop');
  }
  
  if (device.cordova()) {
    addClass('cordova');
  }
  
  // Orientation Handling
  // --------------------
  
  // Handle device orientation changes.
  function handleOrientation() {
    if (device.landscape()) {
      removeClass('portrait');
      addClass('landscape');
      walkOnChangeOrientationList('landscape');
    } else {
      removeClass('landscape');
      addClass('portrait');
      walkOnChangeOrientationList('portrait');
    }
    setOrientationCache();
  }
  
  function walkOnChangeOrientationList(newOrientation) {
    for (var index in changeOrientationList) {
      changeOrientationList[index](newOrientation);
    }
  }
  
  device.onChangeOrientation = function (cb) {
    if (typeof cb == 'function') {
      changeOrientationList.push(cb);
    }
  };
  
  // Detect whether device supports orientationchange event,
  // otherwise fall back to the resize event.
  var orientationEvent = 'resize';
  if (Object.prototype.hasOwnProperty.call(window, 'onorientationchange')) {
    orientationEvent = 'orientationchange';
  }
  
  // Listen for changes in orientation.
  if (window.addEventListener) {
    window.addEventListener(orientationEvent, handleOrientation, false);
  } else if (window.attachEvent) {
    window.attachEvent(orientationEvent, handleOrientation);
  } else {
    window[orientationEvent] = handleOrientation;
  }
  
  handleOrientation();
  
  // Public functions to get the current value of type, os, or orientation
  // ---------------------------------------------------------------------
  
  function findMatch(arr) {
    for (var i = 0; i < arr.length; i++) {
      if (device[arr[i]]()) {
        return arr[i];
      }
    }
    return 'unknown';
  }
  
  device.type = findMatch(['mobile', 'tablet', 'desktop']);
  device.os = findMatch(['ios', 'iphone', 'ipad', 'ipod', 'android', 'blackberry', 'windows', 'fxos', 'meego', 'television']);
  
  function setOrientationCache() {
    device.orientation = findMatch(['portrait', 'landscape']);
  }
  
  setOrientationCache();
  
  exports.default = device;
  
  /***/ }),
  /* 4 */
  /***/ (function(module, exports) {
  
  module.exports = "data:application/vnd.ms-fontobject;base64,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"
  
  /***/ }),
  /* 5 */
  /***/ (function(module, exports) {
  
  module.exports = "data:application/vnd.ms-fontobject;base64,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"
  
  /***/ }),
  /* 6 */
  /***/ (function(module, exports, __webpack_require__) {
  
  __webpack_require__(7);
  __webpack_require__(17);
  __webpack_require__(22);
  __webpack_require__(28);
  __webpack_require__(34);
  __webpack_require__(39);
  __webpack_require__(44);
  __webpack_require__(50);
  __webpack_require__(55);
  __webpack_require__(63);
  __webpack_require__(69);
  __webpack_require__(74);
  __webpack_require__(79);
  __webpack_require__(84);
  __webpack_require__(90);
  __webpack_require__(96);
  __webpack_require__(101);
  module.exports = __webpack_require__(107);
  
  
  /***/ }),
  /* 7 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(8);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.AliplayerDanmuComponent = _index2.default;
  
  /***/ }),
  /* 8 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(9);
  
  var _index2 = _interopRequireDefault(_index);
  
  var _danmuControl = __webpack_require__(10);
  
  var _danmuControl2 = _interopRequireDefault(_danmuControl);
  
  var _danmuInput = __webpack_require__(11);
  
  var _danmuInput2 = _interopRequireDefault(_danmuInput);
  
  __webpack_require__(12);
  
  var _CommentCoreLibrary = __webpack_require__(14);
  
  __webpack_require__(15);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 弹幕组件
   */
  var AliplayerDanmuComponent = function () {
    /**
     * @constructor 弹幕组件构造函数
     * @param {Array danmuList 弹幕数组, 参考 CommentCoreLibrary 文档 https://github.com/jabbany/CommentCoreLibrary/}
     * @param {id 或者 Element, sendEl, 发送弹幕的输入框, 默认为 null}  
     */
    function AliplayerDanmuComponent(danmukuList) {
      var sendEl = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'controlbar';
  
      _classCallCheck(this, AliplayerDanmuComponent);
  
      this.sendEl = sendEl;
      this.danmukuList = danmukuList;
      this.html = (0, _utils.parseDom)(_index2.default);
      this.danmuControlHtml = (0, _utils.parseDom)(_danmuControl2.default);
      this.sendEl = sendEl;
      this.danmuInput = sendEl === null ? null : (0, _utils.parseDom)(_danmuInput2.default);
      this.CM = null;
      this.userDanmuOpen = true; // 用户打开关闭弹幕的状态, 默认为 true 打开
    }
  
    _createClass(AliplayerDanmuComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var _this = this;
  
        console.log(player);
  
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        if (this.danmuInput !== null) {
          this.danmuInput.querySelector('.danmu-input-enter').innerText = this.isEn ? 'Enter' : '发送';
          this.danmuInput.querySelector('input').setAttribute('placeholder', this.isEn ? 'Input danmu' : '输入弹幕');
        }
        this.danmuControlHtml.querySelector('.player-tooltip.close').innerText = this.isEn ? 'Close Bullect' : '关闭弹幕';
        this.danmuControlHtml.querySelector('.player-tooltip.open').innerText = this.isEn ? 'Open Bullect' : '打开弹幕';
  
        if (this.sendEl === 'controlbar') {
          var danmuInputWrapEle = this.danmuControlHtml.querySelector('.ali-danmu-input-wrap');
          danmuInputWrapEle.style.display = 'inline-block';
          danmuInputWrapEle.appendChild(this.danmuInput);
        } else if (this.sendEl !== null) {
          if ((0, _utils.isElement)(this.sendEl)) {
            this.sendEl.appendChild(this.danmuInput);
          } else if (typeof this.sendEl === 'string') {
            var sendEl = document.querySelector(this.sendEl);
            if ((0, _utils.isElement)(sendEl)) {
              sendEl.appendChild(this.danmuInput);
            } else {
              throw new Error('sendEl must be an element or selector string');
            }
          } else {
            throw new Error('sendEl must be an element or selector string');
          }
        }
  
        el.querySelector('.prism-controlbar').appendChild(this.danmuControlHtml);
        var videoSiblingElement = el.querySelector('video').nextElementSibling;
        if (videoSiblingElement) {
          el.insertBefore(this.html, videoSiblingElement);
        } else {
          el.appendChild(this.html);
        }
        this.CM = new _CommentCoreLibrary.CommentManager(this.html.querySelector('.danmu')); // 初始化 CommentManager
  
        this.CM.init();
        this.CM.load(this.danmukuList);
  
        /* 根据视频播放器的 timeupdate 事件更新弹幕的事件轴   */
        el.querySelector('video').ontimeupdate = function () {
          if (el.querySelector('video') !== null) {
            _this.CM.time(el.querySelector('video').currentTime * 1000);
          }
        };
  
        var danmuCloseElement = this.danmuControlHtml.querySelector('.icon-danmu-close');
        var danmuOpenElement = this.danmuControlHtml.querySelector('.icon-danmu-open');
        /* 绑定控制条关闭弹幕处理函数 */
        danmuCloseElement.onclick = function () {
          _this.userDanmuOpen = false;
          danmuCloseElement.style.display = 'none';
          danmuOpenElement.style.display = 'inline-block';
          _this.CM.clear();
          _this.CM.stop();
        };
        /* 绑定控制条打开弹幕控制条 */
        danmuOpenElement.onclick = function () {
          danmuOpenElement.style.display = 'none';
          danmuCloseElement.style.display = 'inline-block';
          _this.userDanmuOpen = true;
          _this.CM.start();
        };
  
        if (this.danmuInput !== null) {
          var danmuInputEle = this.danmuInput.querySelector('.ali-danmu-input input');
          var danmuEnter = this.danmuInput.querySelector('.danmu-input-enter');
  
          danmuEnter.onclick = this.sendDanmuHandle.bind(this);
  
          danmuInputEle.onkeydown = function (e) {
            if (e.keyCode === 13) {
              _this.sendDanmuHandle.call(_this);
            }
          };
        }
      }
  
      // 弹幕发送按钮点击事件和弹幕输入框按下 enter 键, 处理事件
  
    }, {
      key: 'sendDanmuHandle',
      value: function sendDanmuHandle() {
        var danmuInputEle = this.danmuInput.querySelector('.ali-danmu-input input');
        var danmuText = danmuInputEle.value;
        var commentSize = [16, 18, 25, 36, 45];
        var commentColor = [0xffffff, 0x0000ff, 0xcc0000, 0xff66ff, 0xffff33];
        if (danmuText === '') {
          return;
        }
  
        this.send({
          "mode": 1,
          "text": danmuText,
          "stime": 1000,
          "size": commentSize[this.randomIndex(5)],
          "color": commentColor[this.randomIndex(5)]
        });
        danmuInputEle.value = '';
        danmuInputEle.focus();
      }
    }, {
      key: 'randomIndex',
      value: function randomIndex(max) {
        return Math.floor(Math.random() * max);
      }
    }, {
      key: 'play',
      value: function play(player, e) {
        if (this.userDanmuOpen) {
          this.CM.start();
        }
      }
    }, {
      key: 'pause',
      value: function pause(player, e) {
        if (this.userDanmuOpen) {
          this.CM.stop();
        }
      }
    }, {
      key: 'send',
      value: function send(danmuku) {
        this.CM.send(danmuku);
      }
  
      /**
       * 暴露出去的插入弹幕的方法
       * @param {Object danmuku 弹幕对象 只能一条一条插入}
       */
  
    }, {
      key: 'insert',
      value: function insert(danmuku) {
        this.CM && this.CM.insert(danmuku);
      }
    }, {
      key: 'dispose',
      value: function dispose() {
        if (this.danmuInput !== null) {
          this.danmuInput.parentNode && this.danmuInput.parentNode.removeChild(this.danmuInput);
        }
      }
    }]);
  
    return AliplayerDanmuComponent;
  }();
  
  exports.default = AliplayerDanmuComponent;
  
  /***/ }),
  /* 9 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"aliplayer-danmuku abp\">\n  <div class=\"danmu container\"></div>\n</div>";
  
  /***/ }),
  /* 10 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"ali-danmuku-control\">\n  <div class=\"ali-danmu-input-wrap\"></div>\n  <i class=\"iconfont icon-danmu-close\"></i>\n  <div class=\"player-tooltip close\"></div>\n  <i class=\"iconfont icon-danmu-open\" style=\"display: none;\"></i>\n  <div class=\"player-tooltip open\"></div>  \n</div>";
  
  /***/ }),
  /* 11 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"ali-danmu-input\">\n  <input type=\"text\" placeholder=\"\">\n  <button class=\"danmu-input-enter\"></button>\n</div>";
  
  /***/ }),
  /* 12 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(13);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 13 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".aliplayer-danmuku {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%; }\n  .aliplayer-danmuku.abp {\n    position: absolute; }\n    .aliplayer-danmuku.abp .container {\n      z-index: 0; }\n  .aliplayer-danmuku .danmu {\n    position: absolute;\n    width: 100%;\n    height: 100%; }\n\n.ali-danmuku-control {\n  float: right;\n  color: #fff;\n  margin-right: 5px;\n  position: relative;\n  display: table-cell;\n  margin-top: 8px; }\n  .ali-danmuku-control .iconfont {\n    font-size: 35px;\n    cursor: pointer;\n    vertical-align: middle;\n    user-select: none;\n    line-height: initial; }\n    .ali-danmuku-control .iconfont.icon-danmu-close {\n      display: inline-block; }\n  .ali-danmuku-control .player-tooltip.close, .ali-danmuku-control .player-tooltip.open {\n    right: -12px; }\n  @media (min-width: 768px) {\n    .ali-danmuku-control .icon-danmu-close:hover + .player-tooltip {\n      display: block; }\n    .ali-danmuku-control .icon-danmu-open:hover + .player-tooltip {\n      display: block; } }\n  .ali-danmuku-control .ali-danmu-input-wrap {\n    width: 200px;\n    display: none; }\n\n.ali-danmu-input {\n  position: relative;\n  width: 100%;\n  display: inline-block; }\n  .ali-danmu-input input {\n    display: inline-block;\n    width: 100%;\n    padding-right: 40px;\n    box-sizing: border-box;\n    padding: 5px 40px 5px 5px;\n    background-color: rgba(130, 132, 138, 0.4);\n    border: 1px solid #c0c4cc;\n    font-size: 14px;\n    color: #fff;\n    border-radius: 3px; }\n    .ali-danmu-input input:focus {\n      outline: none;\n      border-color: #0f84fd; }\n  .ali-danmu-input .danmu-input-enter {\n    position: absolute;\n    right: 0;\n    top: 0;\n    border: 1px solid;\n    color: #fff;\n    background-color: #0f84fd;\n    border-color: #0f84fd;\n    font-size: 12px;\n    padding: 6px 7px;\n    display: inline-block;\n    height: 28px;\n    border-radius: 0 3px 3px 0;\n    cursor: pointer; }\n    .ali-danmu-input .danmu-input-enter:focus {\n      outline: none; }\n    .ali-danmu-input .danmu-input-enter:hover {\n      background-color: #288df5;\n      border-color: #288df5; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 14 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  /**
   * Binary Search Stubs for JS Arrays
   * @license MIT
   * <AUTHOR> Chen
   */
  var BinArray = function () {
    var BinArray = {};
    BinArray.bsearch = function (arr, what, how) {
      if (arr.length === 0) {
        return 0;
      }
      if (how(what, arr[0]) < 0) {
        return 0;
      }
      if (how(what, arr[arr.length - 1]) >= 0) {
        return arr.length;
      }
      var low = 0;
      var i = 0;
      var count = 0;
      var high = arr.length - 1;
      while (low <= high) {
        i = Math.floor((high + low + 1) / 2);
        count++;
        if (how(what, arr[i - 1]) >= 0 && how(what, arr[i]) < 0) {
          return i;
        }
        if (how(what, arr[i - 1]) < 0) {
          high = i - 1;
        } else if (how(what, arr[i]) >= 0) {
          low = i;
        } else {
          console.error('Program Error');
        }
        if (count > 1500) {
          console.error('Too many run cycles.');
        }
      }
      return -1; // Never actually run
    };
    BinArray.binsert = function (arr, what, how) {
      var index = BinArray.bsearch(arr, what, how);
      arr.splice(index, 0, what);
      return index;
    };
    return BinArray;
  }();
  
  var __extends = undefined && undefined.__extends || function (d, b) {
    for (var p in b) {
      if (b.hasOwnProperty(p)) d[p] = b[p];
    }function __() {
      this.constructor = d;
    }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
  var CommentSpaceAllocator = function () {
    function CommentSpaceAllocator(width, height) {
      if (width === void 0) {
        width = 0;
      }
      if (height === void 0) {
        height = 0;
      }
      this._pools = [[]];
      this.avoid = 1;
      this._width = width;
      this._height = height;
    }
    CommentSpaceAllocator.prototype.willCollide = function (existing, check) {
      return existing.stime + existing.ttl >= check.stime + check.ttl / 2;
    };
    CommentSpaceAllocator.prototype.pathCheck = function (y, comment, pool) {
      var bottom = y + comment.height;
      var right = comment.right;
      for (var i = 0; i < pool.length; i++) {
        if (pool[i].y > bottom || pool[i].bottom < y) {
          continue;
        } else if (pool[i].right < comment.x || pool[i].x > right) {
          if (this.willCollide(pool[i], comment)) {
            return false;
          } else {
            continue;
          }
        } else {
          return false;
        }
      }
      return true;
    };
    CommentSpaceAllocator.prototype.assign = function (comment, cindex) {
      while (this._pools.length <= cindex) {
        this._pools.push([]);
      }
      var pool = this._pools[cindex];
      if (pool.length === 0) {
        comment.cindex = cindex;
        return 0;
      } else if (this.pathCheck(0, comment, pool)) {
        comment.cindex = cindex;
        return 0;
      }
      var y = 0;
      for (var k = 0; k < pool.length; k++) {
        y = pool[k].bottom + this.avoid;
        if (y + comment.height > this._height) {
          break;
        }
        if (this.pathCheck(y, comment, pool)) {
          comment.cindex = cindex;
          return y;
        }
      }
      return this.assign(comment, cindex + 1);
    };
    CommentSpaceAllocator.prototype.add = function (comment) {
      if (comment.height > this._height) {
        comment.cindex = -2;
        comment.y = 0;
      } else {
        comment.y = this.assign(comment, 0);
        BinArray.binsert(this._pools[comment.cindex], comment, function (a, b) {
          if (a.bottom < b.bottom) {
            return -1;
          } else if (a.bottom > b.bottom) {
            return 1;
          } else {
            return 0;
          }
        });
      }
    };
    CommentSpaceAllocator.prototype.remove = function (comment) {
      if (comment.cindex < 0) {
        return;
      }
      if (comment.cindex >= this._pools.length) {
        throw new Error("cindex out of bounds");
      }
      var index = this._pools[comment.cindex].indexOf(comment);
      if (index < 0) return;
      this._pools[comment.cindex].splice(index, 1);
    };
    CommentSpaceAllocator.prototype.setBounds = function (width, height) {
      this._width = width;
      this._height = height;
    };
    return CommentSpaceAllocator;
  }();
  var AnchorCommentSpaceAllocator = function (_super) {
    __extends(AnchorCommentSpaceAllocator, _super);
    function AnchorCommentSpaceAllocator() {
      _super.apply(this, arguments);
    }
    AnchorCommentSpaceAllocator.prototype.add = function (comment) {
      _super.prototype.add.call(this, comment);
      comment.x = (this._width - comment.width) / 2;
    };
    AnchorCommentSpaceAllocator.prototype.willCollide = function (a, b) {
      return true;
    };
    AnchorCommentSpaceAllocator.prototype.pathCheck = function (y, comment, pool) {
      var bottom = y + comment.height;
      for (var i = 0; i < pool.length; i++) {
        if (pool[i].y > bottom || pool[i].bottom < y) {
          continue;
        } else {
          return false;
        }
      }
      return true;
    };
    return AnchorCommentSpaceAllocator;
  }(CommentSpaceAllocator);
  //# sourceMappingURL=CommentSpaceAllocator.js.map
  var __extends = undefined && undefined.__extends || function (d, b) {
    for (var p in b) {
      if (b.hasOwnProperty(p)) d[p] = b[p];
    }function __() {
      this.constructor = d;
    }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
  var CoreComment = function () {
    function CoreComment(parent, init) {
      if (init === void 0) {
        init = {};
      }
      this.mode = 1;
      this.stime = 0;
      this.text = "";
      this.ttl = 4000;
      this.dur = 4000;
      this.cindex = -1;
      this.motion = [];
      this.movable = true;
      this._alphaMotion = null;
      this.absolute = true;
      this.align = 0;
      this._alpha = 1;
      this._size = 25;
      this._color = 0xffffff;
      this._border = false;
      this._shadow = true;
      this._font = "";
      if (!parent) {
        throw new Error("Comment not bound to comment manager.");
      } else {
        this.parent = parent;
      }
      if (init.hasOwnProperty("stime")) {
        this.stime = init["stime"];
      }
      if (init.hasOwnProperty("mode")) {
        this.mode = init["mode"];
      } else {
        this.mode = 1;
      }
      if (init.hasOwnProperty("dur")) {
        this.dur = init["dur"];
        this.ttl = this.dur;
      }
      this.dur *= this.parent.options.global.scale;
      this.ttl *= this.parent.options.global.scale;
      if (init.hasOwnProperty("text")) {
        this.text = init["text"];
      }
      if (init.hasOwnProperty("motion")) {
        this._motionStart = [];
        this._motionEnd = [];
        this.motion = init["motion"];
        var head = 0;
        for (var i = 0; i < init["motion"].length; i++) {
          this._motionStart.push(head);
          var maxDur = 0;
          for (var k in init["motion"][i]) {
            var m = init["motion"][i][k];
            maxDur = Math.max(m.dur, maxDur);
            if (m.easing === null || m.easing === undefined) {
              init["motion"][i][k]["easing"] = CoreComment.LINEAR;
            }
          }
          head += maxDur;
          this._motionEnd.push(head);
        }
        this._curMotion = 0;
      }
      if (init.hasOwnProperty("color")) {
        this._color = init["color"];
      }
      if (init.hasOwnProperty("size")) {
        this._size = init["size"];
      }
      if (init.hasOwnProperty("border")) {
        this._border = init["border"];
      }
      if (init.hasOwnProperty("opacity")) {
        this._alpha = init["opacity"];
      }
      if (init.hasOwnProperty("alpha")) {
        this._alphaMotion = init["alpha"];
      }
      if (init.hasOwnProperty("font")) {
        this._font = init["font"];
      }
      if (init.hasOwnProperty("x")) {
        this._x = init["x"];
      }
      if (init.hasOwnProperty("y")) {
        this._y = init["y"];
      }
      if (init.hasOwnProperty("shadow")) {
        this._shadow = init["shadow"];
      }
      if (init.hasOwnProperty("position")) {
        if (init["position"] === "relative") {
          this.absolute = false;
          if (this.mode < 7) {
            console.warn("Using relative position for CSA comment.");
          }
        }
      }
    }
    CoreComment.prototype.init = function (recycle) {
      if (recycle === void 0) {
        recycle = null;
      }
      if (recycle !== null) {
        this.dom = recycle.dom;
      } else {
        this.dom = document.createElement("div");
      }
      this.dom.className = this.parent.options.global.className;
      this.dom.appendChild(document.createTextNode(this.text));
      this.dom.textContent = this.text;
      this.dom.innerText = this.text;
      this.size = this._size;
      if (this._color != 0xffffff) {
        this.color = this._color;
      }
      this.shadow = this._shadow;
      if (this._border) {
        this.border = this._border;
      }
      if (this._font !== "") {
        this.font = this._font;
      }
      if (this._x !== undefined) {
        this.x = this._x;
      }
      if (this._y !== undefined) {
        this.y = this._y;
      }
      if (this._alpha !== 1 || this.parent.options.global.opacity < 1) {
        this.alpha = this._alpha;
      }
      if (this.motion.length > 0) {
        this.animate();
      }
    };
    Object.defineProperty(CoreComment.prototype, "x", {
      get: function get() {
        if (this._x === null || this._x === undefined) {
          if (this.align % 2 === 0) {
            this._x = this.dom.offsetLeft;
          } else {
            this._x = this.parent.width - this.dom.offsetLeft - this.width;
          }
        }
        if (!this.absolute) {
          return this._x / this.parent.width;
        }
        return this._x;
      },
      set: function set(x) {
        this._x = x;
        if (!this.absolute) {
          this._x *= this.parent.width;
        }
        if (this.align % 2 === 0) {
          this.dom.style.left = this._x + "px";
        } else {
          this.dom.style.right = this._x + "px";
        }
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "y", {
      get: function get() {
        if (this._y === null || this._y === undefined) {
          if (this.align < 2) {
            this._y = this.dom.offsetTop;
          } else {
            this._y = this.parent.height - this.dom.offsetTop - this.height;
          }
        }
        if (!this.absolute) {
          return this._y / this.parent.height;
        }
        return this._y;
      },
      set: function set(y) {
        this._y = y;
        if (!this.absolute) {
          this._y *= this.parent.height;
        }
        if (this.align < 2) {
          this.dom.style.top = this._y + "px";
        } else {
          this.dom.style.bottom = this._y + "px";
        }
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "bottom", {
      get: function get() {
        return this.y + this.height;
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "right", {
      get: function get() {
        return this.x + this.width;
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "width", {
      get: function get() {
        if (this._width === null || this._width === undefined) {
          this._width = this.dom.offsetWidth;
        }
        return this._width;
      },
      set: function set(w) {
        this._width = w;
        this.dom.style.width = this._width + "px";
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "height", {
      get: function get() {
        if (this._height === null || this._height === undefined) {
          this._height = this.dom.offsetHeight;
        }
        return this._height;
      },
      set: function set(h) {
        this._height = h;
        this.dom.style.height = this._height + "px";
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "size", {
      get: function get() {
        return this._size;
      },
      set: function set(s) {
        this._size = s;
        this.dom.style.fontSize = this._size + "px";
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "color", {
      get: function get() {
        return this._color;
      },
      set: function set(c) {
        this._color = c;
        var color = c.toString(16);
        color = color.length >= 6 ? color : new Array(6 - color.length + 1).join("0") + color;
        this.dom.style.color = "#" + color;
        if (this._color === 0) {
          this.dom.className = this.parent.options.global.className + " rshadow";
        }
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "alpha", {
      get: function get() {
        return this._alpha;
      },
      set: function set(a) {
        this._alpha = a;
        this.dom.style.opacity = Math.min(this._alpha, this.parent.options.global.opacity) + "";
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "border", {
      get: function get() {
        return this._border;
      },
      set: function set(b) {
        this._border = b;
        if (this._border) {
          this.dom.style.border = "1px solid #00ffff";
        } else {
          this.dom.style.border = "none";
        }
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "shadow", {
      get: function get() {
        return this._shadow;
      },
      set: function set(s) {
        this._shadow = s;
        if (!this._shadow) {
          this.dom.className = this.parent.options.global.className + " noshadow";
        }
      },
      enumerable: true,
      configurable: true
    });
    Object.defineProperty(CoreComment.prototype, "font", {
      get: function get() {
        return this._font;
      },
      set: function set(f) {
        this._font = f;
        if (this._font.length > 0) {
          this.dom.style.fontFamily = this._font;
        } else {
          this.dom.style.fontFamily = "";
        }
      },
      enumerable: true,
      configurable: true
    });
    CoreComment.prototype.time = function (time) {
      this.ttl -= time;
      if (this.ttl < 0) {
        this.ttl = 0;
      }
      if (this.movable) {
        this.update();
      }
      if (this.ttl <= 0) {
        this.finish();
      }
    };
    CoreComment.prototype.update = function () {
      this.animate();
    };
    CoreComment.prototype.invalidate = function () {
      this._x = null;
      this._y = null;
      this._width = null;
      this._height = null;
    };
    CoreComment.prototype._execMotion = function (currentMotion, time) {
      for (var prop in currentMotion) {
        if (currentMotion.hasOwnProperty(prop)) {
          var m = currentMotion[prop];
          this[prop] = m.easing(Math.min(Math.max(time - m.delay, 0), m.dur), m.from, m.to - m.from, m.dur);
        }
      }
    };
    CoreComment.prototype.animate = function () {
      if (this._alphaMotion) {
        this.alpha = (this.dur - this.ttl) * (this._alphaMotion["to"] - this._alphaMotion["from"]) / this.dur + this._alphaMotion["from"];
      }
      if (this.motion.length === 0) {
        return;
      }
      var ttl = Math.max(this.ttl, 0);
      var time = this.dur - ttl - this._motionStart[this._curMotion];
      this._execMotion(this.motion[this._curMotion], time);
      if (this.dur - ttl > this._motionEnd[this._curMotion]) {
        this._curMotion++;
        if (this._curMotion >= this.motion.length) {
          this._curMotion = this.motion.length - 1;
        }
        return;
      }
    };
    CoreComment.prototype.finish = function () {
      this.parent.finish(this);
    };
    CoreComment.prototype.toString = function () {
      return ["[", this.stime, "|", this.ttl, "/", this.dur, "]", "(", this.mode, ")", this.text].join("");
    };
    CoreComment.LINEAR = function (t, b, c, d) {
      return t * c / d + b;
    };
    return CoreComment;
  }();
  var ScrollComment = function (_super) {
    __extends(ScrollComment, _super);
    function ScrollComment(parent, data) {
      _super.call(this, parent, data);
      this.dur *= this.parent.options.scroll.scale;
      this.ttl *= this.parent.options.scroll.scale;
    }
    Object.defineProperty(ScrollComment.prototype, "alpha", {
      set: function set(a) {
        this._alpha = a;
        this.dom.style.opacity = Math.min(Math.min(this._alpha, this.parent.options.global.opacity), this.parent.options.scroll.opacity) + "";
      },
      enumerable: true,
      configurable: true
    });
    ScrollComment.prototype.init = function (recycle) {
      if (recycle === void 0) {
        recycle = null;
      }
      _super.prototype.init.call(this, recycle);
      this.x = this.parent.width;
      if (this.parent.options.scroll.opacity < 1) {
        this.alpha = this._alpha;
      }
      this.absolute = true;
    };
    ScrollComment.prototype.update = function () {
      this.x = this.ttl / this.dur * (this.parent.width + this.width) - this.width;
    };
    return ScrollComment;
  }(CoreComment);
  //# sourceMappingURL=Comment.js.map
  /** 
   * Comment Filters Module Simplified (only supports modifiers & types)
   * @license MIT
   * <AUTHOR> Chen
   */
  function CommentFilter() {
    this.modifiers = [];
    this.runtime = null;
    this.allowTypes = {
      "1": true,
      "4": true,
      "5": true,
      "6": true,
      "7": true,
      "8": true,
      "17": true
    };
    this.doModify = function (cmt) {
      for (var k = 0; k < this.modifiers.length; k++) {
        cmt = this.modifiers[k](cmt);
      }
      return cmt;
    };
    this.beforeSend = function (cmt) {
      return cmt;
    };
    this.doValidate = function (cmtData) {
      if (!this.allowTypes[cmtData.mode]) return false;
      return true;
    };
    this.addRule = function (rule) {};
    this.addModifier = function (f) {
      this.modifiers.push(f);
    };
    this.runtimeFilter = function (cmt) {
      if (this.runtime == null) return cmt;
      return this.runtime(cmt);
    };
    this.setRuntimeFilter = function (f) {
      this.runtime = f;
    };
  }
  
  /*!
   * Comment Core Library CommentManager
   * @license MIT
   * <AUTHOR> Chen
   *
   * Copyright (c) 2014 Jim Chen
   */
  var CommentManager = function () {
    var getRotMatrix = function getRotMatrix(yrot, zrot) {
      // Courtesy of @StarBrilliant, re-adapted to look better
      var DEG2RAD = Math.PI / 180;
      var yr = yrot * DEG2RAD;
      var zr = zrot * DEG2RAD;
      var COS = Math.cos;
      var SIN = Math.sin;
      var matrix = [COS(yr) * COS(zr), COS(yr) * SIN(zr), SIN(yr), 0, -SIN(zr), COS(zr), 0, 0, -SIN(yr) * COS(zr), -SIN(yr) * SIN(zr), COS(yr), 0, 0, 0, 0, 1];
      // CSS does not recognize scientific notation (e.g. 1e-6), truncating it.
      for (var i = 0; i < matrix.length; i++) {
        if (Math.abs(matrix[i]) < 0.000001) {
          matrix[i] = 0;
        }
      }
      return "matrix3d(" + matrix.join(",") + ")";
    };
  
    function CommentManager(stageObject) {
      var __timer = 0;
  
      this._listeners = {};
      this._lastPosition = 0;
  
      this.stage = stageObject;
      this.options = {
        global: {
          opacity: 1,
          scale: 1,
          className: "cmt"
        },
        scroll: {
          opacity: 1,
          scale: 1
        },
        limit: 0
      };
      this.timeline = [];
      this.runline = [];
      this.position = 0;
      this.limiter = 0;
      this.filter = null;
      this.csa = {
        scroll: new CommentSpaceAllocator(0, 0),
        top: new AnchorCommentSpaceAllocator(0, 0),
        bottom: new AnchorCommentSpaceAllocator(0, 0),
        reverse: new CommentSpaceAllocator(0, 0),
        scrollbtm: new CommentSpaceAllocator(0, 0)
      };
  
      /** Precompute the offset width **/
      this.width = this.stage.offsetWidth;
      this.height = this.stage.offsetHeight;
      this.startTimer = function () {
        if (__timer > 0) return;
        var lastTPos = new Date().getTime();
        var cmMgr = this;
        __timer = window.setInterval(function () {
          var elapsed = new Date().getTime() - lastTPos;
          lastTPos = new Date().getTime();
          cmMgr.onTimerEvent(elapsed, cmMgr);
        }, 10);
      };
      this.stopTimer = function () {
        window.clearInterval(__timer);
        __timer = 0;
      };
    }
  
    /** Public **/
    CommentManager.prototype.stop = function () {
      this.stopTimer();
    };
  
    CommentManager.prototype.start = function () {
      this.startTimer();
    };
  
    CommentManager.prototype.seek = function (time) {
      this.position = BinArray.bsearch(this.timeline, time, function (a, b) {
        if (a < b.stime) return -1;else if (a > b.stime) return 1;else return 0;
      });
    };
  
    CommentManager.prototype.validate = function (cmt) {
      if (cmt == null) return false;
      return this.filter.doValidate(cmt);
    };
  
    CommentManager.prototype.load = function (a) {
      this.timeline = a;
      this.timeline.sort(function (a, b) {
        if (a.stime > b.stime) return 2;else if (a.stime < b.stime) return -2;else {
          if (a.date > b.date) return 1;else if (a.date < b.date) return -1;else if (a.dbid != null && b.dbid != null) {
            if (a.dbid > b.dbid) return 1;else if (a.dbid < b.dbid) return -1;
            return 0;
          } else return 0;
        }
      });
      this.dispatchEvent("load");
    };
  
    CommentManager.prototype.insert = function (c) {
      var index = BinArray.binsert(this.timeline, c, function (a, b) {
        if (a.stime > b.stime) return 2;else if (a.stime < b.stime) return -2;else {
          if (a.date > b.date) return 1;else if (a.date < b.date) return -1;else if (a.dbid != null && b.dbid != null) {
            if (a.dbid > b.dbid) return 1;else if (a.dbid < b.dbid) return -1;
            return 0;
          } else return 0;
        }
      });
      if (index <= this.position) {
        this.position++;
      }
      this.dispatchEvent("insert");
    };
  
    CommentManager.prototype.clear = function () {
      while (this.runline.length > 0) {
        this.runline[0].finish();
      }
      this.dispatchEvent("clear");
    };
  
    CommentManager.prototype.setBounds = function () {
      this.width = this.stage.offsetWidth;
      this.height = this.stage.offsetHeight;
      this.dispatchEvent("resize");
      for (var comAlloc in this.csa) {
        this.csa[comAlloc].setBounds(this.width, this.height);
      }
      // Update 3d perspective
      this.stage.style.perspective = this.width * Math.tan(40 * Math.PI / 180) / 2 + "px";
      this.stage.style.webkitPerspective = this.width * Math.tan(40 * Math.PI / 180) / 2 + "px";
    };
    CommentManager.prototype.init = function () {
      this.setBounds();
      if (this.filter == null) {
        this.filter = new CommentFilter(); //Only create a filter if none exist
      }
    };
    CommentManager.prototype.time = function (time) {
      time = time - 1;
      if (this.position >= this.timeline.length || Math.abs(this._lastPosition - time) >= 2000) {
        this.seek(time);
        this._lastPosition = time;
        if (this.timeline.length <= this.position) {
          return;
        }
      } else {
        this._lastPosition = time;
      }
      for (; this.position < this.timeline.length; this.position++) {
        if (this.timeline[this.position]['stime'] <= time) {
          if (this.options.limit > 0 && this.runline.length > this.limiter) {
            continue; // Skip comments but still move the position pointer
          } else if (this.validate(this.timeline[this.position])) {
            this.send(this.timeline[this.position]);
          }
        } else {
          break;
        }
      }
    };
    CommentManager.prototype.rescale = function () {};
    CommentManager.prototype.send = function (data) {
      if (data.mode === 8) {
        console.log(data);
        if (this.scripting) {
          console.log(this.scripting.eval(data.code));
        }
        return;
      }
      if (this.filter != null) {
        data = this.filter.doModify(data);
        if (data == null) return;
      }
      if (data.mode === 1 || data.mode === 2 || data.mode === 6) {
        var cmt = new ScrollComment(this, data);
      } else {
        var cmt = new CoreComment(this, data);
      }
      switch (cmt.mode) {
        case 1:
          cmt.align = 0;break;
        case 2:
          cmt.align = 2;break;
        case 4:
          cmt.align = 2;break;
        case 5:
          cmt.align = 0;break;
        case 6:
          cmt.align = 1;break;
      }
      cmt.init();
      this.stage.appendChild(cmt.dom);
      switch (cmt.mode) {
        default:
        case 1:
          {
            this.csa.scroll.add(cmt);
          }break;
        case 2:
          {
            this.csa.scrollbtm.add(cmt);
          }break;
        case 4:
          {
            this.csa.bottom.add(cmt);
          }break;
        case 5:
          {
            this.csa.top.add(cmt);
          }break;
        case 6:
          {
            this.csa.reverse.add(cmt);
          }break;
        case 17:
        case 7:
          {
            if (data.rY !== 0 || data.rZ !== 0) {
              /** TODO: revise when browser manufacturers make up their mind on Transform APIs **/
              cmt.dom.style.transform = getRotMatrix(data.rY, data.rZ);
              cmt.dom.style.webkitTransform = getRotMatrix(data.rY, data.rZ);
              cmt.dom.style.OTransform = getRotMatrix(data.rY, data.rZ);
              cmt.dom.style.MozTransform = getRotMatrix(data.rY, data.rZ);
              cmt.dom.style.MSTransform = getRotMatrix(data.rY, data.rZ);
            }
          }break;
      }
      cmt.y = cmt.y;
      this.dispatchEvent("enterComment", cmt);
      this.runline.push(cmt);
    };
    CommentManager.prototype.sendComment = function (data) {
      console.log("CommentManager.sendComment is deprecated. Please use send instead");
      this.send(data); // Wrapper for Backwards Compatible APIs
    };
    CommentManager.prototype.finish = function (cmt) {
      this.dispatchEvent("exitComment", cmt);
      this.stage.removeChild(cmt.dom);
      var index = this.runline.indexOf(cmt);
      if (index >= 0) {
        this.runline.splice(index, 1);
      }
      switch (cmt.mode) {
        default:
        case 1:
          {
            this.csa.scroll.remove(cmt);
          }break;
        case 2:
          {
            this.csa.scrollbtm.remove(cmt);
          }break;
        case 4:
          {
            this.csa.bottom.remove(cmt);
          }break;
        case 5:
          {
            this.csa.top.remove(cmt);
          }break;
        case 6:
          {
            this.csa.reverse.remove(cmt);
          }break;
        case 7:
          break;
      }
    };
    CommentManager.prototype.addEventListener = function (event, listener) {
      if (typeof this._listeners[event] !== "undefined") {
        this._listeners[event].push(listener);
      } else {
        this._listeners[event] = [listener];
      }
    };
    CommentManager.prototype.dispatchEvent = function (event, data) {
      if (typeof this._listeners[event] !== "undefined") {
        for (var i = 0; i < this._listeners[event].length; i++) {
          try {
            this._listeners[event][i](data);
          } catch (e) {
            console.err(e.stack);
          }
        }
      }
    };
    /** Static Functions **/
    CommentManager.prototype.onTimerEvent = function (timePassed, cmObj) {
      for (var i = 0; i < cmObj.runline.length; i++) {
        var cmt = cmObj.runline[i];
        if (cmt.hold) {
          continue;
        }
        cmt.time(timePassed);
      }
    };
    return CommentManager;
  }();
  
  /** 
   * AcFun Format Parser
   * @license MIT License
   * An alternative format comment parser
   */
  function AcfunParser(jsond) {
    var list = [];
    try {
      var jsondt = JSON.parse(jsond);
    } catch (e) {
      console.log('Error: Could not parse json list!');
      return [];
    }
    for (var i = 0; i < jsondt.length; i++) {
      //Read each comment and generate a correct comment object
      var data = {};
      var xc = jsondt[i]['c'].split(',');
      if (xc.length > 0) {
        data.stime = parseFloat(xc[0]) * 1000;
        data.color = parseInt(xc[1]);
        data.mode = parseInt(xc[2]);
        data.size = parseInt(xc[3]);
        data.hash = xc[4];
        data.date = parseInt(xc[5]);
        data.position = "absolute";
        if (data.mode != 7) {
          data.text = jsondt[i].m.replace(/(\/n|\\n|\n|\r\n|\\r)/g, "\n");
          data.text = data.text.replace(/\r/g, "\n");
          data.text = data.text.replace(/\s/g, '\xA0');
        } else {
          data.text = jsondt[i].m;
        }
        if (data.mode == 7) {
          //High level positioned dm
          try {
            var x = JSON.parse(data.text);
          } catch (e) {
            console.log('[Err] Error parsing internal data for comment');
            console.log('[Dbg] ' + data.text);
            continue;
          }
          data.position = "relative";
          data.text = x.n; /*.replace(/\r/g,"\n");*/
          data.text = data.text.replace(/\ /g, '\xA0');
          if (x.a != null) {
            data.opacity = x.a;
          } else {
            data.opacity = 1;
          }
          if (x.p != null) {
            data.x = x.p.x / 1000; // relative position
            data.y = x.p.y / 1000;
          } else {
            data.x = 0;
            data.y = 0;
          }
          data.shadow = x.b;
          data.dur = 4000;
          if (x.l != null) data.moveDelay = x.l * 1000;
          if (x.z != null && x.z.length > 0) {
            data.movable = true;
            data.motion = [];
            var moveDuration = 0;
            var last = { x: data.x, y: data.y, alpha: data.opacity, color: data.color };
            for (var m = 0; m < x.z.length; m++) {
              var dur = x.z[m].l != null ? x.z[m].l * 1000 : 500;
              moveDuration += dur;
              var motion = {
                x: { from: last.x, to: x.z[m].x / 1000, dur: dur, delay: 0 },
                y: { from: last.y, to: x.z[m].y / 1000, dur: dur, delay: 0 }
              };
              last.x = motion.x.to;
              last.y = motion.y.to;
              if (x.z[m].t !== last.alpha) {
                motion.alpha = { from: last.alpha, to: x.z[m].t, dur: dur, delay: 0 };
                last.alpha = motion.alpha.to;
              }
              if (x.z[m].c != null && x.z[m].c !== last.color) {
                motion.color = { from: last.color, to: x.z[m].c, dur: dur, delay: 0 };
                last.color = motion.color.to;
              }
              data.motion.push(motion);
            }
            data.dur = moveDuration + (data.moveDelay ? data.moveDelay : 0);
          }
          if (x.r != null && x.k != null) {
            data.rX = x.r;
            data.rY = x.k;
          }
        }
        list.push(data);
      }
    }
    return list;
  }
  
  /** 
   * Bilibili Format Parser
   * @license MIT License
   * Takes in an XMLDoc/LooseXMLDoc and parses that into a Generic Comment List
   **/
  function BilibiliParser(xmlDoc, text, warn) {
    function format(string) {
      // Format the comment text to be JSON Valid.
      return string.replace(/\t/, "\\t");
    }
  
    if (xmlDoc !== null) {
      var elems = xmlDoc.getElementsByTagName('d');
    } else {
      if (!document || !document.createElement) {
        // Maybe we are in a restricted context? Bail.
        return [];
      }
      if (warn) {
        if (!confirm("XML Parse Error. \n Allow tag soup parsing?\n[WARNING: This is unsafe.]")) {
          return [];
        }
      } else {
        // TODO: Make this safer in the future
        text = text.replace(new RegExp("</([^d])", "g"), "</disabled $1");
        text = text.replace(new RegExp("</(\S{2,})", "g"), "</disabled $1");
        text = text.replace(new RegExp("<([^d/]\W*?)", "g"), "<disabled $1");
        text = text.replace(new RegExp("<([^/ ]{2,}\W*?)", "g"), "<disabled $1");
      }
      var tmp = document.createElement("div");
      tmp.innerHTML = text;
      var elems = tmp.getElementsByTagName('d');
    }
  
    var tlist = [];
    for (var i = 0; i < elems.length; i++) {
      if (elems[i].getAttribute('p') != null) {
        var opt = elems[i].getAttribute('p').split(',');
        if (!elems[i].childNodes[0]) continue;
        var text = elems[i].childNodes[0].nodeValue;
        var obj = {};
        obj.stime = Math.round(parseFloat(opt[0]) * 1000);
        obj.size = parseInt(opt[2]);
        obj.color = parseInt(opt[3]);
        obj.mode = parseInt(opt[1]);
        obj.date = parseInt(opt[4]);
        obj.pool = parseInt(opt[5]);
        obj.position = "absolute";
        if (opt[7] != null) obj.dbid = parseInt(opt[7]);
        obj.hash = opt[6];
        obj.border = false;
        if (obj.mode < 7) {
          obj.text = text.replace(/(\/n|\\n|\n|\r\n)/g, "\n");
        } else {
          if (obj.mode == 7) {
            try {
              adv = JSON.parse(format(text));
              obj.shadow = true;
              obj.x = parseFloat(adv[0]);
              obj.y = parseFloat(adv[1]);
              if (Math.floor(obj.x) < obj.x || Math.floor(obj.y) < obj.y) {
                obj.position = "relative";
              }
              obj.text = adv[4].replace(/(\/n|\\n|\n|\r\n)/g, "\n");
              obj.rZ = 0;
              obj.rY = 0;
              if (adv.length >= 7) {
                obj.rZ = parseInt(adv[5], 10);
                obj.rY = parseInt(adv[6], 10);
              }
              obj.motion = [];
              obj.movable = false;
              if (adv.length >= 11) {
                obj.movable = true;
                var singleStepDur = 500;
                var motion = {
                  x: { from: obj.x, to: parseFloat(adv[7]), dur: singleStepDur, delay: 0 },
                  y: { from: obj.y, to: parseFloat(adv[8]), dur: singleStepDur, delay: 0 }
                };
                if (adv[9] !== '') {
                  singleStepDur = parseInt(adv[9], 10);
                  motion.x.dur = singleStepDur;
                  motion.y.dur = singleStepDur;
                }
                if (adv[10] !== '') {
                  motion.x.delay = parseInt(adv[10], 10);
                  motion.y.delay = parseInt(adv[10], 10);
                }
                if (adv.length > 11) {
                  obj.shadow = adv[11];
                  if (obj.shadow === "true") {
                    obj.shadow = true;
                  }
                  if (obj.shadow === "false") {
                    obj.shadow = false;
                  }
                  if (adv[12] != null) {
                    obj.font = adv[12];
                  }
                  if (adv.length > 14) {
                    // Support for Bilibili Advanced Paths
                    if (obj.position === "relative") {
                      console.log("Cannot mix relative and absolute positioning");
                      obj.position = "absolute";
                    }
                    var path = adv[14];
                    var lastPoint = { x: motion.x.from, y: motion.y.from };
                    var pathMotion = [];
                    var regex = new RegExp("([a-zA-Z])\\s*(\\d+)[, ](\\d+)", "g");
                    var counts = path.split(/[a-zA-Z]/).length - 1;
                    var m = regex.exec(path);
                    while (m !== null) {
                      switch (m[1]) {
                        case "M":
                          {
                            lastPoint.x = parseInt(m[2], 10);
                            lastPoint.y = parseInt(m[3], 10);
                          }break;
                        case "L":
                          {
                            pathMotion.push({
                              "x": { "from": lastPoint.x, "to": parseInt(m[2], 10), "dur": singleStepDur / counts, "delay": 0 },
                              "y": { "from": lastPoint.y, "to": parseInt(m[3], 10), "dur": singleStepDur / counts, "delay": 0 }
                            });
                            lastPoint.x = parseInt(m[2], 10);
                            lastPoint.y = parseInt(m[3], 10);
                          }break;
                      }
                      m = regex.exec(path);
                    }
                    motion = null;
                    obj.motion = pathMotion;
                  }
                }
                if (motion !== null) {
                  obj.motion.push(motion);
                }
              }
              obj.dur = 2500;
              if (adv[3] < 12) {
                obj.dur = adv[3] * 1000;
              }
              var tmp = adv[2].split('-');
              if (tmp != null && tmp.length > 1) {
                var alphaFrom = parseFloat(tmp[0]);
                var alphaTo = parseFloat(tmp[1]);
                obj.opacity = alphaFrom;
                if (alphaFrom !== alphaTo) {
                  obj.alpha = { from: alphaFrom, to: alphaTo };
                }
              }
            } catch (e) {
              console.log('[Err] Error occurred in JSON parsing');
              console.log('[Dbg] ' + text);
            }
          } else if (obj.mode == 8) {
            obj.code = text; //Code comments are special
          }
        }
        if (obj.text != null) obj.text = obj.text.replace(/\u25a0/g, '\u2588');
        tlist.push(obj);
      }
    }
    return tlist;
  }
  
  exports.CommentManager = CommentManager;
  
  /***/ }),
  /* 15 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(16);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 16 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".abp {\n  position: relative; }\n\n.abp .container {\n  -webkit-transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);\n  transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);\n  position: absolute;\n  display: block;\n  overflow: hidden;\n  margin: 0;\n  border: 0;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: 9999;\n  touch-callout: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none; }\n\n.abp .container .cmt {\n  -webkit-transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);\n  transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);\n  -webkit-transform-origin: 0% 0%;\n  -ms-transform-origin: 0% 0%;\n  transform-origin: 0% 0%;\n  position: absolute;\n  padding: 3px 0 0 0;\n  margin: 0;\n  color: #fff;\n  font-family: SimHei, SimSun, Heiti, \"MS Mincho\", \"Meiryo\", \"Microsoft YaHei\", monospace;\n  font-size: 25px;\n  text-decoration: none;\n  text-shadow: -1px 0 black, 0 1px black, 1px 0 black, 0 -1px black;\n  -webkit-text-size-adjust: none;\n  -ms-text-size-adjust: none;\n  text-size-adjust: none;\n  line-height: 100%;\n  letter-spacing: 0;\n  word-break: keep-all;\n  white-space: pre; }\n\n.abp .container .cmt.noshadow {\n  text-shadow: none; }\n\n.abp .container .cmt.rshadow {\n  text-shadow: -1px 0 white, 0 1px white, 1px 0 white, 0 -1px white; }\n\n/** Aliases for Chinese named fonts because they don't work on *nix **/\n@font-face {\n  font-family: \"\\9ED1\\4F53\";\n  src: local(\"SimHei\"); }\n\n@font-face {\n  font-family: \"\\5B8B\\4F53\";\n  src: local(\"SimSun\"); }\n\n@font-face {\n  font-family: \"\\534E\\6587\\6977\\4F53\";\n  src: local(\"SimKai\"); }\n\n@font-face {\n  font-family: \"\\5E7C\\5706\";\n  src: local(\"YouYuan\"); }\n\n@font-face {\n  font-family: \"\\5FAE\\8F6F\\96C5\\9ED1\";\n  src: local(\"Microsoft YaHei\"); }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 17 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(18);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.BulletScreenComponent = _index2.default;
  
  /***/ }),
  /* 18 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(19);
  
  var _index2 = _interopRequireDefault(_index);
  
  __webpack_require__(20);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 跑马灯组件
   */
  var BulletScreenComponent = function () {
    /**
     * @constructor 跑马灯构造函数
     * @param {String text 跑马灯内容}
     * @param {Object style 跑马灯样式}
     * param {String bulletPosition 跑马灯所在的位置, 可能的值 'top', 'bottom' , 'random, 默认为 'random'}
     */
    function BulletScreenComponent(text, style) {
      var bulletPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'random';
  
      _classCallCheck(this, BulletScreenComponent);
  
      this.text = text;
      this.style = style || { fontSize: '14px', color: '#fff' };
      this.html = (0, _utils.parseDom)(_index2.default);
      // this.html.style.animationPlayState = 'paused'
      this.bulletPosition = bulletPosition;
    }
  
    _createClass(BulletScreenComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        this.html.innerText = this.text;
        el.appendChild(this.html);
      }
    }, {
      key: 'ready',
      value: function ready(player, e) {
        var _this = this;
  
        console.log(player.getOptions());
        if (player.getOptions().autoplay === false) {
          this.html.style.animationPlayState = 'paused';
        }
        Object.keys(this.style).forEach(function (key) {
          return _this.html.style[key] = _this.style[key];
        });
        //字体高度
        var bulletHeight = this.html.offsetHeight;
        //播放器高度
        var playerHeight = parseInt(getComputedStyle(player.tag).height.replace('px', ''));
        //字体距离播放器底部最大高度
        var maxHeight = playerHeight - bulletHeight;
  
        if (this.bulletPosition === 'bottom') {
          this.html.style.bottom = 0;
        } else {
          var top = this.bulletPosition === 'top' ? 0 : this.randomTop(maxHeight);
          this.html.style.top = top;
        }
  
        if (this.bulletPosition === 'random') {
          this.html.addEventListener('animationiteration', function () {
            _this.html.style.top = _this.randomTop(maxHeight);
          });
        }
      }
    }, {
      key: 'playing',
      value: function playing(player, e) {
        console.log('playering');
        this.html.style.animationPlayState = 'running';
      }
    }, {
      key: 'timeupdate',
      value: function timeupdate(player, timeStamp) {
        var el = player.el();
        var componentEl = el.querySelector('.bullet-screen');
        if (!componentEl) {
          el.appendChild(this.html);
        } else {
          if (componentEl.className !== 'bullet-screen') {
            componentEl.className = 'bullet-screen';
          }
          var cssStyles = getComputedStyle(componentEl);
          var display = cssStyles.getPropertyValue('display');
          var opacity = cssStyles.getPropertyValue('opacity');
          var visibility = cssStyles.getPropertyValue('visibility');
          var currentwords = this.text;
          var modifywords = componentEl.innerText;
          if (display === 'none') {
            componentEl.style.setProperty('display', 'block');
          }
          if (opacity !== '1') {
            componentEl.style.setProperty('opacity', '1');
          }
          if (visibility === 'hidden') {
            componentEl.style.setProperty('visibility', 'visible');
          }
          if (currentwords != modifywords) {
            componentEl.innerText = currentwords;
          }
        }
      }
    }, {
      key: 'pause',
      value: function pause(player, e) {
        console.log('pause');
        this.html.style.animationPlayState = 'paused';
      }
    }, {
      key: 'randomTop',
      value: function randomTop(max) {
        return Math.floor(Math.random() * max) + 'px';
      }
    }]);
  
    return BulletScreenComponent;
  }();
  
  exports.default = BulletScreenComponent;
  
  /***/ }),
  /* 19 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"bullet-screen paused\"></div>";
  
  /***/ }),
  /* 20 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(21);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 21 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".bullet-screen {\n  position: absolute;\n  white-space: nowrap;\n  animation: bullet 10s linear infinite; }\n\n@keyframes bullet {\n  from {\n    left: 100%; }\n  to {\n    left: 0%;\n    transform: translateX(-100%); } }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 22 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(23);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.CaptionComponent = _index2.default;
  
  /***/ }),
  /* 23 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(24);
  
  var _index2 = _interopRequireDefault(_index);
  
  var _captionModal = __webpack_require__(25);
  
  var _captionModal2 = _interopRequireDefault(_captionModal);
  
  __webpack_require__(26);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 字幕组件
   */
  var CaptionComponent = function () {
    /**
     * @constructor 字幕组件构造函数
     */
  
    function CaptionComponent() {
      _classCallCheck(this, CaptionComponent);
  
      this.captionList = null;
      this.html = (0, _utils.parseDom)(_index2.default);
      this.modalHtml = (0, _utils.parseDom)(_captionModal2.default);
      this.hasCreated = false;
      this.definition = '';
    }
  
    _createClass(CaptionComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var _this = this;
  
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        //this.html.querySelector('.current-caption').innerText = this.isEn ? 'Subtitle language' : '字幕语言'
        this.modalHtml.querySelector('.switchimg').innerText = this.isEn ? 'Switching to you for' : '字幕切换到';
        this.modalHtml.querySelector('.switchimg').style.display = 'none';
        var eleControlbar = el.querySelector('.prism-controlbar');
        eleControlbar.appendChild(this.html);
        el.appendChild(this.modalHtml);
        player.on('textTrackReady', function (_ref) {
          var paramData = _ref.paramData;
  
          _this.captionList = paramData;
          var lis_ele = paramData.map(function (v) {
            return '<li data-def="' + v.value + '">' + v.text + '</li>';
          });
          _this.html.querySelector('.caption-list').innerHTML = '<li style="background:rgba(88,87,86,.5);color:#fff">\u5B57\u5E55</li>' + lis_ele.join('');
        });
        var currentCaptionEle = this.html.querySelector('.current-caption');
        var captionListEle = this.html.querySelector('.caption-list');
  
        if (this.hasCreated == false && this.definition) {
          var li_target = captionListEle.querySelector('li[data-def="' + this.definition + '"]');
          li_target.className = 'current';
        }
        this.hasCreated = true;
  
        var timeId = null;
        currentCaptionEle.onclick = function () {
          captionListEle.style.display = 'block';
        };
  
        currentCaptionEle.onmouseleave = function () {
          timeId = setTimeout(function () {
            captionListEle.style.display = 'none';
          }, 100);
        };
  
        captionListEle.onmouseenter = function () {
          clearTimeout(timeId);
        };
        captionListEle.onmouseleave = function () {
          captionListEle.style.display = 'none';
          // this.html.querySelector('.caption-modal').style.display = 'none'
          _this.modalHtml.style.display = 'none';
        };
  
        captionListEle.onclick = function (_ref2) {
          var target = _ref2.target;
  
          var definition = target.dataset.def;
          if (definition) {
            if (target.className !== 'current') {
              var caption = Array.isArray(_this.captionList) ? _this.captionList.filter(function (v) {
                return v.value === definition;
              })[0] : _this.captionList.find(function (v) {
                return v.value === definition;
              });
  
              player._ccService.switch(caption.value);
              _this.setCurrentCaption(caption.text, caption.value);
              _this.modalHtml.style.display = 'block';
              _this.modalHtml.querySelector('.switchimg').style.display = 'block';
              _this.modalHtml.querySelector('span.current-caption-tag').innerText = caption.text;
            }
          }
        };
      }
    }, {
      key: 'setCurrentCaption',
      value: function setCurrentCaption(caption, def) {
        var currentCaption = this.html.querySelector('.current-caption');
        currentCaption.innerText = caption;
        currentCaption.dataset.def = def;
        this.definition = def;
  
        var captionListEle = this.html.querySelector('.caption-list');
        var currentEle = captionListEle.querySelector('.current');
        if (currentEle) {
          currentEle.className = '';
        }
        //let li_target = captionListEle.querySelector(`li[data-def="${def}"]`)
        var lis = captionListEle.querySelectorAll('li');
        lis.forEach(function (element) {
          var text = element.innerText;
          if (text === caption) {
            element.className = 'current';
          }
        });
        if (lis) {
          lis.className = 'current';
        }
      }
    }, {
      key: 'created',
      value: function created(player) {}
    }, {
      key: 'ready',
      value: function ready(player) {
        this.modalHtml.style.display = 'none';
        // 隐藏设置里面的倍速播放
        var settingEle = document.querySelector('.prism-setting-item.prism-setting-cc');
        if (settingEle) {
          settingEle.classList.add('player-hidden');
        }
      }
    }]);
  
    return CaptionComponent;
  }();
  
  exports.default = CaptionComponent;
  
  /***/ }),
  /* 24 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"caption-components\">\n    <div class=\"current-caption\" data-ref=\"\"></div>\n    <ul class=\"caption-list\">\n    </ul>\n  </div>";
  
  /***/ }),
  /* 25 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"caption-modal prism-info-display prism-info-left-bottom\">\n  <span class=\"switchimg\"></span> <span class=\"current-caption-tag\"></span>\n</div>";
  
  /***/ }),
  /* 26 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(27);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 27 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".player-hidden {\n  display: none !important; }\n\n.caption-components {\n  float: right;\n  color: #fff;\n  height: 35px;\n  position: relative;\n  box-sizing: border-box;\n  margin-top: 5px; }\n\n.current-caption {\n  display: flex;\n  height: 100%;\n  align-items: center;\n  justify-content: center;\n  width: 100px;\n  cursor: pointer; }\n\n.caption-list {\n  position: absolute;\n  bottom: 46px;\n  display: none;\n  padding: 0;\n  margin: 0;\n  list-style: none;\n  height: 150px;\n  width: 100px;\n  overflow-y: scroll; }\n  .caption-list li {\n    text-align: center;\n    width: 100px;\n    line-height: 30px;\n    background-color: rgba(0, 0, 0, 0.6);\n    cursor: pointer; }\n    .caption-list li.current {\n      color: #00c1de; }\n    .caption-list li + li {\n      border-top: 1px solid rgba(78, 78, 78, 0.3); }\n    .caption-list li:hover {\n      background-color: rgba(0, 0, 0, 0.5); }\n\n@media (max-width: 768px) {\n  .current-caption {\n    width: 40px; }\n  .caption-list li {\n    width: 43px; } }\n\n.prism-player .prism-info-display .switchimg {\n  color: #fff; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 28 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(29);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.ManyVideoADComponent = _index2.default;
  
  /***/ }),
  /* 29 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(30);
  
  var _index2 = _interopRequireDefault(_index);
  
  __webpack_require__(31);
  
  var _utils = __webpack_require__(2);
  
  var _mbIndex = __webpack_require__(33);
  
  var _mbIndex2 = _interopRequireDefault(_mbIndex);
  
  var _currentDevice = __webpack_require__(3);
  
  var _currentDevice2 = _interopRequireDefault(_currentDevice);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /*
   * PC 多视频广告组件
   */
  var ManyVideoAdComponent = function () {
    /** 
     * @constructor PC 多视频广告的构造函数
     * @param adVideoSource {@String 广告视频的视频地址 必须参数}
     * @param adLink {@String 广告视频的链接地址 必须参数}
     * @param adCloseFunction {@Function 关闭广告的点击事件处理函数, 可选参数, 不传则默认关闭广告视频}
     * @param closeText {@String 关闭广告的文字内容, 可选参数, 不传则默认为 '关闭广告'}
     */
  
    function ManyVideoAdComponent(adVideoSource, adCloseFunction) {
      var closeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '关闭广告';
  
      _classCallCheck(this, ManyVideoAdComponent);
  
      this.adVideoSource = adVideoSource;
      this.html = (0, _utils.parseDom)(_index2.default);
      this.adInterval = null;
      this.adCloseFunction = adCloseFunction;
      this.html.querySelector('.many-video-ad-close-text').innerText = closeText;
      this.adDuration = null; // 视频广告的时长, 用于倒计时, 
      this.player = null;
      this.indexVideo = 1; //给广告视频标号
    }
  
    _createClass(ManyVideoAdComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        this.html.querySelector('.many-video-ad-detail').innerText = this.isEn ? 'For more information' : '查看广告详情';
        this.html.querySelector('.limit').innerText = this.isEn ? 'Your browser limits autoplay' : '您的浏览器限制';
        this.html.querySelector('.manual').innerText = this.isEn ? 'Please Click' : '自动播放请点击';
        el.appendChild(this.html);
      }
    }, {
      key: 'created',
      value: function created(player) {
        //添加video
        var videos = this.adVideoSource.map(function (url, index) {
          return '<video id="many-video-ad-content' + (index + 1) + '" style="' + (index === 0 ? 'display: block' : 'display:none') + ';width:100%;height:100%" x5-video-player-type="h5" x5-video-player-fullscreen="false" src="' + url.adVideo + '"></video>';
        });
        this.html.querySelector('.videos').innerHTML = videos.join(' ');
  
        this.indexVideo = Number(this.indexVideo);
        var indexVideo_ = this.indexVideo;
        var adVideoSource_ = this.adVideoSource;
        var manyVideoAd_ele = this.html.querySelector('#many-video-ad-content' + this.indexVideo);
        var videoAdDetak = this.html.querySelector('.many-video-ad-detail');
        var adDuration_ele = this.html.querySelector('#many-video-ad-duration');
        var self = this;
        function getManyVideoAdTime() {
          manyVideoAd_ele.removeEventListener('canplay', getManyVideoAdTime);
          videoAdDetak.href = adVideoSource_[indexVideo_ - 1].adVideoLink;
          manyVideoAd_ele.play().then(function () {
            var duration = Math.ceil(manyVideoAd_ele.duration);
            adDuration_ele.innerText = duration;
            self.setAdInterval();
          }).catch(function (err) {
            self.html.querySelector('.autoplay-many-video-ad').style.display = 'block';
            self.html.querySelector('.icon-player-play').onclick = function () {
              self.playManyVideoAd();
              self.html.querySelector('.autoplay-many-video-ad').style.display = 'none';
            };
          });
        }
        //对浏览器会限制，有时会自动播放失败的判断
        var promise = manyVideoAd_ele.play();
        if (promise !== undefined) {
          promise.then(function () {
            manyVideoAd_ele.play(); //播放广告
          }).catch(function (error) {
            document.querySelector('.autoplay-many-video-ad').style.display = 'block';
            manyVideoAd_ele.oncanplay = function () {
              var ad_time = Math.ceil(manyVideoAd_ele.duration);
              document.querySelector('#many-video-ad-duration').innerText = ad_time;
            };
          });
        }
        manyVideoAd_ele.addEventListener('canplay', getManyVideoAdTime);
      }
    }, {
      key: 'ready',
      value: function ready(player, e) {
        var _this = this;
  
        this.indexVideo = Number(this.indexVideo);
        var manyVideoAd_ele = this.html.querySelector('#many-video-ad-content' + this.indexVideo);
        var self = this;
        manyVideoAd_ele.addEventListener('ended', function (event) {
          // self.html.querySelector('.many-video-ad-detail').href=self.adVideoSource[self.indexVideo].adVideoLink
          var flag = self.playNext(self);
          if (flag == '-1') {
            player.play();
          }
        });
        this.html.querySelector('.many-video-ad-close label').onclick = function () {
          if (typeof _this.adCloseFunction === 'function') {
            _this.adCloseFunction(_this);
          } else {
            _this.closeManyVideoAd();
          }
        };
      }
  
      // 视频广告倒计时
  
    }, {
      key: 'setAdInterval',
      value: function setAdInterval(index) {
        var _this2 = this;
  
        var adDuration_ele = this.html.querySelector('#many-video-ad-duration');
        var manyVideoAd_ele = this.html.querySelector('#many-video-ad-content' + this.indexVideo);
        this.adInterval = setInterval(function () {
          var duration = Math.ceil(manyVideoAd_ele.duration);
          var currentTime = Math.ceil(manyVideoAd_ele.currentTime);
          var time = duration - currentTime;
          adDuration_ele.innerText = time;
          if (time == 1) {
            clearInterval(_this2.adInterval);
          }
        }, 1000);
      }
  
      // 暂停视频广告 
  
    }, {
      key: 'pauseManyVideoAd',
      value: function pauseManyVideoAd() {
        this.clearAdInterval();
        this.html.querySelector('#many-video-ad-content' + this.indexVideo).pause();
      }
  
      // 播放视频广告
  
    }, {
      key: 'playManyVideoAd',
      value: function playManyVideoAd() {
        this.setAdInterval();
        this.html.querySelector('#many-video-ad-content' + this.indexVideo).play();
      }
  
      // 清除视频广告倒计时
  
    }, {
      key: 'clearAdInterval',
      value: function clearAdInterval() {
        this.adInterval !== null && clearInterval(this.adInterval);
        this.adInterval = null;
      }
  
      // 关闭当前广告并播放下一个视频
  
    }, {
      key: 'playNext',
      value: function playNext(self) {
        if (self.indexVideo >= self.adVideoSource.length) {
          self.html.parentNode.removeChild(self.html);
          return -1;
        }
        this.indexVideo = Number(this.indexVideo);
        if (document.getElementById('many-video-ad-content' + this.indexVideo) != null) {
          document.getElementById('many-video-ad-content' + this.indexVideo).remove();
        }
  
        var indexVideo_ = this.indexVideo + 1;
        self.html.querySelector('.many-video-ad-detail').href = self.adVideoSource[self.indexVideo].adVideoLink;
        var manyVideoAd_ele = this.html.querySelector('#many-video-ad-content' + indexVideo_);
        var adDuration_ele = this.html.querySelector('#many-video-ad-duration');
        manyVideoAd_ele.style.display = 'block';
        if (this.adVideoSource.length >= this.indexVideo) {
          this.indexVideo = this.indexVideo + 1;
        }
  
        var duration = Math.ceil(manyVideoAd_ele.duration);
        adDuration_ele.innerText = duration;
        self.setAdInterval();
        manyVideoAd_ele.play();
  
        manyVideoAd_ele.addEventListener('ended', function (event) {
          var flag = self.playNext(self);
          //广告结束之后自动播放主视频
          if (flag == '-1') {
            document.getElementById(player._options.id).getElementsByTagName('video')[0].play();
          }
        });
      }
  
      // 关闭视频广告
  
    }, {
      key: 'closeManyVideoAd',
      value: function closeManyVideoAd() {
        this.clearAdInterval();
        this.html.parentNode.removeChild(this.html);
        this.html = null;
        //广告结束之后自动播放主视频
        document.getElementById(player._options.id).getElementsByTagName('video')[0].play();
      }
    }]);
  
    return ManyVideoAdComponent;
  }();
  
  /**
   * 移动端多视频广告组件
   */
  
  
  var MbManyVideoAdComponent = function () {
    /**
     * @constructor 移动端多视频广告组件
     * @param {String adVideoSource 视频广告播放地址}
     * @param {String adLink 广告链接}
     * @param {Function adCloseFunction 关闭广告按钮点击出发的函数}
     * @param {String closeText 可选参数 关闭按钮中的文字, 默认为 '关闭广告'}
     */
    function MbManyVideoAdComponent(adVideoSource, adCloseFunction) {
      var closeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '关闭广告';
  
      _classCallCheck(this, MbManyVideoAdComponent);
  
      this.adVideoSource = adVideoSource;
      this.html = (0, _utils.parseDom)(_mbIndex2.default);
      this.adInterval = null;
      this.adCloseFunction = adCloseFunction;
      this.html.querySelector('.many-video-ad-close-text').innerText = closeText;
      this.adDuration = null; // 视频广告的时长, 用于倒计时, 
      this.player = null;
      this.indexVideo = 1; //给广告视频标号
    }
  
    _createClass(MbManyVideoAdComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var _this3 = this;
  
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        this.html.querySelector('.many-video-ad-detail').innerText = this.isEn ? 'For more information' : '查看广告详情';
        this.html.querySelector('.limit').innerText = this.isEn ? 'Your browser limits autoplay' : '您的浏览器限制';
        this.html.querySelector('.manual').innerText = this.isEn ? 'Please Click' : '自动播放请点击';
        el.appendChild(this.html);
        el.querySelector('.videosmb');
        el.querySelector('.videosmb').setAttribute('preload', 'load');
  
        // 隐藏 controlbar 
        var controlBar = el.querySelector('.prism-controlbar');
        controlBar.className = controlBar.className + ' controlbar-element-hidden';
  
        this.html.querySelector('.icon-player-play').onclick = function () {
          _this3.playManyVideoAd();
          _this3.html.querySelector('.autoplay-many-video-ad').style.display = 'none';
        };
      }
    }, {
      key: 'created',
      value: function created(player) {
        //添加video
        var videos = this.adVideoSource.map(function (url, index) {
          return '<video id="many-video-ad-content' + (index + 1) + '" style="' + (index === 0 ? 'display: block' : 'display:none') + ';width:100%;height:100%" x5-video-player-type="h5" x5-video-player-fullscreen="false" src="' + url.adVideo + '"></video>';
        });
        this.html.querySelector('.videosmb').innerHTML = videos.join(' ');
  
        this.indexVideo = Number(this.indexVideo);
        var indexVideo_ = this.indexVideo;
        var adVideoSource_ = this.adVideoSource;
        var manyVideoAd_ele = this.html.querySelector('#many-video-ad-content' + this.indexVideo);
        var videoAdDetak = this.html.querySelector('.many-video-ad-detail');
        var self = this;
        function getManyVideoAdTime() {
          manyVideoAd_ele.removeEventListener('canplay', getManyVideoAdTime);
          videoAdDetak.href = adVideoSource_[indexVideo_ - 1].adVideoLink;
          self.html.querySelector('#many-video-ad-duration').innerText = Math.ceil(manyVideoAd_ele.duration);
        }
        manyVideoAd_ele.addEventListener('canplay', getManyVideoAdTime);
      }
    }, {
      key: 'ready',
      value: function ready(player) {
        var _this4 = this;
  
        this.indexVideo = Number(this.indexVideo);
        var manyVideoAd_ele = this.html.querySelector('#many-video-ad-content' + this.indexVideo);
        var self = this;
        manyVideoAd_ele.addEventListener('ended', function (event) {
          var flag = self.playNext(self);
          if (flag == '-1') {
            player.play();
            var controlBar = document.querySelector('.prism-controlbar');
            controlBar.className = controlBar.className.replace(' controlbar-element-hidden', '');
          }
        });
        this.html.querySelector('.many-video-ad-close label').onclick = function () {
          if (typeof _this4.adCloseFunction === 'function') {
            _this4.adCloseFunction(_this4);
          } else {
            _this4.closeManyVideoAd();
          }
        };
      }
      // 视频广告倒计时
  
    }, {
      key: 'setAdInterval',
      value: function setAdInterval() {
        var _this5 = this;
  
        var adDuration_ele = this.html.querySelector('#many-video-ad-duration');
        var manyVideoAd_ele = this.html.querySelector('#many-video-ad-content' + this.indexVideo);
        this.adInterval = setInterval(function () {
          var duration = Math.ceil(manyVideoAd_ele.duration);
          var currentTime = Math.ceil(manyVideoAd_ele.currentTime);
          var time = duration - currentTime;
          adDuration_ele.innerText = time;
          if (time == 1) {
            clearInterval(_this5.adInterval);
          }
        }, 1000);
      }
  
      // 关闭视频广告
  
    }, {
      key: 'closeManyVideoAd',
      value: function closeManyVideoAd() {
        this.clearAdInterval();
        var controlBar = this.html.parentNode.querySelector('.prism-controlbar');
        controlBar.className = controlBar.className.replace(' controlbar-element-hidden', '');
        this.html.parentNode.removeChild(this.html);
        this.html = null;
        //广告结束之后自动播放主视频
        document.getElementById(player._options.id).getElementsByTagName('video')[0].play();
      }
  
      // 清除视频广告倒计时
  
    }, {
      key: 'clearAdInterval',
      value: function clearAdInterval() {
        this.adInterval !== null && clearInterval(this.adInterval);
        this.adInterval = null;
      }
  
      // 播放视频广告
  
    }, {
      key: 'playManyVideoAd',
      value: function playManyVideoAd() {
        this.setAdInterval();
        this.html.querySelector('#many-video-ad-content' + this.indexVideo).play();
      }
  
      // 暂停视频广告 
  
    }, {
      key: 'pauseManyVideoAd',
      value: function pauseManyVideoAd() {
        this.clearAdInterval();
        this.html.querySelector('#many-video-ad-content' + this.indexVideo).pause();
      }
  
      // 关闭当前广告并播放下一个视频
  
    }, {
      key: 'playNext',
      value: function playNext(self) {
        if (self.indexVideo >= self.adVideoSource.length) {
          self.html.parentNode.removeChild(self.html);
          return -1;
        }
        this.indexVideo = Number(this.indexVideo);
        if (document.getElementById('many-video-ad-content' + this.indexVideo) != null) {
          document.getElementById('many-video-ad-content' + this.indexVideo).remove();
        }
  
        var indexVideo_ = this.indexVideo + 1;
        self.html.querySelector('.many-video-ad-detail').href = self.adVideoSource[self.indexVideo].adVideoLink;
        var manyVideoAd_ele = this.html.querySelector('#many-video-ad-content' + indexVideo_);
        var adDuration_ele = this.html.querySelector('#many-video-ad-duration');
        manyVideoAd_ele.style.display = 'block';
        if (this.adVideoSource.length >= this.indexVideo) {
          this.indexVideo = this.indexVideo + 1;
        }
  
        var duration = Math.ceil(manyVideoAd_ele.duration);
        adDuration_ele.innerText = duration;
        self.setAdInterval();
        manyVideoAd_ele.play();
        manyVideoAd_ele.addEventListener('ended', function (event) {
          var flag = self.playNext(self);
          //广告结束之后自动播放主视频
          if (flag == '-1') {
            document.getElementById(player._options.id).getElementsByTagName('video')[0].play();
            var controlBar = document.querySelector('.prism-controlbar');
            controlBar.className = controlBar.className.replace(' controlbar-element-hidden', '');
          }
        });
      }
    }]);
  
    return MbManyVideoAdComponent;
  }();
  
  var defaultComponent = ManyVideoAdComponent;
  
  if (_currentDevice2.default.mobile()) {
    defaultComponent = MbManyVideoAdComponent;
  }
  
  exports.default = defaultComponent;
  
  /***/ }),
  /* 30 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"many-video-ad-component\">\n  <div class=\"videos\">\n    <!-- <video id=\"many-video-ad-content1\" class=\"test\" style=\"display: block\" x5-video-player-type=\"h5\" x5-video-player-fullscreen=\"false\" src=\"https://alivc-demo-cms.alicdn.com/video/videoAD.mp4\"></video>\n    <video id=\"many-video-ad-content2\" class=\"test\" style=\"display: none\" x5-video-player-type=\"h5\" x5-video-player-fullscreen=\"false\" src=\"https://player.alicdn.com/resource/player/qupai.mp4\"></video>\n    <video id=\"many-video-ad-content3\" class=\"test\" style=\"display: none\" x5-video-player-type=\"h5\" x5-video-player-fullscreen=\"false\" src=\"https://player.alicdn.com/video/guanggao.mp4\"></video> -->\n  </div>\n  <a class=\"many-video-ad-link\" target=\"_blank\"></a>\n  <div class=\"many-video-ad-close\">\n    <span id=\"many-video-ad-duration\"></span>\n    <label>\n      <span class=\"many-video-ad-close-text\"></span>\n      <i class=\"iconfont icon-close\"></i>\n    </label>\n  </div>\n  <a class=\"many-video-ad-detail\" target=\"_blank\">查看详情</a>\n  <div class=\"autoplay-many-video-ad\">\n    <i class=\"iconfont icon-player-play\"></i>\n    <span class=\"limit\"></span>\n    <span class=\"manual\"></span>\n  </div>\n  <!-- <div id=\"loadflag\" class=\"prism-loading center\">\n    <div class=\"circle\"></div> \n    <div class=\"circle1\"></div>\n  </div> -->\n</div>";
  
  /***/ }),
  /* 31 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(32);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 32 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".many-video-ad-component {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 2147483647;\n  background-color: #000;\n  font-size: 12px; }\n  .many-video-ad-component #many-video-ad-content {\n    width: 100%;\n    height: 100%; }\n  .many-video-ad-component .many-video-ad-link {\n    width: 100%;\n    height: 100%;\n    display: block;\n    position: absolute;\n    top: 0;\n    left: 0; }\n  .many-video-ad-component .many-video-ad-close {\n    background-color: rgba(59, 59, 59, 0.85);\n    position: absolute;\n    top: 15px;\n    right: 20px;\n    color: #fff;\n    line-height: 26px;\n    padding: 0 10px;\n    user-select: none; }\n    .many-video-ad-component .many-video-ad-close #many-video-ad-duration {\n      color: #00c1de;\n      vertical-align: top;\n      font-size: 14px;\n      position: relative;\n      display: inline-block;\n      text-align: right; }\n      .many-video-ad-component .many-video-ad-close #many-video-ad-duration::after {\n        background-color: #fff;\n        position: absolute;\n        content: '';\n        right: -7px;\n        top: 8px;\n        bottom: 8px;\n        width: 1px; }\n    .many-video-ad-component .many-video-ad-close label {\n      cursor: pointer;\n      display: inline-block; }\n    .many-video-ad-component .many-video-ad-close .icon-close {\n      font-size: 12px;\n      vertical-align: top; }\n      @media (min-width: 768px) {\n        .many-video-ad-component .many-video-ad-close .icon-close:hover {\n          color: #00c1de; } }\n    .many-video-ad-component .many-video-ad-close .many-video-ad-close-text {\n      padding: 0 5px 0 10px; }\n  .many-video-ad-component .many-video-ad-detail {\n    position: absolute;\n    right: 35px;\n    bottom: 30px;\n    background-color: rgba(0, 222, 255, 0.85);\n    color: #fff;\n    padding: 8px 12px;\n    user-select: none;\n    cursor: pointer;\n    transition: background-color .15s; }\n    @media (min-width: 768px) {\n      .many-video-ad-component .many-video-ad-detail:hover {\n        background-color: #00deff; } }\n  .many-video-ad-component .autoplay-many-video-ad {\n    position: absolute;\n    color: #fff;\n    top: 50%;\n    left: 50%;\n    text-align: center;\n    padding: 10px;\n    border-radius: 5px;\n    background-color: rgba(144, 147, 153, 0.85);\n    transform: translate(-50%, -50%);\n    display: none; }\n    .many-video-ad-component .autoplay-many-video-ad i {\n      font-size: 42px;\n      margin-bottom: 7px;\n      display: inline-block;\n      cursor: pointer; }\n      .many-video-ad-component .autoplay-many-video-ad i + span {\n        margin-bottom: 5px; }\n    .many-video-ad-component .autoplay-many-video-ad span {\n      display: block; }\n\n.controlbar-element-hidden {\n  display: none !important; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 33 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"many-video-ad-component\" style=\"background-color: transparent;\">\n  <div class=\"videosmb\"></div>\n  <a class=\"many-video-ad-link\" target=\"_blank\"></a>\n  <div class=\"many-video-ad-close\">\n    <span id=\"many-video-ad-duration\"></span>\n    <label>\n      <span class=\"many-video-ad-close-text\"></span>\n      <i class=\"iconfont icon-close\"></i>\n    </label>\n  </div>\n  <a class=\"many-video-ad-detail\" target=\"_blank\"></a>\n  <div class=\"autoplay-many-video-ad\" style=\"display: block;\">\n    <i class=\"iconfont icon-player-play\"></i>\n    <span class=\"limit\"></span>\n    <span class=\"manual\"></span>\n  </div>\n</div>";
  
  /***/ }),
  /* 34 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(35);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.MemoryPlayComponent = _index2.default;
  
  /***/ }),
  /* 35 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(36);
  
  var _index2 = _interopRequireDefault(_index);
  
  __webpack_require__(37);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 记忆播放组件
   */
  var MemoryPlayComponent = function () {
    /**
     * @constructor 记忆播放组件构造函数
     */
    function MemoryPlayComponent() {
      var autoPlay = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      var getTime = arguments[1];
      var saveTimeFunction = arguments[2];
  
      _classCallCheck(this, MemoryPlayComponent);
  
      this.html = (0, _utils.parseDom)(_index2.default);
      this.autoPlay = autoPlay;
      this.getTime = getTime || this._getTime;
      this.saveTimeFunction = saveTimeFunction || this._saveTime;
      this.hasMemoryDisplay = false;
    }
  
    _createClass(MemoryPlayComponent, [{
      key: 'createEl',
      value: function createEl(el) {
        el.appendChild(this.html);
      }
    }, {
      key: 'ready',
      value: function ready(player, e) {
        var _this = this;
  
        var playerOptions = player.getOptions();
        var memoryVideo = playerOptions.vid || playerOptions.source.replace(/\?.*$/, ''); // 根据视频 vid 或者 source 去存储 localeStorage
        var memoryTime = this.getTime(memoryVideo);
        memoryTime = memoryTime ? parseInt(memoryTime) : 0;
        if (memoryTime !== null && memoryTime !== 0 && !this.hasMemoryDisplay) {
          // 标识记忆播放是否有触发, 解决清晰度切换也会触发记忆播放的问题
          this.hasMemoryDisplay = true;
          if (this.autoPlay) {
            player.seek(memoryTime);
            if (player.getStatus() !== 'playing') {
              player.play();
            }
          } else {
            var memoryVideoTime = this.getVideoTime(memoryTime);
            //判断是否是视频播放完了，如果没到，就有拼接的跳转播放
            if (memoryTime !== parseInt(player._duration)) {
              var memoryDomString = '<div class="memory-play">\n          <i class="iconfont icon-close"></i>\n          <span>\u4E0A\u6B21\u770B\u5230</span>\n          <span>' + memoryVideoTime + '</span>\n          <span class="play-jump">\u8DF3\u8F6C\u64AD\u653E</span>\n          </div>';
              this.html.innerHTML = memoryDomString;
              var timeoutMemory = setTimeout(function () {
                _this.html.innerHTML = '';
              }, 15000);
              this.html.querySelector('.icon-close').onclick = function () {
                _this.html.innerHTML = '';
                clearTimeout(timeoutMemory);
              };
              this.html.querySelector('.play-jump').onclick = function () {
                player.seek(memoryTime);
                if (player.getStatus() !== 'playing') {
                  player.play();
                }
                _this.html.innerHTML = '';
                clearTimeout(timeoutMemory);
              };
            }
          }
        }
  
        document.onvisibilitychange = function () {
          if (document.visibilityState === 'hidden' && player.getCurrentTime() !== 0) {
            _this.saveTimeFunction(memoryVideo, player.getCurrentTime());
            console.log(play);
          }
        };
  
        window.onbeforeunload = function () {
          if (player.getCurrentTime() !== 0) {
            _this.saveTimeFunction(memoryVideo, player.getCurrentTime());
          }
        };
      }
    }, {
      key: 'error',
      value: function error(player, e) {
        this.setMemory(player);
      }
    }, {
      key: 'dispose',
      value: function dispose(player, e) {
        this.setMemory(player);
      }
    }, {
      key: 'setMemory',
      value: function setMemory(player) {
        var playerOptions = player.getOptions();
        var memoryVideo = playerOptions.vid || playerOptions.source.replace(/\?.*$/, '');
        this.saveTimeFunction(memoryVideo, player.getCurrentTime());
      }
    }, {
      key: 'getVideoTime',
      value: function getVideoTime(duration) {
        var secondTotal = Math.round(duration);
  
        var hour = Math.floor(secondTotal / 3600);
        var minute = Math.floor((secondTotal - hour * 3600) / 60);
  
        var second = secondTotal - hour * 3600 - minute * 60;
  
        if (minute < 10) {
          minute = '0' + minute;
        }
        if (second < 10) {
          second = '0' + second;
        }
        return hour === 0 ? minute + ':' + second : hour + ':' + minute + ':' + second;
      }
    }, {
      key: '_getTime',
      value: function _getTime(memoryVideo) {
        return localStorage.getItem(memoryVideo);
      }
    }, {
      key: '_saveTime',
      value: function _saveTime(memoryVideo, currentTime) {
        localStorage.setItem(memoryVideo, currentTime);
      }
    }]);
  
    return MemoryPlayComponent;
  }();
  
  exports.default = MemoryPlayComponent;
  
  /***/ }),
  /* 36 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"memory-play-wrap\"></div>";
  
  /***/ }),
  /* 37 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(38);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 38 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".memory-play-wrap {\n  position: absolute;\n  right: 10px;\n  bottom: 55px; }\n  .memory-play-wrap .memory-play {\n    padding: 13px 20px;\n    background-color: #191919;\n    background-color: rgba(25, 25, 25, 0.88);\n    border-radius: 4px;\n    color: #fff;\n    font-size: 14px;\n    user-select: none; }\n    .memory-play-wrap .memory-play .icon-close {\n      transition: color .3s;\n      font-size: 14px;\n      cursor: pointer; }\n      @media (min-width: 768px) {\n        .memory-play-wrap .memory-play .icon-close:hover {\n          color: #00c1de; } }\n    .memory-play-wrap .memory-play .play-jump {\n      color: #00c1de;\n      padding: 5px;\n      border-radius: 3px;\n      cursor: pointer; }\n      @media (min-width: 768px) {\n        .memory-play-wrap .memory-play .play-jump:hover {\n          background-color: rgba(255, 255, 255, 0.2); } }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 39 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(40);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.PauseADComponent = _index2.default;
  
  /***/ }),
  /* 40 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(41);
  
  var _index2 = _interopRequireDefault(_index);
  
  __webpack_require__(42);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 暂停图片广告组件
   */
  var PauseADComponent = function () {
    /**
     * @constructor 暂停图片广告组件构造函数
     * @param {String coverUrl 封面图片地址}
     * @param {String adUrl 广告地址}
     */
    function PauseADComponent(coverUrl, adUrl) {
      _classCallCheck(this, PauseADComponent);
  
      this.coverUrl = coverUrl;
      this.adUrl = adUrl;
      this.html = (0, _utils.parseDom)(_index2.default);
    }
  
    _createClass(PauseADComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var _this = this;
  
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        this.html.querySelector('.ad-text').innerText = this.isEn ? 'Ad' : '广告';
  
        var adContent = this.html.querySelector('.ad-content');
        var adImg = adContent.querySelector('img');
        adContent.setAttribute('href', this.adUrl);
        adImg.setAttribute('src', this.coverUrl);
  
        var btnClose = this.html.querySelector('.btn-close');
        btnClose.onclick = function () {
          _this.html.style.display = 'none';
        };
  
        el.appendChild(this.html);
      }
    }, {
      key: 'play',
      value: function play(player, e) {
        this.html.style.display = 'none';
      }
    }, {
      key: 'pause',
      value: function pause(player, e) {
        this.html.style.display = 'block';
      }
    }]);
  
    return PauseADComponent;
  }();
  
  exports.default = PauseADComponent;
  
  /***/ }),
  /* 41 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"pause-ad\">\n  <a class=\"btn-close\">\n    <i class=\"split-left\"></i>\n    <i class=\"split-right\"></i>\n  <a>\n  <span class=\"ad-text\"></span>\n  <a class=\"ad-content\" target=\"_blank\">\n    <img />\n  </a>\n</div>";
  
  /***/ }),
  /* 42 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(43);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 43 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".pause-ad {\n  width: 60%;\n  height: 50%;\n  background: #000;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  position: absolute;\n  display: none; }\n  .pause-ad .btn-close {\n    position: absolute;\n    top: 5px;\n    right: 5px;\n    background-color: #333;\n    width: 2px;\n    height: 15px;\n    padding: 2px 8px;\n    cursor: pointer; }\n    .pause-ad .btn-close i {\n      background-color: #fff;\n      width: 2px;\n      height: 15px;\n      position: absolute; }\n    .pause-ad .btn-close .split-left {\n      transform: rotate(-45deg); }\n    .pause-ad .btn-close .split-right {\n      transform: rotate(45deg); }\n  .pause-ad .ad-text {\n    position: absolute;\n    left: 5px;\n    top: 5px;\n    background-color: #333;\n    color: #fff;\n    padding: 3px 5px;\n    font-size: 14px; }\n  .pause-ad .ad-content {\n    width: 100%;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: center; }\n    .pause-ad .ad-content img {\n      max-width: 100%;\n      max-height: 100%; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 44 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(45);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.PlaylistComponent = _index2.default;
  
  /***/ }),
  /* 45 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(46);
  
  var _index2 = _interopRequireDefault(_index);
  
  var _list = __webpack_require__(47);
  
  var _list2 = _interopRequireDefault(_list);
  
  __webpack_require__(48);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 播放列表事件
   */
  var PlaylistComponentEvent = {
    VideoClick: 'plugin-playlist-click-video',
    PrevClick: 'plugin-playlist-click-prev',
    NextClick: 'plugin-playlist-click-next',
    VideoChange: 'plugin-playlist-change'
  
    /**
     * 播放列表组件
     */
  };
  var PlaylistComponent = function () {
    /**
     * @constructor 播放列表组件构造函数
     * @param {Array playlist 播放列表数组}
     */
    function PlaylistComponent(playlist) {
      _classCallCheck(this, PlaylistComponent);
  
      this.controlHtml = (0, _utils.parseDom)(_index2.default);
      this.listHtml = (0, _utils.parseDom)(_list2.default);
      this.playlist = playlist;
      this.playingVideoIndex = 0;
      this.listHideTimeout = null;
    }
  
    _createClass(PlaylistComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var _this = this;
  
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        this.controlHtml.querySelector('.player-tooltip.prev').innerText = this.isEn ? 'Previous' : '上一个';
        this.controlHtml.querySelector('.player-tooltip.list').innerText = this.isEn ? 'Playlist' : '播放列表';
        this.controlHtml.querySelector('.player-tooltip.next').innerText = this.isEn ? 'Next' : '下一个';
  
        var controlbarElement = el.querySelector('.prism-controlbar');
        var siblingElement = controlbarElement.querySelector('.prism-time-display');
        controlbarElement.insertBefore(this.controlHtml, siblingElement);
  
        this.listHtml.onmouseleave = function () {
          _this.listHtml.style.width = 0;
        };
  
        this.listHtml.onmouseenter = this.clearHideListTimeout.bind(this);
  
        this.controlHtml.querySelector('.icon-list').onclick = this.tooglePlaylist.bind(this);
  
        this.listHtml.querySelector('.list').innerHTML = this.computedListDom(this.playlist);
  
        var source = player.getOptions() && player.getOptions().source;
        var defaultPlayIndex = 0;
        if (source) {
          defaultPlayIndex = this.playlist.findIndex(function (item) {
            return item.source === source;
          });
          defaultPlayIndex = defaultPlayIndex > -1 ? defaultPlayIndex : 0;
          this.playingVideoIndex = defaultPlayIndex > -1 ? defaultPlayIndex : 0;
        }
  
        this.listHtml.querySelector('.list').childNodes[0].className = 'video-item active';
        el.appendChild(this.listHtml);
      }
    }, {
      key: 'ready',
      value: function ready(player, e) {
        var _this2 = this;
  
        this.controlHtml.querySelector('.icon-skip-previous').onclick = function () {
  
          player && player.trigger(PlaylistComponentEvent.PrevClick, {
            currentIndex: Math.max(_this2.playingVideoIndex - 1, 0)
          });
  
          if (_this2.playingVideoIndex === 0) {
            _this2.playlistTip(_this2.isEn ? 'Already the first one~' : '已经是第一个了~', player._el);
            return;
          }
          _this2.playVideo(player, _this2.playingVideoIndex - 1);
        };
  
        this.controlHtml.querySelector('.icon-skipnext').onclick = function () {
  
          player && player.trigger(PlaylistComponentEvent.NextClick, {
            currentIndex: Math.min(_this2.playingVideoIndex + 1, _this2.playlist.length - 1)
          });
  
          if (_this2.playingVideoIndex === _this2.playlist.length - 1) {
            _this2.playlistTip(_this2.isEn ? 'Already the last one~' : '已经是最后一个了~', player._el);
            return;
          }
          _this2.playVideo(player, _this2.playingVideoIndex + 1);
        };
  
        this.listHtml.querySelector('.list').onclick = function (e) {
          var target = e.target;
          var videoIndex = parseInt(target.getAttribute('data-index'));
  
          player && player.trigger(PlaylistComponentEvent.VideoClick, {
            currentIndex: _this2.playingVideoIndex,
            clickedIndex: videoIndex
          });
  
          if (target.className === 'video-item') {
            _this2.playVideo(player, videoIndex);
          }
        };
      }
  
      /* clear 自动隐藏右侧播放列表 timeout 的函数 */
  
    }, {
      key: 'clearHideListTimeout',
      value: function clearHideListTimeout() {
        if (this.listHideTimeout !== null) {
          clearTimeout(this.listHideTimeout);
          this.listHideTimeout = null;
        }
      }
  
      /* 播放 playlist 中 index 为 @param videoIndex 的视频 */
  
    }, {
      key: 'playVideo',
      value: function playVideo(player, videoIndex) {
        if (this.playingVideoIndex === videoIndex) {
          return;
        }
  
        player && player.trigger(PlaylistComponentEvent.VideoChange, { currentIndex: videoIndex });
  
        this.playingVideoIndex = parseInt(videoIndex);
        player.loadByUrl(this.playlist[videoIndex].source);
        this.listHtml.querySelector('.video-item.active').className = 'video-item';
        this.listHtml.querySelector('.list').childNodes[videoIndex].className = 'video-item active';
      }
  
      /* 点击 controlbar 上的播放列表按钮显示隐藏播放列表 */
  
    }, {
      key: 'tooglePlaylist',
      value: function tooglePlaylist() {
        var _this3 = this;
  
        this.clearHideListTimeout();
        if (this.listHtml.style.width === '30%') {
          this.listHtml.style.width = 0;
        } else {
          this.listHtml.style.width = '30%';
          this.listHideTimeout = setTimeout(function () {
            _this3.listHtml.style.width = 0;
          }, 5000);
        }
      }
  
      /* 播放器提示信息 */
  
    }, {
      key: 'playlistTip',
      value: function playlistTip(msg, el) {
        var tipElement = document.createElement('div');
        tipElement.className = 'playlist-skip-tip';
        tipElement.innerText = msg;
        el.appendChild(tipElement);
        setTimeout(function () {
          el.removeChild(tipElement);
        }, 3000);
      }
    }, {
      key: 'computedListDom',
      value: function computedListDom(playlist) {
        var domList = playlist.map(function (item, index) {
          return '<div class="video-item" data-index="' + index + '" title="' + item.name + '">' + item.name + '</div>';
        });
        return domList.join('');
      }
    }]);
  
    return PlaylistComponent;
  }();
  
  exports.default = PlaylistComponent;
  
  /***/ }),
  /* 46 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"playlist-component\">\n  <i class=\"iconfont icon-skip-previous\"></i>\n  <div class=\"player-tooltip prev\">上一个</div>\n  <i class=\"iconfont icon-list\"></i>\n  <div class=\"player-tooltip list\">播放列表</div>\n  <i class=\"iconfont icon-skipnext\"></i>\n  <div class=\"player-tooltip next\">下一个</div>  \n</div>";
  
  /***/ }),
  /* 47 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"playlist-content\">\n  <div class=\"list\"></div>\n</div>";
  
  /***/ }),
  /* 48 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(49);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 49 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".playlist-component {\n  color: #fff;\n  float: left;\n  height: 35px;\n  margin-top: 5px;\n  display: flex;\n  align-items: center;\n  position: relative; }\n  .playlist-component i {\n    color: #fff;\n    display: inline-block;\n    font-size: 22px;\n    display: block;\n    margin-top: 7px;\n    cursor: pointer; }\n    .playlist-component i + i {\n      margin-left: 3px; }\n    @media (min-width: 768px) {\n      .playlist-component i:hover + .player-tooltip {\n        display: block; } }\n  .playlist-component .player-tooltip.prev {\n    left: -10px; }\n  .playlist-component .player-tooltip.list {\n    left: 5px; }\n  .playlist-component .player-tooltip.next {\n    right: -12px; }\n\n.playlist-content {\n  position: absolute;\n  right: 0;\n  width: 0px;\n  padding-bottom: 48px;\n  box-sizing: border-box;\n  height: 100%;\n  transition: all .38s ease-in-out;\n  overflow: hidden; }\n  .playlist-content .list {\n    background-color: #000;\n    background-color: rgba(0, 0, 0, 0.3);\n    height: 100%;\n    overflow: auto; }\n    .playlist-content .list .video-item {\n      color: #fff;\n      padding: 0px 10px;\n      line-height: 35px;\n      font-size: 14px;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      cursor: pointer; }\n      .playlist-content .list .video-item.active {\n        background-color: #000;\n        color: #00ddff; }\n      @media (min-width: 768px) {\n        .playlist-content .list .video-item:hover {\n          background-color: #000;\n          color: #00ddff; } }\n\n.player-tooltip {\n  position: absolute;\n  display: none;\n  font-size: 12px;\n  color: #fff;\n  line-height: 28px;\n  letter-spacing: 0;\n  text-align: center;\n  background: #3c3c3c;\n  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.1);\n  height: 28px;\n  top: -48px;\n  padding: 0 5px;\n  white-space: nowrap; }\n\n.playlist-skip-tip {\n  padding: 5px 15px;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  z-index: 30;\n  line-height: 30px;\n  font-size: 14px;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.8);\n  color: #000;\n  text-align: center;\n  transform: translate(-50%, -50%); }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 50 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(51);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.PreviewVodComponent = _index2.default;
  
  /***/ }),
  /* 51 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(52);
  
  var _index2 = _interopRequireDefault(_index);
  
  __webpack_require__(53);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 结合点播服务的试看组件
   */
  var PreviewVodComponent = function () {
    /**
     * @constructor 试看组件构造函数
     * @param {Number previewDuration 试看时长单位为秒, 传入0表示可以完整观看}
     * @param {String previewEndHtml 可选参数, 插入到试看结束之后显示的 dom 字符串, 默认为 null}
     * @param {String previewBarHtml 可选参数, 插入到免费试看多少分钟之后的 html}
     */
    function PreviewVodComponent(previewDuration) {
      var previewEndHtml = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      var previewBarHtml = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
  
      _classCallCheck(this, PreviewVodComponent);
  
      this.previewDuration = previewDuration;
      this.html = (0, _utils.parseDom)(_index2.default);
  
      // 在试看结束之后, 如果用户自定义
      if (previewEndHtml !== null) {
        this.insertHTtml(previewEndHtml, 'previewEndHtml');
      }
      if (previewBarHtml !== null) {
        this.insertHTtml(previewBarHtml, 'previewBarHtml');
      }
    }
  
    _createClass(PreviewVodComponent, [{
      key: 'play',
      value: function play(player) {
        if (this.previewEnd) {
          player._el.querySelector('.center').classList.remove('preview-hide');
          player.seek(0);
          this.previewEnd = false;
        }
      }
    }, {
      key: 'insertHTtml',
      value: function insertHTtml(idOrHmtl, params) {
        var parentSelector = params === 'previewEndHtml' ? '.preview-custom' : '.preview-custom-bar';
        var parentEle = this.html.querySelector(parentSelector);
        if (typeof idOrHmtl === 'string') {
          if (idOrHmtl[0] === '#') {
            var templeteEle = document.querySelector(idOrHmtl);
            if (templeteEle) {
              parentEle.innerHTML = templeteEle.innerHTML;
            } else {
              console.warn('[aliplayer components warn]: Invalid parameter ' + params + ', can\'t find element by this id');
            }
          } else {
            parentEle.innerHTML = idOrHmtl;
          }
        } else {
          console.warn('[aliplayer components warn]: Invalid parameter ' + params + ', ' + params + ' must be a string type');
        }
      }
    }, {
      key: 'ready',
      value: function ready(player) {
        var videoDuration = parseInt(player.getDuration());
        this.videoDuration = videoDuration;
        if (!this.invalidPreview && this.previewDuration >= videoDuration) {
          this.invalidPreview = true;
          console.warn('[aliplayer components warn]: Invalid parameter previewDuration, previewDuration must be less than the video duration!');
        }
        if (this.previewDuration !== 0 && this.previewDuration < videoDuration) {
          this.html.style.display = 'block';
        }
      }
    }, {
      key: 'createEl',
      value: function createEl(el, player) {
        var _this = this;
  
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        console.log(this.html.querySelector('.preview-default'));
        var previewDefaultEle = this.html.querySelector('.preview-default');
        if (previewDefaultEle) {
          previewDefaultEle.innerText = this.isEn ? 'Preview is over' : '试看已结束';
        }
        this.html.querySelector('.can-preview').innerText = this.isEn ? 'Try' : '可试看';
  
        var previewDuration = this.previewDuration;
        if (previewDuration === 0) {
          this.html.style.display = 'none';
        }
  
        var previewDuration_text = previewDuration / 60;
        var decimal = previewDuration_text.toString().split('.')[1];
        if (decimal && decimal.length > 1) {
          previewDuration_text = ' ' + previewDuration + (this.isEn ? ' senconds' : ' 秒');
        } else {
          previewDuration_text = ' ' + previewDuration_text + (this.isEn ? ' minutes' : ' 分钟');
        }
        if (previewDuration < 60) {
          previewDuration_text = ' ' + previewDuration + (this.isEn ? ' senconds' : ' 秒');
        }
  
        // 考虑到可能不是整分钟数, 整分钟数去掉小数点后面的 0
        this.html.querySelector('.preview-time').innerText = previewDuration_text;
  
        var videoSiblingElement = el.querySelector('video').nextElementSibling;
        if (videoSiblingElement) {
          el.insertBefore(this.html, videoSiblingElement);
        } else {
          el.appendChild(this.html);
        }
  
        var closeSpan = this.html.querySelector('.preview-vod-close');
        closeSpan.addEventListener('click', function () {
          _this.html.querySelector('.preview-component-tip').style.display = 'none';
        });
        player.setPreviewTime(Number(this.previewDuration));
      }
    }, {
      key: 'closePreviewLayer',
      value: function closePreviewLayer() {
        this.previewEnd = false;
        this.html.querySelector('.preview-component-layer').style.display = 'none';
      }
    }, {
      key: 'timeupdate',
      value: function timeupdate(player) {
        if (!this.previewEnd && this.previewDuration !== 0 && this.previewDuration < this.videoDuration) {
          var previewTime = player.getPreviewTime();
          var currentTime = player.getCurrentTime();
          if (Math.floor(previewTime) < currentTime) {
            this.previewEnd = true;
            player._el.querySelector('.center') && player._el.querySelector('.center').classList.add('preview-hide');
            player.seek(previewTime);
            player.pause();
            this.html.querySelector('.preview-component-layer').style.display = 'block';
          }
        }
      }
    }, {
      key: 'ended',
      value: function ended(player, e) {
        if (player.isPreview()) {
          this.html.querySelector('.preview-component-layer').style.display = 'block';
        }
      }
    }]);
  
    return PreviewVodComponent;
  }();
  
  exports.default = PreviewVodComponent;
  
  /***/ }),
  /* 52 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"preview-vod-component\">\n  <div class=\"preview-component-layer\">\n    <div class=\"preview-custom\">\n      <p class=\"preview-default\"></p>\n    </div>\n  </div>\n  <div class=\"preview-component-tip\">\n    <span class=\"can-preview\"></span><span class=\"preview-time\"></span>， <span class=\"preview-custom-bar\">Become VIP Watch Full Version </span>\n    <span class=\"preview-vod-close\">x</span>\n  </div>\n</div>";
  
  /***/ }),
  /* 53 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(54);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 54 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".preview-hide {\n  display: none !important; }\n\n.preview-vod-component {\n  display: none; }\n  .preview-vod-component .preview-component-layer {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    left: 0;\n    background-color: rgba(0, 0, 0, 0.8);\n    z-index: 9998;\n    box-sizing: border-box;\n    padding: 20px 20px 70px;\n    display: none; }\n    .preview-vod-component .preview-component-layer .preview-close {\n      position: absolute;\n      right: 18px;\n      top: 15px;\n      border-radius: 50%;\n      color: #000;\n      background: white;\n      width: 20px;\n      height: 20px;\n      text-align: center;\n      line-height: 18px;\n      vertical-align: top;\n      color: #333;\n      box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);\n      cursor: pointer;\n      z-index: 9998; }\n    .preview-vod-component .preview-component-layer .preview-custom {\n      width: 100%;\n      height: 100%;\n      position: relative; }\n      .preview-vod-component .preview-component-layer .preview-custom .preview-default {\n        font-size: 14px;\n        color: #fff;\n        text-align: center;\n        position: absolute;\n        top: 50%;\n        transform: translateY(-50%);\n        width: 100%; }\n  .preview-vod-component .preview-custom {\n    color: #fff;\n    font-size: 14px; }\n  .preview-vod-component .preview-component-tip {\n    position: absolute;\n    bottom: 50px;\n    left: 25px;\n    border-radius: 15px;\n    background-color: #333;\n    color: #fff;\n    line-height: 26px;\n    font-size: 14px;\n    padding: 0 12px;\n    z-index: 9998; }\n  .preview-vod-component .preview-vod-close {\n    font-size: 18px;\n    cursor: pointer;\n    padding: 0 5px;\n    font-family: PingFangSC-Regular, sans-serif;\n    font-weight: 200; }\n    .preview-vod-component .preview-vod-close:hover {\n      color: #00c1de; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 55 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(56);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.ProgressComponent = _index2.default;
  
  /***/ }),
  /* 56 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(57);
  
  var _index2 = _interopRequireDefault(_index);
  
  var _utils = __webpack_require__(2);
  
  __webpack_require__(58);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 视频打点组件
   */
  var ProgressMarker = function () {
    function ProgressMarker() {
      _classCallCheck(this, ProgressMarker);
  
      this.html = (0, _utils.parseDom)(_index2.default);
      this.imgEle = this.html.querySelector('.img-wrap img');
      this.timeEle = this.html.querySelector('.time');
      this.playBtnEle = this.html.querySelector('.pregress-play-btn');
      this.describeEle = this.html.querySelector('.describe');
      this.timer = null;
      this.currentOffset = null;
    }
  
    _createClass(ProgressMarker, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var _this = this;
  
        el.appendChild(this.html);
  
        this.html.onmouseenter = function () {
          if (_this.timer !== null) {
            clearTimeout(_this.timer);
            _this.timer = null;
          }
        };
  
        this.html.onmouseleave = function () {
          _this.html.style.display = 'none';
        };
        this.html.onclick = function () {
          _this.html.style.display = 'none';
        };
        this.html.querySelector('.pregress-play-btn').addEventListener('click', function () {
          player.seek(_this.currentOffset);
        });
      }
    }, {
      key: 'markerDotOver',
      value: function markerDotOver(player, data) {
        var maxWidth = player._el.clientWidth;
        var componentLeft = 'calc(' + data.left * 100 + '% - 10px)';
        if (maxWidth * data.left + 323 > maxWidth) {
          componentLeft = maxWidth - 330 + 'px';
          this.html.querySelector('.icon-arrowdown').style.left = maxWidth * data.left - maxWidth + 317 + 'px';
        } else {
          this.html.querySelector('.icon-arrowdown').style.left = '-2px';
        }
        var _data$progressMarker = data.progressMarker,
            coverUrl = _data$progressMarker.coverUrl,
            title = _data$progressMarker.title,
            describe = _data$progressMarker.describe,
            offset = _data$progressMarker.offset;
  
        this.currentOffset = offset;
        this.html.style.left = componentLeft;
        this.imgEle.src = coverUrl;
        this.timeEle.innerText = title;
        this.describeEle.innerText = describe;
        this.html.style.display = 'block';
      }
    }, {
      key: 'markerDotOut',
      value: function markerDotOut(player, data) {
        var _this2 = this;
  
        this.timer = setTimeout(function () {
          _this2.html.style.display = 'none';
        }, 100);
      }
    }]);
  
    return ProgressMarker;
  }();
  
  exports.default = ProgressMarker;
  
  /***/ }),
  /* 57 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"progress-component\">\n  <div class=\"progress-content clearfix\">\n    <div class=\"img-wrap\"><img /></div>    \n\n    <div class=\"info\">\n      <div class=\"time\"></div>\n      <div class=\"describe\"></div>\n    </div>\n\n    <i href=\"\" target=\"_blank\" class=\"iconfont icon-play pregress-play-btn\"></i>\n  </div>\n  <i class=\"iconfont icon-arrowdown\"></i>\n</div>";
  
  /***/ }),
  /* 58 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(59);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 59 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, "@font-face {\n  font-family: \"iconfont\";\n  src: url(" + __webpack_require__(4) + ");\n  /* IE9 */\n  src: url(" + __webpack_require__(4) + "#iefix) format(\"embedded-opentype\"), url(\"data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAALoAAsAAAAABswAAAKZAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDBgqBLIEoATYCJAMMCwgABCAFhG0HPxv3BciemjwJBBawWJiKrz0ASDwe/hv7dt/MfNW2KmnPdtG6CbVE49AhkkRLp5FIhAwheQn/z+kv/1pWQYEwj7X+Uuyl/nTcdSTtTi8NcwkH30gwCtRYSyHREE3ufj9P1OLQShNpd5+4d/rneReO/SzLZW3rorEmfwww7rwLcKxFEQ2RQLxh7AKXcJhA07wmodOhiRQqVFijAvHU1GVUmAsrCsvVhWrNwiLeqKinV+kQr8Pvx397KSSpZNbEyyeDGur5VflDWJ2tols+IUKAyyvI2IUU4k5t+hpBML6gaRfbS6uKkOaK/T+hXndWd/rHS0SVVd0Gm9CWbsrgqgh+00EB8vtVFGxALmYoJOTctiY+K0kvXH6mrr7SNz/Z6+9Zx5qJtFlNxSWTO6vj1HYuhJefJ9ftXh9abHpfLGqU77bG13rmtKH98PROBBAh724RzHCxLE2XTr/nplZqkQ6eox7Wz9KPuBAIal+/fLw7HX+FxQX8PFYXDpmtiii/QP1UnuDXdSohKNxkuXJR7VqrA9nqbYxMaGpihrB/6Hm8c9K7mlA3SZHUzCGrWyULu4uKln1U1R2gacfw8pYRRhClgW1bAKFvD0nXN2R9R2Rhn1Ax9YuqfgQ0XYmyDVvWY0r9DMsca6DMgalSjzhuLkW1CaxPOTLLmkJpBrPAjEEsHC3mhrGH2RxTgmk9zjkBwqgLQ+A47DgUfEYtrPKwwbnfFomQuheFVeoiyQkGk3GYBijmAJOK8hC/NSeVPp+A6aY4ZKyFU1PPwJiA2T2ICYv2IIftXi/OvTwTmKaL4zgCEAzlAkOgH+ZwUMCv72fBVFyYMSLpaxOxm5G+9vD6Tvf7tkGTNSlHihxF9z5jdEZzenvNKvXnRN+R5xACAAAA\") format(\"woff2\"), url(" + __webpack_require__(60) + ") format(\"woff\"), url(" + __webpack_require__(61) + ") format(\"truetype\"), url(" + __webpack_require__(62) + "#iconfont) format(\"svg\");\n  /* iOS 4.1- */ }\n\n.iconfont {\n  font-family: \"iconfont\" !important;\n  font-size: 16px;\n  font-style: normal;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale; }\n\n.icon-play:before {\n  content: \"\\E726\"; }\n\n.icon-arrowdown:before {\n  content: \"\\E623\"; }\n\n.clearfix:before, .clearfix:after {\n  content: \"\";\n  display: table;\n  line-height: 0; }\n\n.clearfix:after {\n  clear: both; }\n\n.clearfix {\n  *zoom: 1; }\n\n.progress-component {\n  padding: 8px;\n  background-color: rgba(0, 0, 0, 0.35);\n  position: absolute;\n  display: none;\n  box-sizing: border-box;\n  bottom: 55px;\n  font-size: 12px;\n  color: #fff;\n  height: 74px;\n  z-index: 10; }\n  .progress-component .progress-content {\n    height: 100%;\n    position: relative;\n    padding-right: 45px; }\n  .progress-component .img-wrap {\n    float: left;\n    width: 100px;\n    text-align: center;\n    border: 1px solid rgba(0, 0, 0, 0.6);\n    height: 100%; }\n    .progress-component .img-wrap img {\n      vertical-align: top;\n      max-width: 100%;\n      max-height: 100%;\n      object-fit: contain; }\n  .progress-component .info {\n    float: left;\n    padding-left: 10px;\n    width: 150px; }\n    .progress-component .info .describe {\n      line-height: 18px;\n      margin-top: 8px;\n      height: 36px;\n      overflow: hidden; }\n  .progress-component .pregress-play-btn {\n    display: inline-block;\n    position: absolute;\n    right: 6px;\n    bottom: 0px;\n    font-size: 28px;\n    color: #ca7838;\n    cursor: pointer; }\n  .progress-component .icon-arrowdown {\n    left: -2px;\n    position: absolute;\n    font-size: 31px;\n    color: rgba(0, 0, 0, 0.35);\n    line-height: 6px;\n    top: 100%; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 60 */
  /***/ (function(module, exports) {
  
  module.exports = "data:application/font-woff;base64,d09GRgABAAAAAARoAAsAAAAABswAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABHU1VCAAABCAAAADMAAABCsP6z7U9TLzIAAAE8AAAARAAAAFY8mUkYY21hcAAAAYAAAABWAAABhmkn0RRnbHlmAAAB2AAAAJEAAACsoFCg7WhlYWQAAAJsAAAALwAAADYU+6wjaGhlYQAAApwAAAAeAAAAJAfeA4VobXR4AAACvAAAAAwAAAAMDAEAAGxvY2EAAALIAAAACAAAAAgADgBWbWF4cAAAAtAAAAAeAAAAIAEQADBuYW1lAAAC8AAAAUUAAAJtPlT+fXBvc3QAAAQ4AAAALgAAAD/D5yXjeJxjYGRgYOBikGPQYWB0cfMJYeBgYGGAAJAMY05meiJQDMoDyrGAaQ4gZoOIAgCKIwNPAHicY2BkYWCcwMDKwMHUyXSGgYGhH0IzvmYwYuRgYGBiYGVmwAoC0lxTGByeKT9XY27438AQw9zA0AAUZgTJAQDi6gwteJztkMENgDAMAy9p6QOxBRLiwUC8GJ+OUdwUtsDSxYqTl4EJSOIQGezC6DqVWuSJOfIcP0Vu+L3WrTX4XDLdSrhrFH4tMfd3897WoDdbtwH+ADzlDm4AAHicTYu7DcIwGITvt/FDjiIEQnFtRTItBQTmQGIBJskWbOQeUURykyI7pEKGP12uuu8eILDEQB8IgIIXQ7kzvZdYAr9+A9mjgkfLvYZp4DtcI2TEkQ2jhmwOW9Lxsjt3XiCVWSlyKZFTqswpl5E55EyBeXxaO9m6tvKxlKvx97RaZX6JyuzNSxtHN2c0/rNfLF8AAAB4nGNgZGBgAGLxnctexvPbfGXgZmEAgRvvTIQR9P8GFgbmBiCXg4EJJAoAMOgKOgB4nGNgZGBgbvjfwBDDAmQxMLAwMIBpJMAMAEciAm0AAAQAAAAEAAAABAEAAAAAAAAADgBWeJxjYGRgYGBmUAFiEGACYi4gZGD4D+YzAAAMZAFBAAB4nGWPTU7DMBCFX/oHpBKqqGCH5AViASj9EatuWFRq911036ZOmyqJI8et1ANwHo7ACTgC3IA78EgnmzaWx9+8eWNPANzgBx6O3y33kT1cMjtyDRe4F65TfxBukF+Em2jjVbhF/U3YxzOmwm10YXmD17hi9oR3YQ8dfAjXcI1P4Tr1L+EG+Vu4iTv8CrfQ8erCPuZeV7iNRy/2x1YvnF6p5UHFockikzm/gple75KFrdLqnGtbxCZTg6BfSVOdaVvdU+zXQ+ciFVmTqgmrOkmMyq3Z6tAFG+fyUa8XiR6EJuVYY/62xgKOcQWFJQ6MMUIYZIjK6Og7VWb0r7FDwl57Vj3N53RbFNT/c4UBAvTPXFO6stJ5Ok+BPV8bUnV0K27LnpQ0kV7NSRKyQl7WtlRC6gE2ZVeOEXpc0Yk/KGdI/wAJWm7IAAAAeJxjYGKAAC4G7ICZkYmRmZGFgS+xqCi/PCW/PE83Ob+gkqUgJ7GSgQEAYa8HrwAA"
  
  /***/ }),
  /* 61 */
  /***/ (function(module, exports) {
  
  module.exports = "data:application/x-font-ttf;base64,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"
  
  /***/ }),
  /* 62 */
  /***/ (function(module, exports) {
  
  module.exports = "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiID4KPCEtLQoyMDEzLTktMzA6IENyZWF0ZWQuCi0tPgo8c3ZnPgo8bWV0YWRhdGE+CkNyZWF0ZWQgYnkgaWNvbmZvbnQKPC9tZXRhZGF0YT4KPGRlZnM+Cgo8Zm9udCBpZD0iaWNvbmZvbnQiIGhvcml6LWFkdi14PSIxMDI0IiA+CiAgPGZvbnQtZmFjZQogICAgZm9udC1mYW1pbHk9Imljb25mb250IgogICAgZm9udC13ZWlnaHQ9IjUwMCIKICAgIGZvbnQtc3RyZXRjaD0ibm9ybWFsIgogICAgdW5pdHMtcGVyLWVtPSIxMDI0IgogICAgYXNjZW50PSI4OTYiCiAgICBkZXNjZW50PSItMTI4IgogIC8+CiAgICA8bWlzc2luZy1nbHlwaCAvPgogICAgCiAgICA8Z2x5cGggZ2x5cGgtbmFtZT0iYXJyb3dkb3duLWNvcHkiIHVuaWNvZGU9IiYjNTg5MTU7IiBkPSJNNzI2LjY1MyA0NjYuNjk0aC00MjkuMzA2bDIxNC44NDYtMjA4Ljg1MXoiICBob3Jpei1hZHYteD0iMTAyNCIgLz4KCiAgICAKICAgIDxnbHlwaCBnbHlwaC1uYW1lPSJwbGF5IiB1bmljb2RlPSImIzU5MTc0OyIgZD0iTTUxMiA4NTMuMzMyOTkyQzI1NC43MzMzMTIgODUzLjMzMjk5MiA0Mi42NjU5ODQgNjQxLjI2MzYxNiA0Mi42NjU5ODQgMzg0czIxMi4wNjczMjgtNDY5LjMzMTk2OCA0NjkuMzMxOTY4LTQ2OS4zMzE5NjhjMjU3LjI2NDY0IDAgNDY5LjMzNDAxNiAyMTIuMDY3MzI4IDQ2OS4zMzQwMTYgNDY5LjMzMTk2OCAwIDI1Ny4yNjQ2NC0yMTIuMDY5Mzc2IDQ2OS4zMzQwMTYtNDY5LjMzNDAxNiA0NjkuMzM0MDE2TTUxMi0xMjhDMjI4LjY5Mjk5Mi0xMjggMCAxMDAuNjkyOTkyIDAgMzg0IDAgNjY3LjMwNzAwOCAyMjguNjkyOTkyIDg5NiA1MTIgODk2YzI4My4zMDcwMDggMCA1MTItMjI4LjY5Mjk5MiA1MTItNTEyIDAtMjgzLjMwNzAwOC0yMjguNjkyOTkyLTUxMi01MTItNTEyTTYzMS44ODM3NzYgMzk5Ljg5NzZjMTAuNjE4ODgtNy45NDkzMTIgMTAuNjE4ODgtMjMuODQ2OTEyIDAtMzEuNzk1Mkw0MDUuMjM3NzYgMjE3LjA2NzUyQzM5NC42MTg4OCAyMDkuMTE5MjMyIDM4NCAyMTQuNDE3NDA4IDM4NCAyMjcuNjY1OTJWNTQwLjMzMjAzMmMwIDEzLjI0OTUzNiAxMC42MTk5MDQgMTguNTQ4NzM2IDIxLjIzNzc2IDEwLjYwMDQ0OGwyMjYuNjQ2MDE2LTE1MS4wMzQ4OHoiICBob3Jpei1hZHYteD0iMTAyNSIgLz4KCiAgICAKCgogIDwvZm9udD4KPC9kZWZzPjwvc3ZnPgo="
  
  /***/ }),
  /* 63 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(64);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.QualityComponent = _index2.default;
  
  /***/ }),
  /* 64 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(65);
  
  var _index2 = _interopRequireDefault(_index);
  
  var _qualityModal = __webpack_require__(66);
  
  var _qualityModal2 = _interopRequireDefault(_qualityModal);
  
  __webpack_require__(67);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 切换清晰度组件
   */
  var QualityComponent = function () {
    function QualityComponent(getQuality) {
      _classCallCheck(this, QualityComponent);
  
      this.html = (0, _utils.parseDom)(_index2.default);
      this.modalHtml = (0, _utils.parseDom)(_qualityModal2.default);
      this.hasCreated = false;
      this.definition = '';
      this.getQuality = getQuality;
    }
  
    _createClass(QualityComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        this.html.querySelector('.current-quality').innerText = this.isEn ? 'Resolution' : '清晰度';
        this.modalHtml.querySelector('.switchimg').innerText = this.isEn ? 'Switching to you for' : '正在为您切换到';
        this.modalHtml.querySelector('.wait').innerText = this.isEn ? 'Please wait...' : '请稍后...';
        var eleControlbar = el.querySelector('.prism-controlbar');
        eleControlbar.appendChild(this.html);
        el.appendChild(this.modalHtml);
      }
    }, {
      key: 'setCurrentQuality',
      value: function setCurrentQuality(quality, def) {
        var currentQuality = this.html.querySelector('.current-quality');
        currentQuality.innerText = quality;
        currentQuality.dataset.def = def;
        this.definition = def;
  
        var qualityListEle = this.html.querySelector('.quality-list');
        var currentEle = qualityListEle.querySelector('.current');
        if (currentEle) {
          currentEle.className = '';
        }
        var li_target = qualityListEle.querySelector('li[data-def="' + def + '"]');
        if (li_target) {
          li_target.className = 'current';
        }
      }
    }, {
      key: 'created',
      value: function created(player) {
        var _this = this;
  
        this._urls = player._urls;
  
        var currentQualityEle = this.html.querySelector('.current-quality');
        var qualityListEle = this.html.querySelector('.quality-list');
  
        var lis_ele = this._urls.map(function (url) {
          return '<li data-def="' + url.definition + '">' + url.desc + '</li>';
        });
        this.html.querySelector('.quality-list').innerHTML = lis_ele.join('');
  
        console.log(this.definition);
        if (this.hasCreated == false && this.definition) {
          var li_target = qualityListEle.querySelector('li[data-def="' + this.definition + '"]');
          li_target.className = 'current';
        }
        this.hasCreated = true;
  
        var timeId = null;
  
        currentQualityEle.onclick = function () {
          var listVisible = qualityListEle.style.display !== 'none';
          if (listVisible) {
            qualityListEle.style.display = 'none';
          } else {
            qualityListEle.style.display = 'block';
          }
        };
  
        currentQualityEle.onmouseleave = function () {
          if (timeId) clearTimeout(timeId);
          timeId = setTimeout(function () {
            qualityListEle.style.display = 'none';
          }, 150);
        };
  
        qualityListEle.onmouseenter = function () {
          clearTimeout(timeId);
        };
        qualityListEle.onmouseleave = function () {
          if (timeId) clearTimeout(timeId);
          timeId = setTimeout(function () {
            qualityListEle.style.display = 'none';
          }, 150);
        };
  
        qualityListEle.onclick = function (_ref) {
          var target = _ref.target;
  
          var definition = target.dataset.def;
          var desc = target.innerText;
          if (definition && target.className !== 'current') {
            var url = Array.isArray(_this._urls) ? _this._urls.filter(function (url) {
              return url.definition === definition;
            })[0] : _this._urls.find(function (url) {
              return url.definition === definition;
            });
            if (url) {
              (0, _utils.cookieSet)('selectedStreamLevel', url.definition, 365);
  
              if (player._switchLevel && !player._options.isLive) {
                player._switchLevel(url.Url);
              } else {
                player._loadByUrlInner(url.Url, player.getCurrentTime(), true /*autoPlay*/, true /* isSwitchLevel */);
              }
  
              _this.setCurrentQuality(url.desc, url.definition);
  
              _this.modalHtml.style.display = 'block';
              _this.modalHtml.querySelector('span.current-quality-tag').innerText = url.desc;
            }
          }
          //点击切换清晰度时，调用这个方法
          _this.getQuality(definition, desc);
        };
      }
    }, {
      key: 'ready',
      value: function ready(player) {
        this.modalHtml.style.display = 'none';
        // 隐藏设置里面的倍速播放
        var settingEle = document.querySelector('.prism-setting-item.prism-setting-quality');
        if (settingEle) {
          settingEle.classList.add('player-hidden');
        }
      }
    }]);
  
    return QualityComponent;
  }();
  
  exports.default = QualityComponent;
  
  /***/ }),
  /* 65 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"quality-components\">\n  <div class=\"current-quality\" data-ref=\"\"></div>\n  <ul class=\"quality-list\">\n  </ul>\n</div>";
  
  /***/ }),
  /* 66 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"quality-modal\">\n  <span class=\"switchimg\"></span> <span class=\"current-quality-tag\"></span> , <span class=\"wait\"></span>\n</div>";
  
  /***/ }),
  /* 67 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(68);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 68 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".player-hidden {\n  display: none !important; }\n\n.quality-components {\n  float: right;\n  color: #fff;\n  height: 35px;\n  position: relative;\n  box-sizing: border-box;\n  margin-top: 4px; }\n\n.current-quality {\n  display: flex;\n  height: 100%;\n  align-items: center;\n  justify-content: center;\n  width: 70px;\n  cursor: pointer; }\n\n.quality-list {\n  position: absolute;\n  bottom: 46px;\n  display: none;\n  padding: 0;\n  margin: 0;\n  list-style: none; }\n  .quality-list li {\n    text-align: center;\n    width: 70px;\n    line-height: 30px;\n    background-color: rgba(0, 0, 0, 0.6);\n    cursor: pointer; }\n    .quality-list li.current {\n      color: #00c1de; }\n    .quality-list li + li {\n      border-top: 1px solid rgba(78, 78, 78, 0.3); }\n    .quality-list li:hover {\n      background-color: rgba(0, 0, 0, 0.5); }\n\n.quality-modal {\n  position: absolute;\n  bottom: 20%;\n  left: 5%;\n  background-color: rgba(0, 0, 0, 0.6);\n  border-radius: 5px;\n  color: #fff;\n  padding: 10px 15px;\n  font-size: 14px;\n  display: none; }\n  .quality-modal span.current-quality-tag {\n    color: #00c1de; }\n\n@media (max-width: 768px) {\n  .current-quality {\n    width: 40px; }\n  .quality-list li {\n    width: 45px; } }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 69 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(70);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.RateComponent = _index2.default;
  
  /***/ }),
  /* 70 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(71);
  
  var _index2 = _interopRequireDefault(_index);
  
  __webpack_require__(72);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 倍速播放组件
   */
  var RateComponent = function () {
    /**
     * @constructor 倍速播放组件构造函数
     */
    function RateComponent() {
      _classCallCheck(this, RateComponent);
  
      this.html = (0, _utils.parseDom)(_index2.default);
    }
  
    _createClass(RateComponent, [{
      key: 'createEl',
      value: function createEl(el) {
        var eleControlbar = el.querySelector('.prism-controlbar');
        eleControlbar.appendChild(this.html);
      }
    }, {
      key: 'ready',
      value: function ready(player, e) {
        var currentRateEle = this.html.querySelector('.current-rate');
        var rateListEle = this.html.querySelector('.rate-list');
        var timeId = null;
  
        // 隐藏设置里面的倍速播放
        var settingRate = document.querySelector('.prism-setting-item.prism-setting-speed');
        if (settingRate) {
          settingRate.classList.add('player-hidden');
        }
  
        currentRateEle.onclick = function () {
          rateListEle.style.display = 'block';
        };
        currentRateEle.onmouseleave = function () {
          timeId = setTimeout(function () {
            rateListEle.style.display = 'none';
          }, 100);
        };
  
        rateListEle.onmouseenter = function () {
          clearTimeout(timeId);
        };
        rateListEle.onmouseleave = function () {
          rateListEle.style.display = 'none';
        };
  
        rateListEle.onclick = function (_ref) {
          var target = _ref.target;
  
          var rate = target.dataset.rate;
          if (rate) {
            player.setSpeed(rate);
            if (target.className !== 'current') {
              var currentEle = rateListEle.querySelector('.current');
              if (currentEle) {
                currentEle.className = '';
              }
              target.className = 'current';
            }
            rateListEle.style.display = 'none';
            currentRateEle.innerText = rate + 'x';
          }
        };
      }
    }]);
  
    return RateComponent;
  }();
  
  exports.default = RateComponent;
  
  /***/ }),
  /* 71 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"rate-components\">\n  <div class=\"current-rate\">1.0x</div>\n  <ul class=\"rate-list\">\n    <li data-rate=\"2.0\">2.0x</li>\n    <li data-rate=\"1.5\">1.5x</li>\n    <li data-rate=\"1.25\">1.25x</li>\n    <li data-rate=\"1.0\" class=\"current\">1.0x</li>\n    <li data-rate=\"0.5\">0.5x</li>\n  </ul>\n</div>";
  
  /***/ }),
  /* 72 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(73);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 73 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".player-hidden {\n  display: none !important; }\n\n.rate-components {\n  float: right;\n  color: #fff;\n  height: 35px;\n  position: relative;\n  box-sizing: border-box;\n  margin-top: 5px; }\n\n.current-rate {\n  display: flex;\n  height: 100%;\n  align-items: center;\n  justify-content: center;\n  width: 70px;\n  cursor: pointer; }\n\n.rate-list {\n  position: absolute;\n  bottom: 46px;\n  display: none;\n  padding: 0;\n  margin: 0;\n  list-style: none; }\n  .rate-list li {\n    text-align: center;\n    width: 70px;\n    line-height: 30px;\n    background-color: rgba(0, 0, 0, 0.6);\n    cursor: pointer; }\n    .rate-list li.current {\n      color: #00c1de; }\n    .rate-list li + li {\n      border-top: 1px solid rgba(78, 78, 78, 0.3); }\n    .rate-list li:hover {\n      background-color: rgba(0, 0, 0, 0.5); }\n\n@media (max-width: 768px) {\n  .current-rate {\n    width: 40px; }\n  .rate-list li {\n    width: 43px; } }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 74 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(75);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.RotateMirrorComponent = _index2.default;
  
  /***/ }),
  /* 75 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(76);
  
  var _index2 = _interopRequireDefault(_index);
  
  __webpack_require__(77);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 旋转镜像组件 
   */
  var RotateMirrorComponent = function () {
    /**
     * @constructor 旋转镜像组件构造函数
     */
    function RotateMirrorComponent() {
      _classCallCheck(this, RotateMirrorComponent);
  
      this.html = (0, _utils.parseDom)(_index2.default);
    }
  
    _createClass(RotateMirrorComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        this.html.querySelector('.player-tooltip.counterclockwise').innerText = this.isEn ? 'Rotate 45 degrees counterclockwise' : '逆时针旋转45度';
        this.html.querySelector('.mirror-item[data-id="counterclockwise"]').innerText = this.isEn ? 'Rotate left 45 ̊' : '左旋转45˚';
        this.html.querySelector('.player-tooltip.clockwise').innerText = this.isEn ? 'Rotate 45 degrees clockwise' : '顺时针旋转45度';
        this.html.querySelector('.mirror-item[data-id="clockwise"]').innerText = this.isEn ? 'Rotate right 45 ̊' : '右旋转45˚';
        this.html.querySelector('.player-tooltip.switch').innerText = this.isEn ? 'Mirror' : '镜像';
        this.html.querySelector('.mirror-item[data-id=vertical]').innerText = this.isEn ? 'Vertical mirroring' : '垂直镜像';
        this.html.querySelector('.mirror-item[data-id=horizon]').innerText = this.isEn ? 'Horizontal mirroring' : '水平镜像';
  
        var eleControlbar = el.querySelector('.prism-controlbar');
        eleControlbar.appendChild(this.html);
      }
    }, {
      key: 'ready',
      value: function ready(player, e) {
        this.html.querySelector('.icon-player-rotate-left').onclick = function () {
          var rotate = player.getRotate();
          player.setRotate(rotate - 45);
        };
        this.html.querySelector('.icon-player-rotate-right').onclick = function () {
          var rotate = player.getRotate();
          player.setRotate(rotate + 45);
        };
        var eleMirror = this.html.querySelector('.mirror-option');
        this.html.querySelector('.icon-player-switch').onclick = function () {
          var displayStyle = eleMirror.style.display;
          eleMirror.style.display = displayStyle === 'none' ? 'block' : 'none';
        };
        eleMirror.onmouseleave = function () {
          this.style.display = 'none';
        };
        eleMirror.onclick = function (e) {
          var target = e.target;
          if (target.dataset.id === 'counterclockwise') {
            var rotate = player.getRotate();
            player.setRotate(rotate - 45);
            return;
          }
          if (target.dataset.id === 'clockwise') {
            var _rotate = player.getRotate();
            player.setRotate(_rotate + 45);
            return;
          }
          if (!target.className.match('active')) {
            var siblingEle = target.dataset.id === 'horizon' ? target.previousElementSibling : target.nextElementSibling;
            if (siblingEle.className.match('active')) {
              siblingEle.className = 'mirror-item';
              player.cancelImage();
            }
            var param = target.getAttribute('data-id');
            player.setImage(param);
            target.className = 'mirror-item active';
          } else {
            player.cancelImage();
            target.className = 'mirror-item';
          }
        };
      }
    }]);
  
    return RotateMirrorComponent;
  }();
  
  exports.default = RotateMirrorComponent;
  
  /***/ }),
  /* 76 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"aliplayer-rotate-mirror\">\n  <i class=\"iconfont icon-player-rotate-left\"></i>\n  <div class=\"player-tooltip counterclockwise\"></div>\n  <i class=\"iconfont icon-player-rotate-right\"></i>\n  <div class=\"player-tooltip clockwise\"></div>\n  <i class=\"iconfont icon-player-switch\"></i>\n  <div class=\"player-tooltip switch\"></div>\n  <div class=\"mirror-option\" style=\"display: none;\">\n    <div class=\"mirror-item\" data-id=\"vertical\"></div>\n    <div class=\"mirror-item\" data-id=\"horizon\"></div>\n    <div class=\"mirror-item\" data-id=\"counterclockwise\"></div>\n    <div class=\"mirror-item\" data-id=\"clockwise\"></div>\n  </div>\n</div>";
  
  /***/ }),
  /* 77 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(78);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 78 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".aliplayer-rotate-mirror {\n  float: right;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  height: 35px;\n  position: relative;\n  margin-top: 5px; }\n  .aliplayer-rotate-mirror i {\n    color: #fff;\n    display: inline-block;\n    font-size: 22px;\n    display: block;\n    margin-top: 7px;\n    cursor: pointer;\n    margin-right: 10px; }\n    .aliplayer-rotate-mirror i.icon-player-switch {\n      font-size: 24px;\n      font-weight: bold; }\n    @media (min-width: 768px) {\n      .aliplayer-rotate-mirror i:hover + .player-tooltip {\n        display: block; } }\n  .aliplayer-rotate-mirror .mirror-option {\n    position: absolute;\n    background-color: #3c3c3c;\n    top: 0;\n    transform: translateY(-103%);\n    right: -20px; }\n    .aliplayer-rotate-mirror .mirror-option .mirror-item {\n      font-size: 14px;\n      color: #ebecec;\n      line-height: 30px;\n      white-space: nowrap;\n      padding: 0 15px;\n      cursor: pointer;\n      user-select: none; }\n      .aliplayer-rotate-mirror .mirror-option .mirror-item[data-id=\"counterclockwise\"] {\n        display: none; }\n      .aliplayer-rotate-mirror .mirror-option .mirror-item[data-id=\"clockwise\"] {\n        display: none; }\n      .aliplayer-rotate-mirror .mirror-option .mirror-item.active {\n        background-color: rgba(216, 216, 216, 0.1);\n        color: #00c1de; }\n  .aliplayer-rotate-mirror .player-tooltip.counterclockwise {\n    left: -35px; }\n  .aliplayer-rotate-mirror .player-tooltip.clockwise {\n    left: -5px; }\n  .aliplayer-rotate-mirror .player-tooltip.switch {\n    right: 7px; }\n  @media (max-width: 768px) {\n    .aliplayer-rotate-mirror .mirror-option .mirror-item[data-id=\"counterclockwise\"] {\n      display: block; }\n    .aliplayer-rotate-mirror .mirror-option .mirror-item[data-id=\"clockwise\"] {\n      display: block; } }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 79 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(80);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.StartADComponent = _index2.default;
  
  /***/ }),
  /* 80 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(81);
  
  var _index2 = _interopRequireDefault(_index);
  
  __webpack_require__(82);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 开始图片广告组件
   */
  var StartADComponent = function () {
    /**
     * @constructor 开始图片广告组件构造函数
     * @param {String coverUrl 广告图片地址}
     * @param {String adUrl 广告链接}
     * @param {Number adDuration 广告时长, 单位为秒}
     */
    function StartADComponent(coverUrl, adUrl, adDuration) {
      _classCallCheck(this, StartADComponent);
  
      this.coverUrl = coverUrl;
      this.adUrl = adUrl;
      this.adDuration = adDuration;
      if (adDuration <= 0) {
        throw Error('adDuration must must be greater than 0');
      }
      this.html = (0, _utils.parseDom)(_index2.default);
    }
  
    _createClass(StartADComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var _this = this;
  
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        this.html.querySelector('.ad-name').innerText = this.isEn ? 'Ad' : '广告';
        this.html.querySelector('.second').innerText = this.isEn ? 's' : '秒';
  
        var adUrlElement = this.html.querySelector('.ad-content');
        adUrlElement.setAttribute('href', this.adUrl);
  
        var adImgElement = adUrlElement.querySelector('img');
        adImgElement.setAttribute('src', this.coverUrl);
  
        var durationElement = this.html.querySelector('.tip i');
        durationElement.innerText = this.adDuration;
  
        el.appendChild(this.html);
  
        if (this.html !== null) {
          if (player.getStatus() !== 'init') {
            player.pause();
          }
          var duration = this.adDuration;
          var _durationElement = this.html.querySelector('.tip i');
          var durationInterval = setInterval(function () {
            duration = duration - 1;
            _durationElement.innerText = duration;
            if (duration === 0) {
              clearInterval(durationInterval);
              _this.removeComponent();
              // 如果试看组件和记忆播放组件一起用了, 那么不让播放器播放
              var playerOptions = player.getOptions();
              var memoryVideo = playerOptions.vid || playerOptions.source.replace(/\?.*$/, '');
              var memoryTime = localStorage.getItem(memoryVideo) || 0;
              if (memoryTime) {
                memoryTime = parseInt(memoryTime);
              }
              var components = player.getOptions() && player.getOptions().components;
              var memoryComponent = Array.isArray(components) ? components.filter(function (item) {
                return item.type.name === 'MemoryPlayComponent';
              })[0] : components.find(function (item) {
                return item.type.name === 'MemoryPlayComponent';
              });
              var memoryAutoPlay = memoryComponent && memoryComponent.args[0];
              if (!memoryAutoPlay || player.getCurrentTime() >= memoryTime) {
                player.play();
              }
            }
          }, 1000);
        }
      }
    }, {
      key: 'removeComponent',
      value: function removeComponent() {
        this.html.parentNode.removeChild(this.html);
        this.html = null;
      }
    }, {
      key: 'ready',
      value: function ready(player) {
        if (this.html !== null) {
          player.pause();
        }
      }
    }]);
  
    return StartADComponent;
  }();
  
  exports.default = StartADComponent;
  
  /***/ }),
  /* 81 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"start-ad\">\n  <span class=\"tip\"><span class=\"ad-name\">广告</span>: <i></i><span class=\"second\">秒</span></span>\n  <a class=\"ad-content\" target=\"_blank\">\n    <img />\n  </a>\n</div>";
  
  /***/ }),
  /* 82 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(83);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 83 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".start-ad {\n  width: 100%;\n  height: 100%;\n  box-sizing: border-box;\n  background-color: #000;\n  position: absolute;\n  z-index: 9999; }\n  .start-ad .tip {\n    position: absolute;\n    right: 5px;\n    top: 5px;\n    background-color: #333;\n    color: #fff;\n    font-size: 14px;\n    text-align: center;\n    padding: 3px 7px; }\n    .start-ad .tip i {\n      font-style: normal; }\n  .start-ad a {\n    width: 100%;\n    height: 100%;\n    display: inline-block;\n    text-align: center;\n    display: flex;\n    align-items: center;\n    justify-content: center; }\n    .start-ad a img {\n      max-width: 100%;\n      max-height: 100%; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 84 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(85);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.TrackComponent = _index2.default;
  
  /***/ }),
  /* 85 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(86);
  
  var _index2 = _interopRequireDefault(_index);
  
  var _trackModal = __webpack_require__(87);
  
  var _trackModal2 = _interopRequireDefault(_trackModal);
  
  __webpack_require__(88);
  
  var _utils = __webpack_require__(2);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /**
   * 音轨组件
   */
  var TrackComponent = function () {
    /**
     * @constructor 音轨组件构造函数
     */
  
    function TrackComponent() {
      _classCallCheck(this, TrackComponent);
  
      this.trackList = null;
      this.html = (0, _utils.parseDom)(_index2.default);
      this.modalHtml = (0, _utils.parseDom)(_trackModal2.default);
      this.hasCreated = false;
      this.definition = '';
    }
  
    _createClass(TrackComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var _this = this;
  
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        this.modalHtml.querySelector('.switchimg').innerText = this.isEn ? 'Track to you for' : '音轨切换到';
        this.modalHtml.querySelector('.switchimg').style.display = 'none';
        var eleControlbar = el.querySelector('.prism-controlbar');
        eleControlbar.appendChild(this.html);
        el.appendChild(this.modalHtml);
        player.on('audioTrackReady', function (_ref) {
          var paramData = _ref.paramData;
  
          _this.trackList = paramData;
          var lis_ele = paramData.map(function (v) {
            return '<li data-def="' + v.value + '">' + v.text + '</li>';
          });
          _this.html.querySelector('.track-list').innerHTML = '<li style="background:rgba(88,87,86,.5);color:#fff">\u97F3\u8F68</li>' + lis_ele.join('');
        });
        var currentTrackEle = this.html.querySelector('.current-track');
        var trackListEle = this.html.querySelector('.track-list');
  
        if (this.hasCreated == false && this.definition) {
          var li_target = trackListEle.querySelector('li[data-def="' + this.definition + '"]');
          li_target.className = 'current';
        }
  
        this.hasCreated = true;
  
        var timeId = null;
        currentTrackEle.onclick = function () {
          trackListEle.style.display = 'block';
        };
  
        currentTrackEle.onmouseleave = function () {
          timeId = setTimeout(function () {
            trackListEle.style.display = 'none';
          }, 100);
        };
  
        trackListEle.onmouseenter = function () {
          clearTimeout(timeId);
        };
        trackListEle.onmouseleave = function () {
          trackListEle.style.display = 'none';
          _this.modalHtml.style.display = 'none';
        };
  
        trackListEle.onclick = function (_ref2) {
          var target = _ref2.target;
  
          var definition = target.dataset.def;
          if (definition) {
            if (target.className !== 'current') {
              var track = Array.isArray(_this.trackList) ? _this.trackList.filter(function (v) {
                return v.value.toString() === definition;
              })[0] : _this.trackList.find(function (v) {
                return v.value.toString() === definition;
              });
              player._audioTrackService.switch(track.value);
              _this.setCurrentTrack(track.text, track.value);
              _this.modalHtml.style.display = 'block';
              _this.modalHtml.querySelector('.switchimg').style.display = 'block';
              _this.modalHtml.querySelector('span.current-track-tag').innerText = track.text;
            }
          }
        };
      }
    }, {
      key: 'setCurrentTrack',
      value: function setCurrentTrack(track, def) {
        var currentTrack = this.html.querySelector('.current-track');
        currentTrack.innerText = track;
        currentTrack.dataset.def = def;
        this.definition = def;
  
        var trackListEle = this.html.querySelector('.track-list');
        var currentEle = trackListEle.querySelector('.current');
        if (currentEle) {
          currentEle.className = '';
        }
        // let li_target = trackListEle.querySelector(`li[data-def="${def}"]`)
        var lis = trackListEle.querySelectorAll('li');
        lis.forEach(function (element) {
          var text = element.innerText;
          if (text === track) {
            element.className = 'current';
          }
          console.log(text);
        });
        if (lis) {
          lis.className = 'current';
        }
      }
    }, {
      key: 'created',
      value: function created(player) {}
    }, {
      key: 'ready',
      value: function ready(player) {
        this.modalHtml.style.display = 'none';
        // 隐藏设置里面的音轨
        var settingEle = document.querySelector('.prism-setting-item.prism-setting-audio');
        if (settingEle) {
          settingEle.classList.add('player-hidden');
        }
      }
    }]);
  
    return TrackComponent;
  }();
  
  exports.default = TrackComponent;
  
  /***/ }),
  /* 86 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"track-components\">\n  <div class=\"current-track\"></div>\n  <ul class=\"track-list\">\n    \n  </ul>\n</div>";
  
  /***/ }),
  /* 87 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"track-modal prism-info-display prism-info-left-bottom\">\n    <span class=\"switchimg\"></span> <span class=\"current-track-tag\"></span>\n</div>";
  
  /***/ }),
  /* 88 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(89);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 89 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".player-hidden {\n  display: none !important; }\n\n.track-components {\n  float: right;\n  color: #fff;\n  height: 35px;\n  position: relative;\n  box-sizing: border-box;\n  margin-top: 5px; }\n\n.current-track {\n  display: flex;\n  height: 100%;\n  align-items: center;\n  justify-content: center;\n  width: 100px;\n  cursor: pointer; }\n\n.track-list {\n  position: absolute;\n  bottom: 46px;\n  display: none;\n  padding: 0;\n  margin: 0;\n  list-style: none;\n  height: auto;\n  width: 100px;\n  overflow-y: scroll; }\n  .track-list li {\n    text-align: center;\n    width: 100px;\n    line-height: 30px;\n    background-color: rgba(0, 0, 0, 0.6);\n    cursor: pointer; }\n    .track-list li.current {\n      color: #00c1de; }\n    .track-list li + li {\n      border-top: 1px solid rgba(78, 78, 78, 0.3); }\n    .track-list li:hover {\n      background-color: rgba(0, 0, 0, 0.5); }\n\n@media (max-width: 768px) {\n  .current-track {\n    width: 40px; }\n  .track-list li {\n    width: 43px; } }\n\n.prism-player .prism-info-display .switchimg {\n  color: #fff; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 90 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(91);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.VideoADComponent = _index2.default;
  
  /***/ }),
  /* 91 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  var _index = __webpack_require__(92);
  
  var _index2 = _interopRequireDefault(_index);
  
  __webpack_require__(93);
  
  var _utils = __webpack_require__(2);
  
  var _mbIndex = __webpack_require__(95);
  
  var _mbIndex2 = _interopRequireDefault(_mbIndex);
  
  var _currentDevice = __webpack_require__(3);
  
  var _currentDevice2 = _interopRequireDefault(_currentDevice);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  /*
   * PC 视频广告组件
   */
  var VideoAdComponent = function () {
    /** 
     * @constructor PC 视频广告的构造函数
     * @param adVideoSource {@String 广告视频的视频地址 必须参数}
     * @param adLink {@String 广告视频的链接地址 必须参数}
     * @param adCloseFunction {@Function 关闭广告的点击事件处理函数, 可选参数, 不传则默认关闭广告视频}
     * @param closeText {@String 关闭广告的文字内容, 可选参数, 不传则默认为 '关闭广告'}
     */
    function VideoAdComponent(adVideoSource, adLink, adCloseFunction) {
      var closeText = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '关闭广告';
  
      _classCallCheck(this, VideoAdComponent);
  
      this.adVideoSource = adVideoSource;
      this.adLink = adLink;
      this.html = (0, _utils.parseDom)(_index2.default);
      this.adInterval = null;
      this.adCloseFunction = adCloseFunction;
      this.html.querySelector('.video-ad-close-text').innerText = closeText;
  
      this.adDuration = null; // 视频广告的时长, 用于倒计时, 
      this.player = null;
    }
  
    _createClass(VideoAdComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var _this = this;
  
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        this.html.querySelector('.video-ad-detail').innerText = this.isEn ? 'For more information' : '查看广告详情';
        this.html.querySelector('.limit').innerText = this.isEn ? 'Your browser limits autoplay' : '您的浏览器限制';
        this.html.querySelector('.manual').innerText = this.isEn ? 'Please Click' : '自动播放请点击';
        // 给广告视频添加 source
        var videoAd_ele = this.html.querySelector('#video-ad-content');
        videoAd_ele.setAttribute('src', this.adVideoSource);
  
        // 获取广告视频的时长, 并赋值到 this.duration 中
        var self = this;
        function getVideoAdTime() {
          videoAd_ele.removeEventListener('canplay', getVideoAdTime);
          //广告起播loading 
          document.getElementById('loadflag').style.display = 'none';
          self.adDuration = Math.ceil(videoAd_ele.duration);
          var adDuration_ele = self.html.querySelector('#video-ad-duration');
          adDuration_ele.innerText = self.adDuration;
          videoAd_ele.play().then(function () {
            self.setAdInterval();
          }).catch(function (err) {
            self.html.querySelector('.autoplay-video-ad').style.display = 'block';
          });
        }
  
        this.html.querySelector('.icon-player-play').onclick = function () {
          _this.playVideoAd();
          _this.html.querySelector('.autoplay-video-ad').style.display = 'none';
        };
  
        videoAd_ele.addEventListener('canplay', getVideoAdTime);
  
        // 查看详情 整个广告链接添加 href
        var videoLink = this.html.querySelector('.video-ad-link');
        var videoDetail = this.html.querySelector('.video-ad-detail');
        videoLink.setAttribute('href', this.adLink);
        videoDetail.setAttribute('href', this.adLink);
  
        el.appendChild(this.html);
      }
    }, {
      key: 'ready',
      value: function ready(player, e) {
        var _this2 = this;
  
        if (this.html !== null) {
          player.pause();
          this.player = player;
          this.html.querySelector('.video-ad-close label').onclick = function () {
            if (typeof _this2.adCloseFunction === 'function') {
              _this2.adCloseFunction(_this2);
            } else {
              _this2.closeVideoAd();
            }
            //广告播放完了之后自动播放视频
            document.getElementById('player-con').getElementsByTagName('video')[0].play();
          };
        }
      }
  
      // 暂停视频广告 
  
    }, {
      key: 'pauseVideoAd',
      value: function pauseVideoAd() {
        this.clearAdInterval();
        this.html.querySelector('#video-ad-content').pause();
      }
  
      // 播放视频广告
  
    }, {
      key: 'playVideoAd',
      value: function playVideoAd() {
        this.setAdInterval();
        this.html.querySelector('#video-ad-content').play();
      }
  
      // 清除视频广告倒计时
  
    }, {
      key: 'clearAdInterval',
      value: function clearAdInterval() {
        this.adInterval !== null && clearInterval(this.adInterval);
        this.adInterval = null;
      }
  
      // 视频广告倒计时
  
    }, {
      key: 'setAdInterval',
      value: function setAdInterval() {
        var _this3 = this;
  
        var adDuration_ele = this.html.querySelector('#video-ad-duration');
        this.adInterval = setInterval(function () {
          _this3.adDuration -= 1;
          if (_this3.adDuration <= 0) {
            _this3.closeVideoAd();
            //广告播放完了之后自动播放视频
            document.getElementById('player-con').getElementsByTagName('video')[0].play();
          } else {
            adDuration_ele.innerText = _this3.adDuration;
          }
        }, 1000);
      }
  
      // 关闭视频广告
  
    }, {
      key: 'closeVideoAd',
      value: function closeVideoAd() {
        this.clearAdInterval();
        this.html.parentNode.removeChild(this.html);
        this.html = null;
        if (this.player.getOptions().autoplay) {
          this.player.play();
        }
      }
    }]);
  
    return VideoAdComponent;
  }();
  
  /**
   * 移动端视频广告组件
   */
  
  
  var MbVideoAdComponent = function () {
    /**
     * @constructor 移动端视频广告组件
     * @param {String adVideoSource 视频广告播放地址}
     * @param {String adLink 广告链接}
     * @param {Function adCloseFunction 关闭广告按钮点击出发的函数}
     * @param {String closeText 可选参数 关闭按钮中的文字, 默认为 '关闭广告'}
     */
    function MbVideoAdComponent(adVideoSource, adLink, adCloseFunction) {
      var closeText = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '关闭广告';
  
      _classCallCheck(this, MbVideoAdComponent);
  
      this.adVideoSource = adVideoSource;
      this.adLink = adLink;
      this.html = (0, _utils.parseDom)(_mbIndex2.default);
      this.adInterval = null;
      this.adCloseFunction = adCloseFunction;
      this.html.querySelector('.video-ad-close-text').innerText = closeText;
      this.html.querySelector('.video-ad-link').setAttribute('href', this.adLink);
      this.html.querySelector('.video-ad-detail').setAttribute('href', this.adLink);
      this.adDuration = null; // 视频广告的时长, 用于倒计时, 
    }
  
    _createClass(MbVideoAdComponent, [{
      key: 'createEl',
      value: function createEl(el, player) {
        var lang = player._options && player._options.language;
        this.isEn = lang && lang === 'en-us';
        this.html.querySelector('.video-ad-detail').innerText = this.isEn ? 'For more information' : '查看广告详情';
        this.html.querySelector('.limit').innerText = this.isEn ? 'Your browser limits autoplay' : '您的浏览器限制';
        this.html.querySelector('.manual').innerText = this.isEn ? 'Please Click' : '自动播放请点击';
        el.appendChild(this.html);
        el.querySelector('video');
        el.querySelector('video').setAttribute('preload', 'load');
  
        // 隐藏 controlbar 
        var controlBar = el.querySelector('.prism-controlbar');
        controlBar.className = controlBar.className + ' controlbar-element-hidden';
        // 隐藏播放暂停按钮
        var pauseBtn = el.querySelector('.prism-big-play-btn');
        pauseBtn.className = pauseBtn.className + ' controlbar-element-hidden';
      }
    }, {
      key: 'created',
      value: function created(player) {
        var _this4 = this;
  
        this.player = player;
  
        this.vdSource = player.getOptions().source;
        player.loadByUrl(this.adVideoSource);
  
        var btnPlay_el = this.html.querySelector('.autoplay-video-ad');
        btnPlay_el.onclick = function () {
          //alert('created')
          if (_this4.adDuration === null) {
            return;
          }
          player.loadByUrl(_this4.adVideoSource);
          _this4.html.parentNode.querySelector('.prism-big-play-btn').click();
        };
  
        if (this.adDuration === null) {
          var _timeupdateHandle = function _timeupdateHandle() {
            var duration = aliplayer_el.duration;
            console.log('duration', duration);
            if (!isNaN(duration) && duration !== 0) {
              aliplayer_el.removeEventListener('timeupdate', _timeupdateHandle);
              self.adDuration = Math.ceil(aliplayer_el.duration);
              if (self.html.querySelector('.autoplay-video-ad').style.display !== 'none') {
                self.html.querySelector('.autoplay-video-ad').style.display = 'none';
                player.play();
              }
              self.html.querySelector('#video-ad-duration').innerText = self.adDuration;
              self.setAdInterval();
              // document.getElementById('loadflag').style.display = 'none'
            }
          };
  
          //player.loadByUrl(this.adVideoSource)
          this.adDuration = undefined;
  
          var aliplayerWrap_el = this.html.parentNode;
          var aliplayer_el = aliplayerWrap_el.querySelector('video');
          var self = this;
  
  
          aliplayer_el.addEventListener('timeupdate', _timeupdateHandle);
        }
      }
    }, {
      key: 'ready',
      value: function ready(player) {
        var _this5 = this;
  
        //alert("2222222")
        // let btnPlay_el = this.html.querySelector('.autoplay-video-ad')
        // btnPlay_el.onclick = () => {
        //   alert('ready')
        //   if (this.adDuration === null) {
        //     return
        //   }
        //   this.html.parentNode.querySelector('.prism-big-play-btn').click()
        // }
  
        // if (this.adDuration === null) {
        //   player.loadByUrl(this.adVideoSource)
        //   this.adDuration = undefined
  
        //   let aliplayerWrap_el = this.html.parentNode
        //   let aliplayer_el = aliplayerWrap_el.querySelector('video')
        //   let self = this
        //   function timeupdateHandle () {
        //     let duration = aliplayer_el.duration
        //     console.log('duration', duration)
        //     if (!isNaN(duration) && duration !== 0) {
        //       aliplayer_el.removeEventListener('timeupdate', timeupdateHandle)
        //       self.adDuration = Math.ceil(aliplayer_el.duration)
        //       if (self.html.querySelector('.autoplay-video-ad').style.display !== 'none') {
        //         self.html.querySelector('.autoplay-video-ad').style.display = 'none'
        //         player.play()
        //       }
        //       self.html.querySelector('#video-ad-duration').innerText = self.adDuration
        //       self.setAdInterval()
        //     }
        //   }
  
        //   aliplayer_el.addEventListener('timeupdate', timeupdateHandle)
  
        // 关闭广告点击事件
        this.html.querySelector('.video-ad-close label').onclick = function () {
          if (typeof _this5.adCloseFunction === 'function') {
            _this5.adCloseFunction(_this5);
          } else {
            _this5.closeVideoAd();
          }
          //广告播放完了之后自动播放视频
          document.getElementById('player-con').getElementsByTagName('video')[0].play();
        };
        // }
      }
  
      // 视频广告倒计时
  
    }, {
      key: 'setAdInterval',
      value: function setAdInterval() {
        var _this6 = this;
  
        var adDuration_ele = this.html.querySelector('#video-ad-duration');
        this.adInterval = setInterval(function () {
          _this6.adDuration -= 1;
          if (_this6.adDuration <= 0) {
            _this6.closeVideoAd();
            //广告播放完了之后自动播放视频
            document.getElementById('player-con').getElementsByTagName('video')[0].play();
          } else {
            adDuration_ele.innerText = _this6.adDuration;
          }
        }, 1000);
      }
  
      // 关闭视频广告
  
    }, {
      key: 'closeVideoAd',
      value: function closeVideoAd() {
        this.clearAdInterval();
        this.player.loadByUrl(this.vdSource);
        var controlBar = this.html.parentNode.querySelector('.prism-controlbar');
        controlBar.className = controlBar.className.replace(' controlbar-element-hidden', '');
        var pauseBtn = this.html.parentNode.querySelector('.prism-big-play-btn');
        pauseBtn.className = pauseBtn.className.replace(' controlbar-element-hidden', '');
        if (this.player.getOptions().autoplay) {
          this.player.play();
        }
        this.html.parentNode.removeChild(this.html);
      }
  
      // 清除视频广告倒计时
  
    }, {
      key: 'clearAdInterval',
      value: function clearAdInterval() {
        this.adInterval !== null && clearInterval(this.adInterval);
        this.adInterval = null;
      }
  
      // 播放视频广告
  
    }, {
      key: 'playVideoAd',
      value: function playVideoAd() {
        this.setAdInterval();
        this.player.play();
      }
  
      // 暂停视频广告 
  
    }, {
      key: 'pauseVideoAd',
      value: function pauseVideoAd() {
        this.clearAdInterval();
        this.player.pause();
      }
    }]);
  
    return MbVideoAdComponent;
  }();
  
  var defaultComponent = VideoAdComponent;
  
  if (_currentDevice2.default.mobile()) {
    defaultComponent = MbVideoAdComponent;
  }
  
  exports.default = defaultComponent;
  
  /***/ }),
  /* 92 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"video-ad-component\">\n  <video id=\"video-ad-content\" x5-video-player-type=\"h5\" x5-video-player-fullscreen=\"false\"></video>\n  <a class=\"video-ad-link\" target=\"_blank\"></a>\n  <div class=\"video-ad-close\">\n    <span id=\"video-ad-duration\"></span>\n    <label>\n      <span class=\"video-ad-close-text\"></span>\n      <i class=\"iconfont icon-close\"></i>\n    </label>\n  </div>\n  <a class=\"video-ad-detail\" target=\"_blank\"></a>\n  <div class=\"autoplay-video-ad\">\n    <i class=\"iconfont icon-player-play\"></i>\n    <span class=\"limit\"></span>\n    <span class=\"manual\"></span>\n  </div>\n  <div id=\"loadflag\" class=\"prism-loading center\">\n    <div class=\"circle\"></div> \n    <div class=\"circle1\"></div>\n  </div>\n</div>";
  
  /***/ }),
  /* 93 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(94);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 94 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".video-ad-component {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 2147483647;\n  background-color: #000;\n  font-size: 12px; }\n  .video-ad-component #video-ad-content {\n    width: 100%;\n    height: 100%; }\n  .video-ad-component .video-ad-link {\n    width: 100%;\n    height: 100%;\n    display: block;\n    position: absolute;\n    top: 0;\n    left: 0; }\n  .video-ad-component .video-ad-close {\n    background-color: rgba(59, 59, 59, 0.85);\n    position: absolute;\n    top: 15px;\n    right: 20px;\n    color: #fff;\n    line-height: 26px;\n    padding: 0 10px;\n    user-select: none; }\n    .video-ad-component .video-ad-close #video-ad-duration {\n      color: #00c1de;\n      vertical-align: top;\n      font-size: 14px;\n      position: relative;\n      display: inline-block;\n      text-align: right; }\n      .video-ad-component .video-ad-close #video-ad-duration::after {\n        background-color: #fff;\n        position: absolute;\n        content: '';\n        right: -7px;\n        top: 8px;\n        bottom: 8px;\n        width: 1px; }\n    .video-ad-component .video-ad-close label {\n      cursor: pointer;\n      display: inline-block; }\n    .video-ad-component .video-ad-close .icon-close {\n      font-size: 12px;\n      vertical-align: top; }\n      @media (min-width: 768px) {\n        .video-ad-component .video-ad-close .icon-close:hover {\n          color: #00c1de; } }\n    .video-ad-component .video-ad-close .video-ad-close-text {\n      padding: 0 5px 0 10px; }\n  .video-ad-component .video-ad-detail {\n    position: absolute;\n    right: 35px;\n    bottom: 30px;\n    background-color: rgba(0, 222, 255, 0.85);\n    color: #fff;\n    padding: 8px 12px;\n    user-select: none;\n    cursor: pointer;\n    transition: background-color .15s; }\n    @media (min-width: 768px) {\n      .video-ad-component .video-ad-detail:hover {\n        background-color: #00deff; } }\n  .video-ad-component .autoplay-video-ad {\n    position: absolute;\n    color: #fff;\n    top: 50%;\n    left: 50%;\n    text-align: center;\n    padding: 10px;\n    border-radius: 5px;\n    background-color: rgba(144, 147, 153, 0.85);\n    transform: translate(-50%, -50%);\n    display: none; }\n    .video-ad-component .autoplay-video-ad i {\n      font-size: 42px;\n      margin-bottom: 7px;\n      display: inline-block;\n      cursor: pointer; }\n      .video-ad-component .autoplay-video-ad i + span {\n        margin-bottom: 5px; }\n    .video-ad-component .autoplay-video-ad span {\n      display: block; }\n\n.controlbar-element-hidden {\n  display: none !important; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 95 */
  /***/ (function(module, exports) {
  
  module.exports = "<div class=\"video-ad-component\" style=\"background-color: transparent;\">\n  <a class=\"video-ad-link\" target=\"_blank\"></a>\n  <div class=\"video-ad-close\">\n    <span id=\"video-ad-duration\"></span>\n    <label>\n      <span class=\"video-ad-close-text\"></span>\n      <i class=\"iconfont icon-close\"></i>\n    </label>\n  </div>\n  <a class=\"video-ad-detail\" target=\"_blank\"></a>\n  <div class=\"autoplay-video-ad\" style=\"display: block;\">\n    <i class=\"iconfont icon-player-play\"></i>\n    <span class=\"limit\"></span>\n    <span class=\"manual\"></span>\n  </div>\n</div>";
  
  /***/ }),
  /* 96 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(97);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.PlayerNextComponent = _index2.default;
  
  /***/ }),
  /* 97 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
      value: true
  });
  
  var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
  
  __webpack_require__(98);
  
  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
  
  var PlayerNextComponent = function () {
      function PlayerNextComponent(clickHandle) {
          _classCallCheck(this, PlayerNextComponent);
  
          this.clickHandle = clickHandle;
          this._html = document.createElement('div');
          this._html.setAttribute('class', 'player-olympic-player-next');
          var innerDiv = document.createElement('div');
          innerDiv.setAttribute('class', 'player-olympic-player-next-tip');
          innerDiv.textContent = "Next";
          this._html.appendChild(innerDiv);
      }
  
      _createClass(PlayerNextComponent, [{
          key: 'createEl',
          value: function createEl(el) {
              this._html.addEventListener('click', this.clickHandle);
              el.querySelector('.prism-play-btn').insertAdjacentElement('afterend', this._html);
          }
      }]);
  
      return PlayerNextComponent;
  }();
  
  exports.default = PlayerNextComponent;
  
  /***/ }),
  /* 98 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(99);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 99 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".player-olympic-player-next {\n  width: 32px;\n  height: 32px;\n  background: url(" + __webpack_require__(100) + ") center no-repeat;\n  background-size: contain;\n  float: left;\n  margin-left: 10px;\n  margin-top: 8px;\n  cursor: pointer;\n  position: relative; }\n\n.player-olympic-player-next-tip {\n  position: absolute;\n  top: -45px;\n  display: none;\n  font-size: 12px;\n  color: #ffffff;\n  line-height: 28px;\n  letter-spacing: 0;\n  text-align: center;\n  background: #3c3c3c;\n  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.1);\n  width: 58px;\n  height: 28px; }\n\n.player-olympic-player-next:hover .player-olympic-player-next-tip {\n  display: block; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 100 */
  /***/ (function(module, exports) {
  
  module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAAAXNSR0IArs4c6QAAApJJREFUeAHt2T9OwzAUBvAatTC1lZIwsTJzAC7ACTgBLEhdOjGWDSYWVsQB6AVgR2wsCAkhsTI1ttQyIP4I87nCkpEiVJU0fkEfQ506afv847n2axoN/lGAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUmFVAzXpheF2e5z2l1EuSJGdobXgu5vEozwtjWc2yucbpxrI0z4CsUtmntafamCtjzMY871GX18wF5Adnrd0E1M1I6+ORtW3f/5/aPwE5CCA18dBXxtxrrbf/E44by5+BPAig1pBN57nWF+PxeN33170tDchDAGrr7f39Dtk0wPGK769rWzrQN8QKsukA2eSgtuqK4+JeFJA3WQfUBaDOsTVY8511ahcNNLXAVHNf3u5LvI/jJoEKBLCDayObjnNjbgC1WXCJyK5KMujHyK3dANYV9k6nk8kk/XFO4JPqgYCAaabwsIPV7gHZtDN9LhDHhRQFyFsAJsW0E12yRAUKoMSWLCKAHNR0dRNYsogBCrJJVMkiDiiAElGyiAX6hopeskgH8gkVrZW+7X9tKHWUJckhftp9jaEkFgggl61ms9ftdh9jwPjPFAcEmCf8wt5P03Tog4zZigECzAdqtBObJINMqeeYKOFniwACzvWSUnu4jXQbBifhOCoQYDSm0760+2vhPyYKEGAsptPZcqu13+l0dBiQtOPqgZS6RdbsZWl6LQ2jKJ7KgIDyjMwZYDqdoP0oCkZiXyVAABm6m4tYup8kIvwW06KBHrE69QBz+VsQks8tCih6iVAWeulAmE4iSgRxQIARVSKIAXIrksQSQQQQcMSWCFGBsA/OgbMruUQoC4jvQwEKUIACFKAABShAAQpQgAIUoAAFKEABClBgdoEvgRgNiNlEUbkAAAAASUVORK5CYII="
  
  /***/ }),
  /* 101 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  var _index = __webpack_require__(102);
  
  var _index2 = _interopRequireDefault(_index);
  
  function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
  
  if (!window.AliPlayerComponent) {
    window.AliPlayerComponent = {};
  }
  window.AliPlayerComponent.StaticAdComponent = _index2.default;
  
  /***/ }),
  /* 102 */
  /***/ (function(module, exports, __webpack_require__) {
  
  "use strict";
  
  
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var html = __webpack_require__(103);
  __webpack_require__(105);
  
  /**
  * 静态广告组件
  */
  var StaticADComponent = Aliplayer.Component({
    /**
    * 初始函数，在new对象时调用
    *
    * @param {string} adAddress - 广告视频地址
    * @param {string} toAddress - 广告链接地址
    */
    init: function init(adAddress, toAddress) {
      this.adAddress = adAddress;
      this.toAddress = toAddress;
      this.$html = $(html);
    },
  
    /**
    * 创建广告Dom元素
    */
    createEl: function createEl(el) {
      (Array.isArray(this.$html) ? this.$html.filter('.ad')[0] : this.$html.find('.ad')).attr('src', this.adAddress);
      var $adWrapper = Array.isArray(this.$html) ? this.$html.filter('.ad-wrapper')[0] : this.$html.find('.ad-wrapper');
      $adWrapper.attr('href', this.toAddress);
      $adWrapper.click(function () {
        Aliplayer.util.stopPropagation();
      });
      (Array.isArray(this.$html) ? this.$html.filter('.close')[0] : this.$html.find('.close')).click(function () {
        this.$html.hide();
      });
      $(el).append(this.$html);
    },
    ready: function ready(player, e) {},
    /**
    * 隐藏广告
    */
    play: function play(player, e) {
      this.$html.hide();
    },
    /**
    * 显示广告
    */
    pause: function pause(player, e) {
      this.$html.show();
    },
    /**
    * 隐藏广告
    */
    playing: function playing(player, e) {
      this.$html.hide();
    },
    waiting: function waiting(player, e) {},
    timeupdate: function timeupdate(player, e) {},
    error: function error(player, e) {},
    /**
    * 显示广告
    */
    ended: function ended(player, e) {
      this.$html.show();
    }
  });
  
  exports.default = StaticADComponent;
  
  /***/ }),
  /* 103 */
  /***/ (function(module, exports, __webpack_require__) {
  
  module.exports = "<div class=\"ad-container\">\n    <span>广告</span>\n    <img class=\"close\" src=\"" + __webpack_require__(104) + "\"></img>\n    <a class='ad-wrapper' target=\"_blank\">\n       <img class=\"ad\"></img>\n    </a>\n</div>";
  
  /***/ }),
  /* 104 */
  /***/ (function(module, exports) {
  
  module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA2hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDpCNzdDNzkwNTE1MjA2ODExODIyQTg3RDcyQkRGNjk2MiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo3Rjg1NzQ2NDY0MjYxMUU2ODk0QUZBRTg4N0Y5MDgxRCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo3Rjg1NzQ2MzY0MjYxMUU2ODk0QUZBRTg4N0Y5MDgxRCIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M2IChNYWNpbnRvc2gpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6OUU0MTk5OEUxRTIwNjgxMTgyMkE4N0Q3MkJERjY5NjIiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6Qjc3Qzc5MDUxNTIwNjgxMTgyMkE4N0Q3MkJERjY5NjIiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7gi5EfAAABEklEQVR42qyUTQ6CMBCFW004hLrSU3gL/zd6Cb0AsJSt3kIT40lcG2N0oXdQN/W9pJgKLT/iSz5Spp0HZcpIpZSwyAM9TRc0wAvcwAHswF7HvkXDBH1wUvnimkEy37ypg6Uqr6XOTRn+YmaafhmOVHXRQ0hcWIAzaIlquoN2DZdx0iwIAiGlFGEYprIY4xzXJNQEE253Y3t/3/d5nhQSPzGOGeOcQ1saXl2zpmkBM+pKw0fWiti0gBn1rFlPuyF+L9vYKbgeXY8zt2n7pq4tb/LMsgplK8q0iFlB09m/D3YnLspCVNccPM3mEFX4jyNX+1r9YLZ2ta+YYYkGO8pqsCaerv4WXMBLc9GxqV6Tyn0LMACAi/Wdy4svogAAAABJRU5ErkJggg=="
  
  /***/ }),
  /* 105 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(106);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 106 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, ".ad-container {\n  display: none;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  margin-left: -150px;\n  margin-top: -100px; }\n\n.ad-container span {\n  width: 27px;\n  height: 16px;\n  border-radius: 1px;\n  background-color: rgba(0, 0, 0, 0.5);\n  position: absolute;\n  font-size: 10px;\n  color: #fff;\n  line-height: 16px;\n  text-align: center;\n  top: 0px;\n  left: 0px; }\n\n.ad-container .close {\n  position: absolute;\n  top: 2px;\n  right: 2px;\n  width: 20px;\n  height: 20px; }\n\n.ad-container .close:hover {\n  cursor: pointer; }\n\n.ad-container .ad {\n  width: 300px;\n  height: 200px; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 107 */
  /***/ (function(module, exports, __webpack_require__) {
  
  // style-loader: Adds some css to the DOM by adding a <style> tag
  
  // load the styles
  var content = __webpack_require__(108);
  if(typeof content === 'string') content = [[module.i, content, '']];
  // add the styles to the DOM
  var update = __webpack_require__(1)(content, {});
  if(content.locals) module.exports = content.locals;
  // Hot Module Replacement
  if(false) {}
  
  /***/ }),
  /* 108 */
  /***/ (function(module, exports, __webpack_require__) {
  
  exports = module.exports = __webpack_require__(0)();
  // imports
  
  
  // module
  exports.push([module.i, "@charset \"UTF-8\";\n@font-face {\n  font-family: \"iconfont\";\n  src: url(" + __webpack_require__(5) + ");\n  /* IE9*/\n  src: url(" + __webpack_require__(5) + "#iefix) format(\"embedded-opentype\"), url(\"data:application/x-font-woff;charset=utf-8;base64,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\") format(\"woff\"), url(" + __webpack_require__(109) + ") format(\"truetype\"), url(" + __webpack_require__(110) + "#iconfont) format(\"svg\");\n  /* iOS 4.1- */ }\n\n.iconfont {\n  font-family: \"iconfont\" !important;\n  font-size: 16px;\n  font-style: normal;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale; }\n\n.icon-list:before {\n  content: \"\\E643\"; }\n\n.icon-skipnext:before {\n  content: \"\\EA9E\"; }\n\n.icon-skip-previous:before {\n  content: \"\\E6D3\"; }\n\n.icon-close:before {\n  content: \"\\E616\"; }\n\n.icon-player-rotate-left:before {\n  content: \"\\E658\"; }\n\n.icon-player-rotate-right:before {\n  content: \"\\E659\"; }\n\n.icon-player-switch:before {\n  content: \"\\E781\"; }\n\n.icon-danmu-close:before {\n  content: \"\\E696\"; }\n\n.icon-danmu-open:before {\n  content: \"\\E697\"; }\n\n.icon-player-play:before {\n  content: \"\\E641\"; }\n\nbody {\n  font-family: \"Helvetica Neue\",Helvetica,\"PingFang SC\",\"Hiragino Sans GB\",\"Microsoft YaHei\",\"\\5FAE\\8F6F\\96C5\\9ED1\",Arial,sans-serif; }\n\n/* previewVodComponents 中的自定义 css */\n.vip-join {\n  color: #00c1de; }\n\n.vip_limit_content {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  flex-wrap: wrap;\n  justify-content: center;\n  align-items: center; }\n\n.vip_limit_content .title {\n  font-size: 18px;\n  line-height: 36px;\n  color: #fff;\n  text-align: center;\n  width: 100%; }\n\n.vip_limit_button_box {\n  text-align: center;\n  white-space: nowrap;\n  overflow: hidden;\n  width: 100%; }\n\n.vip_limit_btn {\n  display: inline-block;\n  min-width: 100px;\n  position: relative;\n  background: #f60;\n  padding: 0 35px;\n  margin: 0px 5px 20px 5px;\n  border-radius: 38px;\n  font-size: 18px;\n  line-height: 38px;\n  color: #623A0C;\n  text-align: center;\n  background-image: linear-gradient(-135deg, #FBE8A8 0%, #F8E7AC 15%, #E2C078 100%);\n  cursor: pointer; }\n\n.vip_limit_close {\n  text-align: center; }\n  .vip_limit_close span {\n    display: inline-block;\n    width: 40px;\n    height: 40px;\n    line-height: 36px;\n    background: rgba(165, 165, 165, 0.54);\n    border-radius: 50%;\n    font-size: 24px;\n    cursor: pointer; }\n", ""]);
  
  // exports
  
  
  /***/ }),
  /* 109 */
  /***/ (function(module, exports) {
  
  module.exports = "data:application/x-font-ttf;base64,AAEAAAALAIAAAwAwR1NVQrD+s+0AAAE4AAAAQk9TLzJW70yOAAABfAAAAFZjbWFwO8hK/wAAAgQAAAI8Z2x5ZmPo5JYAAARcAAAHLGhlYWQSO9jUAAAA4AAAADZoaGVhB94DjQAAALwAAAAkaG10eC/pAAAAAAHUAAAAMGxvY2EK2AjqAAAEQAAAABptYXhwASAAcgAAARgAAAAgbmFtZT5U/n0AAAuIAAACbXBvc3RSkUPCAAAN+AAAAMAAAQAAA4D/gABcBAAAAAAABAAAAQAAAAAAAAAAAAAAAAAAAAwAAQAAAAEAAEFDQiBfDzz1AAsEAAAAAADXjkptAAAAANeOSm0AAP9/BAADfgAAAAgAAgAAAAAAAAABAAAADABmAAoAAAAAAAIAAAAKAAoAAAD/AAAAAAAAAAEAAAAKAB4ALAABREZMVAAIAAQAAAAAAAAAAQAAAAFsaWdhAAgAAAABAAAAAQAEAAQAAAABAAgAAQAGAAAAAQAAAAAAAQP+AZAABQAIAokCzAAAAI8CiQLMAAAB6wAyAQgAAAIABQMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUGZFZABAAHjqngOA/4AAXAOAAIEAAAABAAAAAAAABAAAAAPpAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAAAAAABQAAAAMAAAAsAAAABAAAAagAAQAAAAAAogADAAEAAAAsAAMACgAAAagABAB2AAAAFAAQAAMABAB45hbmQeZD5lnml+bT54Hqnv//AAAAeOYW5kHmQ+ZY5pbm0+eB6p7//wAAAAAAAAAAAAAAAAAAAAAAAAABABQAFAAUABQAFAAWABgAGAAYAAAAAQAEAAUAAgAHAAgACQAKAAYACwADAAABBgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAACUAAAAAAAAAAsAAAB4AAAAeAAAAAEAAOYWAADmFgAAAAQAAOZBAADmQQAAAAUAAOZDAADmQwAAAAIAAOZYAADmWAAAAAcAAOZZAADmWQAAAAgAAOaWAADmlgAAAAkAAOaXAADmlwAAAAoAAObTAADm0wAAAAYAAOeBAADngQAAAAsAAOqeAADqngAAAAMAAAAAAHYA7AECAT4BbgGCAcYCDAKWAzIDlgAAAAUAAP/hA7wDGAATACgAMQBEAFAAAAEGKwEiDgIdASEnNC4CKwEVIQUVFxQOAycjJyEHIyIuAz0BFyIGFBYyNjQmFwYHBg8BDgEeATMhMjYnLgInATU0PgI7ATIWHQEBGRsaUxIlHBIDkAEKGCcehf5KAqIBFR8jHA8+Lf5JLD8UMiATCHcMEhIZEhKMCAYFBQgCAgQPDgFtFxYJBQkKBv6kBQ8aFbwfKQIfAQwZJxpMWQ0gGxJhiDRuHSUXCQEBgIABExsgDqc/ERoRERoRfBoWExIZBxANCBgaDSMkFAF35AsYEwwdJuMAAAAACQAAAAAD5QLvAAAACQAZABoAIwAzADQAPQBNAAATIz4BMhYUBiImJSEiBh0BFBYzITI2PQE0JgEjPgEyFhQGIiYlIQ4BHQEUFjMhMjY9ATQmASM+ATIWFAYiJiUhIgYdARQWMyEyNj0BNCZrQwEmOSYmOSYDrP1VCAgJBwKrBwkK/IVDASU6JSU6JQOq/VUHCQoGAqsHCQn8kUMBJTolJTolA6r9VQcJCgYCqwcJCQKrHSYmOSYmXwkHZgcJCgZmCAj+oB0mJjkmJmABCQZnBwkKBmcGCf6UHCYmOSYmYAoGZggICQdmBgoAAAACAAAAAAMAAnsAAwAGAAAlMxEjCQICq1VV/lUBa/6VegIA/gABAAEAAAAAAAMAAP+/A8EDQQAAABwAHQAAAQUBNjQmIgcJASYiBhQXCQEGFBYyNwkBFjI2NCcBA23+0gF0DRskDf6M/pAOJBsNAXH+jA0bJA0BdAFzDiQbDf6MAZQSAXEOJBsN/o4Bcg0bJA7+j/6ODiMbDQFx/osNGyQNAXYAAAIAAP9/BAADfgAMABoAAAEiDgEQHgEgPgEQLgETBQYiJjURNDYyFwUWFAIAi+uJiesBFuuJies9/t0GEQ0NEQYBIwYDfonr/urriYnrARbrif3z9wYLCQHvCAsG9wYQAAIAAAAAAwACgAADAAYAAAEzESMTAREBAFVVlQFrAoD+AAEA/wACAAABAAD/wQOtAz8AKQAAExc+ATceARcOAQcuAScmNjcyFx4BMz4BNy4BJw4BBxcWBgcjLgE9ATQ2YFE9ol2+/QUF/L5kqj4bICsdEyx5R4izAgS1iEFyKk8JCQ38CAoWAwtQPUYBBfy9v/wFAU5FIkcDFTE4A7WIh7ECATIrUAkWAQEKCPwNCQAAAQAA/8EDrQM/ACkAAAEVFAYHIy4BPwEuAScOAQceARcyNjc2Mx4BBw4BBy4BJz4BNx4BFzc2FgOtCwj8DAkIUCtxQoe1BQK0h0d5LBQdKx8aP6pkvvwEBf29XqE9UQoVAv78CAoBARYJUCsyAQKxh4i1AzgxFQNHIkVOAQX8v738BQFGPVAJCQAAAAAKAAAAAAPSAq8AIwAnACsALwAzADcAOwBHAE8AWAAAJSEiJicRPgE3IR4BFxUUFjI2PQEuASchDgEHER4BFyEyNjQmAzMVIzczFSMHMxUjNzMVIxUzFSM3MxUjJQ4BBx4BFz4BNy4BAy4BJzY3FwY3JzY3HgEXFAYCL/5tGSUBASUZAjwaJAENFQ0CPy39xC0/AQE/LQGTCg0N8y4uXbq66S8vXbq6Ly9du7sBr0pjAgJjSkljAgJjSTZJAQEPrxw/tCQuNkgCEK8lGQFUGSUBASUZHgsMDAseLT8CAj8t/qwtPwENFQwBdS8vL0UvLy9GLi4uowJjSkpjAgJjSkpj/tMBSTYjHK8PLrMdAQFJNhcqAAAKAAAAAAPSAq8AIwAnACsALwAzADcAOwBHAFMAZQAAJSEiJicRPgE3IR4BFxUUFjI2PQEuASchDgEHER4BFyEyNjQmAzMVIzczFSMHMxUjNzMVIxczFSMnMxUjJQ4BBx4BFz4BNy4BAy4BJz4BNx4BFw4BNyYiDwEnJiIGFB8BFjI/ATY0Ai/+bRklAQElGQI8GiQBDRUNAj8t/cQtPwEBPy0BkwoNDfMuLl26uukvL126ul27u10vLwIMSmMCAmNKSWMCAmNJNkkBAUk2NkgCAkgsCBIHUTEIEg4HQQgSB2IHryUZAVQZJQEBJRkeCwwMCx4tPwICPy3+rC0/AQ0VDAF1Ly8vRS8vL0YuLi6jAmNKSmMCAmNKSmP+0wFJNjZJAQFJNjZJtQcHUjEHDhIIQQcHYgcSAAACAAD/0wOqAzQAIQBDAAA3IT4BNzU0NjIWHQEOAQchFxYUBiIvAS4BNTQ/ATYyFhQHASEOAQcVFAYiJj0BPgE3IScmNDYyHwEeARUUDwEGIiY0N8YCGio1AREeEQJZRf3NdgoUGQqcBQUKnAoZFAoCJ/2WKjUBER4RAllFAlNdCRMZCp0FBQqdChkTCcABNSpgDxERD2BFWQJ2ChkUCp0FDAUOCJ0KFBkKAUoBNSpgDxERD2BFWQJdChkTCaAFDQUNCZ0KFBkKAAAAEgDeAAEAAAAAAAAAFQAAAAEAAAAAAAEACAAVAAEAAAAAAAIABwAdAAEAAAAAAAMACAAkAAEAAAAAAAQACAAsAAEAAAAAAAUACwA0AAEAAAAAAAYACAA/AAEAAAAAAAoAKwBHAAEAAAAAAAsAEwByAAMAAQQJAAAAKgCFAAMAAQQJAAEAEACvAAMAAQQJAAIADgC/AAMAAQQJAAMAEADNAAMAAQQJAAQAEADdAAMAAQQJAAUAFgDtAAMAAQQJAAYAEAEDAAMAAQQJAAoAVgETAAMAAQQJAAsAJgFpCkNyZWF0ZWQgYnkgaWNvbmZvbnQKaWNvbmZvbnRSZWd1bGFyaWNvbmZvbnRpY29uZm9udFZlcnNpb24gMS4waWNvbmZvbnRHZW5lcmF0ZWQgYnkgc3ZnMnR0ZiBmcm9tIEZvbnRlbGxvIHByb2plY3QuaHR0cDovL2ZvbnRlbGxvLmNvbQAKAEMAcgBlAGEAdABlAGQAIABiAHkAIABpAGMAbwBuAGYAbwBuAHQACgBpAGMAbwBuAGYAbwBuAHQAUgBlAGcAdQBsAGEAcgBpAGMAbwBuAGYAbwBuAHQAaQBjAG8AbgBmAG8AbgB0AFYAZQByAHMAaQBvAG4AIAAxAC4AMABpAGMAbwBuAGYAbwBuAHQARwBlAG4AZQByAGEAdABlAGQAIABiAHkAIABzAHYAZwAyAHQAdABmACAAZgByAG8AbQAgAEYAbwBuAHQAZQBsAGwAbwAgAHAAcgBvAGoAZQBjAHQALgBoAHQAdABwADoALwAvAGYAbwBuAHQAZQBsAGwAbwAuAGMAbwBtAAAAAAIAAAAAAAAACgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAECAQMBBAEFAQYBBwEIAQkBCgELAQwBDQABeAUxbXVsdQhza2lwbmV4dAVjbG9zZQRwbGF5DXNraXAtcHJldmlvdXMRYXJyb3ctcm90YXRlLWxlZnQSYXJyb3ctcm90YXRlLXJpZ2h0EmJvZmFuZ3FpLWRhbm11Z3VhbhFib2ZhbmdxaS1kYW5tdWthaQ5uZWlyb25ncWllaHVhbgAA"
  
  /***/ }),
  /* 110 */
  /***/ (function(module, exports) {
  
  module.exports = "data:image/svg+xml;base64,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"
  
  /***/ })
  /******/ ]);