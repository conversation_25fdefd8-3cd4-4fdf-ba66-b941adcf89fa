<template>
  <div style="width: 250px;">
    <div class="left-cate">{{ title }}</div>
    <Draggable tag="ul" item-key="type" ghostClass="ghost" :group="{ name: 'people', pull: 'clone', put: false }"
      :sort="false" :list="list" :move="handleMove">
      <template #item="{ element }">
        <li v-if="fields.includes(element.type)" class="form-edit-left-label"
          :class="{ 'no-put': element.type === 'divider' }">
          <a>
            <cusSvgIcon :iconClass="element.type" />
            <span>{{ element.label }}</span>
          </a>
        </li>
      </template>
    </Draggable>
  </div>
</template>

<script>
import { defineComponent } from 'vue'
import Draggable from 'vuedraggable'
export default defineComponent({
  name: 'ComponentGroup',
  components: {
    Draggable
  },
  props: {
    title: {
      type: String,
      required: true
    },
    fields: {
      type: Array,
      required: true
    },
    list: {
      required: true
    }
  },
  methods: {
    handleMove(e) {
      console.log(e, '移动')
      //不允许停靠
      if (e.relatedContext.element.key == 'title') return false;
      if (e.relatedContext.index < 1) return false;
    }
  }
})
</script>
<style lang="scss" scoped>
$primary-color: #2745B2;
$primary-background-color : #ecf5ff;

.left-cate {
  padding: 8px 12px;
  font-size: 13px;

}

ul {
  overflow: hidden;
  padding: 0 10px 10px;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 10px;
  justify-items: center;
}

.form-edit-left-label {
  font-size: 12px;
  width: 100%;
  padding: 10px 0;
  cursor: move;
  border: 1px solid #F4F6FC;
  background: #f4f6fc;
  border-radius: 5px;

  &:hover {
    border: 1px dashed $primary-color;

    &>a {
      color: $primary-color;
    }
  }

  &>a {
    color: #333;
    display: flex;
    flex-direction: column;
    align-items: center;


    .svg-icon {
      margin-right: 6px;
      margin-left: 8px;
      font-size: 18px;
      margin-bottom: 5px;
    }

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>