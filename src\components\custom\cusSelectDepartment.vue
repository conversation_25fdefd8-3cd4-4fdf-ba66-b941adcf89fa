<template>
	<el-cascader
		v-model="value"
		:options="treeData"
		:props="prop"
		collapse-tags
		collapse-tags-tooltip
		:placeholder="placeholder"
		:disabled="disabled"
		:style="{ width: width }"
		@change="handleChange"
	/>
</template>

<script>
import cusTom from '@/utils/cusTom'
export default {
	props: {
		modelValue: {
			type: [String, Number, Object],
			default: ''
		},
		multiple: {
			type: Boolean,
			default: false
		},
		placeholder: {
			typetype: String,
			default: '请选择'
		},
		disabled: {
			type: Boolean,
			default: false
		},
		width: {
			type: String,
			default: '100%'
		},
		tenantId: {
			type: Number,
			default: 0
		},
		campusId: {
			type: Number,
			default: 0
		},
		prop: {
			type: Object,
			default: () => {
				return {
					multiple: false,
					emitPath: false,
					value: 'id',
					label: 'department_name',
					checkStrictly: false
				}
			}
		}
	},
	data() {
		return {
			treeData: [],
			value: null,
			params: {
				tenant_id: '',
				campus_id: ''
			}
		}
	},
	watch: {
		modelValue: {
			handler(val) {
				this.value = val
			},
			immediate: true
		},
		multiple: {
			handler(val) {
				this.prop.multiple = val
				console.log(val)
			},
			deep: true,
			immediate: true
		},
		treeData: {
			handler(val) {
				this.value = val
			},
			immediate: true
		},
		value() {
			this.$emit('update:modelValue', this.value)
		}
	},
	created() {
		if (this.campusId === 0 || this.tenantId === 0) {
			const { campusId, tenantId } = cusTom.getBaseQuery()
			this.params = {
				tenant_id: tenantId,
				campus_id: campusId
			}
		} else {
			this.params = {
				tenant_id: this.tenantId,
				campus_id: this.campusId
			}
		}
		this.getDept()
	},
	methods: {
		handleChange(val) {
			this.$emit('departmentChange', val)
		},
		async getDept() {
			const { data } = await this.$API.system.dept.all.get(this.params)
			this.treeData = cusTom.arrayToTree(data)
		}
	}
}
</script>

<style scoped></style>
