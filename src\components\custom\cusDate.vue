<template>
	<div>
		<template v-if="options.type === 'date'">
			<el-date-picker
				v-bind="$attrs"
				type="date"
				:placeholder="options.placeholder || '请选择日期'"
				format="YYYY-MM-DD"
				value-format="YYYY-MM-DD"
			/>
		</template>
		<template v-if="options.type === 'datetime'">
			<el-date-picker
				v-bind="$attrs"
				type="datetime"
				:placeholder="options.placeholder || '请选择日期时间'"
				format="YYYY/MM/DD HH:mm:ss"
				value-format="YYYY-MM-DD HH:mm:ss"
			/>
		</template>
		<template v-if="options.type === 'daterange'">
			<el-date-picker
				v-bind="$attrs"
				type="daterange"
				unlink-panels
				range-separator="至"
				:start-placeholder="options.startPlaceholder || '开始日期'"
				:end-placeholder="options.endPlaceholder || '结束日期'"
				:shortcuts="shortcuts"
				format="YYYY-MM-DD"
				value-format="YYYY-MM-DD"
			/>
		</template>
		<template v-if="options.type === 'year'">
			<el-date-picker v-bind="$attrs" placeholder="请选择年份" type="year" format="YYYY" value-format="YYYY" />
		</template>
	</div>
</template>

<script>
// 快捷时间
const shortcuts = [
	{
		text: '本月',
		value: () => {
			let now = new Date()
			let startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
			let endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)
			return [startOfMonth, endOfMonth]
		}
	},
	{
		text: '今年至今',
		value: () => {
			const end = new Date()
			const start = new Date(new Date().getFullYear(), 0)
			return [start, end]
		}
	},
	{
		text: '最近三个月',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setMonth(start.getMonth() - 3)
			return [start, end]
		}
	},
	{
		text: '最近六个月',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setMonth(start.getMonth() - 6)
			return [start, end]
		}
	},
	{
		text: '最近一年',
		value: () => {
			const end = new Date()
			const start = new Date()
			start.setMonth(start.getMonth() - 12)
			return [start, end]
		}
	}
]

const shortcuts1 = [
	{
		text: '今天',
		value: new Date()
	},
	{
		text: '昨天',
		value: () => {
			const date = new Date()
			date.setTime(date.getTime() - 3600 * 1000 * 24)
			return date
		}
	},
	{
		text: '一周前',
		value: () => {
			const date = new Date()
			date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
			return date
		}
	}
]

export default {
	name: '',
	props: {
		options: {
			type: Object,
			default: () => ({
				type: 'daterange',
				placeholder: '请选择日期',
				startPlaceholder: '开始日期',
				endPlaceholder: '结束日期'
			})
		}
	},
	data() {
		return {
			shortcuts,
			shortcuts1
		}
	},
	methods: {}
}
</script>

<style scoped></style>
