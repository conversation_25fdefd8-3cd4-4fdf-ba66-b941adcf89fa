<template>
    <el-container>
        <el-header style="border: none;">
            <el-tabs v-model="activeName" @tab-change="handleClick">
                <el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
            </el-tabs>
        </el-header>
        <el-main class="el-main-hascontainer">
            <component :is="currComponent.component"></component>
        </el-main>
    </el-container>
</template>
<script setup>
import gradeSchedule from './components/gradeSchedule.vue'
import classSchedule from './components/classSchedule.vue'
import teacherSchedule from './components/teacherSchedule.vue'
import classRoomSchedule from './components/classRoomSchedule.vue'
import scheduleData from './components/scheduleData.vue'
import scheduleRules from './components/scheduleRules.vue'
import { ref } from 'vue'
const activeName = ref('gradeSchedule')
const currComponent = ref({
    name: 'gradeSchedule',
    component: gradeSchedule
})
const tabs = [
    {
        name: 'gradeSchedule',
        label: '年级课表',
        component: gradeSchedule
    },
    {
        name: 'classSchedule',
        label: '班级课表',
        component: classSchedule
    },
    {
        name: 'teacherSchedule',
        label: '教师课表',
        component: teacherSchedule
    },
    {
        name: 'classRoomSchedule',
        label: '教室课表',
        component: classRoomSchedule
    },
    {
        name: 'scheduleData',
        label: '课表数据视图',
        component: scheduleData
    },
    {
        name: 'scheduleRules',
        label: '排课规则',
        component: scheduleRules
    }
]
const handleClick = (name) => {
    currComponent.value = tabs.find((item) => item.name === name)
}
</script>