<template>
	<el-dialog
		v-model="visible"
		:title="titleMap[mode]"
		:width="500"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<el-form ref="dialogForm" :model="form" :disabled="mode == 'show'">
			<el-form-item label="新增" v-if="mode == 'add'">
				<el-button type="primary" @click="addType" :disabled="form.item_name.length >= 9">新增</el-button>
			</el-form-item>
			<el-form-item
				label="名称"
				v-for="(item, index) in form.item_name"
				:rules="rules"
				:key="item.key"
				:prop="`item_name.${index}.value`"
			>
				<div class="flex_bc">
					<el-input v-model="item.value" placeholder="请输入名称" style="width: 240px" clearable> </el-input
					><el-icon v-if="mode == 'add'" style="font-size: 24px; margin-left: 10px" @click="removetype(index)"
						><el-icon-remove
					/></el-icon>
				</div>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
const defaultData = () => {
	return {
		tenant_id: '',
		campus_id: '',
		item_name: [{ value: '', key: Date.now() }]
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			titleMap: {
				add: '新增类别',
				edit: '编辑类别'
			},
			//验证规则
			rules: {
				required: true,
				message: '请输入类别名称',
				trigger: 'blur'
			}
			//所需数据选项
		}
	},
	methods: {
		//添加类型
		addType() {
            console.log(this.form);
			this.form.item_name.push({ value: '', key: Date.now() })
		},
		//删除类型
		removetype(index) {
			this.form.item_name.splice(index, 1)
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.campus_id = this.params.campus_id
			this.form.tenant_id = this.params.tenant_id
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					if (this.mode == 'add') {
						this.form.item_name = this.form.item_name.map((v) => v.value)

						var res = await this.$API.finance.recruit.recordTypeAdd.post(this.form)
						this.isSaveing = false
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode)
							this.visible = false
							this.$message.success('操作成功')
						} else {
							this.$alert(res.message, '提示', { type: 'error' })
						}
					} else {
						this.form.item_name = this.form.item_name.map((v) => v.value)[0]
						var res = await this.$API.finance.recruit.recordTypeEdit.post(this.form)
						this.isSaveing = false
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode)
							this.visible = false
							this.$message.success('操作成功')
						} else {
							this.$alert(res.message, '提示', { type: 'error' })
						}
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			if (data.type_name) {
				this.form.item_name = [{ value: data.type_name, key: Date.now() }]
			}
		}
	}
}
</script>

<style></style>
