import config from '@/config'
import http from '@/utils/request'

export default {
	my: {
		url: `${config.API_URL}/eduapi/famous/my`,
		name: '我的个人空间',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	open: {
		url: `${config.API_URL}/eduapi/famous/open`,
		name: '开通个人空间',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	introductionEdit: {
		url: `${config.API_URL}/eduapi/famous/introduction/edit`,
		name: '编辑个人简介',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	list: {
		url: `${config.API_URL}/eduapi/famous/list`,
		name: '获取名师空间列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	recommend: {
		url: `${config.API_URL}/eduapi/famous/recommend`,
		name: '推荐或取消推荐',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	order: {
		url: `${config.API_URL}/eduapi/famous/order`,
		name: '排序',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	resources: {
		list: {
			url: `${config.API_URL}/eduapi/famous/resources/list`,
			name: '获取文章或视频列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		one: {
			url: `${config.API_URL}/eduapi/famous/resources/one`,
			name: '获取单个详情',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		getCollectLikeList: {
			url: `${config.API_URL}/eduapi/famous/resources/getCollectLikeList`,
			name: '获取文章或视频点赞收藏信息列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		creat: {
			url: `${config.API_URL}/eduapi/famous/resources/add`,
			name: '添加视频或文章',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		edit: {
			url: `${config.API_URL}/eduapi/famous/resources/edit`,
			name: '修改视频或文章',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		del: {
			url: `${config.API_URL}/eduapi/famous/resources/del`,
			name: '删除视频或文章',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		}
	},
	comment: {
		list: {
			url: `${config.API_URL}/eduapi/famous/comment/list`,
			name: '获取评论列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/famous/comment/save`,
			name: '修改或新增评论',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		audit: {
			url: `${config.API_URL}/eduapi/famous/comment/audit`,
			name: '审核评论',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		del: {
			url: `${config.API_URL}/eduapi/famous/comment/del`,
			name: '删除评论',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		}
	}
}
