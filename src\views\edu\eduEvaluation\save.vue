<template>
	<el-dialog v-model="visible" :title="title" width="600px">
		<el-form ref="form" :model="form" :rules="rules" label-width="150px">
			<el-form-item label="评教名称" prop="title">
				<el-input v-model="form.title" placeholder="请输入评教名称" />
			</el-form-item>
			<el-form-item label="班主任评教表单" prop="leader_form">
				<el-select
					v-model="form.leader_form"
					placeholder="请选择班主任评教表单"
					filterable
					clearable
					style="max-width: unset;width: 100%"
				>
					<el-option v-for="item in formList" :key="item.id" :label="item.title" :value="item.id"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="科教老师评教表单" prop="teacher_form">
				<el-select
					v-model="form.teacher_form"
					placeholder="请选择科教老师评教表单"
					filterable
					clearable
					style="max-width: unset;width: 100%"
				>
					<el-option v-for="item in formList" :key="item.id" :label="item.title" :value="item.id"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="学期" prop="semester_id">
				<cusCascader
					v-model="form.semester_id"
					:options="getSemester"
					placeholder="请选择学期"
					clearable
					style="max-width: unset;width: 100%"
					@valChange="semesterChange"
				></cusCascader>
			</el-form-item>
			<el-form-item label="关联年级" prop="grade_ids">
				<el-select
					v-model="form.grade_ids"
					placeholder="请选择关联年级"
					no-data-text="请先选择学期"
					filterable
					clearable
					style="max-width: unset; width: 100%"
					@change="gradeChange"
				>
					<el-option v-for="item in gradeList" :key="item.id" :label="item.grade_name" :value="item.id"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="评教学科" prop="course_ids">
				<el-select
					v-model="form.course_ids"
					placeholder="请选择评教学科"
					no-data-text="请先选择年级"
					filterable
					clearable
					multiple
					collapse-tags
					:max-collapse-tags="6"
					collapse-tags-tooltip
					style="max-width: unset;width: 100%"
				>
					<el-option
						v-for="item in courseList"
						:key="item.course_id"
						:label="item.course_name"
						:value="item.course_id"
					></el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="截至时间" prop="end_time">
				<el-date-picker
					v-model="form.end_time"
					type="datetime"
					format="YYYY-MM-DD HH:mm:ss"
					value-format="YYYY-MM-DD HH:mm:ss"
					placeholder="请选择截至时间"
				/>
			</el-form-item>
			<el-form-item label="是否隐藏学生姓名" prop="hide_student">
				<el-switch v-model="form.hide_student" :active-value="1" :inactive-value="-1" />
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取消</el-button>
			<el-button type="primary" @click="confirm">确定</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'

const { semesterInfo } = cusTom.getBaseQuery()
export default {
	data() {
		return {
			visible: false,
			title: '新增',
			typeMap: {
				add: '新增',
				edit: '修改'
			},
			form: {},
			rules: {
				teacher_form: [{ required: true, message: '请选择科教老师评教表单', trigger: 'change'}],
				title: [{ required: true, message: '请输入评教名称', trigger: 'blur' }],
				grade_ids: [{ required: true, message: '请选择关联年级' , trigger: 'change'}],
				course_ids: [{ required: true, message: '请选择评教学科', trigger: 'blur' }],
				leader_form: [{ required: true, message: '请选择班主任评教表单' , trigger: 'change'}],
				semester_id: [{ required: true, message: '请选择学期', trigger: 'change' }],
				end_time: [{ required: true, message: '请选择截至时间', trigger: 'blur' }]
			},
			courseList: [],
			gradeList: [],
			formList: [],
			semesterInfo
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.form.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	methods: {
		semesterChange(val) {
			if (val !== this.form.semester_id) {
				this.form.semester_id = val
				this.form.grade_ids = null
				this.getGradeList()
			}
		},
		gradeChange(val) {
			this.form.grade_ids = val
			this.form.course_ids = []
			this.getCourseList()
		},
		open(type, item) {
			console.log(item)
			this.title = this.typeMap[type]
			this.visible = true
			if (type === 'add') {
				Object.assign(this.form, item)
			} else {
				Object.assign(this.form, item)
				this.form.grade_ids = parseInt(item.grade_ids)
				this.form.course_ids = item.course_ids.split(',').map((item) => Number(item))
			}
			this.getCourseList()
			this.getGradeList()
			this.getFormList()
		},
		confirm() {
			console.log('confirm', this.form)
			this.$refs.form.validate(async (valid) => {
				if (valid) {
					let params = {
						id: this.form.id || null,
						tenant_id: this.form.tenant_id,
						campus_id: this.form.campus_id,
						title: this.form.title,
						course_ids: this.form.course_ids.join(','),
						grade_ids: this.form.grade_ids + '',
						semester_id: this.form.semester_id,
						leader_form: this.form.leader_form,
						teacher_form: this.form.teacher_form,
						end_time: this.form.end_time,
						hide_student: this.form.hide_student
					}
					const { code, message } = await this.$API.eduEvaluation.save.post(params)
					this.showMessage(code, message)
				}
			})
		},
		async getCourseList() {
			let { data } = await this.$API.eduCourseSet.course.single_course.get({
				tenant_id: this.form.tenant_id,
				campus_id: this.form.campus_id,
				id: this.form.grade_ids
			})
			this.courseList = data
		},
		async getFormList() {
			let { data } = await this.$API.form.form.all.get({
				tenant_id: this.form.tenant_id,
				campus_id: this.form.campus_id,
				form_type: 3
			})
			this.formList = data
		},
		async getGradeList() {
			let { data } = await this.$API.eduGradeClass.grade.all.get({
				tenant_id: this.form.tenant_id,
				campus_id: this.form.campus_id,
				semester_id: this.form.semester_id
			})
			this.gradeList = data
		},
		showMessage(code, message) {
			if (code === 200) {
				ElMessage({ type: 'success', message: message })
				this.visible = false
				this.$emit('success', this.form)
			} else {
				ElMessage({ type: 'error', message: message })
			}
		}
	}
}
</script>
<style lang="scss" scoped></style>
