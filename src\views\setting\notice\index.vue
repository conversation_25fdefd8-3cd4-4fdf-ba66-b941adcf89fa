<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="tabChange">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main>
			<el-header>
				<div class="left-panel">
					<div class="left-panel-search">
						<el-form-item v-if="CampusManagementList.length > 1" label="">
							<el-select v-model="params.campus_id" placeholder="请选择校区" filterable style="width: 180px"
								@change="campusChange">
								<el-option v-for="item in CampusManagementList" :key="item.code" :label="item.name"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-input v-model="params.title" placeholder="请输入标题" style="width: 200px; margin: 0"
								@change="titleChange"></el-input>
						</el-form-item>
					</div>
				</div>
				<div class="right-panel">
					<el-button type="primary" @click="table_add">发布公告</el-button>
				</div>

			</el-header>
			<scTable ref="table" :apiObj="apiObj" :params="params" height="calc(100% - 59px)">
				<el-table-column prop="title" label="公告标题">
					<template #default="{ row }">
						<div style="cursor: pointer" @click="table_info(row)">
							<p v-if="row.importance == 1">
								<span style="color: #f56c6c; font-weight: bold">[重要] </span>{{ row.title }}
							</p>
							<p v-else>{{ row.title }}</p>
						</div>
					</template>
				</el-table-column>
				<el-table-column prop="publisher" label="发布人" width="150">
					<template #default="{ row }">
						{{ row.publisher_info.name }}
					</template>
				</el-table-column>
				<!-- <el-table-column prop="receiver_type" label="接收对象">
                    <template #default="scope">
                        {{ scope.row.sync_student == 1 ? '全部' : '选择人员' }}
                    </template>
                </el-table-column> -->
				<el-table-column prop="sync_student" label="同步至学员" width="120">
					<template #default="{ row }">
						{{ row.sync_student == 1 ? '同步' : '不同步' }}
					</template>
				</el-table-column>
				<el-table-column prop="sync_class_dev" label="同步至班牌" width="300">
					<template #default="{ row }">
						<el-tag v-if="row.sync_class_dev == 1" type="success">同步</el-tag>
						<el-tag v-else-if="row.sync_class_dev == -2" type="danger">同步失败</el-tag>
						<el-tag v-else type="info">不同步</el-tag>

						<div v-if="row.sync_class_dev == 1">
							<p>截止时间：{{ row.end_time }}</p>
						</div>
					</template>
				</el-table-column>
				<el-table-column prop="status" label="状态" width="150">
					<template #default="{ row }">
						<el-tag v-if="row.status == 1" type="success">发布</el-tag>
						<el-tag v-else type="danger">草稿</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="180">
					<template #default="scope">
						<el-button-group>
							<el-button text type="primary" size="small" @click="table_info(scope.row)">查看</el-button>
							<el-button v-if="scope.row.status == 2" text type="primary" size="small"
								@click="table_edit(scope.row)">编辑</el-button>
						</el-button-group>
						<el-popconfirm title="确定删除该公告吗？" @confirm="table_del(scope.row)">
							<template #reference>
								<el-button text type="danger" size="small">删除</el-button>
							</template>
						</el-popconfirm>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<el-drawer v-model="showNotice" :title="noticeData.title" direction="rtl">
			<div class="notice-info">
				<div class="notice-title">
					<p>发布人：{{ noticeData.publisher_info.name + ' ' + noticeData.created_at }}</p>
					<p>收件人：{{ noticeData.receiver }}</p>
					<p v-if="noticeData.sync_student == 1">同步至学员：{{ noticeData.sync_grade }}</p>
					<p v-if="noticeData.sync_class_dev == 1">同步至班牌：
						<span v-if="noticeData.class_dev_type==1">全部班牌</span>
						<span v-if="noticeData.class_dev_type==2">
							{{ noticeData.sync_room }}
						</span>

					</p>
					<p v-if="noticeData.sync_class_dev == 1">班牌展示截止时间：{{ noticeData.end_time }}</p>
				</div>
				<div class="notice-content" v-html="noticeData.content"></div>
				<div v-if="noticeData.file?.length > 0" class="notice-file">
					<h3>附件：</h3>
					<p v-for="row in noticeData.file">
						<el-link type="primary" :href="row.url" target="_blank" style="margin-right: 10px">{{ row.name
							}}</el-link>
					</p>
				</div>
			</div>
		</el-drawer>
		<save ref="saveDrawer" @success="saveSuccess"></save>
	</el-container>
</template>

<script>
import { ElMessage } from 'element-plus'
import save from './save.vue'
import { cloneDeep } from 'lodash'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo } = cusTom.getBaseQuery()
export default {
	components: {
		save
	},
	data() {
		return {
			activeName: 'all',
			campusId,
			tenantId,
			tabs: [
				{
					name: 'all',
					label: '全部公告'
				},
				{
					name: 'published',
					label: '我发布的'
				},
				{
					name: 'draft',
					label: '我的草稿'
				}
			],
			apiObj: this.$API.notice.all,
			CampusManagementList: campusInfo,
			params: {
				tenant_id: tenantId,
				campus_id: campusId
			},
			showNotice: false,
			noticeData: {}
		}
	},
	methods: {
		tabChange(name) {
			this.activeName = name
			switch (name) {
				case 'all':
					this.apiObj = this.$API.notice.all
					break
				case 'published':
					this.apiObj = this.$API.notice.myPublish
					break
				case 'draft':
					this.apiObj = this.$API.notice.myDraft
					break
			}
		},
		titleChange(val) {
			this.params.title = val
			this.$refs.table.getData(this.params)
		},
		campusChange(val) {
			this.params.campus_id = val
			this.$refs.table.upData(this.params)
		},
		// 新增公告
		table_add() {
			this.$refs.saveDrawer.open('add', this.params.campus_id)
		},
		saveSuccess(data) {
			
			let params = {
				tenant_id: tenantId,
				campus_id: this.params.campus_id,
				...data,
				file: data.file?.length ? JSON.stringify(data.file) : '',
				sync_grade: data.sync_grade?.length ? data.sync_grade.join(',') : '',
				receiver: data.receiver?.length ? data.receiver.join(',') : ''
			}
			/*if (data.room_ids && data.room_ids.length > 0) {
						let room_id = []
						data.room_ids.forEach((item) => {
							room_id.push(item.id)
						})
						params.room_ids = room_id
			}*/

			this.$API.notice.publish.post(params).then((res) => {
				if (res.code === 200) {
					ElMessage.success('成功')
					this.$refs.table.getData(this.params)
				} else {
					ElMessage({ type: 'error', message: res.message })
				}
			})
			console.log(params, 'saveSuccess')
		},
		// 公告详情
		table_info(row) {
			this.showNotice = true
			let data = cloneDeep(row)
			if (data.receiver_type === 1) {
				data.receiver = '全部人员'
			} else if (Array.isArray(data.receiver)) {
				data.receiver = data.receiver.map((item) => item.name).join('、')
			}
			if (data.sync_student === 1 && Array.isArray(data.sync_grade)) {
				data.sync_grade = data.sync_grade.map((item) => item.name).join('、')
			}
			if (data.file && !Array.isArray(data.file)) {
				data.file = JSON.parse(data.file)
			}
			if (data.class_dev_type === 2 && Array.isArray(data.room_list)) {
				data.sync_room = data.room_list.map((item) => item.name).join(' | ')
			}
			this.noticeData = data
		},
		table_edit(row) {
			console.log(row, 'table_edit')
			this.$refs.saveDrawer.setData(row)
			this.$refs.saveDrawer.open('edit',row.campus_id)
		},
		table_del(row) {
			let params = {
				tenant_id: tenantId,
				campus_id: campusId,
				id: row.id
			}
			this.$API.notice.del.post(params).then((res) => {
				if (res.code === 200) {
					ElMessage({ type: 'success', message: '删除成功' })
					this.$refs.table.getData(this.params)
				} else {
					ElMessage({ type: 'error', message: res.message })
				}
			})
		}
	}
}
</script>
<style lang="scss" scoped>
.notice-info {
	padding: 0 10px;

	.notice-title {
		margin-bottom: 15px;
		padding-bottom: 10px;
		border-bottom: 1px solid #eee;

		h3 {
			font-size: 20px;
			font-weight: bold;
			margin-bottom: 10px;
		}

		p {
			font-size: 14px;
			color: #999;
			margin-bottom: 5px;
		}
	}

	.notice-content {
		font-size: 14px;
	}

	.notice-file {
		padding-top: 15px;
		margin-bottom: 5px;
		border-top: 1px solid #eee;

		>p {
			padding-left: 10px;
			margin-bottom: 5px;
		}
	}
}
</style>
