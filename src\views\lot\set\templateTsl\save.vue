<template>
	<el-drawer v-model="visible" :title="titleMap[mode]" :size="size" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" class="tsl" label-position="top" :model="form" :rules="rules" :disabled="mode === 'show'">
			<el-form-item label="功能类型" prop="tsl_type">
				<el-radio-group v-model="form.tsl_type">
					<el-radio-button
						v-for="item in enumConfig.tslTypeMap"
						:key="item.value"
						:label="item.name"
						:value="item.value"
					/>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="功能名称" prop="name">
				<el-input v-model="form.name" placeholder="请输入功能名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="标识符" prop="code">
				<el-input
					v-model="form.code"
					placeholder="支持英文、数字、下划线的组合，首字符不能是数字，最多32个字符"
					clearable
				></el-input>
			</el-form-item>
			<template v-if="form.tsl_type === 1">
				<el-form-item label="数据类型" prop="type_spec.type">
					<el-select
						v-model="form.type_spec.type"
						placeholder="请选择数据类型"
						style="width: 100%; max-width: unset"
						@change="typeChange"
					>
						<el-option
							v-for="item in enumConfig.dataTypeMap"
							:key="item.value"
							:label="item.value + ' (' + item.name + ')'"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<div class="dataDefinition">
					<template v-if="form.type_spec.type === 'int'">
						<int-module ref="intForm" v-model="form.type_spec.specs"></int-module>
					</template>
					<template v-if="form.type_spec.type === 'float'">
						<float-module v-model="form.type_spec.specs"></float-module>
					</template>
					<template v-if="form.type_spec.type === 'enum'">
						<enum-module v-model="form.type_spec.specs"></enum-module>
					</template>
					<template v-if="form.type_spec.type === 'bool'">
						<bool-module v-model="form.type_spec.specs"></bool-module>
					</template>
					<template v-if="form.type_spec.type === 'text'">
						<text-module v-model="form.type_spec.specs"></text-module>
					</template>
					<template v-if="form.type_spec.type === 'date'">
						<date-module v-model="form.type_spec.specs"></date-module>
					</template>
					<template v-if="form.type_spec.type === 'struct'">
						<struct-module v-model="form.type_spec.specs" @changeSize="changeSize"></struct-module>
					</template>
					<template v-if="form.type_spec.type === 'array'">
						<array-module v-model="form.type_spec.specs" @changeSize="changeSize"></array-module>
					</template>
				</div>
				<el-form-item label="读写类型" prop="access_mode">
					<el-select v-model="form.access_mode" placeholder="请选择读写类型" style="width: 100%; max-width: unset">
						<el-option
							v-for="item in enumConfig.accessModeMap"
							:key="item.value"
							:label="item.name"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
			</template>
			<template v-if="form.tsl_type === 2">
				<el-form-item label="事件类型" prop="event_type">
					<el-select
						v-model="form.event_type"
						placeholder="请选择事件类型"
						style="width: 100%; max-width: unset"
						clearable
					>
						<el-option
							v-for="item in enumConfig.eventTypeMap"
							:key="item.value"
							:label="item.name"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<output-module v-model="form.output_params" @changeSize="changeSize"></output-module>
			</template>
			<template v-if="form.tsl_type === 3">
				<el-form-item label="调用方式" prop="call_type">
					<el-select
						v-model="form.call_type"
						placeholder="请选择调用方式"
						style="width: 100%; max-width: unset"
						clearable
					>
						<el-option
							v-for="item in enumConfig.callTypeMap"
							:key="item.value"
							:label="item.name"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<input-module v-model="form.input_params" @changeSize="changeSize"></input-module>
				<output-module v-model="form.output_params" @changeSize="changeSize"></output-module>
			</template>
			<el-form-item label="描述" prop="description">
				<el-input v-model="form.description" placeholder="请输入描述" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import IntModule from '@/components/thingsModel/intModule.vue'
import FloatModule from '@/components/thingsModel/floatModule.vue'
import EnumModule from '@/components/thingsModel/enumModule.vue'
import BoolModule from '@/components/thingsModel/boolModule.vue'
import TextModule from '@/components/thingsModel/textModule.vue'
import DateModule from '@/components/thingsModel/dateModule.vue'
import ArrayModule from '@/components/thingsModel/arrayModule.vue'
import StructModule from '@/components/thingsModel/structModule.vue'
import OutputModule from '@/components/thingsModel/outputModule.vue'
import InputModule from '@/components/thingsModel/inputModule.vue'
import cusTom from '@/utils/cusTom'

const defaultData = () => {
	return {
		template_id: 0,
		tsl_type: 1,
		name: '',
		code: '',
		access_mode: null,
		description: '',
		event_type: null,
		call_type: null,
		input_params: [],
		output_params: [],
		type_spec: {
			type: null,
			specs: {}
		}
	}
}

export default {
	components: {
		OutputModule,
		InputModule,
		StructModule,
		ArrayModule,
		DateModule,
		BoolModule,
		EnumModule,
		FloatModule,
		IntModule,
		TextModule
	},
	emits: ['success', 'closed'],
	props: {},

	data() {
		return {
			size: 550,
			time_format: '整数类型Int64的UTC时间戳（单位：毫秒）',
			unitConfig: [],
			enumConfig: {},
			mode: 'add',
			titleMap: {
				add: '新增物模型',
				edit: '编辑物模型',
				show: '查看物模型'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				name: [{ required: true, message: '请输入功能名称名称' }],
				tsl_type: [{ required: true, message: '请选择功能类型' }],
				code: [{ required: true, message: '请输入功能标识符' }],
				access_mode: [{ required: true, message: '请选择读写类型' }],
				event_type: [{ required: true, message: '请选择事件类型' }],
				call_type: [{ required: true, message: '请选择调用方式' }],
				'type_spec.type': [{ required: true, message: '请选择数据类型', trigger: 'blur' }]
			}
		}
	},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	mounted() {},
	methods: {
		changeSize(size) {
			this.size = size
		},
		typeChange(value) {
			console.log(value)
			switch (value) {
				case 'int':
				case 'float':
					this.form.type_spec.specs = {
						max: null,
						min: null,
						step: null,
						unit: null
					}
					break
				case 'enum':
					this.form.type_spec.specs = [{ value: null, name: null }]
					break
				case 'text':
					this.form.type_spec.specs = {
						len: ''
					}
					break
				case 'bool':
					this.form.type_spec.specs = ['关', '开']
					break
				case 'array':
					this.form.type_spec.specs = {
						size: '',
						type: '',
						specs: {}
					}
					break
				case 'struct':
					this.form.type_spec.specs = []
					break
			}
		},
		//显示
		open(template_id, mode = 'add') {
			this.mode = mode
			this.form.template_id = parseInt(template_id)
			this.visible = true
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (!valid) {
					return
				}
				this.isSaveing = true
				let subForm
				if (this.mode === 'add') {
					subForm = { ...this.params, ...this.form }
				} else if (this.mode === 'edit') {
					subForm = { ...this.form }
				}
				if (typeof subForm.type_spec.specs !== 'string') {
					subForm.type_spec.specs = JSON.stringify(subForm.type_spec.specs)
				}
				var res = await this.$LotApi.templateTsl.save.post(subForm)
				this.isSaveing = false
				if (res.code === 200) {
					this.$emit('success', this.form, this.mode)
					this.visible = false
					this.$message.success('操作成功')
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
		}
	}
}
</script>

<style scoped lang="scss">
.tsl {
	padding: 0 15px;
	font-size: 12px;
}

.enum_header {
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.enum_item {
	margin: 5px 0;
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.dataDefinition {
	padding-left: 15px;
}

.text-center {
	text-align: center;
}
</style>
