<template>
	<el-container>
		<el-aside v-loading="menuloading" width="300px">
			<el-container>
				<el-header>
					<el-input v-model="menuFilterText" placeholder="输入关键字进行过滤" clearable></el-input>
				</el-header>
				<el-main>
					<el-tree
						ref="menu"
						class="menu"
						node-key="id"
						:data="menuList"
						:props="menuProps"
						:draggable="false"
						highlight-current
						:render-after-expand="true"
						:expand-on-click-node="false"
						check-strictly
						show-checkbox
						:filter-node-method="menuFilterNode"
						@node-click="menuClick"
						@node-drop="nodeDrop"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span class="label">
									{{ node.label }}
								</span>
								<span v-if="data.meta.type !== 'button'" class="do">
									<el-button icon="el-icon-plus" size="small" @click.stop="add(node, data)"></el-button>
								</span>
							</span>
						</template>
					</el-tree>
				</el-main>
				<el-footer style="height: 51px">
					<el-button type="primary" size="small" icon="el-icon-plus" @click="add()"></el-button>
					<el-button type="danger" size="small" plain icon="el-icon-delete" @click="delMenu"></el-button>
				</el-footer>
			</el-container>
		</el-aside>
		<el-container>
			<el-main ref="main" class="nopadding" style="padding: 20px">
				<save ref="save" :menu="menuList" @menuReload="getMenu"></save>
			</el-main>
		</el-container>
	</el-container>
</template>

<script>
let newMenuIndex = 1
import save from './save'

export default {
	name: 'settingMenu',
	components: {
		save
	},
	data() {
		return {
			menuloading: false,
			menuList: [],
			menuProps: {
				label: (data) => {
					return data.meta.title
				},
				type: (data) => {
					return data.meta.type
				}
			},
			menuFilterText: ''
		}
	},
	watch: {
		menuFilterText(val) {
			this.$refs.menu.filter(val)
		}
	},
	mounted() {
		this.getMenu()
	},
	methods: {
		//加载树数据
		async getMenu() {
			this.menuloading = true
			var res = await this.$API.system.menu.list.get()
			this.menuloading = false
			this.menuList = res.data
		},
		//树点击
		menuClick(data, node) {
			var pid = node.level === 1 ? undefined : node.parent.data.id
			this.$refs.save.setData(data, pid)
			this.$refs.main.$el.scrollTop = 0
		},
		//树过滤
		menuFilterNode(value, data) {
			if (!value) return true
			var targetText = data.meta.title
			return targetText.indexOf(value) !== -1
		},
		//树拖拽
		nodeDrop(draggingNode, dropNode, dropType) {
			this.$refs.save.setData({})
			let messageText = ''
			switch (dropType) {
				case 'inner':
					messageText = '下级'
					break
				case 'before':
					messageText = '前面'
					break
				case 'after':
					messageText = '后面'
					break
			}
			this.$message(`拖动菜单【${draggingNode.data.meta.title}】 到【${dropNode.data.meta.title}】的 ${messageText}`)
		},
		//增加
		async add(node, data) {
			var newMenuName = 'unnamed' + newMenuIndex++
			var newMenuTitle = '未命名' + newMenuIndex++
			var newMenuData = {
				id: new Date().getTime() * 1000,
				parentId: data ? data.id : 0,
				name: newMenuName,
				path: '',
				redirect: '',
				component: '',
				listorder: 0,
				meta: {
					title: newMenuTitle,
					type: 'menu',
					color: '',
					icon: '',
					active: '',
					hidden: false,
					hiddenBreadcrumb: false,
					affix: false,
					fullpage: false,
					tag: ''
				},
				apiList: [],
				children: []
			}
			/*this.menuloading = true
			var res = await this.$API.demo.post.post(newMenuData)
			this.menuloading = false
			newMenuData.id = res.data
			*/
			this.$refs.menu.append(newMenuData, node)
			this.$refs.menu.setCurrentKey(newMenuData.id)
			var pid = node ? node.data.id : 0
			this.$refs.save.setData(newMenuData, pid)
		},
		editMenu(oldId, newId) {
			var node = this.$refs.menu.getNode(oldId)
			if (node) {
				node.id = newId
			}
		},
		//删除菜单
		async delMenu() {
			var CheckedNodes = this.$refs.menu.getCheckedNodes()
			if (CheckedNodes.length === 0) {
				this.$message.warning('请选择需要删除的项')
				return false
			}
			var confirm = await this.$confirm('确认删除已选择的菜单吗？', '提示', {
				type: 'warning',
				confirmButtonText: '删除',
				confirmButtonClass: 'el-button--danger'
			}).catch(() => {})
			if (confirm !== 'confirm') {
				return false
			}
			this.menuloading = true
			var reqData = {
				ids: CheckedNodes.map((item) => item.id)
			}
			var res = await this.$API.system.menu.del.post(reqData)
			this.menuloading = false
			if (res.code === 200) {
				CheckedNodes.forEach((item) => {
					var node = this.$refs.menu.getNode(item)
					if (node.isCurrent) {
						this.$refs.save.setData({})
					}
					this.$refs.menu.remove(item)
				})
			} else {
				this.$message.warning(res.message)
			}
		}
	}
}
</script>

<style scoped>
.menu:deep(.el-tree-node__label) {
	display: flex;
	flex: 1;
	height: 100%;
}

.custom-tree-node {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	height: 100%;
	padding-right: 24px;
}

.custom-tree-node .label {
	display: flex;
	align-items: center;
	height: 100%;
}

.custom-tree-node .label .el-tag {
	margin-left: 5px;
}

.custom-tree-node .do {
	display: none;
}

.custom-tree-node .do i {
	margin-left: 5px;
	color: #999;
}

.custom-tree-node .do i:hover {
	color: #333;
}

.custom-tree-node:hover .do {
	display: inline-block;
}
</style>
