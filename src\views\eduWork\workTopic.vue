<template>
	<el-container>
		<el-header style="padding: 5px">
			<div class="detailHeader" style="line-height: 45px">
				<el-button text :icon="ArrowLeft" @click="goBack()">返回</el-button>
				<div class="detailHeader_title">{{ worKInfo.work_name }}</div>
			</div>
		</el-header>
		<el-main>
			<el-row :gutter="20" style="overflow: hidden">
				<el-col :span="mode ? 12 : 24">
					<div v-if="topics.list && topics.list.length" class="workPreview">
						<el-scrollbar max-height="calc(100vh - 265px)">
							<el-alert v-if="worKInfo.status === -1" title="点击题目可进行编辑" type="warning" />
							<div
								v-for="(item, index) in topics.list"
								:key="item.id"
								class="topic_item"
								:class="{ active: activeTopic == item.id }"
								@click="editTopic(item)"
							>
								<div class="topic_top" style="font-weight: bold">
									<span>{{ index + 1 }}、</span>
									<span v-if="item.topic_type == 1">（判断题）</span>
									<span v-if="item.topic_type == 2">（单选题）</span>
									<span v-if="item.topic_type == 3">（多选题）</span>
									<div class="topic_name" v-html="item.topic_name"></div>
									<span style="margin-left: 10px; color: #67c23a">{{ item.topic_answer }}</span>
									<el-icon
										v-if="worKInfo.status === -1"
										color="#f56c6c"
										style="margin-left: 20px"
										size="16px"
										@click.stop="delFun(item)"
									>
										<Delete />
									</el-icon>
								</div>
								<div v-for="(item2, index2) in item.topic_desc_json" :key="index2" class="topicDesc">
									<span>{{ item2.serial }}：</span>
									<div v-html="item2.desc"></div>
								</div>
							</div>
						</el-scrollbar>
					</div>
					<el-empty v-else description="暂无试题" />
				</el-col>
				<el-col v-if="mode" :span="12">
					<el-scrollbar max-height="calc(100vh - 265px)">
						<el-form
							ref="workTopicFormRef"
							:model="topicForm.obj"
							:rules="rules"
							label-position="left"
							label-width="80px"
							style="padding-right: 10px"
						>
							<editTopicTemp v-model:topicObj="topicForm.obj"></editTopicTemp>
							<el-form-item label="附件">
								<scUploadFile v-model="topicForm.obj.topic_file" accept="*" fileTypeTag="eduWork"></scUploadFile>
							</el-form-item>
						</el-form>
					</el-scrollbar>
				</el-col>
			</el-row>
			<el-affix v-if="mode" position="bottom" :offset="10">
				<div class="saveBtn">
					<el-button @click="cancelFun">取消</el-button>
					<el-button v-loading="isSaveing" type="primary" @click="saveFun">保存</el-button>
				</div>
			</el-affix>
			<el-affix v-if="!mode && worKInfo.status === -1" position="bottom" :offset="10">
				<div class="saveBtn">
					<el-button type="primary" :icon="FolderAdd" @click="addTopicByBank">从题库选择</el-button>
					<el-button type="primary" :icon="Plus" @click="addTopic">新增题目</el-button>
				</div>
			</el-affix>
		</el-main>
		<dialogSelection ref="dialogSelectionRef" @selectionChange="selectionChange"></dialogSelection>
	</el-container>
</template>

<script setup>
import { reactive, ref, unref, watch, getCurrentInstance, onMounted, nextTick, defineExpose } from 'vue'
import cusTom from '@/utils/cusTom'
import { Delete, Edit, Plus, FolderAdd, ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import editTopicTemp from './editTopicTemp'
import dialogSelection from '@/views/eduTopic/dialogSelection.vue'

const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
const { proxy } = getCurrentInstance()
const workTopicFormRef = ref(null)
const dialogSelectionRef = ref(null)
let work_id = ref(0)
let work_name = ref('')
let isSaveing = ref(false)
let activeTopic = ref(null)
let mode = ref('')
let worKInfo = ref({})
const topics = reactive({
	list: []
})
const defaultForm = () => {
	return {
		id: 0,
		work_id: 0,
		tenant_id: tenantId,
		campus_id: campusId,
		topic_type: 1,
		topic_name: '',
		topic_desc: [
			{
				serial: 'A',
				desc: '对',
				is_right: 1
			},
			{
				serial: 'B',
				desc: '错',
				is_right: 0
			}
		],
		topic_file: '',
		topic_score: 0
	}
}
const topicForm = reactive({
	obj: defaultForm()
})
const validateDesc = (rule, value, callback) => {
	let flag = true
	if (topicForm.obj.topic_type == 1) {
		callback()
		return
	}
	topicForm.obj.topic_desc.forEach((item) => {
		let desc = item.desc.replaceAll('<p><br></p>', '')
		if (!item.serial || !desc) {
			flag = false
		}
	})
	if (flag) {
		callback()
	} else {
		callback(new Error('请完善题目选项'))
	}
}
const validateName = (rule, value, callback) => {
	let flag = true
	let topic_name = topicForm.obj.topic_name.replaceAll('<p><br></p>', '')
	if (!topic_name) {
		flag = false
	}
	if (flag) {
		callback()
	} else {
		callback(new Error('请输入题目'))
	}
}
const rules = reactive({
	topic_score: [{ required: true, message: '请输入分值', trigger: 'blur' }],
	topic_type: [{ required: true, message: '请选择题目类型', trigger: 'change' }],
	topic_name: [{ required: true, validator: validateName, trigger: 'change' }],
	topic_desc: [{ required: true, validator: validateDesc, trigger: 'change' }]
})
const goBack = () => {
	proxy.$router.replace('/eduWork')
}
const getWorkTopic = async () => {
	let { data } = await proxy.$API.eduWork.workTopicList.get({
		work_id: work_id.value,
		tenant_id: tenantId,
		campus_id: campusId
	})
	topics.list = data
}
const getWorkOne = async () => {
	let { data } = await proxy.$API.eduWork.one.get({
		id: work_id.value,
		tenant_id: tenantId,
		campus_id: campusId
	})
	worKInfo.value = data
}
const editTopic = (item) => {
	if (worKInfo.value.status === 1) {
		return false
	}
	mode.value = 'edit'
	nextTick(() => {
		workTopicFormRef.value.resetFields()
		Object.keys(topicForm.obj).forEach((key) => {
			if (key == 'topic_desc') {
				topicForm.obj[key] = JSON.parse(item[key])
			} else {
				topicForm.obj[key] = item[key]
			}
		})
		activeTopic.value = item.id
	})
}
const addTopic = () => {
	mode.value = 'add'
	topicForm.obj = defaultForm()
	nextTick(() => {
		workTopicFormRef.value.resetFields()
	})
}
const addTopicByBank = () => {
	dialogSelectionRef.value.show()
}
const selectionChange = async (ids) => {
	let param = {
		id: 0,
		work_id: work_id.value,
		tenant_id: tenantId,
		campus_id: campusId,
		topic_store_id: ids.join(',')
	}
	let res = await proxy.$API.eduWork.workTopicSave.post(param)
	if (res.code === 200) {
		ElMessage.success('新增成功')
		getWorkTopic()
		dialogSelectionRef.value.dialogVisible = false
	}
}
const delFun = (item) => {
	ElMessageBox.confirm('确定删除题目吗?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	})
		.then(async () => {
			var res = await proxy.$API.eduWork.workTopicDel.post({
				id: item.id,
				tenant_id: item.tenant_id,
				campus_id: item.campus_id
			})
			if (res.code === 200) {
				if (activeTopic.value == item.id) {
					cancelFun()
				}
				ElMessage.success('删除成功')
				getWorkTopic()
			}
		})
		.catch(() => {})
}
const cancelFun = () => {
	mode.value = ''
	activeTopic.value = null
}
const saveFun = async () => {
	const form = unref(workTopicFormRef)
	if (!form) {
		return
	}
	form.validate(async (valid) => {
		if (valid) {
			isSaveing.value = true
			try {
				topicForm.obj.topic_score = Number(topicForm.obj.topic_score)
				topicForm.obj.work_id = work_id.value
				let res = await proxy.$API.eduWork.workTopicSave.post(topicForm.obj)
				isSaveing.value = false
				if (res.code === 200) {
					ElMessage.success('新增成功')
					getWorkTopic()
					cancelFun()
				}
			} catch (err) {
				isSaveing.value = false
			}
		}
	})
}
onMounted(() => {
	work_id.value = Number(proxy.$route.query.id)
	work_name.value = proxy.$route.query.name
	getWorkOne()
	getWorkTopic()
})
</script>

<style lang="scss" scoped>
.detailHeader {
	display: flex;
	align-items: center;
	font-size: 14px;

	.el-button {
		padding: 8px 5px;
		margin-right: 10px;
		line-height: normal;
	}

	.detailHeader_title {
		color: var(--el-text-color-primary);
		font-weight: bold;
		font-size: 16px;
	}
}

.title {
	display: flex;
	justify-content: center;
	padding-bottom: 10px;
}

.workPreview {
	height: 100%;
	overflow: hidden;
	padding: 10px;
	border: 1px solid var(--el-border-color);
	font-size: 14px;

	.topic_item {
		cursor: pointer;
		padding: 8px;
		margin-bottom: 12px;

		&:last-child {
			margin-bottom: 0;
		}

		.el-icon {
			display: none;
		}

		&:hover {
			.el-icon {
				display: block;
			}
		}

		&.active {
			color: var(--el-color-primary);
			border: 1px Dashed var(--el-color-primary);
			border-radius: 4px;
		}

		.topic_top {
			display: flex;
			flex-wrap: wrap;
			margin-bottom: 10px;
		}

		.topic_name {
			display: inline-block;
			margin-left: 5px;
			font-weight: bold;
		}
		:deep(.topic_name img) {
			max-width: 500px;
			max-height: 300px;
		}
		.topicDesc {
			display: flex;
			margin-bottom: 8px;
			cursor: pointer;

			&:last-child {
				margin-bottom: 0;
			}
		}
		:deep(.topicDesc img) {
			max-width: 500px;
			max-height: 300px;
		}
	}
}

.addBtn {
	text-align: right;
	width: 100%;
}

.saveBtn {
	text-align: right;
	width: 100%;
	// border-top: 1px solid var(--el-border-color);
	background-color: #fff;
	padding: 10px 20px;
}
</style>
