<template>
    <el-container>
        <el-header>
            <el-tabs v-model="activeName" @tab-change="handleClick">
                <el-tab-pane v-for="item in tabs" :name="item.name" :label="item.label" :key="item.name"></el-tab-pane>
            </el-tabs>
        </el-header>
        <el-main class="el-main-hascontainer">
            <component :is="currComponent.component"></component>
        </el-main>
    </el-container>
</template>

<script setup>
import classroom from './classroom'
import record from './reservationRecord'
import { ref } from 'vue'
const activeName = ref('classroom')
const currComponent = ref({
    name: 'classroom',
    component: classroom
})
const tabs = [
    {
        name: 'classroom',
        label: '场室管理',
        component: classroom
    },
    {
        name: 'record',
        label: '预约记录',
        component: record
    }
]
const handleClick = (name) => {
    currComponent.value = tabs.find((item) => item.name === name)
}
</script>
<style lang="scss" scoped></style>