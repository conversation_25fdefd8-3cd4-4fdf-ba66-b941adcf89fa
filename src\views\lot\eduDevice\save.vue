<template>
    <el-drawer v-model="visible" :title="deviceInfo.class_name" size="28%" destroy-on-close @close="close">
        <div class="drawer-body">
            <div class="drawer-body-header">
                <p>{{ deviceInfo.device_name }}</p>
                <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshDevice">刷新</el-button>
            </div>
            <div class="drawer-body-content">
                <div class="content-power">
                    <div class="power-list">
                        <div class="power-item" v-for="item in options" :key="item.value"
                            @click="powerCommand(item.value)">
                            <div class="power-item-box"
                                :class="deviceInfo.power == item.value ? 'power-item-active' : ''">
                                <cusSvgIcon :iconClass="item.value" v-if="deviceInfo.power == item.value"></cusSvgIcon>
                            </div>
                            <p>{{ item.label }}</p>
                        </div>
                    </div>
                </div>
                <div class="content-basic">
                    <p>基本信息</p>
                    <div>
                        <span>设备ID：{{ deviceInfo.device_key }}</span>
                        <span>IP：{{ deviceInfo.ip }}</span>
                    </div>
                    <div v-if="deviceInfo.room_info">关联教室：{{ deviceInfo.room_info.name }}</div>
                </div>
                <div class="content-basic">
                    <p>设备状态</p>
                    <div class="device-status">
                        <div class="device-item">
                            <p>内置电脑</p>
                            <el-dropdown @command="pcCommand" :disabled="deviceInfo.power !== 'on'">
                                <div class="theme-color">
                                    {{ deviceInfo.pc == 'on' ? '开机' : deviceInfo.pc == "off" ? '关机' : '强制关机' }}
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </div>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item command="on">开机</el-dropdown-item>
                                        <el-dropdown-item command="off">关机</el-dropdown-item>
                                        <el-dropdown-item command="force">强制关机</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                        <div class="device-item">
                            <p>投影仪</p>
                            <el-dropdown @command="projectorCommand" :disabled="deviceInfo.power !== 'on'">
                                <div class="theme-color">
                                    {{ filterProjector(deviceInfo.projector) }}
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </div>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item command="on">开机</el-dropdown-item>
                                        <el-dropdown-item command="off">关机</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                        <div class="device-item">
                            <p>第二路投影仪</p>
                            <el-dropdown @command="projector2Command" :disabled="deviceInfo.power !== 'on'">
                                <div class="theme-color">
                                    {{ filterProjector(deviceInfo.projector2) }}
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </div>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item command="on">开机</el-dropdown-item>
                                        <el-dropdown-item command="off">关机</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                        <div class="device-item">
                            <p>录播</p>
                            <el-dropdown @command="recordCommand" :disabled="deviceInfo.power !== 'on'">
                                <div class="theme-color">
                                    {{ filterRecord(deviceInfo.record) }}
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </div>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item command="on">开机</el-dropdown-item>
                                        <el-dropdown-item command="off">关机</el-dropdown-item>
                                        <el-dropdown-item command="start"
                                            v-if="deviceInfo.record != 'pause'">开始录制</el-dropdown-item>
                                        <template v-if="deviceInfo.record == 'start'">
                                            <el-dropdown-item command="stop">停止录制</el-dropdown-item>
                                            <el-dropdown-item command="pause">暂停录制</el-dropdown-item>
                                            <el-dropdown-item command="restore">恢复录制</el-dropdown-item>
                                        </template>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </div>
                </div>
                <div class="content-basic content-video">
                    <p>投影仪画面</p>
                    <el-dropdown @command="sourceCommand" :disabled="deviceInfo.power !== 'on'">
                        <div class="theme-color">
                            {{ deviceInfo.channel_source == '1' ? '内置PC' : deviceInfo.channel_source == "2" ? '外接1' :
                                '外接2' }}
                            <el-icon class="el-icon--right">
                                <arrow-down />
                            </el-icon>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item :command="1">内置PC</el-dropdown-item>
                                <el-dropdown-item :command="2">外接1</el-dropdown-item>
                                <el-dropdown-item :command="3">外接2</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
                <div class="content-basic content-audio">
                    <p>音频控制</p>
                    <div class="audio-volume">
                        <div class="audio-volume-title">
                            <span>扬声器</span>
                            <cusSvgIcon :iconClass="`volume-${deviceInfo.main_volume_mute == 1 ? 'close' : 'open'}`"
                                :class="deviceInfo.main_volume_mute == 1 ? 'theme-color-danger' : 'theme-color'"
                                @click="changeVolumeMute">
                            </cusSvgIcon>
                        </div>
                        <div class="content-audio-slider">
                            <el-slider v-model="deviceInfo.main_volume" :min="0" :max="63" :step="1"
                                :show-tooltip="false" :disabled="deviceInfo.power !== 'on'"
                                @change="changeVolume"></el-slider>
                        </div>
                    </div>
                    <div class="audio-mike">
                        <div class="audio-mike-title">
                            <div>
                                <span>麦克风</span>
                                <cusSvgIcon :iconClass="`mike-${deviceInfo.mic_volume_mute == 1 ? 'close' : 'open'}`"
                                    :class="deviceInfo.mic_volume_mute == 1 ? 'theme-color-danger' : 'theme-color'"
                                    @click="changeMikeMute">
                                </cusSvgIcon>
                            </div>
                            <el-button type="primary" icon="el-icon-lock" size="small"
                                @click="micUnLock">麦克风解锁</el-button>
                        </div>
                        <div class="content-audio-slider">
                            <el-slider v-model="deviceInfo.mic_volume" :min="0" :max="63" :step="1"
                                :show-tooltip="false" :disabled="deviceInfo.power !== 'on'"
                                @change="changeMikeVolume"></el-slider>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </el-drawer>
</template>
<script setup>
import { ref, defineExpose, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$LotApi
const visible = ref(false)
const currentId = ref('')
const deviceInfo = ref({})
const filterRecord = (val) => {
    if (val == 'on') {
        return '开机'
    } else if (val == 'off') {
        return '关机'
    } else if (val == 'start') {
        return '开始录制'
    } else if (val == 'stop') {
        return '停止录制'
    } else if (val == 'pause') {
        return '暂停录制'
    } else if (val == 'restore') {
        return '恢复录制'
    } else if (val == 'poweron') {
        return '正在开机'
    } else if (val == 'poweroff') {
        return '正在关机'
    }
}

const filterProjector = (val) => {
    switch (val) {
        case 'on':
            return '开机'
        case 'off':
            return '关机'
        case 'poweron':
            return '正在开机'
        case 'poweroff':
            return '正在关机'
    }
}

const options = [{ label: '关机', value: 'off' }, { label: '开机', value: 'on' }, { label: '禁用', value: 'disable' }]
// 总开关
const powerCommand = (command) => {
    globalPropValue.eduDevice.power.post({
        id: currentId.value,
        tenant_id: tenantId,
        campus_id: campusId,
        power: command
    }).then(res => {
        if (res.code === 200) {
            ElMessage({ type:'success', message: '操作成功！' })
            setTimeout(() => {
                refreshDevice()
            }, 1500)
        }
    })
}

// 内置电脑
const pcCommand = (command) => {
    executeDeviceCommand({
        endpoint: 'pcPower',
        params: {
            power: command
        }
    });
}
// 投影仪开关
const projectorCommand = (command) => {
    executeDeviceCommand({
        endpoint: 'projectorPower',
        params: {
            power: command
        }
    });
}
// 第二路投影仪开关
const projector2Command = (command) => {
    executeDeviceCommand({
        endpoint: 'projector2Power',
        params: {
            power: command
        }
    });
}
// 录播控制
const recordCommand = (command) => {
    executeDeviceCommand({
        endpoint: 'record',
        params: {
            power: command
        }
    });
}

// 投影仪画面切换
const sourceCommand = (val) => {
    executeDeviceCommand({
        endpoint: 'videoChannel',
        params: {
            channel: val
        }
    });
}

// 公共请求函数
const executeDeviceCommand = ({
    endpoint,
    params
}) => {
    return globalPropValue.eduDevice[endpoint].post({
        id: currentId.value,
        tenant_id: tenantId,
        campus_id: campusId,
        ...params
    }).then(res => {
        if (res.code === 200) {
            ElMessage({ type:'success', message: '操作成功！' })
            setTimeout(() => {
                refreshDevice()
            }, 1500)
        }
    }).catch(err => {
        console.log(`设备控制失败： ${err.message}`)
    })
}

// 音频控制
// 扬声器
const changeVolumeMute = () => {
    executeDeviceCommand({
        endpoint: 'volume',
        params: {
            main_volume_mute: deviceInfo.value.main_volume_mute == 1 ? -1 : 1,
            main_volume: deviceInfo.value.main_volume,
            mic_volume_mute: deviceInfo.value.mic_volume_mute,
            mic_volume: deviceInfo.value.mic_volume
        }
    });
}
const changeVolume = (val) => {
    executeDeviceCommand({
        endpoint: 'volume',
        params: {
            main_volume_mute: deviceInfo.value.main_volume_mute,
            main_volume: val,
            mic_volume_mute: deviceInfo.value.mic_volume_mute,
            mic_volume: deviceInfo.value.mic_volume
        }
    });
}
//麦克风
const changeMikeMute = () => {
    executeDeviceCommand({
        endpoint: 'volume',
        params: {
            main_volume_mute: deviceInfo.value.main_volume_mute,
            main_volume: deviceInfo.value.main_volume,
            mic_volume_mute: deviceInfo.value.mic_volume_mute == 1 ? -1 : 1,
            mic_volume: deviceInfo.value.mic_volume
        }
    });
}

const changeMikeVolume = (val) => {
    executeDeviceCommand({
        endpoint: 'volume',
        params: {
            main_volume_mute: deviceInfo.value.main_volume_mute,
            main_volume: deviceInfo.value.main_volume,
            mic_volume_mute: deviceInfo.value.mic_volume_mute,
            mic_volume: val
        }
    });
}

// 麦克风解锁
const micUnLock = () => {
    executeDeviceCommand({
        endpoint: 'micChargeStubUnlock',
        params: {}
    });
}
// 刷新设备状态
const refreshDevice = () => {
    globalPropValue.eduDevice.refresh.post({
        id: currentId.value,
        tenant_id: tenantId,
        campus_id: campusId
    }).then(res => {
        if (res.code === 200) {
            deviceInfo.value = res.data
            // ElMessage({ type: 'success', message: '刷新成功！' })
        }
    })
}
// 获取设备信息
const getDeviceInfo = () => {
    globalPropValue.eduDevice.one.get({
        id: currentId.value,
        tenant_id: tenantId,
        campus_id: campusId
    }).then(res => {
        deviceInfo.value = res.data
        console.log(res)
    })
}
const getInfoTimer = ref(null)
const show = (id) => {
    clearInterval(getInfoTimer.value)
    currentId.value = id
    getDeviceInfo()
    getInfoTimer.value = setInterval(() => {
        refreshDevice()
    }, 60000)
    visible.value = true
}
const emit = defineEmits(['refresh'])
const close = () => {
    clearInterval(getInfoTimer.value)
    visible.value = false
    emit('refresh')
}

defineExpose({
    show,
    close
})
</script>

<style lang="scss" scoped>
.drawer-body {
    &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 8px;
        border-bottom: 1px solid #ebeef5;

        >p {
            font-size: 16px;
        }

        .power {
            font-size: 16px;
            display: flex;
            align-items: center;

            &-mark {
                color: #ffffff;
                padding: 5px;
                font-size: 14px;
                border-radius: 50%;
                margin-right: 5px;
            }
        }
    }

    &-content {
        .content-power {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0 10px;

            .power-list {
                width: 80%;
                display: flex;
                justify-content: space-between;
                position: relative;

                .power-item {
                    font-size: 14px;
                    text-align: center;
                    position: relative;
                    z-index: 1;
                    cursor: pointer;

                    &-box {
                        height: 40px;
                        width: 40px;
                        line-height: 36px;
                        border-radius: 50%;
                        border: 1px solid #ebeef5;
                        background: #ffffff;
                        transition: all 0.3s ease;
                    }

                    &-active {
                        color: #ffffff;
                        transform: scale(1.1);
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                        .svg-icon {
                            width: 40px;
                            height: 40px;
                            transition: all 0.3s ease;
                        }
                    }

                    >p {
                        line-height: 50px;
                    }
                }

                &::before {
                    content: '';
                    height: 1px;
                    width: 80%;
                    background: #ebeef5;
                    position: absolute;
                    left: 40px;
                    top: 25%;
                    z-index: 0;
                }
            }
        }

        .content-basic {
            padding: 8px 0;
            line-height: 25px;

            >p {
                font-size: 16px;
                font-weight: bold;
                padding-bottom: 8px;
            }

            >div {
                span {
                    margin-right: 15px;
                    font-size: 13px;
                }
            }
        }

        .device-status {
            display: flex;
            flex-wrap: wrap;
            font-size: 13px;

            .device-item {
                width: 45%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin: 10px 15px 10px 0;

                &:nth-child(2n) {
                    margin-right: 0;
                }
            }
        }

        .content-video {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-audio {
            cursor: pointer;

            .audio-volume {
                margin: 8px 0;

                &-title {
                    font-size: 14px;

                }
            }

            .audio-mike {
                &-title {
                    font-size: 14px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
            }

            &-slider {
                padding: 5px 10px;
            }
        }
    }
}

.theme-color {
    color: var(--el-color-primary)
}

.theme-color-danger {
    color: var(--el-color-danger)
}
</style>