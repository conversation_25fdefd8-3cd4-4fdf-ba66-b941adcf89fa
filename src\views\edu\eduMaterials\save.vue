<template>
	<el-dialog v-model="visible" :title="type[model][form.tree_type - 1]" :width="500" destroy-on-close>
		<el-form ref="dialogForm" :model="form">
			<el-form-item v-if="model === 'add'" label="新增类">
				<el-button type="primary" :disabled="form.type_name.length >= 9" @click="addFloor">新增</el-button>
			</el-form-item>
			<el-form-item v-for="(item, index) in form.type_name" :key="index" label="名称">
				<div class="flex_bc">
					<el-input v-model="item.value" placeholder="请输入名称" style="width: 240px" clearable></el-input>
					<el-icon v-if="model === 'add'" style="font-size: 24px; margin-left: 10px" @click="removeFloor(index)"
						><el-icon-remove
					/></el-icon>
				</div>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { ref, reactive, getCurrentInstance } from 'vue'
const props = defineProps({
	params: {
		type: Object,
		default: () => {}
	}
})
const emit = defineEmits(['success'])
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultData = () => {
	return {
		tenant_id: '', // 学校id
		campus_id: '', // 校区id
		tree_type: 1,
		parent_id: '',
		type_name: [{ value: '' }]
	}
}
let visible = ref(false)
let isSaveing = ref(false)
let model = ref('add')
let type = reactive({
	add: ['新增类', '新增子类'],
	edit: ['编辑类', '编辑子类']
})
let dialogForm = ref(null)
// 表单字段
const form = ref(defaultData())
const addFloor = () => {
	form.value.type_name.push({ value: '' })
}
// const rules = reactive({
// 	name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
// })
const removeFloor = (index) => {
	console.log(index)
	form.value.type_name.splice(index, 1)
}
const open = (mode = 'add', type = 1, parent_id) => {
	form.value = defaultData()
	model.value = mode
	form.value.campus_id = props.params.campus_id
	form.value.tenant_id = props.params.tenant_id
	form.value.tree_type = type
	form.value.parent_id = parent_id
}
// 表单提交方法
const submit = async () => {
	await dialogForm.value.validate()
	isSaveing.value = true
	if (model.value === 'add') {
		form.value.type_name = form.value.type_name.map((v) => v.value)
		let res = await globalPropValue.eduMaterials.material.tree_add.post(form.value)
		isSaveing.value = false
		if (res.code === 200) {
			emit('success', form.value, model.value)
			visible.value = false
			ElMessage({ type: 'success', message: '添加成功' })
		} else {
			ElMessage({ type: 'error', message: res.message })
		}
	} else {
		form.value.type_name = form.value.type_name.map((v) => v.value)[0]
		let res = await globalPropValue.eduMaterials.material.tree_edit.post(form.value)
		isSaveing.value = false
		if (res.code === 200) {
			emit('success', form.value, model.value)
			ElMessage({ type: 'success', message: '修改成功' })
		} else {
			ElMessage({ type: 'error', message: res.message })
		}
		visible.value = false
	}
}
// 表单数据注入
const setData = (data) => {
	Object.assign(form.value, data)
}
// 统一向外暴露组件实例
defineExpose({
	visible,
	open,
	setData
})
</script>

<style lang="scss" scoped></style>
