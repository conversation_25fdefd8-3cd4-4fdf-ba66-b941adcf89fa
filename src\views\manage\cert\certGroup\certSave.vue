<template>
	<el-dialog v-model="visible" width="500" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-width="100">
			<el-form-item v-if="form.object === 1" label="学生" prop="objecter">
				<cusSelectStudent
					v-model="form.objecter"
					:disabled="mode === 'edit'"
					:multiple="true"
					width="100%"
				></cusSelectStudent>
			</el-form-item>
			<el-form-item v-if="form.object === 2" label="教师" prop="objecter">
				<cusSelectTeacher
					v-model="form.objecter"
					:disabled="mode === 'edit'"
					:multiple="true"
					width="100%"
				></cusSelectTeacher>
			</el-form-item>
			<el-form-item v-if="form.object === 3" label="部门" prop="objecter">
				<cusSelectDepartment
					v-model="form.objecter"
					:disabled="mode === 'edit'"
					:multiple="false"
					:prop="{
						multiple: false,
						emitPath: false,
						value: 'id',
						label: 'department_name',
						checkStrictly: true
					}"
					width="100%"
				></cusSelectDepartment>
			</el-form-item>
			<el-form-item v-if="form.object === 4" label="年级" prop="objecter">
				<el-select v-model="form.objecter">
					<el-option
						v-for="item in grade"
						:key="item.id"
						:disabled="mode === 'edit'"
						:label="item.grade_name"
						:value="item.id"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item v-if="form.object === 5" label="班级" prop="objecter">
				<cusSelectClass
					v-model="form.objecter"
					:disabled="mode === 'edit'"
					:semester-id="this.form.semester_id"
					clearable
					placeholder="请选择班级"
					width="100%"
				/>
			</el-form-item>
			<el-form-item v-if="form.object === 6" label="学科" prop="objecter">
				<el-select v-model="form.objecter" clearable :disabled="mode === 'edit'">
					<el-option
						v-for="item in allCourseList"
						:key="item.id"
						:label="item.course_name"
						:value="item.id"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="获奖名称" prop="award_name">
				<el-input v-model="form.award_name" placeholder="请输入获奖名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="获奖名次" prop="award_ranking">
				<el-input v-model="form.award_ranking" placeholder="请输入获奖名次" clearable></el-input>
			</el-form-item>
			<el-form-item label="获奖日期" prop="award_date">
				<el-date-picker
					v-model="form.award_date"
					type="date"
					format="YYYY-MM-DD"
					value-format="YYYY-MM-DD"
					placeholder="请输入获奖日期"
					clearable
				></el-date-picker>
			</el-form-item>
			<el-form-item label="获奖信息" prop="award_desc">
				<el-input v-model="form.award_desc" placeholder="请输入获奖信息" clearable></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode === 'add'" type="primary" :loading="isSaveing" @click="creatSubmit()">保 存</el-button>
			<el-button v-if="mode === 'edit'" type="primary" :loading="isSaveing" @click="editSubmit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import CusSelectDepartment from '@/components/custom/cusSelectDepartment.vue'
import cusSelectClass from '@/components/custom/cusSelectClass.vue'

import cusTom from '@/utils/cusTom'
const { certTypeMap, semesterInfo, certObjectMap } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		object_ids: null,
		object: null,
		cert_group_id: null,
		award_name: null,
		award_desc: null,
		award_date: 1,
		award_ranking: null,
		semester_id: null,
		id: null
	}
}

export default {
	emits: ['success', 'closed'],
	components: {
		CusSelectDepartment,
		cusSelectClass
	},
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			certTypeMap,
			certObjectMap,
			semesterInfo,
			mode: 'add',
			templateMap: [],
			// 所有课程列表
			allCourseList: [],
			grade: [],
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				objecter: [{ required: true, message: '请选择证书对象' }],
				award_name: [{ required: true, message: '请输入获奖名称' }],
				award_date: [{ required: true, message: '请输入获奖日期' }]
			},
			course: [],
			discipline: [],
			treeData: [],
			position: []
		}
	},
	watch: {},
	mounted() {},
	computed: {},
	created() {},
	methods: {
		async getCourse() {
			const res = await this.$API.eduCourseSet.course.all.get({
				tenant_id: this.form.tenant_id,
				campus_id: this.form.campus_id
			})
			if (res.code === 200) {
				this.allCourseList = res.data
			}
		},
		async getGrade() {
			if (this.params.semester_id) {
				var res = await this.$API.eduGradeClass.grade.all.get({
					tenant_id: this.form.tenant_id,
					campus_id: this.form.campus_id,
					semester_id: this.form.semester_id
				})
				this.grade = res.data
			} else {
				this.grade = []
			}
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			/*	this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			this.form.cert_group_id = this.params.id*/
			return this
		},
		//表单提交方法
		creatSubmit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let res
					switch (this.form.object) {
						case 1:
						case 2:
							this.form.object_ids = this.form.objecter.length
								? this.form.objecter.map((item) => item.id).join(',')
								: ''
							break
						default:
							this.form.object_ids = this.form.objecter+''
							break
					}
					res = await this.$API.cert.cert.creat.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		editSubmit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.$confirm(`编辑证书信息后，证书将会重置，需重新提交审核，确定编辑吗？`, '提示', {
						type: 'warning'
					})
						.then(async () => {
							this.isSaveing = true
							let res
							res = await this.$API.cert.cert.save.post(this.form)
							this.isSaveing = false
							if (res.code === 200) {
								this.$emit('success', this.form, this.mode)
								this.visible = false
								this.$message.success('操作成功')
							} else {
								this.$alert(res.message, '提示', { type: 'error' })
							}
						})
						.catch(() => {})
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			if (data.objecter && (data.object === 1 || data.object === 2)) {
				this.form.objecter = [
					{
						id: data.objecter.id,
						label: data.objecter.name
					}
				]
			} else if (data.objecter) {
				this.form.objecter = data.objecter.id
			}
			this.getCourse()
			this.getGrade()
		}
	}
}
</script>

<style scoped lang="scss">
.el-select {
	width: 100%;
	max-width: unset;
}
</style>
