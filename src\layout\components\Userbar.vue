<!--
 * @Author: Xujianchen
 * @Date: 2023-02-28 14:12:15
 * @LastEditors: Xujianchen
 * @LastEditTime: 2023-03-19 12:00:42
 * @Description:
-->
<template>
	<div class="user-bar">
		<div class="panel-item" @click="refresh">
			<el-tooltip effect="dark" content="刷新页面" placement="bottom">
				<el-button icon="el-icon-refresh" :loading="refreshLoading" circle />
			</el-tooltip>
		</div>
		<div class="panel-item" @click="search">
			<el-tooltip effect="dark" content="搜索菜单" placement="bottom">
				<el-button icon="el-icon-search" circle />
			</el-tooltip>
		</div>
		<div class="screen panel-item hidden-sm-and-down" @click="screen">
			<el-tooltip effect="dark" content="全屏" placement="bottom">
				<el-button icon="el-icon-full-screen" circle />
			</el-tooltip>
		</div>
		<!--		<div class="tasks panel-item hidden-sm-and-down" @click="tasks">
			<el-tooltip effect="dark" content="任务中心" placement="bottom">
				<el-button icon="el-icon-sort" circle />
			</el-tooltip>
		</div>-->
		<div class="msg panel-item" @click="showMsg">
			<el-badge :hidden="msgList.length === 0" :value="msgList.length" class="badge" type="danger">
				<el-tooltip effect="dark" content="消息中心" placement="bottom">
					<el-button icon="el-icon-chat-dot-round" circle />
				</el-tooltip>
			</el-badge>
		</div>
		<el-dropdown class="user panel-item" trigger="click" @command="handleUser">
			<div class="user-avatar">
				<el-avatar :size="30" :src="avatar">{{ userNameF }}</el-avatar>
				<label class="hidden-sm-and-down">{{ userName }}</label>
				<el-icon class="el-icon--right">
					<el-icon-arrow-down />
				</el-icon>
			</div>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="uc">帐号信息</el-dropdown-item>
					<el-dropdown-item command="clearCache">清除缓存</el-dropdown-item>
					<el-dropdown-item command="refreshPermissions">刷新权限</el-dropdown-item>
					<el-dropdown-item divided command="outLogin">退出登录</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<div v-if="!prod" class="tasks panel-item hidden-sm-and-down" @click="openSetting">
			<el-tooltip effect="dark" content="系统设置" placement="bottom">
				<el-button icon="el-icon-setting" circle />
			</el-tooltip>
			<el-drawer v-model="settingDialog" title="系统设置" :size="400" append-to-body destroy-on-close>
				<setting></setting>
			</el-drawer>
		</div>
		<div v-else>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
	</div>

	<el-dialog v-model="searchVisible" :width="700" title="搜索" center destroy-on-close>
		<search @success="searchVisible = false"></search>
	</el-dialog>

	<el-drawer v-model="tasksVisible" :size="450" title="任务中心" destroy-on-close>
		<tasks></tasks>
	</el-drawer>
	<el-drawer v-model="msg" title="新消息" :size="500" append-to-body destroy-on-close class="msg-drawer">
		<el-container>
			<el-main class="nopadding">
				<ul class="msg-type">
					<li
						v-for="item in messageTypeMaps"
						:class="{ active: item.value == selectedValue }"
						@click="selectType(item)"
					>
						{{ item.name }}
					</li>
				</ul>
				<el-scrollbar style="width: 100%">
					<ul v-if="msgList?.length !== 0" class="msg-list">
						<li v-for="item in msgList" v-bind:key="item.id" @click="showMessage">
							<a :href="item.link" target="_blank">
								<div class="msg-list__icon">
									<el-badge is-dot type="danger">
										<el-avatar>{{ item.title.substring(0, 1) }}</el-avatar>
									</el-badge>
								</div>
								<div class="msg-list__main">
									<h2>{{ item.title }}</h2>
									<!-- <p>{{ item.content }}</p> -->
								</div>
								<div class="msg-list__time">
									<p>{{ item.time }}</p>
								</div>
							</a>
						</li>
					</ul>
					<el-empty v-else description="暂无新消息" :image-size="100"></el-empty>
				</el-scrollbar>
			</el-main>
			<el-footer>
				<el-button type="primary" @click="goPage">消息中心</el-button>
				<el-button @click="markRead">全部设为已读</el-button>
			</el-footer>
		</el-container>
	</el-drawer>
</template>

<script>
import search from './search.vue'
import tasks from './tasks.vue'
import setting from '@/layout/components/Setting.vue'
import useTabs from '@/utils/useTabs'
import cusTom from '@/utils/cusTom'
import { Message } from '@element-plus/icons-vue'
import { getDateBefore } from '@/utils/dayjs'
import { ElLoading } from 'element-plus'

const { campusId, tenantId, campusInfo, messageTypeMap } = cusTom.getBaseQuery()
import { useCommonStore } from '@/stores/common.js'
const commonStore = useCommonStore()

export default {
	components: {
		setting,
		search,
		tasks
	},
	data() {
		return {
			userName: '',
			prod: false,
			userNameF: '',
			avatar: '',
			searchVisible: false,
			tasksVisible: false,
			refreshLoading: false,
			settingDialog: false,
			msg: false,
			msgList: [],
			params: {
				tenant_id: tenantId,
				campus_id: campusId,
				message_type: 0
			},
			messageTypeMaps: [],
			selectedValue: 0,
			Message: 'el-icon-message'
		}
	},
	created() {
		var userInfo = this.$TOOL.data.get('USER_INFO')
		this.userName = userInfo.nickname
		if (userInfo.avatar === '') {
			this.userNameF = this.userName.substring(0, 1)
		} else {
			this.avatar = userInfo.avatar
		}
		this.messageTypeMaps = [
			{
				name: '全部消息',
				value: 0
			},
			...messageTypeMap
		]
		this.prod = import.meta.env.MODE.VITE_NODE_ENV === 'production'
		this.getUnread()
	},
	methods: {
		//个人信息
		handleUser(command) {
			if (command === 'uc') {
				this.$router.push({ path: '/usercenter' })
			}
			if (command === 'cmd') {
				this.$router.push({ path: '/cmd' })
			}
			if (command === 'clearCache') {
				this.$confirm('清除缓存会将系统初始化，包括登录状态、主题、语言设置等，是否继续？', '提示', {
					type: 'info'
				})
					.then(() => {
						const loading = this.$loading()
						this.$TOOL.data.clear()
						this.$router.replace({ path: '/login' })
						setTimeout(() => {
							loading.close()
							location.reload()
						}, 1000)
					})
					.catch(() => {
						//取消
					})
			}
			if (command === 'outLogin') {
				this.$confirm('确认是否退出当前用户？', '提示', {
					type: 'warning',
					confirmButtonText: '退出',
					confirmButtonClass: 'el-button--danger'
				})
					.then(async () => {
						var res = await this.$API.auth.logout.post()
						if (res.code === 200) {
							this.$router.replace({ path: '/login' })
						} else {
							this.$message.warning(res.message)
							return false
						}
					})
					.catch(() => {
						//取消退出
					})
			}
			if (command === 'refreshPermissions') {
				this.refreshPermissions()
			}
		},
		//全屏
		screen() {
			var element = document.documentElement
			this.$TOOL.screen(element)
		},
		selectType(item) {
			this.params.message_type = item.value
			this.selectedValue = item.value
			this.getUnread()
		},
		//显示短消息
		showMsg() {
			this.msg = true
		},
		showMessage() {
			this.$router.push({ path: '/message' })
			this.msg = false
		},
		goPage() {
			this.$router.push({ path: '/message' })
			this.msg = false
		},
		//标记已读
		markRead() {
			this.$API.notice.readall.post(this.params).then((res) => {
				if (res.code === 200) {
					this.getUnread()
				} else {
					this.$message.warning(res.message)
				}
			})
		},
		//搜索
		search() {
			this.searchVisible = true
		},
		refresh() {
			this.refreshLoading = true
			useTabs.refresh()
			setTimeout(() => {
				this.refreshLoading = false
			}, 1000)
		},
		//刷新权限
		async refreshPermissions() {
			commonStore.saveCommonData() //保存公共数据 如校区列表信息
			//刷新权限 重新获取菜单
			var menu = null
			menu = await this.$API.system.menu.myMenus.get()
			if (menu.code === 200) {
				this.$TOOL.data.remove('MENU')
				this.$TOOL.data.remove('PERMISSIONS')
				this.$TOOL.data.remove('DASHBOARDGRID')
				this.$TOOL.data.remove('grid')
				if (menu.data.menu.length === 0) {
					this.$alert('当前用户无任何菜单权限，请重新登录！', '无权限访问', {
						type: 'error',
						center: true
					})
					this.$router.replace({ path: '/login' })
					setTimeout(() => {
						location.reload()
					}, 1500)
					return false
				}
				this.$TOOL.data.set('MENU', menu.data.menu)
				this.$TOOL.data.set('PERMISSIONS', menu.data.permissions)
				if (menu.data.dashboardGrid) {
					this.$TOOL.data.set('DASHBOARDGRID', menu.data.dashboardGrid)
				}
				if (menu.data.dashboardCopmsList && menu.data.dashboardLayout) {
					this.$TOOL.data.set('GRID', {
						copmsList: menu.data.dashboardCopmsList,
						layout: menu.data.dashboardLayout
					})
				}
				if (menu.data.mods !== null) {
					this.$TOOL.data.set('my-mods', menu.data.mods)
				}
				const loading = ElLoading.service({
					lock: true,
					text: '权限刷新中...'
				})
				//this.$router.replace({ path: '/dashboard' })
				setTimeout(() => {
					loading.close()
					location.reload()
				}, 1500)
			} else {
				this.$message.warning(menu.message)
				return false
			}
		},
		//任务
		tasks() {
			this.tasksVisible = true
		},
		openSetting() {
			this.settingDialog = true
		},
		getUnread() {
			this.$API.notice.unread.get(this.params).then((res) => {
				if (res.code == 200) {
					this.msgList = res.data.map((item) => {
						item.time = getDateBefore(item.created_at)
						return item
					})
				}
			})
		}
	}
}
</script>
<style lang="scss">
.msg-drawer {
	.el-drawer__body {
		padding-left: 0;
		border-top: 1px solid #eee;
	}
}
</style>
<style scoped>
.user-bar {
	display: flex;
	align-items: center;
	height: 100%;
}

.user-bar .panel-item {
	padding: 0 5px;
	cursor: pointer;
	height: 100%;
	display: flex;
	align-items: center;
}

.user-bar .panel-item i {
	font-size: 16px;
}

.user-bar .panel-item:hover {
	/*background: rgba(0, 0, 0, 0.1);*/
}

.user-bar .user-avatar {
	height: 49px;
	display: flex;
	align-items: center;
}

.user-bar .user-avatar label {
	display: inline-block;
	margin-left: 5px;
	font-size: 12px;
	cursor: pointer;
}

.msg-drawer .el-drawer__body {
	padding-left: 0;
}

.nopadding {
	display: flex;
}

.nopadding .msg-type {
	width: 120px;
	font-size: 14px;
}

.msg-type li {
	height: 40px;
	line-height: 40px;
	cursor: pointer;
	text-align: center;
}

.msg-type li:hover {
	background: #ecf5ff;
	color: var(--el-color-primary);
}

.msg-type .active {
	background: #ecf5ff;
	color: var(--el-color-primary);
}

.nopadding .msg-list {
}

.msg-list li {
	/* border-top: 1px solid #eee; */
	border-bottom: 1px solid #eee;
	cursor: pointer;
}
.msg-list li:last-child {
	/* border-top: 1px solid #eee; */
	border-bottom: none;
}

.msg-list li a {
	display: flex;
	padding: 20px;
}

.msg-list li a:hover {
	background: #ecf5ff;
}

.msg-list__icon {
	width: 40px;
	margin-right: 15px;
}

.msg-list__main {
	flex: 1;
}

.msg-list__main h2 {
	font-size: 14px;
	font-weight: normal;
	color: #333;
}

.msg-list__main p {
	font-size: 12px;
	color: #999;
	line-height: 1.8;
	margin-top: 5px;
}

.msg-list__time {
	width: 100px;
	text-align: right;
	color: #999;
}

.dark .msg-list__main h2 {
	color: #d0d0d0;
}

.dark .msg-list li {
	border-top: 1px solid #363636;
}

.dark .msg-list li a:hover {
	background: #383838;
}
</style>
