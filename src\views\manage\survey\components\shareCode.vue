<template>
    <el-dialog v-if="dialogVisible" v-model="dialogVisible" title="分享问卷" width="500">
        <div class="share">
            <div class="share_wx">
                <div class="share_wx_title">
                    微信扫码分享
                    <p>微信扫码，通过公众号/微信推文分享表单</p>
                </div>
                <div class="share_wx_code">
                    <sc-qr-code :size="150" :text="codeData.codeUrl" colorDark="#333" colorLight="#fff"></sc-qr-code>
                </div>
            </div>
            <div class="share_link">
                <div class="share_link_title">
                    链接分享
                    <span>(可以通过微信打开链接)</span>
                </div>
                <div class="share_link_content">
                    <el-input v-model="codeData.codeUrl" disabled style="margin-right: 10px;"></el-input>
                    <el-button type="primary" @click="copy(codeData.codeUrl)">复制</el-button>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup>
import useClipboard from 'vue-clipboard3'
import { ElMessage } from 'element-plus'
// 复制
const copy = async (val) => {
    try {
        const { toClipboard } = useClipboard()
        await toClipboard(val)
        ElMessage.success('复制成功')
    } catch (error) {
        ElMessage.error('复制失败')
    }
}

const dialogVisible = ref(false)
const codeData = ref({})
const open = (val) => {
    codeData.value = val
    codeData.value.codeUrl = getDomain()
    dialogVisible.value = true
}
const close = () => {
    dialogVisible.value = false
}
// 获取当前运行域名
const getDomain = () => {
    const codeUrl = window.location.origin
    return codeUrl + '/#/surveyFormGlean?code=' + codeData.value.code
}


defineExpose({
    open,
    close
})
</script>

<style lang="scss" scoped>
.share {
    padding: 15px;
    padding-top: 0;
}

.share_wx {
    .share_wx_title {
        padding: 10px 0;
        font-size: 14px;

        p {
            font-size: 12px;
            color: #999;
        }
    }

    .share_wx_code {
        text-align: center;
        margin: 15px 0;
    }
}

.share_link {
    .share_link_title {
        padding: 10px 0;
        font-size: 14px;

        span {
            font-size: 12px;
            color: #999;
        }
    }

    .share_link_content {
        display: flex;
        align-items: center;

        .el-input {
            flex: 1;
        }
    }
}
</style>