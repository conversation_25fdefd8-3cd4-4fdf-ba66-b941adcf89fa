<template>
    <div v-if="hasError">
        <el-result icon="error" :title="errorMsg" sub-title=""> </el-result>
    </div>
    <div v-else-if="hasSubmit">
        <el-result icon="warning" title="你已提交过问卷，请勿重复提交" sub-title=""> </el-result>
    </div>
    <div v-else>
        <el-result icon="success" title="问卷提交成功" sub-title=""> </el-result>
    </div>
</template>
<script setup>
const { query } = useRoute()
const hasSubmit = computed(() => query.hasSubmit === 'true')
const errorMsg = computed(() => query.errorMsg)
const hasError = computed(() => query.hasError === 'true')
</script>
<style>
#app,
body {
    /* min-width: 1600px; */
    min-width: 100%;
}
</style>