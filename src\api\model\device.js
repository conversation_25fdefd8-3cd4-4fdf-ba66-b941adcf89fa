import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		//url: `${config.MOCK_URL}/system/user/list`,
		url: `${config.API_URL}/affapi/device/list`,
		name: '获取设备列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	opendoorRecord: {
		//url: `${config.MOCK_URL}/system/user/list`,
		url: `${config.API_URL}/manapi/campus_org/opendoorRecord`,
		name: '开门记录',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	unbind: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/affapi/device/unbind`,
		name: '设备解绑',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	bind: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/affapi/device/bind`,
		name: '手动绑定',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	temporaryPwd: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/affapi/device/temporaryPwd`,
		name: '临时密码',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	del: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/manapi/car/del`,
		name: '删除用户',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	allClassRoomDeviceList: {
		url: `${config.API_URL}/affapi/device/allClassRoomDeviceList`,
		name: '获取所有教室班牌',
		get: async function (params) {
			return await http.get(this.url, params)
		}
	},
	changePlatoonMode: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/affapi/device/changePlatoonMode`,
		name: '更换班牌模式',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	changePlatoonArea: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/affapi/device/changeWeatherAreaCode`,
		name: '更换绑定地域',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	}
}