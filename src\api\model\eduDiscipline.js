import config from '@/config'
import http from '@/utils/request'

export default {
	discipline: {
		list: {
			url: `${config.API_URL}/eduapi/discipline/list`,
			name: '获取学科列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/eduapi/discipline/all`,
			name: '获取学科列表不分页',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/discipline/save`,
			name: '新增学科/修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},

		del: {
			url: `${config.API_URL}/eduapi/discipline/del`,
			name: '删除学科',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		status: {
			url: `${config.API_URL}/eduapi/discipline/changeStatus`,
			name: '修改学科状态',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	},
	class: {
		list: {
			url: `${config.API_URL}/eduapi/class/list`,
			name: '获取学科列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/eduapi/class/all`,
			name: '获取学科列表不分页',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/class/save`,
			name: '新增学科/修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},

		del: {
			url: `${config.API_URL}/eduapi/class/del`,
			name: '删除学科',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		status: {
			url: `${config.API_URL}/eduapi/class/changeStatus`,
			name: '修改学科状态',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	}
}
