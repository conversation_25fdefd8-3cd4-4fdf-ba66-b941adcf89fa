<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item label="">
						<el-select
							v-if="CampusManagementList.length > 1"
							v-model="params.campus_id"
							placeholder="请选择校区"
							filterable
							style="width: 150px"
						>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<cusSelectField v-model="params.room" :multiple="false" placeholder="请选择场室"></cusSelectField>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="请输入场景名称" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<div ref="scTableMain" v-loading="loading" class="scTable" style="height: 100%">
				<div class="scTable-table" style="height: calc(100% - 40px)">
					<div style="height: 100%">
						<el-scrollbar class="no-horizontal-scrollbar" style="height: 100%">
							<div v-if="listData && listData.length > 0" style="padding-right: 10px">
								<el-row :gutter="15">
									<el-col v-for="(item, index) in listData" :key="index" :xl="6" :lg="6" :md="8" :sm="12" :xs="24">
										<el-card class="task task-item" shadow="hover">
											<h2>{{ item.scene_name }}</h2>
											<ul>
												<li>
													<h4>场景描述</h4>
													<p>
														<el-tooltip class="tableTips" :raw-content="true" effect="light">
															<template #content>
																<p style="max-width: 500px">{{ item.scene_desc }}</p>
															</template>
															<div class="overflowTips">
																{{ item.scene_desc ? item.scene_desc : '无' }}
															</div>
														</el-tooltip>
													</p>
												</li>
												<li>
													<h4>生效场室</h4>
													<p>
														<el-tooltip
															v-if="item.room_info.length > 0"
															class="tableTips"
															:raw-content="true"
															effect="light"
														>
															<template #content>
																<div style="max-width: 800px">
																	<el-tag
																		v-for="(item, index) in item.room_info"
																		:key="index"
																		style="margin-bottom: 8px"
																		>{{ item.name }}
																	</el-tag>
																</div>
															</template>
															<div class="overflowTips">
																<template v-for="item in item.room_info" :key="item.id">
																	<span>{{ item.name }} </span>
																	<el-divider direction="vertical" />
																</template>
															</div>
														</el-tooltip>
													</p>
												</li>
											</ul>
											<div class="bottom">
												<div class="state">
													<el-tag v-if="item.status === 1" size="small">准备就绪</el-tag>
													<el-tag v-if="item.status === -1" size="small" type="info">停用</el-tag>
												</div>
												<div class="handler">
													<el-button
														v-loading="runLoading"
														type="primary"
														icon="el-icon-caret-right"
														circle
														@click="run(item)"
													></el-button>
												</div>
											</div>
										</el-card>
									</el-col>
								</el-row>
							</div>
							<el-empty v-else description="暂无定义场景"></el-empty>
						</el-scrollbar>
					</div>
				</div>
				<div class="scTable-page">
					<div class="pagination">
						<el-pagination
							v-if="total > 0"
							v-model:current-page="params.page"
							:page-sizes="[12, 24, 36, 72, 96]"
							:page-size="params.pageSize"
							size="small"
							background
							layout="total,sizes, prev, pager, next,jumper"
							:total="total"
							@size-change="handleSizeChange"
							@current-change="handleCurrentChange"
						/>
					</div>
				</div>
			</div>
		</el-main>
	</el-container>
	<el-dialog v-model="runDialog" title="选择场室" width="500">
		<div style="color: var(--el-color-danger)">
			请在下方选择场室进行运行场景【<b>{{ selectScene.scene_name }}</b
			>】：
		</div>
		<div style="margin-top: 15px">
			<el-radio-group v-model="selectScene.selectRoom">
				<el-radio
					v-for="(item, index) in selectScene.room_info"
					:key="index"
					:value="item.id"
					border
					style="margin-bottom: 10px"
					>{{ item.name }}</el-radio
				>
			</el-radio-group>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="runDialog = false">取 消</el-button>
				<el-button type="primary" @click="runConfirm()">确 定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import { ElMessageBox } from 'element-plus'

const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		campus_id: campusId,
		page: 1,
		room_id: null,
		status: 1,
		pageSize: 12,
		room: null
	}
}
export default {
	name: 'deviceList',
	props: {
		deviceInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			runLoading: false,
			runDialog: false,
			CampusManagementList: [],
			groupFilterText: '',
			loading: false,
			total: 0,
			enumConfig: [],
			selectScene: null,
			productList: [],
			listData: [],
			deviceStatusType: {
				1: 'info',
				2: 'success',
				3: 'info',
				4: 'warning',
				5: 'danger'
			},
			params: defaultParams(),
			search: {
				name: null
			},
			dialog: {
				save: false
			}
		}
	},
	watch: {
		'params.room': {
			handler(val) {
				console.log(val)
				if (val && val.length > 0) {
					this.params.room_id = val[0].id
				} else {
					this.params.room_id = null
				}
			},
			deep: true
		}
	},
	created() {
		this.CampusManagementList = campusInfo
		this.getList()
	},
	methods: {
		handleSizeChange(page_size) {
			this.params.pageSize = page_size
			this.getList()
		},
		handleCurrentChange(page) {
			this.params.page = page
			this.getList()
		},
		async getList() {
			this.loading = true
			const res = await this.$LotApi.scene.list.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.listData = res.data.rows
				this.total = res.data.total
			}
		},
		run(item) {
			if (item.room_info && item.room_info.length == 1) {
				let room = item.room_info[0]
				var reqData = { scene_id: item.id, tenant_id: item.tenant_id, campus_id: item.campus_id, room_id: room.id }
				ElMessageBox.confirm('确认在场室【' + room.name + '】执行场景【' + item.scene_name + '】吗？', '操作', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(async () => {
					await this.runAction(reqData)
				})
			} else {
				this.selectScene = item
				this.selectScene.selectRoom = null
				this.runDialog = true
			}
		},
		async runConfirm() {
			if (!this.selectScene.selectRoom) {
				this.$message.error('请选择场景执行的场室！')
				return
			}
			console.log(this.selectScene)
			var reqData = {
				scene_id: this.selectScene.id,
				tenant_id: this.selectScene.tenant_id,
				campus_id: this.selectScene.campus_id,
				room_id: this.selectScene.selectRoom
			}
			await this.runAction(reqData)
		},
		async runAction(reqData) {
			var res = await this.$LotApi.scene.run.post(reqData)
			if (res.code === 200) {
				this.$message.success('执行成功')
				this.runDialog = false
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//搜索
		upsearch() {
			this.params.page = 1
			this.getList()
		},
		refresh() {
			this.params = defaultParams()
			this.params.room = null
			this.upsearch()
		},
		//新增职位
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open().setData(null, this.productList, this.params.campus_id)
			})
		},
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.getList()
			}
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row, this.productList, this.params.campus_id)
			})
		},
		//查看
		table_show(row) {
			/*this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})*/
			this.$router.push({
				path: '/lot/device/detail',
				query: {
					id: row.id,
					campus_id: row.campus_id
				}
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: row.tenant_id, campus_id: row.campus_id }
			ElMessageBox.confirm('谨慎操作！确保当前设备已拆除，是否确认删除当前设备！', '谨慎操作', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.device.del.post(reqData)
				if (res.code === 200) {
					this.$message.success('删除成功')
					this.getList()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.el-header > .left-panel {
	flex: 4;
}

.scTable-table {
	height: calc(100% - 40px);
}

.scTable-page {
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding: 0 15px;
	border-top: 1px solid var(--el-border-color-light);

	.pagination {
	}
}

.scTable-do {
	white-space: nowrap;
}

.scTable:deep(.el-table__footer) .cell {
	font-weight: bold;
}

.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
	height: 8px;
	border-radius: 8px;
}

.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
	width: 8px;
	border-radius: 8px;
}

:deep(.el-scrollbar__wrap) {
	overflow-x: hidden !important;
}

.task {
	height: 210px;
}

.overflowTips {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.task-item h2 {
	font-size: 15px;
	color: #3c4a54;
	padding-bottom: 15px;
}

.task-item li {
	list-style-type: none;
	margin-bottom: 10px;
}

.task-item li h4 {
	font-size: 12px;
	font-weight: normal;
	color: #999;
}

.task-item li p {
	margin-top: 5px;
}

.task-item .bottom {
	border-top: 1px solid #ebeef5;
	text-align: right;
	padding-top: 10px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.task-add {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
	cursor: pointer;
	color: #999;
}

.task-add:hover {
	color: #409eff;
}

.task-add i {
	font-size: 30px;
}

.task-add p {
	font-size: 12px;
	margin-top: 20px;
}

.dark .task-item .bottom {
	border-color: var(--el-border-color-light);
}
</style>
