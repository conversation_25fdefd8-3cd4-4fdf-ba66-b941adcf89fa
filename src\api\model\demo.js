import config from '@/config'
import http from '@/utils/request'

export default {
	ver: {
		url: `${config.MOCK_URL}/demo/ver`,
		name: '获取最新版本号',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	post: {
		url: `${config.MOCK_URL}/demo/post`,
		name: '分页列表',
		post: async function (data) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data, { headers: {} })
		}
	},
	page: {
		url: `${config.MOCK_URL}/demo/page`,
		name: '分页列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	list: {
		url: `${config.MOCK_URL}/demo/list`,
		name: '数据列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	menu: {
		url: `${config.MOCK_URL}/demo/menu`,
		name: '普通用户菜单',
		get: async function () {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url)
		}
	},
	status: {
		url: `${config.MOCK_URL}/demo/status`,
		name: '模拟无权限',
		get: async function (code) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, {}, { headers: { 'response-status': code } })
		}
	}
}
