<template>
  <el-form ref="form" :model="form" label-suffix="" :label-width="formItems.config?.labelWidth" 
    :label-position="formItems.config?.labelPosition" size="large">
    <template v-for="element in formItems.list" :key="element.key">
      <div v-if="element.type === 'divider'">
        <el-divider :direction="element.options.direction" :border-style="element.options.borderStyle"
          :content-position="element.options.contentPosition">
          {{ element.options.content }}
        </el-divider>
      </div>
      <div style="margin-bottom: 18px;" v-if="element.type === 'text'">
        <div style="line-height: 1.8;" v-html="element.options.defaultValue"></div>
      </div>
      <div style="text-align: center;margin-bottom: 18px;" v-if="element.type === 'insert-img'">
        <el-image :src="element.options.defaultValue" fit="cover">
          <template #error>
            请上传插入图片
          </template>
        </el-image>
      </div>

      <el-form-item
        v-if="element && element.type !== 'divider' && element.type !== 'text' && element.type !== 'title' && element.type != 'headImage' && element.type !== 'insert-img'"
        :key="element.key" :label="element.label" :prop="element.model" :rules="element.options.rules"
        v-show="!element.options.showCode || (element.options.showCode && form[element.options.showCode.model] === element.options.showCode.val)">
        <template v-if="element.type === 'input'">
          <el-input v-model="form[element.model]" :style="{ width: element.options.width }"
            :placeholder="element.options.placeholder" :minlength="parseInt(element.options.minlength)"
            :maxlength="parseInt(element.options.maxlength)" :clearable="element.options.clearable"
            :readonly="element.options.readonly" :disabled="disabled || element.options.disabled"
            :show-word-limit="element.options.showWordLimit">
            <template #prefix v-if="element.options.prefix">{{ element.options.prefix }}</template>
            <template #suffix v-if="element.options.suffix">{{ element.options.suffix }}</template>
            <template #prepend v-if="element.options.prepend">{{ element.options.prepend }}</template>
            <template #append v-if="element.options.append">{{ element.options.append }}</template>
          </el-input>
        </template>

        <template v-if="element.type === 'textarea'">
          <el-input type="textarea" v-model="form[element.model]" :rows="element.options.rows"
            :style="{ width: element.options.width }" :placeholder="element.options.placeholder"
            :minlength="parseInt(element.options.minlength)" :maxlength="parseInt(element.options.maxlength)"
            :show-word-limit="element.options.showWordLimit" :autosize="element.options.autosize"
            :clearable="element.options.clearable" :readonly="element.options.readonly"
            :disabled="disabled || element.options.disabled" />
        </template>

        <template v-if="element.type === 'number'">
          <el-input-number v-model="form[element.model]" :style="{ width: element.options.width }"
            :max="element.options.max" :min="element.options.min" :disabled="disabled || element.options.disabled" />
        </template>

        <template v-if="element.type === 'radio'">
          <el-radio-group v-model="form[element.model]" :style="{ width: element.options.width, display: 'block' }"
            :disabled="disabled || element.options.disabled" size="large">
            <p class="radio-style" v-for="item of element.options.remote
              ? element.options.remoteOptions
              : element.options.options" :key="item.value">
              <el-radio :value="item.value" :style="{
                display: element.options.inline ? 'inline-block' : 'block'
              }">{{ element.options.showLabel ? item.label : item.value }}</el-radio>
            </p>
          </el-radio-group>
        </template>

        <template v-if="element.type === 'checkbox'">
          <el-checkbox-group v-model="form[element.model]" :style="{ width: element.options.width }"
            :disabled="disabled || element.options.disabled" :min="element.options.min" :max="element.options.max">
            <p class="radio-style checkbox-style" v-for="item of element.options.remote
              ? element.options.remoteOptions
              : element.options.options" :key="item.value">
              <el-checkbox :value="item.value" :style="{
                display: element.options.inline ? 'inline-block' : 'block'
              }">
                {{ element.options.showLabel ? item.label : item.value }}
              </el-checkbox>
            </p>
          </el-checkbox-group>
        </template>

        <template v-if="element.type === 'time'">
          <el-time-picker v-model="form[element.model]" :placeholder="element.options.placeholder"
            :readonly="element.options.readonly" :editable="element.options.editable"
            :clearable="element.options.clearable" :format="element.options.format"
            :value-format="element.options.valueFormat" :disabled="disabled || element.options.disabled"
            :style="{ width: element.options.width }" />
        </template>

        <template v-if="element.type === 'date'">
          <el-date-picker v-model="form[element.model]" :placeholder="element.options.placeholder"
            :readonly="element.options.readonly" :editable="element.options.editable"
            :clearable="element.options.clearable" :type="element.options.type" :format="element.options.format"
            :value-format="element.options.valueFormat" :disabled="disabled || element.options.disabled"
            :style="{ width: element.options.width }" />
        </template>

        <template v-if="element.type === 'rate'">
          <el-rate v-model="form[element.model]" :max="element.options.max" :allowHalf="element.options.allowHalf"
            :disabled="disabled || element.options.disabled" />
        </template>

        <template v-if="element.type === 'select'">
          <el-select v-model="form[element.model]" :multiple="element.options.multiple"
            :collapseTags="element.options.collapseTags" :placeholder="element.options.placeholder"
            :clearable="element.options.clearable" :multipleLimit="element.options.multipleLimit"
            :filterable="element.options.filterable" :disabled="disabled || element.options.disabled"
            :style="{ width: element.options.width }">
            <el-option v-for="item of element.options.remote
              ? element.options.remoteOptions
              : element.options.options" :key="item.value" :value="item.value"
              :label="element.options.showLabel ? item.label : item.value" />
          </el-select>
        </template>

        <template v-if="element.type === 'switch'">
          <el-switch v-model="form[element.model]" :active-text="element.options.activeText"
            :active-value="element.options.activeText" :inactive-text="element.options.inactiveText"
            :inactive-value="element.options.inactiveText" :disabled="disabled || element.options.disabled" />
        </template>

        <template v-if="element.type === 'img-upload'">
          <sc-upload-Multiple v-model="form[element.model]" :limit="element.options.limit"
            :file-list="element.options.defaultValue" :name="element.options.file" :disabled="element.options.disabled"
            :multiple="element.options.multiple"></sc-upload-Multiple>
        </template>

        <template v-if="element.type === 'file-upload'">
          <sc-upload-file v-model="form[element.model]" :limit="element.options.limit"
            :file-list="element.options.defaultValue" :name="element.options.file" :disabled="element.options.disabled"
            :multiple="element.options.multiple"></sc-upload-file>
        </template>
      </el-form-item>
    </template>
  </el-form>
</template>

<script>
import {  ElMessageBox } from 'element-plus'
export default {
  emits: ['update:modelValue', 'validate'],
  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    },
    formItems: {
      type: Object,
      default: () => ({
        list: [],
        config: {
          labelWidth: '120px',
          labelPosition: 'right',
          size: 'default'
        }
      })
    },
    disabled: Boolean
  },
  data() {
    return {
      form: {}
    }
  },
  watch: {
    modelValue: {
      handler(val) {
        this.form = val || {}
      },
      deep: true,
      immediate: true
    },
    form: {
      handler(val) {
        this.$emit('update:modelValue', val)
      },
      deep: true
    }
  },
  methods: {
    validate() {
      return new Promise((resolve, reject) => {
        if (!this.$refs.form) {
          reject(new Error('Form reference not found'))
          return
        }
        this.$refs.form.validate((valid, error) => {
          if (valid) {
            resolve(true)
          } else {
            ElMessageBox.alert('问卷填写校验失败，请检查！', '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              showClose: false
            })
            reject(error)
          }
        })
      })
    },
    resetForm() {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    clearValidate(props) {
      if (this.$refs.form) {
        this.$refs.form.clearValidate(props)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__label {
  font-size: 16px;
}

.radio-style {
  width: 100%;
  box-sizing: border-box;
  padding: 1px 8px;
  font-size: 14px;
  color: #52555a;
  font-weight: 400;
  background: #fbfcfd;
  border: 1px solid #dcdfe4;
  display: block;

  &:nth-child(n+2) {
    border-top: none;
  }
}

.checkbox-style {
  line-height: 25px;
  padding: 5px 8px;
}
::v-deep .el-form-item__error{
  font-size: 14px !important;
  padding-bottom: 10px;
  margin-top: 5px;
  color: #f56c6c;
}
::v-deep .el-form-item{
  padding: 5px;
}
::v-deep .is-error{
  padding: 5px;
  border-radius: 5px;
  border: 1px dashed var(--el-color-danger) !important;
}
</style>