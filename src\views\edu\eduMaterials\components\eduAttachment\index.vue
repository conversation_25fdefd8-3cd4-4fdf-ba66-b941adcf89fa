<template>
	<el-drawer v-model="showDrawer" title="附件管理" direction="rtl" destroy-on-close size="50%">
		<div class="btn">
			<el-button type="primary" icon="el-icon-Plus" @click="add_attachment">新增附件</el-button>
		</div>
		<scTable
			ref="table"
			row-key="id"
			stripe
			:apiObj="apiObj"
			:params="params"
			hidePagination
			hideDo
			style="height: calc(100% - 52px)"
		>
			<el-table-column label="附件名称" prop="name">
				<template #default="{ row }">
					<el-link type="primary" :href="row.url" target="_blank">{{ row.name }}</el-link>
				</template>
			</el-table-column>
			<el-table-column label="附件描述" prop="description" show-overflow-tooltip></el-table-column>
			<el-table-column label="创建人" prop="created_user_name"></el-table-column>
			<el-table-column label="时间" prop="created_at"></el-table-column>
			<el-table-column label="操作">
				<template #default="{ row }">
					<el-button type="primary" text size="small" @click="table_edit(row)">编辑</el-button>
					<el-popconfirm title="确定删除吗？" @confirm="table_del(row)">
						<template #reference>
							<el-button text type="danger" size="small">删除</el-button>
						</template>
					</el-popconfirm>
				</template>
			</el-table-column>
		</scTable>
	</el-drawer>

	<SaveDialog ref="dialogRef" :params="params" @success="handlerSuccess"></SaveDialog>
</template>
<script setup>
import SaveDialog from './save.vue'
import { ref, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		tenant_id: route.query.tenant_id,
		campus_id: route.query.campus_id,
		material_id: route.query.material_id
	}
}
const params = ref(defaultParams())
let dialogRef = ref(null)
let table = ref(null)
let showDrawer = ref(false)
const apiObj = ref(globalPropValue.eduMaterials.material.get_attachment_list)
const open = (data) => {
	params.value = data
	showDrawer.value = true
}
// 新增附件点击事件
const add_attachment = () => {
	dialogRef.value.open()
}
// 编辑点击事件
const table_edit = (row) => {
	dialogRef.value.open('edit', row)
	dialogRef.value.setData(row)
}
// 删除点击事件
const table_del = async (row) => {
	const res = await globalPropValue.eduMaterials.material.del_attachment.post({
		id: row.id,
		tenant_id: Number(params.value.tenant_id),
		campus_id: Number(params.value.campus_id)
	})
	if (res.code === 200) {
		ElMessage({
			type: 'success',
			message: '删除成功'
		})
		table.value.upData(params.value)
	} else {
		ElMessage({
			type: 'error',
			message: res.message
		})
	}
}
// 返回按钮点击事件
const goBack = () => {
	router.back()
}
// 自定义事件
const handlerSuccess = () => {
	table.value.upData(params.value)
}
defineExpose({
	open
})
</script>

<style scoped>
.btn {
	margin: 10px 0;
}
</style>
