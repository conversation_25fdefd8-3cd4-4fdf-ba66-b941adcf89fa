<template>
	<el-main>
		<el-row :gutter="15">
			<el-col :md="12" :sm="24" :lg="12">
				<el-card shadow="hover" header="常用功能">
					<myapp></myapp>
				</el-card>
			</el-col>
			<el-col  :md="12" :sm="24" :lg="12">
				<my-class></my-class>
			</el-col>
			<el-col  :md="18" :sm="24" :lg="18">
				<el-card shadow="hover" header="周课表">
					<schedule></schedule>
				</el-card>
			</el-col>
			<el-col :md="6" :sm="24" :lg="6">
				<el-card shadow="hover">
					<template #header>
						<div class="card-header">
							<span>消息通知</span>
							<el-select v-model="messageTyep" placeholder="消息类型" style="width: 150px;">
								<el-option v-for="item in messageTypeMaps" :key="item.value" :label="item.name"
									:value="item.value" /></el-select>
						</div>
					</template>
					<my-notice :messageType="messageTyep"></my-notice>
				</el-card>
			</el-col>
		</el-row>
	</el-main>
</template>

<script>
import myapp from './components/myapp'
import myClass from './components/myClass';
import myNotice from './components/myNotice.vue';
import schedule from './components/mySchedule.vue';
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, messageTypeMap} = cusTom.getBaseQuery()

export default {
	components: {
		myapp,
		myClass,
		myNotice,
		schedule
	},
	data() {
		return {
			messageTypeMaps: [],
			messageTyep: 0
		}
	},
	created() {
		this.messageTypeMaps = [
			{
				name: '全部消息',
				value: 0,
			},
			...messageTypeMap
		]
	},
	mounted() {
		this.$emit('on-mounted')
	},
	methods: {}
}
</script>

<style scoped>
.card-header{
	display: flex;
	justify-content: space-between;
	align-items: center;
}
</style>
