<template>
	<el-dialog v-model="dialogApplyVisible" :title="title" width="500">
		<!-- <cusForm ref="formref" v-model="form" :config="formConfig"></cusForm> -->

		<showForm ref="formItemref" v-model="form" :formConfig="formConfig"></showForm>
		<template #footer>
			<el-button @click="dialogApplyVisible = false">取消</el-button>
			<el-button type="primary" @click="confirm">确定</el-button>
		</template>
	</el-dialog>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue'
import cusTom from '@/utils/cusTom'
import showForm from './components/showForm.vue'
const { campusId, tenantId, approvalModelMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const emit = defineEmits(['success'])
const title = ref()
const dialogApplyVisible = ref(false)
const form = ref({})
const formInfo = ref([])
const formConfig = ref({})
const open = (data) => {
	title.value = data.approval_name + '申请'
	getFormItme(data.form_id)
}

const confirm = () => {
	formInfo.value = formInfo.value.map((item) => {
		return {
			label: item.label,
			key: item.key,
			value: form.value[item.key] || ''
		}
	})
	console.log(form.value, formInfo.value, '确定')
	emit('success', { form: form.value, formInfo: formInfo.value })
	// emit('success', form.value)
	dialogApplyVisible.value = false
}
const getFormItme = async (id) => {
	const { data } = await globalPropValue.form.form.detail.get({ tenant_id: tenantId, campus_id: campusId, id: id })
	formConfig.value = {
		labelPosition: data.label_position,
		labelWidth: data.lable_width,
		size: data.size,
		formItems: JSON.parse(data.form_content)
	}
	formInfo.value = JSON.parse(data.form_content).map((item) => {
		return {
			label: item.label,
			key: item.model
		}
	})
	dialogApplyVisible.value = true
	console.log(formConfig.value, 'getFormItme')
}
defineExpose({
	dialogApplyVisible,
	open
})
</script>
