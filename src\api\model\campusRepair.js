import config from '@/config'
import http from '@/utils/request'

export default {
	my: {
		url: `${config.API_URL}/manapi/repair/my`,
		name: '获取我的报修',
		get: async function(params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	apply: {
		url: `${config.API_URL}/manapi/repair/apply`,
		name: '申请报修',
		post: async function(data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	edit_my: {
		url: `${config.API_URL}/manapi/repair/edit_my`,
		name: '编辑我的报修',
		post: async function(data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	appraise: {
		url: `${config.API_URL}/manapi/repair/appraise`,
		name: '评价',
		post: async function(data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	urge: {
		url: `${config.API_URL}/manapi/repair/urge`,
		name: '催单',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/manapi/repair/del`,
		name: '删除',
		post: async function(data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	list: {
		url: `${config.API_URL}/manapi/repair/list`,
		name: '获取报修受理列表',
		get: async function(params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	deal: {
		url: `${config.API_URL}/manapi/repair/deal`,
		name: '处理报修',
		post: async function(data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	configList: {
		url: `${config.API_URL}/manapi/repair_config/list`,
		name: '获取报修配置列表',
		get: async function(params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	configSave: {
		url: `${config.API_URL}/manapi/repair_config/save`,
		name: '保存报修配置',
		post: async function(data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	configAll: {
		url: `${config.API_URL}/manapi/repair_config/all`,
		name: '获取报修配置列表',
		get: async function(params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	configDel: {
		url: `${config.API_URL}/manapi/repair_config/del`,
		name: '删除报修配置',
		post: async function(data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	statistics: {
		url: `${config.API_URL}/manapi/repair/statistics`,
		name: '报修统计',
		get: async function(params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	acceptance: {
		list: {
			url: `${config.API_URL}/manapi/repair_acceptance/list`,
			name: '获取校园报修受理配置列表',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/manapi/repair_acceptance/save`,
			name: '保存校园报修受理配置',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		all: {
			url: `${config.API_URL}/manapi/repair_acceptance/all`,
			name: '获取校园报修受理配置列表',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		del: {
			url: `${config.API_URL}/manapi/repair_acceptance/del`,
			name: '删除校园报修受理配置',
			post: async function(data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		}
	}
}
