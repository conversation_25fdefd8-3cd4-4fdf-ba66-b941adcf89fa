import config from '@/config'
import http from '@/utils/request'

export default {
	all: {
		url: `${config.API_URL}/affapi/room_booking/all_booking`,
		name: '查看所有预约记录',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	myBooking: {
		url: `${config.API_URL}/affapi/room_booking/my_booking`,
		name: '我的预约记录',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	room: {
		url: `${config.API_URL}/affapi/room_booking/room`,
		name: '房间详情和预约情况',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	roomtree: {
		url: `${config.API_URL}/affapi/rooms/open_tree`,
		name: '开放房间树',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	list: {
		url: `${config.API_URL}/affapi/room_booking/list`,
		name: '开放预约房间列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	detail: {
		url: `${config.API_URL}/affapi/room_booking/one`,
		name: '单个预约详情',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	add: {
		url: `${config.API_URL}/affapi/room_booking/creat`,
		name: '新增预约',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	open: {
		url: `${config.API_URL}/affapi/room_booking/open`,
		name: '远程开门',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	edit: {
		url: `${config.API_URL}/affapi/room_booking/update`,
		name: '编辑预约',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/affapi/room_booking/del`,
		name: '删除预约',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	temporaryPwd: {
		url: `${config.API_URL}/affapi/room_booking/temporaryPwd`,
		name: '临时密码',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	}
}
