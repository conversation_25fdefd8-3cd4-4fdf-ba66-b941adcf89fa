<template>
	<scTable ref="table" row-key="id" stripe :apiObj="apiObj" :params="params" hidePagination hideDo>
		<el-table-column prop="student_name" label="学生姓名" />
		<el-table-column prop="serial_number" label="学号" width="150" show-overflow-tooltip/>
		<el-table-column prop="user_head" label="学生头像">
			<template #default="scope">
				<cusStudentHead
					loading="lazy"
					:lazy="true"
					fit="contain"
					style="width: 50px; height: 50px"
					:src="scope.row.user_head"
					:preview-src-list="[scope.row.user_head]"
					preview-teleported
				>
				</cusStudentHead>
			</template>
		</el-table-column>
		<el-table-column prop="sex" label="性别">
			<template #default="{ row }">
				<span v-if="row.sex == 1">男</span>
				<span v-if="row.sex == 2">女</span>
				<span v-if="row.sex == 3">保密</span>
			</template>
		</el-table-column>
		<el-table-column prop="academic_name" label="学期"  width="220" show-overflow-tooltip>
			<template #default="{ row }">
				{{ row.academic_name + '-' + row.semester_name }}
			</template>
		</el-table-column>
		<el-table-column prop="phone" label="手机号"  width="120" show-overflow-tooltip/>
		<el-table-column prop="status" label="状态" width="120">
			<template #default="{ row }">
				{{ formData(studentStatusMap, row.student_status) }}
			</template>
		</el-table-column>
		<el-table-column prop="position" label="职务">
			<template #default="{ row }">
				{{ row.position || '-' }}
			</template>
		</el-table-column>
		<el-table-column prop="remark" label="备注" />
		<el-table-column label="操作" fixed="right" align="center" width="210">
			<template #default="scope">
				<el-button-group>
					<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
				</el-button-group>
			</template>
		</el-table-column>
	</scTable>
	<el-dialog v-model="dialog" title="编辑" :width="500" destroy-on-close>
		<cusForm ref="formref" v-model="form" :config="formConfig"> </cusForm>
		<template #footer>
			<el-button @click="dialog = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>
<script>
import cusStudentHead from '@/components/custom/cusStudentHead.vue'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, studentStatusMap } = cusTom.getBaseQuery()
export default {
	components: {
		cusStudentHead
	},
	props: {
		data: {
			tpye: Array,
			default: () => []
		}
	},
	data() {
		return {
			campusId,
			tenantId,
			studentStatusMap,
			dialog: false,
			isSaveing: false,
			form: {
				id: null,
				tenant_id: null,
				campus_id: null,
				student_id: null,
				class_id: null,
				position: null,
				remark: null,
				mode: null
			},
			params: {},
			apiObj: this.$API.class.student,
			studentList: [],
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				labelWidth: '80px',

				formItems: [
					{
						label: '职位',
						name: 'position',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入'
						}
					},
					{
						label: '备注',
						name: 'remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入备注'
						}
					}
				]
			}
		}
	},
	created() {
		this.params = {
			class_id: this.$route.query.id,
			tenant_id: tenantId,
			campus_id: campusId
		}
		// this.getStudentList()
	},
	methods: {
		//表单提交方法
		submit() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.eduGradeClass.classStu.save.post(this.form)
					this.isSaveing = false
					this.dialog = false
					if (res.code === 200) {
						this.$refs.table.upData(this.params)
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		getStudentList() {
			this.$API.class.student
				.get({
					class_id: this.$route.query.id,
					tenant_id: tenantId,
					campus_id: campusId
				})
				.then((res) => {
					this.studentList = res.data
				})
		},
		//编辑
		table_edit(row) {
			this.dialog = true
			this.form.tenant_id = row.tenant_id
			this.form.campus_id = row.campus_id
			this.form.class_id = row.class_id
			this.form.id = row.id
			this.form.position = row.position
			this.form.remark = row.remark
			this.form.student_id = row.user_id
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value === val)?.name || '-'
		}
	}
}
</script>
<style lang="scss" scoped></style>
