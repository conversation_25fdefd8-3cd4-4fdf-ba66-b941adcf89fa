import config from '@/config'
import http from '@/utils/request'

export default {
	schedule: {
		url: `${config.API_URL}/eduapi/class/schedule`,
		name: '获取班级课表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	class: {
		url: `${config.API_URL}/eduapi/class/myclass`,
		name: '获取班级列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	info: {
		url: `${config.API_URL}/eduapi/class/myclass/info`,
		name: '获取班级信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	student: {
		url: `${config.API_URL}/eduapi/class/myclass/student`,
		name: '获取班级学生列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	vacate: {
		url: `${config.API_URL}/eduapi/class/myclass/vacate`,
		name: '获取班级学生请假记录',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	teacherData: {
		url: `${config.API_URL}/eduapi/class/teacher_data`,
		name: '获取班级老师',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	teacherSave: {
		url: `${config.API_URL}/eduapi/class/teacher_save`,
		name: '获取班级老师',
		post: async function (data) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	}
}
