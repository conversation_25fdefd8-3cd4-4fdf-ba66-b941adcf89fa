<template>
	<el-drawer v-model="visible" :title="titleMap[mode1]" destroy-on-close>
		<el-form ref="formRef" :model="form" :rules="rules" :disabled="mode1 === 'show'">
			<el-row>
				<el-col :span="12">
					<el-form-item label="名称" prop="student_name">
						<el-input v-model="form.student_name" placeholder="请输入名称" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="招生名称" prop="plan_id">
						<el-select v-model="form.plan_id" placeholder="请选择" filterable clearable>
							<el-option v-for="item in allList" :key="item.value" :label="item.plan_name" :value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item label="头像" prop="user_head">
				<scUpload v-model="form.user_head"></scUpload>
			</el-form-item>
			<el-row>
				<el-col :span="12">
					<el-form-item label="性别" prop="sex">
						<el-select v-model="form.sex" placeholder="请选择" filterable clearable>
							<el-option v-for="item in sexMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="身份证号" prop="idcard">
						<el-input v-model="form.idcard" placeholder="请输入身份证号" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="手机号" prop="phone">
						<el-input v-model="form.phone" placeholder="请输入手机号" clearable></el-input> </el-form-item
				></el-col>
				<el-col :span="12">
					<el-form-item label="邮箱" prop="email">
						<el-input v-model="form.email" placeholder="请输入邮箱" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="政治面貌" prop="political">
						<el-select v-model="form.political" placeholder="请选择" filterable clearable>
							<el-option
								v-for="item in politicalMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="民族" prop="nation">
						<el-select v-model="form.nation" placeholder="请选择" filterable clearable>
							<el-option v-for="item in nationMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<!-- <el-row>
				<el-col :span="12">
					<el-form-item label="年级">
						<el-select v-model="form.grade_id" placeholder="请选择学年" filterable clearable>
							<el-option v-for="item in getGrade" :key="item.code" :label="item.name" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="班级">
						<el-select v-model="form.class_id" placeholder="请选择学期" filterable clearable>
							<el-option v-for="item in getClass" :key="item.code" :label="item.name" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>
			</el-row> -->
			<el-row>
				<el-col :span="12">
					<el-form-item label="入学学年" prop="entrance_year">
						<el-date-picker
							v-model="form.entrance_year"
							type="year"
							placeholder="请选择"
							format="YYYY"
							value-format="YYYY"
						></el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="出生日期" prop="birthdate">
						<el-date-picker
							v-model="form.birthdate"
							type="date"
							placeholder="请选择日期"
							format="YYYY/MM/DD"
							value-format="YYYY-MM-DD"
						></el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item label="地址" prop="address">
				<el-input v-model="form.address" placeholder="请输入地址" clearable></el-input>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input v-model="form.remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template v-if="mode1 !== 'show'" #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode1 !== 'show'" type="primary" :loading="isSaveing" @click="submit">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { sexMap, politicalMap, nationMap } = cusTom.getBaseQuery()
const emit = defineEmits(['success'])
const props = defineProps(['params'])
console.log(props)
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
// 控制显示隐藏
let visible = ref(false)
// 控制loding
let isSaveing = ref(false)
// 切换模式
let mode1 = ref('add')
let titleMap = ref({ add: '新增', edit: '编辑', show: '查看' })
let formRef = ref()
// 定义表单校验规则
const validateIdcard = (rule, value, callback) => {
	const reg = /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/
	if (reg.test(value)) {
		callback()
	} else {
		callback('身份证格式有误')
	}
}
const validatorPhone = (rule, value, callback) => {
	const reg = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/
	if (reg.test(value)) {
		callback()
	} else {
		callback('手机号格式有误')
	}
}
const rules = reactive({
	student_name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
	idcard: [
		{ required: true, message: '请输入身份证号', trigger: 'blur' },
		{ validator: validateIdcard, trigger: 'blur' }
	],
	sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
	entrance_year: [{ type: 'date', required: true, message: '请选择学年', trigger: 'change' }],
	birthdate: [{ type: 'date', required: true, message: '请选择出生日期', trigger: 'change' }],
	phone: [
		{ required: true, message: '请输入手机号', trigger: 'blur' },
		{ validator: validatorPhone, trigger: 'blur' }
	],
	email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
	address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
	nation: [{ required: true, message: '请选择民族', trigger: 'change' }],
	political: [{ required: true, message: '请选择政治面貌', trigger: 'change' }]
})
// 班级列表
// let myClass = reactive({
// 	list: []
// })
// 年级列表
// let myGrade = reactive({
// 	list: []
// })
// 年级
// let getGrade = computed(() => {
// 	return myGrade.list.filter(
// 		(v) =>
// 			v.parent_id !== 0 &&
// 			v.semester_id === props.params.value.semester_id &&
// 			v.campus_id === props.params.value.campus_id
// 	)
// })
// 班级
// let getClass = computed(() => {
// 	return myClass.list.filter(
// 		(v) =>
// 			v.parent_id !== 0 && v.grade_id === props.params.value.grade_id && v.campus_id === props.params.value.campus_id
// 	)
// })
// 获取年级列表

/**
 *const getGradeData = async () => {
	var res = await globalPropValue.eduGradeClass.grade.all.get(query)
	myGrade.list = res.data
}
*/
/**
 * // 获取班级列表
const getClassData = async () => {
	var res = await globalPropValue.eduGradeClass.class.all.get(query)
	myClass.list = res.data
}
*/
/**
 * onMounted(() => {
	getGradeData()
	getClassData()
})
*/
// body参数
let form = reactive({
	id: null,
	tenant_id: null,
	campus_id: null,
	plan_id: null,
	grade_id: null,
	class_id: null,
	dormitory_room_id: null,
	student_name: '',
	user_head: '',
	entrance_year: null,
	sex: null,
	idcard: '',
	phone: '',
	email: '',
	address: '',
	nation: '',
	political: null,
	birthdate: '',
	remark: ''
})
// params参数
const query = reactive({
	page: 1,
	pageSize: 20,
	name: '',
	tenant_id: null,
	campus_id: null,
	semester_id: null,
	academic_id: null
})
const allList = ref([])
const open = async (mode = 'add') => {
	form = reactive({})
	mode1.value = mode
	visible.value = true
	query.semester_id = props.params.semester_id
	query.academic_id = props.params.academic_id
	query.tenant_id = props.params.tenant_id
	query.campus_id = props.params.campus_id
	form.tenant_id = props.params.tenant_id
	form.campus_id = props.params.campus_id
	const { data } = await globalPropValue.recruitStudent.recruit.all_list.get(query)
	allList.value = data
}
// 保存按钮操作
const submit = async () => {
	await formRef.value.validate()
	isSaveing.value = true
	form.entrance_year = Number(form.entrance_year)
	const res = await globalPropValue.recruitStudent.recruit.add_edit_cadet.post(form)
	isSaveing.value = false
	if (res.code === 200) {
		emit('success', form, mode1.value)
		visible.value = false
		ElMessage({ type: 'success', message: '操作成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
// 表单注入数据
const setData = (data) => {
	data.entrance_year = data.entrance_year + ''
	Object.assign(form, data)
	console.log(form, data)
}
// 统一向外暴露实例
defineExpose({
	visible,
	open,
	setData
})
</script>

<style lang="scss" scoped></style>
