<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"></cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<el-table-column type="selection" width="50" fixed="left" />

				<el-table-column label="通知时间" prop="created_at" width="145" fixed="left"></el-table-column>
				<el-table-column label="通知ID" prop="message_id" width="160" fixed="left"></el-table-column>
				<el-table-column label="标题" prop="title" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="消息类型" prop="title" width="100">
					<template #default="{ row }">
						{{ enumConfig.messageTypeMap.find((i) => i.value === row.message_type)?.name }}
					</template>
				</el-table-column>
				<el-table-column label="状态" prop="title" width="120">
					<template #default="{ row }">
						<el-tag v-if="row.read_status === 1" type="success">已读</el-tag>
						<el-tag v-else type="danger">未读</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="内容" prop="content">
					<template #default="scope">
						<el-tooltip v-if="scope.row.content" :raw-content="true">
							<template #content>
								<p v-for="(item, index) in scope.row.content.split('；')" :key="index">{{ item }} <br /></p>
							</template>
							<div class="overflowTips">{{ scope.row.content }}</div>
						</el-tooltip>
					</template>
				</el-table-column>
				<el-table-column label="操作" prop="content" width="120">
					<template #default="scope">
						<el-button type="primary" plain size="small" @click="rowClick(scope.row)">详情</el-button>
					</template>
				</el-table-column>
				<template v-slot:selectAction>
					<el-popconfirm width="200" title="确定批量将选择的通知设置为已读吗？" @confirm="tableBatchTreated">
						<template #reference>
							<el-button size="small" :disabled="treatedStatus !== true" type="primary">设置为已读</el-button>
						</template>
					</el-popconfirm>
				</template>
			</scTable>
		</el-main>

		<el-drawer v-model="infoDrawer" title="通知详情" direction="rtl">
			<div class="message-info">
				<div class="message-title">
					<h3>{{ messageContent.title }}</h3>
					<p>消息ID： {{ messageContent.message_id }}</p>
					<p>发布时间： {{ messageContent.created_at }}</p>
				</div>
				<div class="message-content">
					<p v-for="(item, index) in messageContent.content.split('；')" :key="index">{{ item }} <br /></p>
				</div>
			</div>
		</el-drawer>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
import ScTable from '@/components/scTable/index.vue'

const { campusId, tenantId, campusInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		begin_time: null,
		end_time: null,
		read_status: 0,
		date: [],
		name: null
	}
}
export default {
	name: 'dept',
	components: { ScTable },
	data() {
		return {
			apiObj: this.$LotApi.message.getList,
			params: defaultParams(),
			messageContent: null,
			CampusManagementList: campusInfo,
			statusMap: [
				{ name: '成功', value: 1 },
				{ name: '失败', value: 2 }
			],
			infoDrawer: false,
			treatedStatus: false,
			enumConfig: [],
			selection: [],
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'date',
						value: null,
						component: 'cusDate',
						options: {
							placeholder: '请选择校区',
							type: 'daterange'
						}
					},
					{
						label: null,
						name: 'read_status',
						value: null,
						component: 'select',
						options: {
							placeholder: '状态',
							items: [
								{
									label: '全部',
									value: 0
								},
								{
									label: '已读',
									value: 1
								},
								{
									label: '未读',
									value: 2
								}
							]
						}
					}
				]
			}
		}
	},
	watch: {},
	computed: {},
	async created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	methods: {
		async tableBatchTreated() {
			if (this.selection.length <= 0) {
				return
			}
			var reqData = {
				msg_ids: this.selection.map((v) => v.id),
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				status: 2
			}
			var res = await this.$LotApi.message.read.post(reqData)
			if (res.code === 200) {
				this.$message.success('操作成功')
				this.$refs.table.refresh()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		selectionChange(selection) {
			this.selection = selection
			if (selection.length > 0) {
				this.treatedStatus = true
			} else {
				this.treatedStatus = false
			}
		},
		rowClick(row) {
			this.infoDrawer = true
			this.messageContent = row
			if (row.read_status === 1) return
			this.$LotApi.message.read
				.post({
					tenant_id: tenantId,
					campus_id: campusId,
					msg_ids: [row.id]
				})
				.then((res) => {
					if (res.code === 200) {
						this.$refs.table.refresh()
					}
				})
		}, //搜索
		upsearch() {
			this.params.begin_time = this.params.date ? this.params.date[0] : null
			this.params.end_time = this.params.date ? this.params.date[1] : null
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped>
.roomItem {
	& + & {
		margin-top: 5px;
	}
}
.message-info {
	padding: 0 10px;
}
.message-title {
	margin-bottom: 20px;
	h3 {
		font-size: 20px;
		font-weight: bold;
		margin-bottom: 10px;
	}
	p {
		font-size: 14px;
		color: #999;
	}
}
.message-content {
	font-size: 14px;
	line-height: 25px;
}
.overflowTips {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
</style>
