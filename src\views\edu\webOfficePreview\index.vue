<template>
	<div id="container"></div>
</template>
<script setup>
defineOptions({
	name: 'webOfficePreview'
})
import { useRoute } from 'vue-router'
import { onMounted, getCurrentInstance, reactive } from 'vue'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const WebofficeToken = reactive({
	webInfo: {}
})
onMounted(async () => {
	await getWebofficeToken()
	await insertScriptTag()
	setTimeout(async function () {
		await preview()
	}, 200)
})
// 获取教材文件在线预览凭证
const getWebofficeToken = async () => {
	const res = await globalPropValue.eduMaterials.material.get_weboffice_token.post({
		tenant_id: Number(route.query.tenant_id),
		campus_id: Number(route.query.campus_id),
		material_id: Number(route.query.material_id)
	})
	WebofficeToken.webInfo = res.data
}
const route = useRoute()
const props = defineProps({
	aliplayerSdkPath: {
		type: String,
		default: 'https://g.alicdn.com/IMM/office-js/1.1.19/aliyun-web-office-sdk.min.js'
	}
})
const insertScriptTag = async () => {
	let playerScriptTag = document.getElementById('playerScriptTag')
	// 如果这个tag不存在，则生成相关代码tag以加载代码
	if (playerScriptTag === null) {
		playerScriptTag = document.createElement('script')
		playerScriptTag.type = 'text/javascript'
		playerScriptTag.src = props.aliplayerSdkPath
		playerScriptTag.id = 'playerScriptTag'
		const s = document.getElementsByTagName('head')[0]
		s.appendChild(playerScriptTag)
	}
}
const preview = async () => {
	let mount = document.querySelector('#container')
	let instance = window.aliyun.config({
		mount: mount,
		url: WebofficeToken.webInfo.weboffice_url,
		mode: 'normal'
	})
	instance.setToken({ token: WebofficeToken.webInfo.access_token, timeout: 25 * 60 * 1000 })
	instance.on('fileOpen', function (data) {
		console.log(data)
	})

	instance.on('error', (err) => {
		console.log('发生错误：', err)
	})
}
defineExpose({
	preview,
	insertScriptTag
})
</script>
<style scoped>
#container {
	width: 100%;
	height: 100%;
}
</style>
