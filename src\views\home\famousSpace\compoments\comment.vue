<template>
	<div v-if="onlyShow === false" style="padding-bottom: 10px">
		<el-select v-model="queryParam.audit" placeholder="请选择审核状态" @change="getCommentList">
			<el-option :value="0" label="全部" />
			<el-option :value="1" label="未审核" />
			<el-option :value="2" label="已审核" />
		</el-select>
	</div>
	<div v-loading="loading" element-loading-text="数据加载中...">
		<div v-if="comments.rows && comments.rows.length">
			<childComment :comments="comments.rows" @suc="getCommentList"></childComment>
		</div>
		<el-empty v-else description="暂无评论" />
	</div>
	<div class="page">
		<scPagination
			v-if="comments.total"
			v-model:page="queryParam.page"
			v-model:size="queryParam.pageSize"
			:total="comments.total"
			layout="sizes, prev, pager, next, jumper"
			@pagination="getCommentList"
		></scPagination>
	</div>
</template>

<script>
import childComment from './childComment.vue'
export default {
	name: 'comment',
	inject: ['resourcesData', 'onlyShow'],
	components: { childComment },
	data() {
		return {
			comments: {
				rows: [],
				total: 0
			},
			loading: false,
			queryParam: {
				page: 1,
				pageSize: 10,
				resources_id: this.resourcesData.id,
				tenant_id: this.resourcesData.tenant_id,
				campus_id: this.resourcesData.campus_id,
				audit: 0
			}
		}
	},
	created() {
		this.getCommentList()
	},
	methods: {
		getCommentList() {
			if (this.onlyShow === true) {
				this.queryParam.audit = 2
			}
			this.loading = true
			this.$API.famous.comment.list.get(this.queryParam).then((res) => {
				this.loading = false
				if (res.code === 200) {
					this.comments = res.data

					this.comments.rows.forEach((item) => {
						item.replyPopover = false
						item.auditAction = 0
						item.auditRemark = ''
					})
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
.page {
	height: 50px;
	padding-top: 10px;
}
</style>
