<template>
	<el-form v-if="data" :key="data.key" label-position="top">
		<!-- <el-form-item v-if="data.type !== 'grid'" label="字段标识">
			<el-input v-model="data.model" readonly disabled />
		</el-form-item> -->

		<el-form-item
			v-if="data.type !== 'grid' && data.type !== 'headImage' && data.type !== 'title' && data.type !== 'insert-img' && data.type !== 'seat'"
			label="标题">
			<el-input v-model="data.label" />
		</el-form-item>

		<el-form-item v-if="hasKey('width')" label="组件宽度">
			<el-input v-model="data.options.width" />
		</el-form-item>

		<el-form-item v-if="hasKey('placeholder')" label="占位内容">
			<el-input v-model="data.options.placeholder" />
		</el-form-item>

		<el-form-item v-if="hasKey('showCode')" label="设置显示条件">
			<el-button icon="el-icon-setUp" @click="showCodeVisible">设置显示条件</el-button>
		</el-form-item>

		<el-form-item v-if="
			hasKey('defaultValue') &&
			(data.type === 'input' ||
				data.type === 'password' ||
				data.type === 'textarea' ||
				data.type === 'text' ||
				data.type === 'rate' ||
				data.type === 'switch' ||
				data.type === 'slider')
		" label="默认内容">
			<el-input v-if="data.type === 'input' || data.type === 'password'" v-model="data.options.defaultValue" />
			<el-input v-if="data.type === 'textarea' || data.type === 'text'" v-model="data.options.defaultValue"
				type="textarea" />
			<el-rate v-if="data.type === 'rate'" v-model="data.options.defaultValue" :max="data.options.max"
				:allowHalf="data.options.allowHalf" />
			<el-switch v-if="data.type === 'switch'" v-model="data.options.defaultValue" />
			<template v-if="data.type === 'slider'">
				<el-input-number v-if="!data.options.range" v-model.number="data.options.defaultValue" />
				<template v-if="data.options.range">
					<el-input-number v-model.number="data.options.defaultValue[0]" :max="data.options.max" />
					<el-input-number v-model.number="data.options.defaultValue[1]" :max="data.options.max" />
				</template>
			</template>
		</el-form-item>

		<el-form-item v-if="hasKey('minlength')" label="最小长度">
			<el-input v-model.number="data.options.minlength" />
		</el-form-item>

		<el-form-item v-if="hasKey('maxlength')" label="最大长度">
			<el-input v-model.number="data.options.maxlength" />
		</el-form-item>

		<el-form-item v-if="hasKey('max')" label="最大值">
			<el-input-number v-model.number="data.options.max" />
		</el-form-item>

		<el-form-item v-if="hasKey('min')" label="最小值">
			<el-input-number v-model.number="data.options.min" />
		</el-form-item>

		<el-form-item v-if="hasKey('step')" label="步长">
			<el-input-number v-model.number="data.options.step" :min="0" />
		</el-form-item>

		<el-form-item v-if="hasKey('prefix')" label="前缀">
			<el-input v-model="data.options.prefix" />
		</el-form-item>

		<el-form-item v-if="hasKey('suffix')" label="后缀">
			<el-input v-model="data.options.suffix" />
		</el-form-item>

		<el-form-item v-if="hasKey('prepend')" label="前置标签">
			<el-input v-model="data.options.prepend" />
		</el-form-item>

		<el-form-item v-if="hasKey('append')" label="后置标签">
			<el-input v-model="data.options.append" />
		</el-form-item>

		<el-form-item v-if="hasKey('activeText')" label="选中时的内容">
			<el-input v-model="data.options.activeText" />
		</el-form-item>

		<el-form-item v-if="hasKey('inactiveText')" label="非选中时的内容">
			<el-input v-model="data.options.inactiveText" />
		</el-form-item>

		<el-form-item v-if="hasKey('editable')" label="文本框可输入">
			<el-switch v-model="data.options.editable" />
		</el-form-item>

		<el-form-item v-if="hasKey('range')" label="范围选择">
			<el-switch v-model="data.options.range" @change="handleSliderModeChange" />
		</el-form-item>

		<el-form-item v-if="hasKey('showPassword')" label="是否显示切换按钮">
			<el-switch v-model="data.options.showPassword" />
		</el-form-item>

		<el-form-item v-if="hasKey('showWordLimit')" label="是否显示统计字数">
			<el-tooltip content="显示需设置最大长度">
				<el-switch v-model="data.options.showWordLimit" />
			</el-tooltip>
		</el-form-item>

		<el-form-item v-if="hasKey('autosize')" label="是否自适应内容高度">
			<el-switch v-model="data.options.autosize" />
		</el-form-item>

		<el-form-item v-if="hasKey('rows') && !data.options.autosize" label="行数">
			<el-input-number v-model="data.options.rows" :min="0" />
		</el-form-item>

		<el-form-item v-if="hasKey('allowHalf')" label="是否允许半选">
			<el-switch v-model="data.options.allowHalf" />
		</el-form-item>

		<!-- <el-form-item v-if="hasKey('inline')" label="布局方式">
			<el-radio-group v-model="data.options.inline">
				<el-radio-button :label="true">行内</el-radio-button>
				<el-radio-button :label="false">块级</el-radio-button>
			</el-radio-group>
		</el-form-item> -->

		<el-form-item v-if="hasKey('multiple')" label="是否多选">
			<el-switch v-model="data.options.multiple" @change="handleSelectModeChange" />
		</el-form-item>

		<el-form-item v-if="hasKey('multipleLimit') && data.options.multiple" label="最多选择项（0为不限制）">
			<el-input-number v-model="data.options.multipleLimit" />
		</el-form-item>

		<el-form-item v-if="hasKey('filterable')" label="是否可搜索">
			<el-switch v-model="data.options.filterable" />
		</el-form-item>

		<el-form-item v-if="hasKey('showLabel')" label="是否显示标签">
			<el-switch v-model="data.options.showLabel" />
		</el-form-item>

		<el-form-item v-if="hasKey('options')" label="选项">
			<template v-if="data.options.remote === false">
				<template v-if="data.type === 'radio' || (data.type === 'select' && !data.options.multiple)">
					<el-radio-group v-model="data.options.defaultValue" style="margin-top: 8px">
						<Draggable tag="ul" item-key="index" ghostClass="ghost" handle=".drag-item"
							:group="{ name: 'options' }" :list="data.options.options">
							<template #item="{ element, index }">
								<div style="display: flex; align-items: center; margin-bottom: 5px">
									<el-radio :label="element.value" style="margin-right: 0px; margin-bottom: 0">
										<el-input v-model="element.value" :style="{
											width: data.options.showLabel ? '90px' : '180px'
										}" />
										<el-input v-if="data.options.showLabel" v-model="element.label" :style="{
											width: '90px'
										}" />
									</el-radio>
									<cusSvgIcon style="margin: 0 5px; cursor: move" iconClass="item"
										className="drag-item" />
									<el-button type="primary" circle @click="handleOptionsRemove(index)">
										<cusSvgIcon iconClass="delete" />
									</el-button>
								</div>
							</template>
						</Draggable>
					</el-radio-group>
				</template>

				<template v-if="data.type === 'checkbox' || (data.type === 'select' && data.options.multiple)">
					<el-checkbox-group v-model="data.options.defaultValue" style="margin-top: 8px">
						<Draggable tag="ul" item-key="index" ghostClass="ghost" handle=".drag-item"
							:group="{ name: 'options' }" :list="data.options.options">
							<template #item="{ element, index }">
								<li style="display: flex; align-items: center; margin-bottom: 5px">
									<el-checkbox :label="element.value" style="margin-right: 0px; margin-bottom: 0">
										<el-input v-model="element.value" :style="{
											width: data.options.showLabel ? '90px' : '180px'
										}" />
										<el-input v-if="data.options.showLabel" v-model="element.label"
											:style="{ width: '90px' }" />
									</el-checkbox>
									<cusSvgIcon style="margin: 0 5px; cursor: move" iconClass="item"
										className="drag-item" />
									<el-button type="primary" circle @click="handleOptionsRemove(index)">
										<cusSvgIcon iconClass="delete" />
									</el-button>
								</li>
							</template>
						</Draggable>
					</el-checkbox-group>
				</template>

				<div style="margin-top: 5px">
					<el-button type="text" @click="handleInsertOption">添加选项</el-button>
				</div>
			</template>
		</el-form-item>
		<!-- 头图 -->
		<template v-if="data.type === 'headImage'">
			<el-form-item label="问卷头图">
				<el-radio-group v-model="data.options.type" @change="handleChangeHeadImageType">
					<el-radio-button label="upload">上传图片</el-radio-button>
					<el-radio-button label="noHeadImage">不使用头图</el-radio-button>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="data.options.type === 'upload'" label="上传图片">
				<sc-upload v-model="data.options.defaultValue" title="上传头图" fileTypeTag="form"></sc-upload>
			</el-form-item>
			<el-form-item v-if="data.options.type === 'upload'" label="选择头图">
				<div class="headImageItem" v-for="(item, index) in 10" :key="index" @click="handlerSelectImage(item)">
					<el-image lazy
						:src="`http://educdn.xjzredu.cn/scms/system/front/pc/survey/headImage/${item < 10 ? '1000' + item : '100' + item}.png`"
						fit="cover"></el-image>
				</div>
			</el-form-item>
		</template>
		<template v-if="data.type === 'insert-img'">
			<el-form-item label="上传图片">
				<sc-upload v-model="data.options.defaultValue" title="插入图片" fileTypeTag="form">
					<template #error>

					</template>
				</sc-upload>
			</el-form-item>
		</template>

		<template v-if="data.type === 'time'">
			<el-form-item label="默认值">
				<el-time-picker v-model="data.options.defaultValue" style="width: 100%" :format="data.options.format"
					:value-format="data.options.valueFormat" :placeholder="data.options.placeholder" />
			</el-form-item>
		</template>
		<template v-if="data.type === 'timerange'">
			<el-form-item label="时间范围分隔符">
				<el-input v-model="data.options.rangeSeparator" />
			</el-form-item>
			<el-form-item label="开始时间的占位内容">
				<el-input v-model="data.options.startPlaceholder" />
			</el-form-item>
			<el-form-item label="结束时间的占位内容">
				<el-input v-model="data.options.endPlaceholder" />
			</el-form-item>
		</template>

		<template v-if="data.type === 'daterange'">
			<el-form-item label="类型">
				<el-radio-group v-model="data.options.type">
					<el-radio label="daterange">日期</el-radio>
					<el-radio label="datetimerange">日期时间</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="格式">
				<el-select v-model="data.options.format">
					<el-option v-if="data.options.type === 'daterange'" label="年-月" value="YYYY-MM" />
					<el-option v-if="data.options.type === 'daterange'" label="年-月-日" value="YYYY-MM-DD" />
					<el-option v-if="data.options.type === 'datetimerange'" label="年-月-日 时-分"
						value="YYYY-MM-DD HH:mm" />
					<el-option v-if="data.options.type === 'datetimerange'" label="年-月-日 时-分-秒"
						value="YYYY-MM-DD HH:mm:ss" />
				</el-select>
			</el-form-item>
			<el-form-item label="">
				<el-input v-model="data.options.format" />
			</el-form-item>
			<el-form-item label="日期范围分隔符">
				<el-input v-model="data.options.rangeSeparator" />
			</el-form-item>
			<el-form-item label="开始日期的占位内容">
				<el-input v-model="data.options.startPlaceholder" />
			</el-form-item>
			<el-form-item label="结束日期的占位内容">
				<el-input v-model="data.options.endPlaceholder" />
			</el-form-item>
		</template>

		<template v-if="data.type === 'date'">
			<el-form-item label="类型">
				<el-radio-group v-model="data.options.type">
					<el-radio label="year">年</el-radio>
					<el-radio label="month">月</el-radio>
					<el-radio label="week">周</el-radio>
					<el-radio label="date">日期</el-radio>
					<el-radio label="datetime">日期时间</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="格式">
				<el-select v-model="data.options.format">
					<el-option v-if="data.options.type === 'year'" label="年" value="YYYY" />
					<el-option v-if="data.options.type === 'month'" label="月" value="MMMM" />
					<el-option v-if="data.options.type === 'week'" label="周" value="[Week] ww" />
					<el-option v-if="data.options.type === 'date'" label="年-月" value="YYYY-MM" />
					<el-option v-if="data.options.type === 'date'" label="年-月-日" value="YYYY-MM-DD" />
					<el-option v-if="data.options.type === 'datetime'" label="年-月-日 时-分" value="YYYY-MM-DD HH:mm" />
					<el-option v-if="data.options.type === 'datetime'" label="年-月-日 时-分-秒"
						value="YYYY-MM-DD HH:mm:ss" />
				</el-select>
			</el-form-item>
		</template>

		<template v-if="data.type === 'time' || data.type === 'date'">
			<el-form-item label="">
				<el-input v-model="data.options.format" />
			</el-form-item>
		</template>

		<template v-if="data.type === 'date'">
			<el-form-item label="默认值">
				<el-date-picker v-model="data.options.defaultValue" style="width: 100%" :format="data.options.format"
					:value-format="data.options.valueFormat" :placeholder="data.options.placeholder" />
			</el-form-item>
		</template>

		<template v-if="data.type === 'divider'">
			<el-form-item label="分割线类型">
				<el-radio-group v-model="data.options.direction">
					<el-radio-button label="horizontal">水平</el-radio-button>
					<el-radio-button label="vertical">垂直</el-radio-button>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="分割线样式">
				<el-radio-group v-model="data.options.borderStyle">
					<el-radio-button label="solid">实线</el-radio-button>
					<el-radio-button label="dotted">圆点</el-radio-button>
					<el-radio-button label="dashed">方形虚线</el-radio-button>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="自定义文本内容">
				<el-input v-model="data.options.content" />
			</el-form-item>
			<el-form-item label="自定义分隔线内容的位置">
				<el-radio-group v-model="data.options.contentPosition">
					<el-radio-button label="left">左</el-radio-button>
					<el-radio-button label="center">中</el-radio-button>
					<el-radio-button label="right">右</el-radio-button>
				</el-radio-group>
			</el-form-item>
		</template>

		<template v-if="data.type === 'img-upload' || data.type === 'file-upload'">
			<!-- <el-form-item label="模式"  v-if="data.type === 'img-upload'">
                <el-radio-group v-model="data.options.listType">
                    <el-radio-button label="text">text</el-radio-button>
                    <el-radio-button label="picture">picture</el-radio-button>
                    <el-radio-button label="picture-card">picture-card</el-radio-button>
                </el-radio-group>
            </el-form-item> -->

			<el-form-item label="文件参数名">
				<el-input v-model="data.options.name" readonly />
			</el-form-item>

			<!-- <el-form-item label="上传地址">
                <el-input v-model="data.options.action" />
            </el-form-item> -->

			<!-- <el-form-item label="接受上传的文件类型(多个使用 , 隔开)">
                <el-input v-model="data.options.accept" />
            </el-form-item> -->

			<el-form-item label="最大上传数量">
				<el-input-number v-model.number="data.options.limit" :min="1" />
			</el-form-item>
		</template>

		<el-form-item v-if="data.type === 'cascader'" label="远端数据">
			<el-space direction="vertical" alignment="start">
				<el-input v-model="data.options.remoteFunc">
					<template #prepend> 远端方法 </template>
				</el-input>
				<el-input v-model="data.options.props.label">
					<template #prepend> 标签 </template>
				</el-input>
				<el-input v-model="data.options.props.value">
					<template #prepend> 值 </template>
				</el-input>
				<el-input v-model="data.options.props.children">
					<template #prepend> 子选项 </template>
				</el-input>
			</el-space>
		</el-form-item>

		<template v-if="data.type === 'grid'">
			<el-form-item label="栅格间隔">
				<el-input-number v-model.number="data.options.gutter" :min="0" />
			</el-form-item>

			<el-form-item label="列配置项">
				<Draggable tag="ul" item-key="index" ghostClass="ghost" handle=".drag-item" :group="{ name: 'options' }"
					:list="data.columns">
					<template #item="{ element, index }">
						<li style="margin-bottom: 5px">
							<cusSvgIcon iconClass="item" className="drag-item" />
							<el-input-number v-model.number="element.span" placeholder="栅格值" :min="0" :max="24" />
							<el-button type="primary" circle style="margin-left: 5px"
								@click="handleOptionsRemove(index)">
								<cusSvgIcon iconClass="delete" />
							</el-button>
						</li>
					</template>
				</Draggable>

				<div>
					<el-button type="text" @click="handleInsertColumn"> 添加列 </el-button>
				</div>
			</el-form-item>

			<el-form-item label="垂直对齐方式">
				<el-radio-group v-model="data.options.align">
					<el-radio-button label="top">顶部对齐</el-radio-button>
					<el-radio-button label="middle">居中对齐</el-radio-button>
					<el-radio-button label="bottom">底部对齐</el-radio-button>
				</el-radio-group>
			</el-form-item>

			<el-form-item label="水平排列方式">
				<el-select v-model="data.options.justify">
					<el-option value="start" label="左对齐" />
					<el-option value="end" label="右对齐" />
					<el-option value="center" label="居中" />
					<el-option value="space-around" label="两侧间隔相等" />
					<el-option value="space-between" label="两端对齐" />
				</el-select>
			</el-form-item>
		</template>

		<template v-if="data.type !== 'grid'">
			<el-form-item v-if="hasKey('rules') || hasKey('readonly') || hasKey('disabled') || hasKey('allowClear')"
				label="操作属性">
				<el-checkbox v-if="hasKey('rules')" v-model="data.options.rules.required">必填</el-checkbox>
				<el-checkbox v-if="hasKey('readonly')" v-model="data.options.readonly">只读</el-checkbox>
				<el-checkbox v-if="hasKey('disabled')" v-model="data.options.disabled">禁用</el-checkbox>
				<el-checkbox v-if="hasKey('clearable')" v-model="data.options.clearable">清除</el-checkbox>
			</el-form-item>

			<template v-if="hasKey('rules')">
				<h4>验证规则</h4>

				<!-- <el-form-item label="触发时机">
                    <el-radio-group v-model="data.options.rules.trigger">
                        <el-radio-button label="blur">Blur</el-radio-button>
                        <el-radio-button label="change">Change</el-radio-button>
                    </el-radio-group>
                </el-form-item> -->

				<el-form-item v-if="data.type === 'input'" label="字段长度">
					<el-input v-model.number="data.options.rules.len" />
				</el-form-item>

				<el-form-item v-if="data.type === 'input' || data.type === 'textarea'" label="最大长度">
					<el-input v-model.number="data.options.rules.max" />
				</el-form-item>

				<el-form-item v-if="data.type === 'input' || data.type === 'textarea'" label="最小长度">
					<el-input v-model.number="data.options.rules.min" />
				</el-form-item>

				<el-form-item label="触发事件">
					<el-radio-group v-model="data.options.rules.trigger">
						<el-radio-button value="blur">blur</el-radio-button>
						<el-radio-button value="change">change</el-radio-button>
					</el-radio-group>
				</el-form-item>

				<el-form-item label="校验文案">
					<el-input v-model="data.options.rules.message" />
				</el-form-item>

				<el-form-item v-if="data.type === 'input'" label="正则表达式">
					<!-- <el-input v-model="data.options.rules.pattern" /> -->
					<el-select v-model="data.options.rules.pattern">
						<el-option value="/^1[3456789]\d{9}$/" label="手机号">手机号</el-option>
						<el-option value="/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/"
							label="邮箱">邮箱</el-option>
						<el-option value="/\d{17}[\d|x]|\d{15}/" label="身份证">身份证</el-option>
						<el-option value="/\d{6}/" label="邮编">邮编</el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="校验类型">
					<el-select v-model="data.options.rules.type">
						<el-option value="string">字符串</el-option>
						<el-option value="number">数字</el-option>
						<el-option value="boolean">布尔值</el-option>
						<el-option value="method">方法</el-option>
						<el-option value="regexp">正则表达式</el-option>
						<el-option value="integer">整数</el-option>
						<el-option value="float">浮点数</el-option>
						<el-option value="array">数组</el-option>
						<el-option value="object">对象</el-option>
						<el-option value="enum">枚举</el-option>
						<el-option value="date">日期</el-option>
						<el-option value="url">URL地址</el-option>
						<el-option value="hex">十六进制</el-option>
						<el-option value="postcode">邮编</el-option>
						<el-option value="phone">手机号</el-option>
						<el-option value="idcard">身份证</el-option>
						<el-option value="email">邮箱地址</el-option>
						<el-option value="any">任意类型</el-option>
					</el-select>
				</el-form-item>
			</template>
		</template>
	</el-form>
	<div class="form-empty" v-else>请先选择组件再设置字段属性</div>
	<el-dialog v-model="dialogShowCodeVisible" title="设置显示条件" width="500">
		<el-form :model="codeForm" label-width="auto">
			<el-form-item label="字段">
				<el-select v-model="codeForm.model" placeholder="请选择字段" @change="changeKey">
					<el-option v-for="item in codeList" :key="item.key" :label="item.label" :value="item.model">
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="选项"
				v-if="codeFormType == 'radio' || codeFormType == 'checkbox' || codeFormType == 'select'">
				<el-select v-model="codeForm.val" placeholder="请选择内容" :multiple="codeFormType == 'checkbox'">
					<el-option v-for="item in valList" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="内容"
				v-if="codeFormType == 'input' || codeFormType == 'textarea' || codeFormType == 'text' || codeFormType == 'number'">
				<el-input v-model="codeForm.val"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="dialogShowCodeVisible = false">取消</el-button>
				<el-button type="primary" @click="saveShowCode">
					确定
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import Draggable from 'vuedraggable'
import CusEditor from '@/components/custom/cusEditor.vue'

const props = defineProps(['select', 'list'])
const emit = defineEmits(['update:select'])

const data = ref(props.select)

watch(
	() => props.select,
	(val) => {
		data.value = val
		console.log('select', val)
	}
)

watch(
	data,
	(val) => {
		emit('update:select', val)
	},
	{ deep: true }
)

watch(
	() => props.list,
	(val) => {
		console.log('list', val)
	}
)

const hasKey = (key) => {
	if (data.value.options) {
		return Object.keys(data.value.options).includes(key)
	} else {
		return false
	}
}

// 显示条件
const codeForm = ref({})
const dialogShowCodeVisible = ref(false)
const codeList = ref([])
const codeFormType = ref('')
const valList = ref([])
const showCodeVisible = () => {
	codeList.value = props.list.filter((item) => item.type != 'headImage' && item.type != 'title' && item.type != 'seat' && item.type != data.value.type)
	setTimeout(() => {
		codeForm.value = {
			model: data.value.options.showCode?.model,
			val: data.value.options.showCode?.val
		}
		console.log('props.list', codeList.value, codeForm.value)
		dialogShowCodeVisible.value = true
	}, 100)
}
const changeKey = (model) => {
	codeList.value.forEach((item) => {
		if (item.model == model) {
			codeFormType.value = item.type
			valList.value = item.options.options
			console.log('item', codeFormType.value, valList.value)
		}
	})
}

const saveShowCode = () => {
	console.log('codeForm.value', data.value)
	data.value.options.showCode = codeForm.value
	dialogShowCodeVisible.value = false
}

const handleInsertColumn = () => {
	data.value.columns.push({ span: 0, list: [] })
}

const handleInsertOption = () => {
	const index = data.value.options.options.length + 1
	data.value.options.options.push({
		label: `Option ${index}`,
		value: `Option ${index}`
	})
}

const handleOptionsRemove = (index) => {
	if (data.value.type === 'grid') {
		data.value.columns.splice(index, 1)
	} else {
		data.value.options.options.splice(index, 1)
	}
}

const handleSliderModeChange = (checked) => {
	if (checked) {
		data.value.options.defaultValue = [10, 90]
	} else {
		data.value.options.defaultValue = 0
	}
}

const handleSelectModeChange = (val) => {
	if (data.value.type === 'img-upload' || data.value.type === 'file-upload') {
		return
	}

	if (val) {
		if (data.value.options.defaultValue) {
			if (!(data.value.options.defaultValue instanceof Array)) {
				data.value.options.defaultValue = [data.value.options.defaultValue]
			}
		} else {
			data.value.options.defaultValue = []
		}
	} else {
		data.value.options.defaultValue = data.value.options.defaultValue.length ? data.value.options.defaultValue[0] : null
	}
}

const handlerSelectImage = (val) => {
	if (val) {
		data.value.options.defaultValue = `http://educdn.xjzredu.cn/scms/system/front/pc/survey/headImage/${val < 10 ? '1000' + val : '100' + val}.png`
	} else {
		data.value.options.defaultValue = ''
	}
}

const handleChangeHeadImageType = (val) => {
	if (val == 'noHeadImage') {
		data.value.options.defaultValue = ''
	}
}

</script>

<style lang="scss" scoped>
$primary-color: #2745b2;

.form-empty {
	text-align: center;
	line-height: 7;
	font-size: 20px;
	margin: auto;
	color: #ccc;
}

.headImageItem {
	cursor: pointer;
	display: flex;
	margin-bottom: 5px;

	&:last-child {
		margin-bottom: 0;
	}

	&:hover {
		outline: 1px solid $primary-color;
	}
}
</style>
