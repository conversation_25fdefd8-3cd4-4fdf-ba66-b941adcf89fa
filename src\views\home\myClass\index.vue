<template>
	<el-container>
		<el-header>
			<div class="search-panel">
				<el-select
					v-if="CampusManagementList.length > 1"
					v-model="params.campus_id"
					placeholder="校区"
					filterable
					style="margin-right: 15px"
					@change="campusChange"
				>
					<el-option
						v-for="item in CampusManagementList"
						:key="item.code"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
				<cusSelectSemester
					v-model="params.semester_id"
					:params="params"
					clearable
					placeholder="请选择学期"
					showDefaultValue
					style="margin-right: 15px"
					width="200px"
					@semesterChange="semesterChange"
				/>
			</div>
		</el-header>
		<el-main>
			<div v-if="classList?.length" class="class-list">
				<div v-for="item in classList" :key="item.id" class="class-item" @click="classItemClick(item)">
					<div class="class-title">
						<cusSvgIcon iconClass="class"></cusSvgIcon>
						{{ item.grade_name + item.class_name }}
					</div>
					<div class="class-item-info">
						<div>
							<el-tag v-if="item.is_head === 1" type="danger">班主任</el-tag>
							<el-tag v-if="item.course_name" type="danger">{{ item.course_name }}</el-tag>
						</div>
						<div class="class-item-info-item">
							<span>{{ item.student_num }}</span
							>人
						</div>
					</div>
				</div>
			</div>
			<el-empty v-else :image-size="200" />
		</el-main>
	</el-container>
</template>

<script>
import cusSelectSemester from '@/components/custom/cusSelectSemester'
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, studentVacateTypeMap } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: ''
	}
}
export default {
	components: {
		cusSelectSemester
	},
	data() {
		return {
			CampusManagementList: campusInfo,
			params: defaultParams(),
			classList: []
		}
	},
	created() {
		this.$nextTick(() => {
			this.getClassList()
		})
	},
	methods: {
		campusChange(val) {
			this.params.campus_id = val
			this.getClassList()
		},
		semesterChange(val) {
			this.params.semester_id = val
			this.getClassList()
		},
		classItemClick(item) {
			this.$router.push({
				name: 'classInfo',
				query: {
					id: item.class_id
				}
			})
		},
		getClassList() {
			this.$API.class.class.get(this.params).then((res) => {
				if (res.code == 200) {
					this.classList = res.data
				} else {
					ElMessage.error(res.message)
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.class-list {
	display: flex;
	flex-wrap: wrap;

	.class-item {
		width: 24%;
		border: 1px solid var(--el-border-color-light);
		margin-right: 10px;
		margin-bottom: 20px;
		border-radius: 5px;
		padding: 25px;
		cursor: pointer;

		&:hover {
			box-shadow: 0 0 9px 2px var(--el-border-color-light);
		}

		.class-title {
			font-size: 16px;
			font-weight: bold;
			display: flex;
			align-items: center;
			margin-bottom: 20px;

			.svg-icon {
				font-size: 48px;
				margin-right: 15px;
			}
		}

		.class-item-info {
			padding: 30px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: var(--el-bg-color-overlay);
			border-radius: 5px;

			.class-item-info-item {
				font-size: 14px;
				font-style: italic;

				span {
					font-size: 20px;
				}
			}
		}
	}
}
</style>
