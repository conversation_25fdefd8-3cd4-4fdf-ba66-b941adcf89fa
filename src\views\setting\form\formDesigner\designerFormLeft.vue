<template>
	<componentsGroup title="基础字段" :fields="basicFields" :list="basicComponents"></componentsGroup>
	<componentsGroup title="高级字段" :fields="advanceFields" :list="advanceComponents"></componentsGroup>
	<!-- <componentsGroup title="布局字段" :fields="layoutFields" :list="layoutComponents"></componentsGroup> -->
	<componentsGroup title="系统字段" :fields="systemFields" :list="systemComponents"></componentsGroup>
</template>

<script setup>
import { ref } from 'vue'
import componentsGroup from './component/ComponentGroup.vue'
import { basicComponents, advanceComponents, layoutComponents, systemComponents } from '@/utils/components'
const basicFields = ref([
	'input',
	'password',
	'textarea',
	'number',
	'radio',
	'checkbox',
	'time',
	'timerange',
	'date',
	'daterange',
	'rate',
	'select',
	'switch',
	'slider',
	'text'
])
const advanceFields = ref(['img-upload', 'file-upload', 'divider', 'richtext-editor', 'cascader'])
const layoutFields = ref(['grid'])
const systemFields = ref([
	'select-asset',
	'select-consumables',
	'select-department',
	'select-tree',
	'select-room',
	'select-student',
	'select-teacher',
	'select-field',
	'select-campus',
	'select-class',
	'select-semester',
	'select-academic',
	'select-grade'
])
</script>
