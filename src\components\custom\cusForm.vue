<!--
 * @Descripttion: 动态表单渲染器
 * @version: 1.0
 * @Author: sakuya
 * @Date: 2021年9月22日09:26:25
 * @LastEditors: Xujianchen
 * @LastEditTime: 2023-03-19 11:45:37
-->

<template>
	<el-skeleton v-if="renderLoading || (Object.keys(form).length == 0 && !inline)" animated />
	<el-skeleton v-if="renderLoading || (Object.keys(form).length == 0 && inline)" animated>
		<template #template>
			<div style="display: flex; align-items: center; justify-items: space-between">
				<el-skeleton-item variant="text" style="margin-right: 16px" />
				<el-skeleton-item variant="text" style="width: 30%" />
			</div>
		</template>
	</el-skeleton>
	<el-form v-else ref="form" v-loading="loading" :model="form" :label-width="config.labelWidth"
		:label-position="config.labelPosition" element-loading-text="Loading..." :inline="inline"
		:class="{ 'cus-form-inline': inline }" :disabled="mode == 'show'">
		<el-row :gutter="15">
			<slot name="ahead"></slot>
			<template v-for="(item, index) in config.formItems" :key="index">
				<el-col v-if="!hideHandle(item)" :span="inline ? -1 : item.span || 24">
					<sc-title v-if="item.component === 'title'" :title="item.label"></sc-title>
					<el-form-item v-else :prop="item.name" :rules="rulesHandle(item)">
						<template v-if="item.label != null" #label>
							{{ item.label }}
							<el-tooltip v-if="item.tips" :content="item.tips">
								<el-icon><el-icon-question-filled /></el-icon>
							</el-tooltip>
						</template>
						<!-- input -->
						<template v-if="item.component === 'input'">
							<el-input v-model="form[item.name]" :placeholder="item.options.placeholder" clearable
								:maxlength="item.options.maxlength" :disabled="item.options.disabled"
								:readonly="item.options.readonly" show-word-limit></el-input>
						</template>
						<!-- textarea -->
						<template v-else-if="item.component === 'textarea'">
							<el-input v-model="form[item.name]" :placeholder="item.options.placeholder" clearable
								type="textarea" :rows="item.options.rows || 2" :maxlength="item.options.maxlength"
								show-word-limit></el-input>
						</template>
						<!-- checkbox -->
						<template v-else-if="item.component === 'checkbox'">
							<template v-if="item.name">
								<el-checkbox v-for="(_item, _index) in item.options.items" :key="_index"
									v-model="form[item.name][_item.name]" :label="_item.label"></el-checkbox>
							</template>
							<template v-else>
								<el-checkbox v-for="(_item, _index) in item.options.items" :key="_index"
									v-model="form[_item.name]" :label="_item.label"></el-checkbox>
							</template>
						</template>
						<!-- checkboxGroup -->
						<template v-else-if="item.component === 'checkboxGroup'">
							<el-checkbox-group v-model="form[item.name]">
								<el-checkbox v-for="_item in item.options.items" :key="_item.value"
									:label="_item.value">{{
										_item.label
									}}</el-checkbox>
							</el-checkbox-group>
						</template>
						<!-- upload -->
						<template v-else-if="item.component === 'upload'">
							<el-col v-for="(_item, _index) in item.options.items" :key="_index">
								<el-form-item :prop="_item.name">
									<sc-upload v-model="form[_item.name]" :title="_item.label"
										:fileTypeTag="_item.type"></sc-upload>
								</el-form-item>
							</el-col>
						</template>
						<!-- uploadMultiple -->
						<template v-else-if="item.component === 'uploadMultiple'">
							<el-col v-for="(_item, _index) in item.options.items" :key="_index">
								<el-form-item :prop="_item.name">
									<uploadMultiple v-model="form[_item.name]" :fileTypeTag="_item.type">
									</uploadMultiple>
								</el-form-item>
							</el-col>
						</template>

						<template v-else-if="item.component === 'uploadFiles'">
							<el-col v-for="(_item, _index) in item.options.items" :key="_index">
								<el-form-item :prop="_item.name">
									<uploadFile v-model="form[_item.name]" :fileTypeTag="_item.type"
										:limit="item.limit"></uploadFile>
								</el-form-item>
							</el-col>
						</template>
						<!-- switch -->
						<template v-else-if="item.component === 'switch'">
							<!-- el-switch 会默认触发一下 导致监听的form 会刷新从而影响编辑操作的数据 因此取消 v-model 用:value -->
							<el-switch :value="form[item.name]" :active-value="item.options.activeValue"
								:inactive-value="item.options.inactiveValue" @click="handleSwitchChange(item)" />
						</template>
						<!-- select -->
						<template v-else-if="item.component == 'select'">
							<el-select v-model="form[item.name]" :multiple="item.options.multiple"
								:collapse-tags="item.options.collapseTags" :placeholder="item.options.placeholder"
								:clearable="!item.options.noClearable"
								:collapse-tags-tooltip="item.options.collapseTagsTooltip" filterable
								:style="{ width: item.options.width || '100%' }">
								<el-option v-for="option in item.options.items" :key="option.value"
									:label="option.label" :value="option.value"></el-option>
							</el-select>
						</template>
						<!-- cascader -->
						<template v-else-if="item.component === 'cascader'">
							<el-cascader v-model="form[item.name]" :show-all-levels="item.options.all"
								:options="item.options.items" :collapse-tags="item.options.collapseTags"
								:collapse-tags-tooltip="item.options.collapseTagsTooltip" clearable
								:placeholder="item.options.placeholder || '请选择'" :props="item.options.prop"
								:style="{ width: item.options.width || 'auto' }"></el-cascader>
						</template>
						<!-- date -->
						<template v-else-if="item.component === 'date'">
							<el-date-picker v-model="form[item.name]" :type="item.options.type"
								:style="{ width: item.options.width }" :shortcuts="item.options.shortcuts"
								:default-time="item.options.defaultTime" :value-format="item.options.valueFormat"
								:placeholder="item.options.placeholder || '请选择'"
								:start-placeholder="item.options.startPlaceholder || '开始日期'"
								:end-placeholder="item.options.endPlaceholder || '结束日期'"></el-date-picker>
						</template>
						<!-- number -->
						<template v-else-if="item.component === 'number'">
							<el-input-number v-model="form[item.name]"
								:controls-position="item.options.controlsPosition ? '' : 'right'"
								:precision="item.options.precision || 0"
								:disabled="item.options.disabled"></el-input-number>
						</template>
						<template v-else-if="item.component === 'radio'">
							<el-radio-group v-model="form[item.name]" :disabled="item.options.disabled">
								<el-radio v-for="_item in item.options.items" :value="_item.value">{{ _item.label
									}}</el-radio>
							</el-radio-group>
						</template>
						<!-- radioGroup -->
						<template v-else-if="item.component === 'radioGroup'">
							<el-radio-group v-model="form[item.name]" :disabled="item.options.disabled">
								<el-radio-button v-for="_item in item.options.items" :key="_item.value"
									:value="_item.value">{{
										_item.label
									}}</el-radio-button>
							</el-radio-group>
						</template>
						<!-- icon -->
						<template v-else-if="item.component === 'icon'">
							<sc-icon-select v-model="form[item.name]" clearable></sc-icon-select>
						</template>
						<!-- color -->
						<template v-else-if="item.component === 'color'">
							<el-color-picker v-model="form[item.name]" />
						</template>
						<!-- rate -->
						<template v-else-if="item.component === 'rate'">
							<!-- 该组件会默认触发一次，导致监听的form 会刷新从而影响编辑操作的数据 因此绑定 options.temp -->
							<el-rate :ref="item.name" v-model="item.options.temp" style="margin-top: 6px"
								@change="handleRateChange($event, item)"></el-rate>
						</template>
						<!-- slider -->
						<template v-else-if="item.component === 'slider'">
							<el-slider v-model="form[item.name]" :marks="item.options.marks"></el-slider>
						</template>
						<!-- tableselect -->
						<template v-else-if="item.component === 'tableselect'">
							<tableselect-render v-model="form[item.name]" :item="item"></tableselect-render>
						</template>
						<!-- editor -->
						<template v-else-if="item.component === 'editor'">
							<!-- <sc-editor v-model="form[item.name]" placeholder="请输入" :height="400"></sc-editor> -->
							<sc-editor v-model="form[item.name]" :placeholder="item.options.placeholder"
								:style="{ width: item.options.width }" :mode="item.options.mode"></sc-editor>
						</template>

						<!-- 场室选择 -->
						<template v-else-if="item.component === 'cusSelectField'">
							<cusSelectField v-model="form[item.name]" :multiple="item.multiple || false"
								:collapseTags="item.collapseTags || false" :width="item.width || '100%'"
								:disabled="item.disabled || false"></cusSelectField>
						</template>

						<!-- 教职工 -->
						<template v-else-if="item.component === 'cusSelectTeacher'">
							<cusSelectTeacher v-model="form[item.name]" :multiple="item.multiple || false"
								:collapseTags="item.collapseTags || false" :width="item.width || '100%'"
								:disabled="item.disabled || false"></cusSelectTeacher>
						</template>

						<!-- 学生 -->
						<template v-else-if="item.component === 'cusSelectStudent'">
							<cusSelectStudent v-model="form[item.name]" :width="item.width || '100%'"
								:multiple="item.multiple || false" :disabled="item.disabled || false">
							</cusSelectStudent>
						</template>
						<template v-else-if="item.component === 'cusSelectModel'">
							<cusSelectModel v-model="form[item.name]" :type="item.type" :multiple="item.multiple"
								:multipleLimit="item.options.multipleLimit" :placeholder="item.options.placeholder"
								:clearable="item.options.clearable" :disabled="item.options.disabled"
								:width="item.options.width"></cusSelectModel>
						</template>
						<template v-else-if="item.component === 'cusSelectConsumables'">
							<cusSelectConsumables v-model="form[item.name]" :width="item.width || '100%'"
								:multiple="item.multiple || false" :disabled="item.disabled || false">
							</cusSelectConsumables>
						</template>
						<!-- 树形选择 -->
						<template v-else-if="item.component === 'cusSelectTree'">
							<cusSelectTree v-model="form[item.name]" :title="item.options.title" :apiObj="{
								tree: item.options.tree,
								title: item.options.title
							}" :disabled="item.options.disabled" :multiple="item.options.multiple || false"></cusSelectTree>
						</template>
						<!-- 日期 -->
						<template v-else-if="item.component === 'cusDate'">
							<cusDate v-model="form[item.name]" :options="item.options"></cusDate>
						</template>
						<!-- noComponent -->
						<template v-else>
							<el-tag type="danger">[{{ item.component }}] Component not found</el-tag>
						</template>
						<div v-if="item.message" class="el-form-item-msg">{{ item.message }}</div>

						<template v-if="item.options && item.options.add">
							<el-button type="primary" @click="itemClick(item)">新增</el-button>
						</template>
					</el-form-item>
				</el-col>
			</template>
			<slot name="customItem"> </slot>
		</el-row>
	</el-form>
</template>

<script>
import http from '@/utils/request'

import { defineAsyncComponent, readonly } from 'vue'
const tableselectRender = defineAsyncComponent(() => import('@/components/scForm/items/tableselect'))
// const scEditor = defineAsyncComponent(() => import('@/components/scEditor'))
import uploadMultiple from '@/components/scUpload/multiple.vue'
import uploadFile from '@/components/scUpload/file.vue'
import scEditor from '@/components/custom/cusEditor.vue'
import cusSelectModel from '@/components/custom/cusSelectInfo.vue'
import scIconSelect from '@/components/scIconSelect'

export default {
	emits: ['update:modelValue', 'itemClick'],
	props: {
		modelValue: { type: Object, default: () => { } },
		config: { type: Object, default: () => { } },
		loading: { type: Boolean, default: false },
		inline: { type: Boolean, default: false },
		mode: { type: String, default: '' }
	},
	components: {
		tableselectRender,
		scEditor,
		uploadMultiple,
		uploadFile,
		scIconSelect,
		cusSelectModel
	},
	data() {
		return {
			form: {},
			renderLoading: false
		}
	},
	watch: {
		modelValue() {
			if (this.hasConfig) {
				this.deepMerge(this.form, this.modelValue)
			}
		},
		config(val) {
			console.log(val)
			this.render()
		},
		form: {
			handler(val) {
				this.$emit('update:modelValue', val)
			},
			deep: true
		}
	},
	computed: {
		hasConfig() {
			return Object.keys(this.config).length > 0
		},
		hasValue() {
			return Object.keys(this.modelValue).length > 0
		}
	},
	created() { },
	mounted() {
		if (this.hasConfig) {
			this.render()
		}
	},
	methods: {
		itemClick(row) {
			this.$emit('itemClick', row)
		},

		handleSwitchChange(item) {
			if (this.form[item.name] === item.options.activeValue) {
				this.form[item.name] = item.options.inactiveValue
			} else {
				this.form[item.name] = item.options.activeValue
			}
		},
		handleRateChange(e, item) {
			// this.$nextTick(() => {
			// 	this.$refs[item.name][0].setCurrentValue(e)
			// }) 不生效
			this.form[item.name] = e
		},
		//构建form对象
		render() {
			this.config.formItems.forEach((item) => {
				if (item.component === 'checkbox') {
					if (item.name) {
						const value = {}
						item.options.items.forEach((option) => {
							value[option.name] = option.value
						})
						this.form[item.name] = value
					} else {
						item.options.items.forEach((option) => {
							this.form[option.name] = option.value
						})
					}
				} else if (item.component === 'upload') {
					if (item.name) {
						const value = {}
						item.options.items.forEach((option) => {
							value[option.name] = option.value
						})
						this.form[item.name] = value
					} else {
						item.options.items.forEach((option) => {
							this.form[option.name] = option.value
						})
					}
				} else {
					this.form[item.name] = item.value
				}
			})
			if (this.hasValue) {
				this.form = this.deepMerge(this.form, this.modelValue)
			}
			this.getData()
		},
		//处理远程选项数据
		getData() {
			this.renderLoading = true
			var remoteData = []
			this.config.formItems.forEach((item) => {
				if (item.options && item.options.remote) {
					var req = http.get(item.options.remote.api, item.options.remote.data).then((res) => {
						item.options.items = res.data
					})
					remoteData.push(req)
				}
			})
			Promise.all(remoteData).then(() => {
				this.renderLoading = false
			})
		},
		//合并深结构对象
		deepMerge(obj1, obj2) {
			let key
			for (key in obj2) {
				obj1[key] =
					obj1[key] &&
						obj1[key].toString() === '[object Object]' &&
						obj2[key] &&
						obj2[key].toString() === '[object Object]'
						? this.deepMerge(obj1[key], obj2[key])
						: (obj1[key] = obj2[key])
			}

			return obj1
			//return JSON.parse(JSON.stringify(obj1))
		},
		//处理动态隐藏
		hideHandle(item) {
			if (item.hideHandle) {
				const exp = eval(item.hideHandle.replace(/\$/g, 'this.form'))
				return exp
			}
			return false
		},
		//处理动态必填
		rulesHandle(item) {
			if (item.requiredHandle) {
				const exp = eval(item.requiredHandle.replace(/\$/g, 'this.form'))
				var requiredRule = item.rules.find((t) => 'required' in t)
				requiredRule.required = exp
			}
			return item.rules
		},
		//数据验证
		validate(valid, obj) {
			return this.$refs.form.validate(valid, obj)
		},
		scrollToField(prop) {
			return this.$refs.form.scrollToField(prop)
		},
		resetFields() {
			setTimeout(() => {
				console.log('this', this.$refs)
			}, 2000)
			return this.$refs.form.resetFields()
		},
		//提交
		submit() {
			this.$emit('submit', this.form)
		}
	}
}
</script>

<style lang="scss" scoped>
.el-form--inline {
	.el-col {
		display: flex;
		align-items: center;
	}

	.el-form-item {
		margin: unset;
	}
}
</style>
