<template>
	<div class="center-form-container">
		<div v-show="widgetForm.list.length == 0" class="form-empty">从左侧拖拽来添加字段</div>
		<el-form
			label-suffix=":"
			:size="widgetForm.config.size"
			:label-position="widgetForm.config.labelPosition"
			:label-width="`${widgetForm.config.labelWidth}px`"
			:hide-required-asterisk="widgetForm.config.hideRequiredAsterisk"
		>
			<Draggable
				class="center-form-list"
				item-key="key"
				ghostClass="ghost"
				handle=".drag-center"
				:animation="200"
				:group="{ name: 'people' }"
				:list="widgetForm.list"
				@add="handleMoveAdd"
			>
				<template #item="{ element, index }">
					<transition-group name="fade" tag="div">
						<template v-if="element.type === 'grid'">
							<el-row
								v-if="element.key"
								:key="element.key"
								class="center-col center-view drag-center"
								type="flex"
								:class="{ active: widgetFormSelect?.key === element.key }"
								:gutter="element.options.gutter ?? 0"
								:justify="element.options.justify"
								:align="element.options.align"
								@click="handleItemClick(element)"
							>
								<el-col v-for="(col, colIndex) of element.columns" :key="colIndex" :span="col.span ?? 0">
									<Draggable
										class="center-col-list"
										item-key="key"
										ghostClass="ghost"
										handle=".drag-center"
										:animation="200"
										:group="{ name: 'people' }"
										:no-transition-on-drag="true"
										:list="col.list"
										@add="handleColMoveAdd($event, element, colIndex)"
									>
										<template #item="{ element, index }">
											<transition-group name="fade" tag="div">
												<CenterFormItem
													v-if="element.key"
													:key="element.key"
													:element="element"
													:config="widgetForm.config"
													:selectWidget="widgetFormSelect"
													@click.stop="handleItemClick(element)"
													@copy="handleCopyClick(index, col.list)"
													@delete="handleDeleteClick(index, col.list)"
												/>
											</transition-group>
										</template>
									</Draggable>
								</el-col>
								<div v-if="widgetFormSelect?.key === element.key" class="center-view-action center-col-action">
									<cusSvgIcon iconClass="delete" @click.stop="handleDeleteClick(index, widgetForm.list)" />
								</div>

								<!-- <div class="center-view-drag center-col-drag"
                                    v-if="widgetFormSelect?.key === element.key">
                                    <cusSvgIcon iconClass="move" className="drag-center" />
                                </div> -->
							</el-row>
						</template>
						<template v-else>
							<CenterFormItem
								v-if="element.key"
								:key="element.key"
								:element="element"
								:config="widgetForm.config"
								:selectWidget="widgetFormSelect"
								@click="handleItemClick(element)"
								@copy="handleCopyClick(index, widgetForm.list)"
								@delete="handleDeleteClick(index, widgetForm.list)"
							/>
						</template>
					</transition-group>
				</template>
			</Draggable>
		</el-form>
	</div>
</template>

<script>
import { defineComponent, nextTick } from 'vue'
import Draggable from 'vuedraggable'
import { v4 as uuidv4 } from 'uuid'
import CenterFormItem from './component/centerItem.vue'
import { widgetForm } from '@/utils/components'

export default defineComponent({
	name: 'DesignerFormCenter',
	components: {
		Draggable,
		CenterFormItem
	},
	props: {
		widgetForm: {
			type: Object,
			required: true
		},
		widgetFormSelect: {
			type: Object
		}
	},
	emits: ['update:widgetForm', 'update:widgetFormSelect'],
	setup(props, { emit }) {
		const handleListInsert = (key, list, obj) => {
			const newList = []
			list.forEach((item) => {
				if (item.key === key) {
					newList.push(item)
					newList.push(obj)
				} else {
					if (item.columns) {
						item.columns = item.columns.map((col) => ({
							...col,
							list: handleListInsert(key, col.list, obj)
						}))
					}
					newList.push(item)
				}
			})
			return newList
		}

		const handleListDelete = (key, list) => {
			const newList = []
			list.forEach((item) => {
				if (item.key !== key) {
					if (item.columns) {
						item.columns = item.columns.map((col) => ({
							...col,
							list: handleListDelete(key, col.list)
						}))
					}
					newList.push(item)
				}
			})
			return newList
		}

		const handleItemClick = (row) => {
			emit('update:widgetFormSelect', row)
		}

		const handleCopyClick = (index, list) => {
			const key = uuidv4().replaceAll('-', '')
			const oldList = JSON.parse(JSON.stringify(props.widgetForm.list))

			let copyData = {
				...list[index],
				key,
				model: `${list[index].type}_${key}`,
				rules: list[index].rules ?? []
			}

			if (list[index].type === 'radio' || list[index].type === 'checkbox' || list[index].type === 'select') {
				copyData = {
					...copyData,
					options: {
						...copyData.options,
						options: copyData.options.options.map((item) => ({ ...item }))
					}
				}
			}

			emit('update:widgetForm', {
				...props.widgetForm,
				list: handleListInsert(list[index].key, oldList, copyData)
			})

			emit('update:widgetFormSelect', copyData)
		}

		const handleDeleteClick = (index, list) => {
			const oldList = JSON.parse(JSON.stringify(props.widgetForm.list))

			if (list.length - 1 === index) {
				if (index === 0) {
					nextTick(() => emit('update:widgetFormSelect', null))
				} else {
					emit('update:widgetFormSelect', list[index - 1])
				}
			} else {
				emit('update:widgetFormSelect', list[index + 1])
			}

			emit('update:widgetForm', {
				...props.widgetForm,
				list: handleListDelete(list[index].key, oldList)
			})
		}

		const handleMoveAdd = (event) => {
			const { newIndex } = event

			const key = uuidv4().replaceAll('-', '')
			const list = JSON.parse(JSON.stringify(props.widgetForm.list))

			list[newIndex] = {
				...list[newIndex],
				key,
				model: `${list[newIndex].type}_${key}`,
				rules: []
			}

			if (list[newIndex].type === 'radio' || list[newIndex].type === 'checkbox' || list[newIndex].type === 'select') {
				list[newIndex] = {
					...list[newIndex],
					options: {
						...list[newIndex].options,
						options: list[newIndex].options.options.map((item) => ({
							...item
						}))
					}
				}
			}

			if (list[newIndex].type === 'grid') {
				list[newIndex] = {
					...list[newIndex],
					columns: list[newIndex].columns.map((item) => ({ ...item }))
				}
			}
			emit('update:widgetForm', { ...props.widgetForm, list })

			emit('update:widgetFormSelect', list[newIndex])
		}

		const handleColMoveAdd = (event, row, index) => {
			const { newIndex, oldIndex, item } = event
			const list = JSON.parse(JSON.stringify(props.widgetForm.list))

			if (item.className.includes('data-grid')) {
				item.tagName === 'DIV' && list.splice(oldIndex, 0, row.columns[index].list[newIndex])
				row.columns[index].list.splice(newIndex, 1)
				return false
			}

			const key = uuidv4().replaceAll('-', '')

			row.columns[index].list[newIndex] = {
				...row.columns[index].list[newIndex],
				key,
				model: `${row.columns[index].list[newIndex].type}_${key}`,
				rules: []
			}

			if (
				row.columns[index].list[newIndex].type === 'radio' ||
				row.columns[index].list[newIndex].type === 'checkbox' ||
				row.columns[index].list[newIndex].type === 'select'
			) {
				row.columns[index].list[newIndex] = {
					...row.columns[index].list[newIndex],
					options: {
						...row.columns[index].list[newIndex].options,
						options: row.columns[index].list[newIndex].options.options.map((item) => ({ ...item }))
					}
				}
			}

			emit('update:widgetFormSelect', row.columns[index].list[newIndex])
		}

		return {
			handleItemClick,
			handleCopyClick,
			handleDeleteClick,
			handleMoveAdd,
			handleColMoveAdd
		}
	}
})
</script>

<style lang="scss">
$primary-color: #2745b2;
$primary-background-color: #ecf5ff;

.center-form-container {
	// position: absolute;
	// top: 0;
	// left: 0;
	// right: 0;
	// bottom: 0;

	.center-form-list {
		background: #fff;
		border: 1px dashed #999;
		min-height: calc(100vh - 160px);
		max-height: calc(100vh - 180px);
		margin: 10px;
		overflow: auto;

		.center-item-container {
			position: relative;

			.center-view-action {
				position: absolute;
				right: 0;
				bottom: -2px;
				height: 28px;
				line-height: 28px;
				background: $primary-color;
				z-index: 10;

				.svg-icon {
					font-size: 14px;
					color: #fff;
					margin: 0 5px;
					cursor: pointer;
				}
			}

			.center-view-drag {
				position: absolute;
				height: 28px;
				left: 0;
				top: -2px;
				line-height: 28px;
				background: $primary-color;
				z-index: 10;

				.svg-icon {
					font-size: 14px;
					color: #fff;
					margin: 0 5px;
					cursor: move;
				}
			}
		}

		.center-col-list {
			min-height: 50px;
			border: 1px dashed #ccc;
			background: #fff;
		}

		.center-view {
			padding-bottom: 18px;
			position: relative;
			border: 1px dashed rgba(170, 170, 170, 0.7);
			background-color: rgba(236, 245, 255, 0.3);
			margin: 2px;
			cursor: -webkit-grab;
			cursor: grab;

			.center-view-description {
				height: 15px;
				line-height: 15px;
				font-size: 13px;
				margin-top: 6px;
				color: #909399;
			}

			&:after {
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				display: block;
			}

			&:hover {
				background: $primary-background-color;
				outline: 1px solid $primary-color;
				outline-offset: 0;

				&.active {
					outline: 2px solid $primary-color;
					border: 1px solid $primary-color;
					outline-offset: 0;

					.center-view-drag {
						display: block;
					}
				}
			}

			&.active {
				outline: 2px solid $primary-color;
				border: 1px solid $primary-color;
			}

			&.ghost {
				background: #f56c6c;
				border: 2px solid #f56c6c;
				outline-width: 0;
				height: 3px;
				box-sizing: border-box;
				font-size: 0;
				content: '';
				overflow: hidden;
				padding: 0;
			}
		}

		.center-col {
			padding: 5px;
			background-color: rgba(253, 246, 236, 0.3);

			&:hover {
				background: #fdf6ec;
				outline: 1px solid #e6a23c;
				outline-offset: 0px;

				&.active {
					outline: 2px solid #e6a23c;
					border: 1px solid #e6a23c;
					outline-offset: 0;
				}
			}

			&.active {
				outline: 2px solid #e6a23c;
				border: 1px solid #e6a23c;
			}

			&.ghost {
				background: #f56c6c;
				border: 2px solid #f56c6c;
				outline-width: 0;
				height: 3px;
				box-sizing: border-box;
				font-size: 0;
				content: '';
				overflow: hidden;
				padding: 0;
			}

			.center-view-action.center-col-action {
				position: absolute;
				height: 28px;
				right: -2px;
				bottom: -2px;
				line-height: 28px;
				background: #e6a23c;
				z-index: 10;

				.svg-icon {
					font-size: 14px;
					color: #fff;
					margin: 0 5px;
					cursor: move;
				}
			}

			.center-view-drag.center-col-drag {
				position: absolute;
				height: 28px;
				left: -2px;
				top: -2px;
				line-height: 28px;
				background: #e6a23c;
				z-index: 10;

				.svg-icon {
					font-size: 14px;
					color: #fff;
					margin: 0 5px;
					cursor: move;
				}
			}
		}

		.ghost {
			background: #f56c6c;
			border: 2px solid #f56c6c;
			outline-width: 0;
			height: 3px;
			box-sizing: border-box;
			font-size: 0;
			content: '';
			overflow: hidden;
			padding: 0;
		}
	}

	.ghost {
		background: #f56c6c;
		border: 2px solid #f56c6c;
		position: relative;

		&::after {
			background: #f56c6c;
		}
	}

	li.ghost {
		height: 5px;
		list-style: none;
		font-size: 0;
		overflow: hidden;
	}

	.form-empty {
		position: absolute;
		text-align: center;
		width: 300px;
		font-size: 20px;
		top: 200px;
		left: 50%;
		margin-left: -150px;
		color: #ccc;
	}

	.widget-empty {
		background-position: 50%;
	}
}
</style>
