<template>
	<el-dialog v-model="dialogVisible" title="移动到" destroy-on-close append-to-body width="25%">
		<el-scrollbar height="350px">
			<el-tree ref="treeRef" :data="treeList" :props="defaultProps" @node-click="treeClick"></el-tree>
		</el-scrollbar>
		<template #footer>
			<el-button @click="dialogVisible = false">取 消</el-button>
			<el-button type="primary" @click="save">确 定</el-button>
		</template>
	</el-dialog>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

let dialogVisible = ref(false)
let treeList = ref([])
let fileId = ref(0)
let treeRef = ref(null)
const rowTree = ref({})
const paramsValue = ref({})
const defaultProps = {
	children: 'child',
	label: 'name'
}

const emit = defineEmits(['success'])

const open = async (params, file_id) => {
	dialogVisible.value = true
	fileId.value = file_id
	paramsValue.value = params
	const res = await globalPropValue.fileManagement.file.file_get_type.get({
		tenant_id: params.tenant_id,
		campus_id: params.campus_id,
		model: params.model
	})
	if (res.code === 200) {
		treeList.value = res.data
	}
}
// 保存按钮事件
const save = async () => {
	const res = await globalPropValue.fileManagement.file.file_move.post({
		tenant_id: paramsValue.value.tenant_id,
		campus_id: paramsValue.value.campus_id,
		file_id: fileId.value,
		dir_id: rowTree.value.dir_id,
		type_id: rowTree.value.type_id
	})
	if (res.code === 200) {
		ElMessage.success('移动成功')
		emit('success')
	} else {
		ElMessage.error(res.msg)
	}
	dialogVisible.value = false
}
// 树点击事件
const treeClick = () => {
	rowTree.value = treeRef.value.getCurrentNode()
}
defineExpose({
	open
})
</script>
<style scoped lang="scss">
:deep(.el-tree-node__content) {
	height: 36px;
}
</style>
