export { default as Vue } from './Vue.vue'
export { default as Code } from './Code.vue'
export { default as Candle } from './candle.vue'
export { default as Wechat } from './Wechat.vue'
export { default as BugFill } from './BugFill.vue'
export { default as BugLine } from './BugLine.vue'
export { default as FileWord } from './FileWord.vue'
export { default as FileExcel } from './FileExcel.vue'
export { default as FilePpt } from './FilePpt.vue'
export { default as Organization } from './Organization.vue'
export { default as Upload } from './Upload.vue'
export { default as Download } from './Download.vue'
export { default as Golang } from './Golang.vue'
export { default as Logo } from './Logo.vue'
export { default as International } from './International.vue'
export { default as Face } from './face.vue'
export { default as Psy } from './psy.vue'
export { default as Cert } from './cert.vue'
export { default as PsyAi } from './psyAi.vue'
export { default as PsyActivity } from './psyActivity.vue'
export { default as PsyArchives } from './psyArchives.vue'
export { default as PsyAssessment } from './psyAssessment.vue'
export { default as PsyClssReport } from './psyClssReport.vue'
export { default as PsyCounseling } from './psyCounseling.vue'
export { default as PsyCounselingManage } from './psyCounselingManage.vue'
export { default as PsyDevice } from './psyDevice.vue'
export { default as PsyEmergencies } from './psyEmergencies.vue'
export { default as PsyEvaluation } from './psyEvaluation.vue'
export { default as PsyGeneralTest } from './psyGeneralTest.vue'
export { default as PsyHelp } from './psyHelp.vue'
export { default as PsyMonthReport } from './psyMonthReport.vue'
export { default as PsyMusic } from './psyMusic.vue'
export { default as PsyResource } from './psyResource.vue'
export { default as PsySenior } from './psySenior.vue'
export { default as PsyWarn } from './psyWarn.vue'
export { default as PsyWarning } from './psyWarning.vue'
export { default as PsyWiki } from './psyWiki.vue'
export { default as BindCard } from './bindCard.vue'
