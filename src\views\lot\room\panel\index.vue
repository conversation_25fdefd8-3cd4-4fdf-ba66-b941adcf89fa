<template>
	<el-container>
		<el-aside width="270px">
			<el-container>
				<el-header v-if="CampusManagementList.length > 1">
					<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-header>
				<el-main>
					<el-tree
						ref="group"
						class="menu"
						node-key="id"
						:data="groupsAdd"
						:highlight-current="true"
						:expand-on-click-node="false"
						:props="defaultProps"
						:default-expanded-keys="[0]"
						@node-click="groupClick"
					>
						<template #default="{ node, data }"> </template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<div class="left-panel">
					<el-input
						v-model="params.name"
						placeholder="请输入场室名称搜索"
						clearable
						style="width: 200px; margin-right: 15px"
					></el-input>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</el-header>
			<el-main>
				<div ref="scTableMain" v-loading="loading" class="scTable" style="height: 100%">
					<div class="scTable-table" style="height: calc(100% - 40px)">
						<div style="height: 100%">
							<el-scrollbar class="no-horizontal-scrollbar" style="height: 100%">
								<div v-if="roomList && roomList.length > 0" style="padding-right: 10px">
									<el-row :gutter="20">
										<el-col v-for="(item, index) in roomList" :key="index" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
											<el-card class="card-item" shadow="hover">
												<div class="card-header">
													<div>
														<b>{{ item.room_name }}</b>
													</div>
													<div>
														<el-button text type="primary" icon="el-icon-Operation" @click="handleShowSet(item)">详情</el-button>
													</div>
												</div>
												<div class="card-info" v-if="item.edu_device_info">
													<el-row :gutter="15">
														<el-col :span="12" class="item-info">
															<div>中控：</div>
															<div>
																<el-text type="success" v-if="item.edu_device_info.power == 'on'">开机</el-text>
																<el-text type="info" v-else-if="item.edu_device_info.power == 'disable'">禁用</el-text>
																<el-text type="info" v-if="item.edu_device_info.power == 'poweron'">正在开机</el-text>
																<el-text type="info" v-if="item.edu_device_info.power == 'poweroff'">正在关机</el-text>
																<el-text type="danger" v-if="item.edu_device_info.power == 'off'">关机</el-text>
															</div>
														</el-col>
														<el-col :span="12" class="item-info">
															<div>电脑：</div>
															<div>
																<el-text type="success" v-if="item.edu_device_info.pc == 'on'">开机</el-text>
																<el-text type="info" v-if="item.edu_device_info.pc == 'poweron'">正在开机</el-text>
																<el-text type="info" v-if="item.edu_device_info.pc == 'poweroff'">正在关机</el-text>
																<el-text type="danger" v-if="item.edu_device_info.pc == 'off'">关机</el-text>
															</div>
														</el-col>
														<el-col :span="12" class="item-info">
															<div>投影仪：</div>
															<div>
																<el-text type="success" v-if="item.edu_device_info.projector == 'on'">开机</el-text>
																<el-text type="info" v-if="item.edu_device_info.projector == 'poweron'">正在开机</el-text>
																<el-text type="info" v-if="item.edu_device_info.projector == 'poweroff'">正在关机</el-text>
																<el-text type="danger" v-if="item.edu_device_info.projector == 'off'">关机</el-text>
															</div>
														</el-col>
														<el-col :span="12" class="item-info">
															<div>第二路投影：</div>
															<div>
																<el-text type="success" v-if="item.edu_device_info.projector2 == 'on'">开机</el-text>
																<el-text type="info" v-if="item.edu_device_info.projector2 == 'poweron'">正在开机</el-text>
																<el-text type="info" v-if="item.edu_device_info.projector2 == 'poweroff'">正在关机</el-text>
																<el-text type="danger" v-if="item.edu_device_info.projector2 == 'off'">关机</el-text>
															</div>
														</el-col>
														<el-col :span="12" class="item-info">
															<div>投影仪画面：</div>
															<div>
																<el-text type="success" v-if="item.edu_device_info.channel_source == '1'">内置PC</el-text>
																<el-text type="success" v-if="item.edu_device_info.channel_source == '2'">外接1</el-text>
																<el-text type="success" v-if="item.edu_device_info.channel_source == '3'">外接2</el-text>
															</div>
														</el-col>
														<el-col :span="12" class="item-info">
															<div>录播设备：</div>
															<div>
																<el-text type="success" v-if="item.edu_device_info.record == 'on'">开机</el-text>
																<el-text type="danger" v-if="item.edu_device_info.record == 'off'">关机</el-text>
																<el-text type="warning" v-if="item.edu_device_info.record == 'start'">开始录制</el-text>
																<el-text type="warning" v-if="item.edu_device_info.record == 'stop'">停止录制</el-text>
																<el-text type="warning" v-if="item.edu_device_info.record == 'pause'">暂停录制</el-text>
																<el-text type="warning" v-if="item.edu_device_info.record == 'restore'">恢复录制</el-text>
																<el-text type="info" v-if="item.edu_device_info.record == 'poweron'">正在开机</el-text>
																<el-text type="info" v-if="item.edu_device_info.record == 'poweroff'">正在关机</el-text>

															</div>
														</el-col>
														<el-col :span="24" class="item-info">
															<div>设备故障：</div>
															<div>
																<el-tag v-if="item.edu_device_info.control_fault == 1" size="small" type="danger">中控故障</el-tag>
																<el-tag v-if="item.edu_device_info.pc_fault == 1" size="small" type="danger">电脑故障</el-tag>
																<el-tag v-if="item.edu_device_info.display_fault == 1" size="small" type="danger">显示故障</el-tag>
																<el-tag v-if="item.edu_device_info.audio_fault == 1" size="small" type="danger">音频故障</el-tag>
																<el-tag v-if="item.edu_device_info.other_fault == 1" size="small" type="danger">其他故障</el-tag>
																<el-tag
																	v-if="
																		item.edu_device_info.other_fault == -1 &&
																		item.edu_device_info.control_fault == -1 &&
																		item.edu_device_info.pc_fault == -1 &&
																		item.edu_device_info.display_fault == -1 &&
																		item.edu_device_info.audio_fault == -1
																	"
																	size="small"
																	type="success"
																	>无故障上报</el-tag>
															</div>
														</el-col>
													</el-row>
												</div>
												<div class="card-info" v-else>
													<el-row :gutter="15">
														<el-col :span="12" class="item-info">
															<div>中控：</div>
															<div>无</div>
														</el-col>
														<el-col :span="12" class="item-info">
															<div>电脑：</div>
															<div>无</div>
														</el-col>
														<el-col :span="12" class="item-info">
															<div>投影仪：</div>
															<div>无</div>
														</el-col>
														<el-col :span="12" class="item-info">
															<div>第二路投影：</div>
															<div>无</div>
														</el-col>
														<el-col :span="12" class="item-info">
															<div>投影仪画面：</div>
															<div>无</div>
														</el-col>
														<el-col :span="12" class="item-info">
															<div>录播设备：</div>
															<div>无</div>
														</el-col>
														<el-col :span="24" class="item-info">
															<div>设备故障：</div>
															<div>无</div>
														</el-col>
													</el-row>
												</div>
											</el-card>
										</el-col>
									</el-row>
								</div>
								<el-empty v-else description="暂无数据"></el-empty>
							</el-scrollbar>
						</div>
					</div>
					<div class="scTable-page">
						<div class="pagination">
							<el-pagination
								v-if="total > 0"
								v-model:current-page="params.page"
								:page-sizes="[10, 20, 30, 50, 100]"
								:page-size="params.pageSize"
								size="small"
								background
								layout="total,sizes, prev, pager, next,jumper"
								:total="total"
								@size-change="handleSizeChange"
								@current-change="handleCurrentChange"
							/>
						</div>
					</div>
				</div>
			</el-main>
		</el-container>
	</el-container>
	<save-dialog v-if="dialog.save" ref="saveDialog" @closed="closeDialog"></save-dialog>

</template>

<script>
import cusTom from '@/utils/cusTom'
const defaultProps = {
	children: 'floor',
	label: 'name'
}
const { campusId, tenantId, campusInfo, roomTypeMap, roomCapacityMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'
import saveDialog from './save.vue'

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		building_id: null,
		floor_id: null,
		page: 1,
		pageSize: 10,
		name: null
	}
}

export default {
	name: 'panel',
	data() {
		return {
			currentId: -1,
			groupFilterText: '',
			groupData: [],
			defaultProps,
			list: {
				apiObj: this.$API.fieldRoom.rooms.list
			},
			roomList: [],
			total: 0,
			loading: false,
			params: defaultParams(),
			CampusManagementList: campusInfo,
			JobData: [],
			dialog: {
				save: false
			},
			showTools: false,
			roomNameFilters: [],
			roomTypeMap,
			roomCapacityMap
		}
	},
	components: { saveDialog },
	watch: {
		'params.campus_id': {
			handler(val) {
				this.building_id = null
				this.floor_id = null
				this.getLou()
				this.getRoom()
			}
		}
	},
	computed: {
		floorFilters() {
			let res = cusTom
				.treeToArray(this.groupData)
				.filter((v) => {
					return v.parent_id > 0
				})
				.map((v) => {
					return {
						text: v.building_name,
						value: v.building_name
					}
				})

			return cusTom.uniqueByValue(res, 'text')
		},
		groupsAdd() {
			let arr = [
				{
					id: 0,
					name: '全部'
				},
				...this.groupData
			]
			return arr
		}
	},
	async created() {
		this.getLou()
	},
	methods: {
		//重置
		closeDialog() {
			this.dialog.save = false
			this.getRoom()
		},
		handleShowSet(item) {
			console.log(item)
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.show(item.room_name,item.room_id)
			})
		},
		handleSizeChange(page_size) {
			this.params.pageSize = page_size
			this.getRoom()
		},
		handleCurrentChange(page) {
			this.params.page = page
			this.getRoom()
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		handleShowTools(item) {
			this.currentId = item.id
		},
		dataChange(val) {
			if (!val.data.rows) return
			let arr = val.data.rows.map((v) => {
				return {
					text: v.room_name,
					value: v.room_name
				}
			})
			this.roomNameFilters = cusTom.uniqueByValue(arr, 'text')
		},
		filterHandler(value, row, column) {
			const property = column['property']
			return row[property] === value
		},
		//获取宿舍楼
		async getLou() {
			const res = await this.$API.fieldRoom.all.get({ ...this.params, building_type: null })
			if (!res.data) res.data = []
			let data1 = res.data.map((v) => {
				return {
					...v,
					showTools: false
				}
			})
			this.groupData = cusTom.arrayToTree(data1)
			this.getRoom()
		},
		//获取房间列表
		async getRoom() {
			this.loading = true
			const res = await this.$LotApi.room.list.get(this.params)
			console.log(res)
			this.loading = false
			if (res.code === 200) {
				this.roomList = res.data.rows
				this.total = res.data.total
			}
		},

		//树点击事件
		groupClick(data) {
			this.params.name = null
			if (data.parent_id <= 0 && data.id != 0) {
				this.params.building_id = data.id
				this.params.floor_id = null
			} else if (data.parent_id > 0 && data.id != 0) {
				this.params.building_id = data.parent_id
				this.params.floor_id = data.id
			} else {
				this.params.building_id = null
				this.params.floor_id = null
			}
			this.params.page = 1
			this.getRoom()
		},
		refresh() {
			this.params.page = 1
			this.params.name = null
			this.upsearch()
		},
		//搜索
		upsearch() {
			this.getRoom()
		}
	}
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;
	.el-button {
		width: 80%;
		margin-bottom: 10px;
	}
}
.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;
	box-sizing: border-box;
	a {
		font-size: 18px;
	}
}

.scTable-table {
	height: calc(100% - 40px);
}

.scTable-page {
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding: 0 15px;
	border-top: 1px solid var(--el-border-color-light);

	.pagination {
	}
}

.scTable-do {
	white-space: nowrap;
}

.scTable:deep(.el-table__footer) .cell {
	font-weight: bold;
}

.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
	height: 8px;
	border-radius: 8px;
}

.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
	width: 8px;
	border-radius: 8px;
}
.card-header{
	height: 40px;
	line-height: 40px;
	padding: 0 10px;
	border-bottom: 1px solid var(--el-border-color-light);
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 14px;
}

:deep(.card-item) {
	.el-card__footer {
		padding: 10px;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}
}

:deep(.el-scrollbar__wrap) {
	overflow-x: hidden !important;
}

:deep(.el-card__body) {
	padding: 0px;
	.item-info{
		margin-bottom: 0px;
		height: 30px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		line-height: 30px;
	}
	.item-info div:nth-child(1) {
		font-size: 13px;
		color:var(--el-color-info)
	}
	.item-info div:nth-child(2) {
		padding-right: 10px;
	}
	.el-form-item--default {
		margin-bottom: 0px;
		height: 30px;
		align-items: center;
		line-height: 30px;
	}

	.el-form-item__label {
		font-size: 12px;
		padding-right: 5px;
		height: 30px;
		line-height: 30px;
	}

	.el-form-item__content {
		font-size: 12px;
		height: 30px;
		text-align: right;
		line-height: 30px;
	}
}

.image-slot {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	background: var(--el-color-primary-light-9);
	color: var(--el-color-primary-light-2);
	font-size: 24px;
}
.card-info{
	padding: 10px;
}
.card-info-img {
	display: flex;

	.el-image {
		margin-right: 10px;
	}

	.device_name {
		cursor: pointer;
		font-size: 15px;
		font-weight: bold;
		line-height: 25px;
		margin-bottom: 5px;
	}
}

.el-col .el-card {
	margin-bottom: 20px;
}
</style>
