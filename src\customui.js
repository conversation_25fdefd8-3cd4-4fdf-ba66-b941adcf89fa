import cusTable from './components/custom/cusTable.vue'
import cusForm from './components/custom/cusForm.vue'
import cusDate from './components/custom/cusDate.vue'
import cusCascader from './components/custom/cusCascader.vue'
import cusSelectTeacher from './components/custom/cusSelectTeacher.vue'
import cusSelectStudent from './components/custom/cusSelectStudent.vue'
import cusSelectRoom from './components/custom/cusSelectRoom.vue'
import cusSelectField from './components/custom/cusSelectField.vue'
import cusSelectConsumables from './components/custom/cusSelectConsumables.vue'
import cusSelectasset from './components/custom/cusSelectasset.vue'
import cusSelectTree from './components/custom/cusSelectTree.vue'
import cusTooltip from './components/custom/cusTooltip.vue'
import cusSvgIcon from './components/custom/cusSvgIcon.vue'
import cusImage from './components/custom/cusImage.vue'

import pinia from '@/stores/pinia.js'
import { useCommonStore } from '@/stores/common'
const commonStore = useCommonStore(pinia)
import '@/utils/dayjs' //安装dayjs插件
export default {
	install(app) {
		////挂载全局方法
		app.config.globalProperties.$formatDictionary = (arr, val) => {
			return arr.find((v) => v.value == val)?.name || val
		}
		app.config.globalProperties.$SET_campus_id = commonStore.SET_campus_id
		app.config.globalProperties.$SET_tenant_id = commonStore.SET_tenant_id

		//注册全局组件
		app.component('cusTable', cusTable)
		app.component('cusForm', cusForm)
		app.component('cusDate', cusDate)
		app.component('cusCascader', cusCascader)
		app.component('cusSelectTeacher', cusSelectTeacher)
		app.component('cusSelectStudent', cusSelectStudent)
		app.component('cusSelectRoom', cusSelectRoom)
		app.component('cusSelectField', cusSelectField)
		app.component('cusSelectConsumables', cusSelectConsumables)
		app.component('cusSelectasset', cusSelectasset)
		app.component('cusSelectTree', cusSelectTree)
		app.component('cusTooltip', cusTooltip)
		app.component('cusSvgIcon', cusSvgIcon)
		app.component('cusImage', cusImage)
	}
}
