<template>
    <el-dialog v-if="dialogLoginVisible" v-model="dialogLoginVisible" width="500" :show-close="false" :close-on-press-escape="false"
        :close-on-click-modal="false" :title="title" center align-center>
        <el-form ref="loginForm" :model="form" :rules="rules" size="large">
            <el-form-item label="手机号" prop="username">
                <el-input v-model="form.username" clearable placeholder="请输入登录手机号">
                    <template #prepend>+86</template>
                </el-input>
            </el-form-item>
            <el-form-item label="登录密码" prop="password" v-if="formData.object == 2">
                <el-input v-model="form.password" prefix-icon="el-icon-lock" clearable show-password
                    placeholder="请输入登录密码"></el-input>
            </el-form-item>
            <el-form-item label="验证码" prop="code" v-if="formData.object == 3">
                <div style="display: flex;">
                    <el-input v-model="form.code" prefix-icon="el-icon-unlock" clearable placeholder="请输入手机验证码"
                        style="margin-right: 15px;"></el-input>
                    <el-button :disabled="disabled" @click="getYzm">获取验证码<span v-if="disabled"> ({{
                        time }})</span></el-button>
                </div>
            </el-form-item>
                <div style="width: 100%;margin-top: 18px;text-align: center;">
                    <el-button :disabled="disabled" type="primary" size="large" style="width: 50%;" @click="login">登录</el-button>
                </div>
        </el-form>
    </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const dialogLoginVisible = ref(false)
const title = ref('登录')
const formData = ref({})
const disabled = ref(false)
const time = ref(60)
const form = ref({})
const rules = ref({
    username: [{ required: true, message: '请输入手机号' }],
    password: [{ required: true, message: '请输入密码' }],
    code: [{ required: true, message: '请输入验证码' }]
})
const loginForm = ref()
const emit = defineEmits(['success'])
const getYzm = async () => {
    var validate = await loginForm.value.validateField('username').catch(() => { })
    if (!validate) {
        return false
    }
    var res = await globalPropValue.common.sendVerifyCode.post({
        phone: form.value.username
    })
    if (res.code === 200) {
        ElMessage.success('已发送短信至手机号码')
        disabled.value = true
        time.value = 60
        var t = setInterval(() => {
            time.value -= 1
            if (time.value < 1) {
                clearInterval(t)
                disabled.value = false
                time.value = 0
            }
        }, 1000)
    } else {
        ElMessage.warning(res.message)
        return false
    }
}

const login = async () => {
    let validate = await loginForm.value.validate()
    if (!validate) {
        return false
    }
    if (formData.value.object == 2) {
        let res = await globalPropValue.auth.login.post(form.value)
        if (res.code == 200) {
            ElMessage.success('登录成功')
            emit('success', { user_type: 2, user_id: res.data.userInfo.userId })
            close()
        }
    } else {
        let data = {
            phone: form.value.username,
            code: form.value.code
        }
        let res = await globalPropValue.auth.studentLogin.post(data)
        if (res.code == 200) {
            ElMessage.success('登录成功')
            emit('success', { user_type: 3, user_id: res.data.user_info.id })
            close()
        }
    }
}
const open = (data) => {
    formData.value = data
    if (data.object == 2) {
        title.value = '老师登录'
    } else {
        title.value = '学生登录'
    }
    dialogLoginVisible.value = true
}
const close = () => {
    dialogLoginVisible.value = false
}
defineExpose({
    open,
    close
})
</script>