<template>
	<div>
		<el-input
			v-model="params.name"
			placeholder="请输入姓名搜索"
			style="width: 200px; margin-bottom: 10px"
			@input="nameSearch"
		></el-input>

		<el-table ref="userTable" :params="params" :data="list.rows">
			<template v-if="params.type !== 3">
				<el-table-column label="教师姓名" prop="staff_name"></el-table-column>
				<el-table-column label="教师属性" prop="is_head">
					<template #default="scope"> {{ scope.row.is_head === 1 ? '班主任' : '科任教师' }}</template>
				</el-table-column>
				<el-table-column label="学科" prop="course_name">
					<template #default="scope"> {{ scope.row.is_head === 1 ? '-' : scope.row.course_name }}</template>
				</el-table-column>
				<el-table-column label="年级班级" prop="grade_name">
					<template #default="scope"> {{ scope.row.grade_name }}{{ scope.row.class_name }}</template>
				</el-table-column>
				<el-table-column label="评教人数" prop="completed_num"></el-table-column>
				<el-table-column label="操作" prop="id">
					<template #default="scope">
						<el-button type="primary" plain size="small" @click="showDetail(scope.row)">详情</el-button>
					</template>
				</el-table-column>
			</template>
		</el-table>
		<div class="pagination">
			<el-pagination
				size="small"
				background
				layout="total, prev, pager, next,jumper"
				:total="list.total"
				:page-size="params.pageSize"
				class="mt-4"
				@current-change="pageChange"
			/>
		</div>
		<el-dialog v-model="showDetailDialog" title="详情" width="800" destroy-on-close>
			<el-container>
				<el-aside width="200px" class="aside">
					<div class="userInfo">
						<p>
							<cusHead
								loading="lazy"
								:lazy="true"
								fit="contain"
								style="width: 50px; height: 50px"
								:src="detailInfo.staff_head"
								:preview-src-list="[detailInfo.staff_head]"
								preview-teleported
							>
							</cusHead>
						</p>
						<p>{{ detailInfo.staff_name }}</p>
					</div>
					<p>教师属性：{{ detailInfo.is_head === 1 ? '班主任' : '科任教师' }}</p>
					<p>评教班级：{{ detailInfo.grade_name }} {{ detailInfo.class_name }}</p>
					<p>评教学科：{{ detailInfo.is_head === 1 ? '-' : detailInfo.course_name }}</p>
					<p>评教人数：{{ detailInfo.completed_num }} 人</p>
				</el-aside>
				<el-main class="main">
					<div v-for="(item, index) in formContent" :key="index" class="list">
						<div class="title">{{ item.label }}</div>
						<ul class="options">
							<template v-if="item.type === 'radio' || item.type === 'checkbox'">
								<li v-for="(options, v) in item.options.options" :key="v">
									<el-row :gutter="20">
										<el-col :span="20">
											<span v-if="item.options.showLabel">{{ options.label }}</span>
											<span v-else> {{ options.value }} </span>
										</el-col>
										<el-col
											v-if="submitNum > 0 && submitNumList[item.model] && submitNumList[item.model][options.value]"
											:span="4"
											style="text-align: right; color: #00b42a"
											>{{ (submitNumList[item.model][options.value] / submitNum).toFixed(2) * 100 }}%</el-col
										>
										<el-col v-else :span="4" style="text-align: right; color: #00b42a">0%</el-col>
									</el-row>
								</li>
							</template>
							<template v-else>
								<template v-if="submitValList[item.model]">
									<li v-for="(options, v) in submitValList[item.model]" :key="v">
										<p>{{ options }}</p>
									</li>
								</template>
							</template>
						</ul>
					</div>
				</el-main>
			</el-container>
		</el-dialog>
	</div>
</template>

<script>
import { ElMessage } from 'element-plus'
import cusHead from '@/components/custom/cusStaffHead.vue'

export default {
	components: { cusHead },
	data() {
		return {
			params: {
				tenant_id: 0,
				campus_id: 0,
				evaluation_id: 0,
				name: '',
				pageSize: 10,
				page: 1
			},
			showDetailDialog: false,
			title: '',
			list: {},
			titleMap: {
				1: '班主任',
				2: '科任教师',
				3: '学生'
			},
			apiObj: null,
			detailInfo: null,
			evaluationInfo: null,
			formContent: null,
			submitNum: 0,
			submitValList: {},
			submitNumList: {}
		}
	},
	inject: ['evaluation_info'],
	created() {
		console.log(this.evaluation_info)
	},
	mounted() {
		console.log(this.evaluation_info)
		this.params.tenant_id = this.evaluation_info.tenant_id
		this.params.evaluation_id = this.evaluation_info.id
		this.params.campus_id = this.evaluation_info.campus_id
		this.getList()
	},
	methods: {
		nameSearch(val) {
			this.params.name = val
			this.getList()
		},
		async showDetail(val) {
			const res = await this.$API.eduEvaluation.analysisDetail.get({
				id: val.id,
				tenant_id: val.tenant_id,
				campus_id: val.campus_id
			})
			if (res.code === 200) {
				this.detailInfo = res.data.info
				this.evaluationInfo = res.data.evaluation_info
				this.formContent = JSON.parse(res.data.form_content)
				this.submitNum = res.data.submit_num
				this.submitValList = res.data.submit_val_list
				this.submitNumList = res.data.submit_num_list
				this.showDetailDialog = true
				console.log(this.formContent)
			} else {
				this.showMessage(res.code, res.message)
			}
		},
		async getList() {
			const res = await this.$API.eduEvaluation.analysis.get(this.params)
			if (res.code === 200) {
				this.list = res.data
			} else {
				this.showMessage(res.code, res.message)
			}
		},
		showMessage(code, message) {
			if (code === 200) {
				ElMessage({ type: 'success', message: message })
			} else {
				ElMessage({ type: 'error', message: message })
			}
		},
		pageChange(val) {
			this.params.page = val
			this.getList()
		}
	}
}
</script>

<style lang="scss" scoped>
.pagination {
	width: 100%;
	height: 50px;
	line-height: 50px;
	background-color: #fff;
	display: inline-flex;
	justify-content: flex-start;
	border-top: 1px solid var(--el-border-color-light);
	position: sticky;
	bottom: -10px;
}

:deep(.el-dialog__body) {
	padding: 0;
}

.aside {
	padding: 20px;
	text-align: left;
	line-height: 35px;

	.userInfo {
		text-align: center;
		line-height: 30px;
		border-bottom: solid 1px var(--el-border-color-light);
		padding-bottom: 10px;
		margin-bottom: 10px;
	}

	.custom-image {
		margin: 0 auto;
	}
}

.main {
	padding: 10px 30px;
	max-height: 600px;
}

.list {
	padding: 10px;
	line-height: 25px;
	margin-bottom: 15px;
	border-radius: 6px;
	box-shadow: var(--el-box-shadow-light);

	.title {
		font-weight: bold;
	}

	.options {
		padding: 0 18px;
	}
}
</style>
