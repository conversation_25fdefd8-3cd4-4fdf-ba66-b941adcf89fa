import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/manapi/position/list`,
		name: '获取职位列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	all: {
		url: `${config.API_URL}/manapi/position/all`,
		name: '获取职位列表不分页',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/manapi/position/save`,
		name: '新增职位/修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},

	del: {
		url: `${config.API_URL}/manapi/position/del`,
		name: '删除职位',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	status: {
		url: `${config.API_URL}/manapi/position/changeStatus`,
		name: '修改职位状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	}
}
