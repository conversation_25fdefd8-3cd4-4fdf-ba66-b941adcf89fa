import config from '@/config'
import http from '@/utils/request'

export default {
	all: {
		//url: `${config.MOCK_URL}/system/role/list2`,
		url: `${config.API_URL}/sysapi/role/all`,
		name: '获取所有角色',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	permissions: {
		//url: `${config.MOCK_URL}/system/menu/list`,
		url: `${config.API_URL}/sysapi/role/permissions`,
		name: '获取/保存角色权限',
		get: async function (roleId) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, { roleId })
		},
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	list: {
		//url: `${config.MOCK_URL}/system/role/list2`,
		url: `${config.API_URL}/sysapi/role/list`,
		name: '获取角色列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/role/save`,
		name: '新增角色/修改',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	del: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/role/del`,
		name: '删除角色',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	changeStatus: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/role/changeStatus`,
		name: '更新角色状态',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	}
}
