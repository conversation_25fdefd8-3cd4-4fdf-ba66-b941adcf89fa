<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<component :is="currComponent.component" @handleTab="handleClick"></component>
	</el-container>
</template>

<script>
import homeComponent from './home'
import videoComponent from './video'
import articleComponent from './article'
import { shallowRef } from 'vue'
import item from '@/components/scContextmenu/item.vue'
export default {
	name: 'layoutTCB',
	components: {
		homeComponent,
		videoComponent,
		articleComponent
	},
	props: {
		userId: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			activeName: 'home',
			tabs: [
				{
					label: '主页',
					name: 'home',
					component: shallowRef(homeComponent)
				},
				{
					label: '文章',
					name: 'article',
					component: shallowRef(articleComponent)
				},
				{
					label: '视频',
					name: 'video',
					component: shallowRef(videoComponent)
				}
			],
			currComponent: {},
			classData: {}
		}
	},
	created() {
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
			this.activeName = name
		}
	},
	provide() {
		return {
			userId: this.userId
		}
	}
}
</script>

<style></style>
