import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/lot/productTsl/list`,
		name: '获取物模型列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	listPage: {
		url: `${config.API_URL}/lot/productTsl/listPage`,
		name: '获取物模型列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/lot/productTsl/save`,
		name: '新增物模型/修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/lot/productTsl/del`,
		name: '删除物模型',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	import: {
		url: `${config.API_URL}/lot/productTsl/import`,
		name: '导入物模型',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	one: {
		url: `${config.API_URL}/lot/productTsl/one`,
		name: '获取单个物模型',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	status: {
		url: `${config.API_URL}/lot/productTsl/changeStatus`,
		name: '修改物模型状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	}
}
