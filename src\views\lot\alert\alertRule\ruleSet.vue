<template>
	<el-drawer v-model="visible" title="告警规则设置" :size="550" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-position="top">
			<el-form-item label="类别" prop="filter_type">
				<el-radio-group v-model="form.filter_type">
					<el-radio-button label="单个设备" :value="1" />
					<el-radio-button label="单类设备" :value="2" />
				</el-radio-group>
			</el-form-item>
			<el-form-item label="触发方式" prop="trigger_mode">
				<el-select
					v-model="form.trigger_mode"
					style="width: 100%; max-width: unset"
					placeholder="请选择触发方式"
					clearable
					@change="changeTriggerMode"
				>
					<el-option label="设备属性触发" :value="1" />
					<el-option label="设备事件触发" :value="2" />
					<el-option label="设备状态触发" :value="3" />
				</el-select>
			</el-form-item>
			<el-row :gutter="0" style="width: 100%">
				<el-col :span="24">
					<el-form-item label="产品品类" prop="product_id">
						<el-select
							v-model="form.product_id"
							style="width: 100%; max-width: unset"
							placeholder="请选择产品品类"
							clearable
							@change="changeProduct"
						>
							<el-option
								v-for="(item, index) in productList"
								:key="index"
								:label="item.product_name"
								:value="item.id"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col v-if="form.filter_type === 1" :span="24">
					<el-form-item label="设备" prop="device_id">
						<el-select
							v-model="form.device_id"
							style="width: 100%; max-width: unset"
							placeholder="请选择设备"
							clearable
						>
							<el-option v-for="(item, index) in deviceList" :key="index" :label="item.device_name" :value="item.id">
								<span style="float: left">{{ item.device_name }}</span>
								<span
									v-if="item.room_info"
									style="float: right; color: var(--el-text-color-secondary); font-size: 12px"
								>
									{{ item.room_info.name }}
								</span>
							</el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item v-if="form.trigger_mode === 1" label="功能属性" prop="tsl_id">
				<el-select
					v-model="form.tsl_id"
					style="width: 100%; max-width: unset"
					placeholder="请选择功能属性"
					clearable
					@change="selectTsl"
				>
					<el-option v-for="(item, index) in propertiesTsl" :key="index" :label="item.name" :value="item.id">
						<span style="float: left">{{ item.name }}</span>
						<span style="float: right; color: var(--el-text-color-secondary); font-size: 12px">
							{{ item.code }}
						</span>
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item v-if="form.trigger_mode === 2" label="事件" prop="tsl_id">
				<el-select
					v-model="form.tsl_id"
					style="width: 100%; max-width: unset"
					placeholder="请选择事件"
					clearable
					@change="selectEventTsl"
				>
					<el-option v-for="(item, index) in eventTsl" :key="index" :label="item.name" :value="item.id">
						<span style="float: left">{{ item.name }}</span>
						<span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
							{{ item.code }}
						</span>
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item v-if="form.trigger_mode === 2" label="事件参数" prop="argument">
				<el-select
					v-model="form.argument"
					style="width: 100%; max-width: unset"
					placeholder="请选择事件参数"
					clearable
					@change="selectEventArgument"
				>
					<el-option
						v-for="(item, index) in selectEventInfo?.output_params"
						:key="index"
						:label="item.name"
						:value="item.code"
					>
						<span style="float: left">{{ item.name }}</span>
						<span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
							{{ item.code }}
						</span>
					</el-option>
				</el-select>
			</el-form-item>
			<template v-if="form.trigger_mode === 1">
				<el-row
					v-if="this.selectTslInfo?.type_spec?.type !== 'bool' && this.selectTslInfo?.type_spec?.type !== 'enum'"
					:gutter="10"
					style="width: 100%"
				>
					<el-col :span="6">
						<el-form-item label="条件" prop="condition">
							<el-select
								v-model="form.condition"
								style="width: 100%; min-width: unset; max-width: unset"
								placeholder="判断条件"
								clearable
							>
								<template v-if="this.selectTslInfo?.type_spec?.type !== 'text'">
									<el-option label="等于" value="eq" />
									<el-option label="大于" value="gt" />
									<el-option label="大于等于" value="gte" />
									<el-option label="小于" value="lt" />
									<el-option label="小于等于" value="lte" />
									<el-option label="不等于" value="neq" />
								</template>
								<template v-if="this.selectTslInfo?.type_spec?.type === 'text'">
									<el-option v-if="this.selectTslInfo?.type_spec?.type === 'text'" label="包含" value="include" />
									<el-option v-if="this.selectTslInfo?.type_spec?.type === 'text'" label="不包含" value="notinclude" />
								</template>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="18">
						<el-form-item label="值" prop="value">
							<el-input v-model="form.value" placeholder="请输入判断值" style="width: 100%; max-width: unset" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item v-if="this.selectTslInfo?.type_spec?.type === 'enum'" label="条件" prop="value">
					<el-select v-model="form.value" style="width: 100%; max-width: unset" placeholder="请选择属性值" clearable>
						<el-option
							v-for="(item, index) in this.selectTslInfo?.type_spec?.specs"
							:key="index"
							:label="item.name"
							:value="item.value + ''"
						/>
					</el-select>
				</el-form-item>
				<el-form-item v-if="this.selectTslInfo?.type_spec?.type === 'bool'" label="条件" prop="value">
					<el-select v-model="form.value" style="width: 100%; max-width: unset" placeholder="请选择属性值" clearable>
						<el-option
							v-for="(item, index) in this.selectTslInfo?.type_spec?.specs"
							:key="index"
							:label="item"
							:value="index + ''"
						/>
					</el-select>
				</el-form-item>
			</template>
			<template v-if="form.trigger_mode === 2">
				<el-row
					v-if="
						this.selectEventArgumentInfo?.type_spec?.type !== 'bool' &&
						this.selectEventArgumentInfo?.type_spec?.type !== 'enum'
					"
					:gutter="10"
					style="width: 100%"
				>
					<el-col :span="6">
						<el-form-item label="条件" prop="condition">
							<el-select
								v-model="form.condition"
								style="width: 100%; min-width: unset; max-width: unset"
								placeholder="判断条件"
								clearable
							>
								<template v-if="this.selectEventArgumentInfo?.type_spec?.type !== 'text'">
									<el-option label="等于" value="eq" />
									<el-option label="大于" value="gt" />
									<el-option label="大于等于" value="gte" />
									<el-option label="小于" value="lt" />
									<el-option label="小于等于" value="lte" />
									<el-option label="不等于" value="neq" />
								</template>
								<template v-if="this.selectEventArgumentInfo?.type_spec?.type === 'text'">
									<el-option
										v-if="this.selectEventArgumentInfo?.type_spec?.type === 'text'"
										label="包含"
										value="include"
									/>
									<el-option
										v-if="this.selectEventArgumentInfo?.type_spec?.type === 'text'"
										label="不包含"
										value="notinclude"
									/>
								</template>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="18">
						<el-form-item label="值" prop="value">
							<el-input v-model="form.value" placeholder="请输入判断值" style="width: 100%; max-width: unset" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item v-if="this.selectEventArgumentInfo?.type_spec?.type === 'enum'" label="参数值" prop="value">
					<el-select v-model="form.value" style="width: 100%; max-width: unset" placeholder="请选择参数值" clearable>
						<el-option
							v-for="(item, index) in this.selectEventArgumentInfo?.type_spec?.specs"
							:key="index"
							:label="item.name"
							:value="item.value + ''"
						/>
					</el-select>
				</el-form-item>
				<el-form-item v-if="this.selectEventArgumentInfo?.type_spec?.type === 'bool'" label="参数值" prop="value">
					<el-select v-model="form.value" style="width: 100%; max-width: unset" placeholder="请选择参数值" clearable>
						<el-option
							v-for="(item, index) in this.selectEventArgumentInfo?.type_spec?.specs"
							:key="index"
							:label="item"
							:value="index + ''"
						/>
					</el-select>
				</el-form-item>
			</template>
			<el-form-item v-if="form.trigger_mode === 3" label="设备状态" prop="device_status">
				<el-select
					v-model="form.device_status"
					style="width: 100%; max-width: unset"
					placeholder="请选择设备状态"
					clearable
				>
					<el-option label="设备上线" :value="2" />
					<el-option label="设备离线" :value="3" />
				</el-select>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import cusTom from '@/utils/cusTom'

const defaultData = () => {
	return {
		tenant_id: null,
		campus_id: null,
		alert_rule_id: null,
		filter_type: 1,
		product_id: null,
		device_id: null,
		tsl_id: null,
		argument: null,
		trigger_mode: 1,
		device_status: null,
		condition: null,
		value: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},

	data() {
		return {
			visible: false,
			isSaveing: false,
			enumConfig: [],
			productList: [],
			deviceList: [],
			ruleInfo: {},
			propertiesTsl: [],
			eventTsl: [],
			rule_filter: {},
			selectTslInfo: null,
			selectEventInfo: null,
			selectEventArgumentInfo: null,
			detailInfo: {},
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				filter_type: [{ required: true, message: '请选择类别' }],
				product_id: [{ required: true, message: '请选择产品品类' }],
				device_id: [{ required: true, message: '请选择设备' }],
				tsl_id: [{ required: true, message: '请选择功能属性' }],
				trigger_mode: [{ required: true, message: '请选择触发方式' }],
				device_status: [{ required: true, message: '请选择设备状态' }],
				condition: [{ required: true, message: '请选择判断条件' }],
				argument: [{ required: true, message: '请选择事件参数' }],
				value: [{ required: true, message: '请输入值' }]
			}
		}
	},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	mounted() {},
	watch: {
		/*'form.product_id': function (val, oldVal) {
			if (val) {
				this.getProductTslList(val)
				this.getProductDevicelList(val)
			} else {
				this.propertiesTsl = []
				this.eventTsl = []
			}
			if (oldVal !== val) {
				this.form.device_id = null
				this.form.tsl_id = null
				this.selectTslInfo = null
			}
		},*/
		'form.filter_type': function (val) {
			if (val === 2) {
				this.form.device_id = null
			}
		}
	},
	methods: {
		changeTriggerMode() {
			this.form.tsl_id = null
			this.selectTslInfo = null
			this.form.value = null
		},
		changeProduct(val) {
			if (val) {
				this.getProductTslList(val)
				this.getProductDevicelList(val)
			} else {
				this.propertiesTsl = []
				this.eventTsl = []
			}
			this.form.device_id = null
			this.form.tsl_id = null
			this.selectTslInfo = null
			this.form.value = null
		},
		selectTsl(val, type = true) {
			let selectTslInfo = this.propertiesTsl.filter((item) => item.id === val)
			if (selectTslInfo.length > 0) {
				this.selectTslInfo = selectTslInfo[0]
			} else {
				this.selectTslInfo = {}
			}
			if (type) {
				this.form.condition = null
				this.form.value = null
			}
		},
		selectEventTsl(val, type = true) {
			let selectTslInfo = this.eventTsl.filter((item) => item.id === val)
			if (selectTslInfo.length > 0) {
				selectTslInfo[0].output_params.map((item) => {
					item.type_spec.specs = JSON.parse(item.type_spec.specs)
				})
				this.selectEventInfo = selectTslInfo[0]
			} else {
				this.selectEventInfo = {}
			}
			if (type) {
				this.form.argument = null
			}
		},
		selectEventArgument(val, type = true) {
			let params = this.selectEventInfo.output_params.filter((item) => item.code === val)
			if (params.length > 0) {
				this.selectEventArgumentInfo = params[0]
			} else {
				this.selectEventArgumentInfo = {}
			}
			if (type) {
				this.form.condition = null
				this.form.value = null
			}
		},
		//显示
		async open(ruleInfo) {
			this.ruleInfo = ruleInfo
			await this.getDetailInfo()
			await this.getProductList()
			this.visible = true
			this.form.tenant_id = this.ruleInfo.tenant_id
			this.form.campus_id = this.ruleInfo.campus_id
			this.form.alert_rule_id = this.ruleInfo.id

			return this
		},
		getProductTslList(product_id) {
			var reqData = {
				product_id: product_id,
				tenant_id: this.ruleInfo.tenant_id
			}
			this.$LotApi.productTsl.list.get(reqData).then((res) => {
				if (res.code === 200) {
					this.tslList = res.data
					let readTslList = []
					let eventTslList = []
					res.data.forEach((item) => {
						if (item.type_spec.specs) {
							item.type_spec.specs = JSON.parse(item.type_spec.specs)
						}
						if (item.type_spec.type === 'date' || item.type_spec.type === 'struct' || item.type_spec.type === 'array') {
							return
						}
						if (item.tsl_type === 1 && (item.access_mode === 1 || item.access_mode === 2)) {
							readTslList.push(item)
						}
						if (item.tsl_type === 2) {
							eventTslList.push(item)
						}
					})
					this.propertiesTsl = readTslList
					this.eventTsl = eventTslList
					if (this.form.tsl_id && this.form.trigger_mode === 1) {
						this.selectTsl(this.form.tsl_id, false)
					}
					if (this.form.tsl_id && this.form.trigger_mode === 2) {
						this.selectEventTsl(this.form.tsl_id, false)
						if (this.form.argument) {
							this.selectEventArgument(this.form.argument, false)
						}
					}
				} else {
					this.propertiesTsl = []
					this.eventTsl = []
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		getProductDevicelList(product_id) {
			var reqData = {
				product_id: product_id,
				tenant_id: this.ruleInfo.tenant_id,
				campus_id: this.ruleInfo.campus_id
			}
			this.$LotApi.device.all.get(reqData).then((res) => {
				if (res.code === 200) {
					this.deviceList = res.data
				} else {
					this.deviceList = []
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		async getDetailInfo() {
			var reqData = { id: this.ruleInfo.id, tenant_id: this.ruleInfo.tenant_id, campus_id: this.ruleInfo.campus_id }
			var res = await this.$LotApi.alertRule.one.get(reqData)
			this.isSaveing = false
			if (res.code === 200) {
				this.detailInfo = res.data
				this.rule_filter = res.data.alert_rule_filter
				if (this.rule_filter) {
					this.form = this.rule_filter
					console.log(this.form)
					await this.getProductTslList(this.form.product_id)
					await this.getProductDevicelList(this.form.product_id)
				}
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		async getProductList() {
			var reqData = {
				node_type: 2,
				tenant_id: this.ruleInfo.tenant_id,
				status: 1
			}
			var res = await this.$LotApi.product.all.get(reqData)
			this.isSaveing = false
			if (res.code === 200) {
				this.productList = res.data
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let subForm
					this.form.tenant_id = this.ruleInfo.tenant_id
					this.form.campus_id = this.ruleInfo.campus_id
					this.form.alert_rule_id = this.ruleInfo.id
					subForm = { ...this.form }
					var res = await this.$LotApi.alertRule.setRule.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, 'edit')
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		}
	}
}
</script>

<style></style>
