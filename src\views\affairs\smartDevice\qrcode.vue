<template>
	<el-drawer
		v-model="visible"
		size="650px"
		title="开门二维码"
		class="importExport"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<div ref="printMain">
			<div v-for="(item, index) in data" :key="index">
				<el-card shadow="never" class="scan">
					<p class="tips">扫码开门</p>
					<sc-qr-code :size="300" :text="item.scan_info" colorDark="#333" colorLight="#fff"></sc-qr-code>
					<p class="name">{{ item.name }}</p>
				</el-card>
				<div v-if="(index + 1) % 2 == 0" style="page-break-after: always;"></div>
			</div>
		</div>

		<template #footer>
			<div style="flex: auto">
				<el-button @click="visible = false">关闭</el-button>
				<el-button type="primary" @click="print">打印</el-button>
			</div>
		</template>
	</el-drawer>
</template>

<script>
import print from '@/utils/print'

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			visible: false,
			data: []
		}
	},
	computed: {},
	mounted() {},
	created() {},
	methods: {
		print() {
			//直接传入REF或者querySelector
			print(this.$refs.printMain)
		},
		open(data) {
			this.data = data
			this.visible = true
			return this
		},
		//表单注入数据
		setData(data) {
			data.entrance_year = data.entrance_year + ''
			Object.assign(this.form, data)
		}
	},
	components: {}
}
</script>

<style scoped lang="scss">
:deep.el-drawer__header {
	margin-bottom: 10px !important;
}

.scan {
	text-align: center;
	width: 400px;
	margin: 0px auto 50px;
	.tips {
		text-align: center;
		font-size: 24px;
		color: #333;
		font-weight: bold;
		line-height: 35px;
		margin-bottom: 15px;
	}

	.name {
		text-align: center;
		font-size: 18px;
		color: #000;
		margin-top: 10px;
		line-height: 35px;
		font-weight: bold;
	}
}
</style>
