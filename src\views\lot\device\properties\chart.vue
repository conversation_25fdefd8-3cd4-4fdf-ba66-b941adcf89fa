<template>
	<el-dialog v-model="visible" title="" width="700" destroy-on-close @closed="$emit('closed')">
		<!--		<el-button circle icon="el-icon-refresh" @click="refresh"></el-button>-->
		<div class="left-panel" style="width: 100%; margin-top: 10px">
			<div class="left-panel-search">
				<el-form-item label="">
					<el-date-picker
						v-model="params.time"
						type="datetimerange"
						:shortcuts="shortcuts"
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						value-format="YYYY-MM-DD HH:mm:ss"
						:disabled-date="disabledDate"
						@change="upsearch"
					/>
				</el-form-item>
				<el-form-item label="">
					<el-button icon="el-icon-refresh" @click="refresh">刷新</el-button>
				</el-form-item>
			</div>
		</div>
		<scEcharts class="scEcharts" :option="chartOption" width="100%" height="400px"></scEcharts>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import dayjs from 'dayjs'
import scEcharts from '@/components/scEcharts/index.vue'

export default {
	name: 'logList',
	data() {
		return {
			visible: false,
			loading: false,
			logData: [],
			propertiesInfo: {},
			shortcuts: [
				{
					text: '最近1小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 1)
						return [start, end]
					}
				},
				{
					text: '最近3小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 3)
						return [start, end]
					}
				},
				{
					text: '最近6小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 6)
						return [start, end]
					}
				},
				{
					text: '最近12小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 12)
						return [start, end]
					}
				},
				{
					text: '最近24小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 24)
						return [start, end]
					}
				}
			],
			chartOption: {
				color: ['#165DFF'],
				title: {
					text: '',
					subtext: ''
				},
				grid: {
					top: '60px',
					bottom: '50px'
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: [],
					boundaryGap: [0, '100%']
				},
				dataZoom: [
					{
						type: 'slider',
						start: 0,
						end: 100,
						height: 24
					}
				],
				yAxis: {
					type: 'value',
					boundaryGap: [0, '100%']
				},
				series: [
					{
						data: [],
						type: 'line',
						smooth: true,
						name: '设备属性',
						showSymbol: false,
						lineStyle: {
							color: '#165DFF' //线条颜色
						},
						areaStyle: {
							// 使用方法二的写法
							color: {
								type: 'linear',
								x: 0, //右
								y: 0, //下
								x2: 0, //左
								y2: 1, //上
								colorStops: [
									{
										offset: 0,
										color: '#4080FF' // 0% 处的颜色
									},
									{
										offset: 1,
										color: '#E8F3FF' // 100% 处的颜色
									}
								]
							}
						}
					}
				]
			},
			params: {
				tenant_id: null,
				campus_id: null,
				device_id: null,
				tsl_id: null,
				time: [],
				begin_time: null,
				end_time: null
			}
		}
	},
	components: { scEcharts },
	watch: {},
	created() {
		this.params.time = [dayjs().add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
	},
	computed: {},
	methods: {
		disabledDate(time) {
			const oneMonthLater = new Date()
			oneMonthLater.setMonth(oneMonthLater.getMonth() - 1) // 获取一个月前的日期
			// 返回true表示禁用, false表示不禁用
			// 禁用早于今天的日期和晚于一个月后的日期
			return time.getTime() > Date.now() || time.getTime() < oneMonthLater
		},
		show(data) {
			this.visible = true
			this.params.device_id = data.device_id
			this.params.tenant_id = data.tenant_id
			this.params.campus_id = data.campus_id
			this.params.tsl_id = data.propertiesInfo.tsl_id
			this.propertiesInfo = data.propertiesInfo
			this.chartOption.title.text = this.propertiesInfo.name + '(' + this.propertiesInfo.code + ')'
			this.chartOption.series[0].name = this.propertiesInfo.name + '(' + this.propertiesInfo.code + ')'
			this.chartOption.title.subtext = this.propertiesInfo.type_spec.specs.unit_symbol
				? '单位：' + (this.propertiesInfo.type_spec.specs.unit_symbol + '')
				: ''
			this.getList()
		},
		async getList() {
			this.loading = true
			if (this.params.time) {
				this.params.begin_time = this.params.time[0]
				this.params.end_time = this.params.time[1]
			} else {
				this.params.begin_time = null
				this.params.end_time = null
			}
			const res = await this.$LotApi.device.getDevicePropertiesChart.get(this.params)
			this.loading = false
			if (res.code === 200) {
				let categoryData = []
				let seriesData = []
				res.data.map((item, index) => {
					categoryData.push(item.report_time.substring(0, 16))
					seriesData.push(item.value)
				})
				categoryData.reverse()
				seriesData.reverse()
				this.chartOption.xAxis.data = categoryData
				this.chartOption.series[0].data = seriesData
			}
		},
		//搜索
		upsearch() {
			this.params.page = 1
			this.getList()
		},
		refresh() {
			this.params.page = 1
			this.params.time = [dayjs().add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
			this.getList()
		}
	}
}
</script>

<style lang="scss" scoped>
.left-panel {
	padding-bottom: 15px;
}

.page {
	padding: 20px;
	display: flex;
	justify-content: flex-end;
}

.payload {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
</style>
