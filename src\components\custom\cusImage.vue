<template>
	<el-image :src="src" :alt="alt" :fit="fit" :infinite="infinite" :preview-src-list="previewSrcList" :lazy="lazy"
		:preview-teleported="previewTeleported" :initial-index="initialIndex" :loading="loading"
		:scroll-container="scrollContainer" :close-on-press-escape="closeOnPressEscape"
		style="width: 100%; height: 100%">
		<template #error>
			<div class="image-slot" v-if="showDefaultImg">
				<img style="height: 50px" class="logo" src="@/assets/img/material_cover.png" />
			</div>
			<div class="image-slot" v-else>
				<el-icon>
					<Picture />
				</el-icon>
			</div>
		</template>
	</el-image>
</template>

<script>
import { Picture } from '@element-plus/icons-vue'
export default {
	name: 'CustomImage',
	components: {
		Picture
	},
	props: {
		// 图片地址
		src: {
			type: String,
			required: true
		},
		showDefaultImg: {
			type: Boolean,
			default: false
		},
		// 图片描述
		alt: {
			type: String,
			default: ''
		},
		// 图片填充模式
		fit: {
			type: String,
			default: 'fill'
		},
		// 图片加载状态
		loading: {
			type: String,
			default: 'lazy' //'lazy'|| 'eager'
		},
		// 是否无限循环预览
		infinite: {
			type: Boolean,
			default: false
		},
		// 预览图片列表
		previewSrcList: {
			type: Array,
			default: () => []
		},
		// 预览图片是否插入至 body 元素
		previewTeleported: {
			type: Boolean,
			default: true
		},
		// 预览图片初始索引
		initialIndex: {
			type: Number,
			default: 0
		},
		// 是否懒加载
		lazy: {
			type: Boolean,
			default: false
		},
		// 滚动容器选择器
		scrollContainer: {
			type: String,
			default: ''
		},
		// 是否可以通过按下 ESC 关闭图片预览
		closeOnPressEscape: {
			type: Boolean,
			default: true
		}
	},
	methods: {
		handleClick() {
			// 处理点击事件，可根据需要自定义逻辑
		}
	}
}
</script>

<style scoped>
.custom-image {
	/* 添加自定义样式，可根据需要进行修改 */
	text-align: center;
}

.image-slot {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	background: var(--el-fill-color-light);
	color: var(--el-border-color);
	font-size: 2.5em;
}
</style>
