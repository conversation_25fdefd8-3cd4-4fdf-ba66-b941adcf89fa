<template>
    <el-dialog v-model="visible" :title="title" width="600" destroy-on-close @close="close">
        <div class="search-box">
            <span>共{{ studentList.length }}人</span>
            <div>
                <el-select v-model="params.course_id" placeholder="选择科目" clearable style="width: 200px;margin-right: 10px"
                    @change="upsearch" v-if="showCourse">
                    <el-option v-for="item in courseList" :key="item.id" :label="item.course_name" :value="item.id" />
                </el-select>
                <el-input v-model="params.name" style="width: 200px" clearable placeholder="输入姓名搜索"
                    prefix-icon="el-icon-search" @change="upsearch" />
            </div>
        </div>
        <scTable ref="table" row-key="id" :data="studentList" height="500px" :hideDo="true" hidePagination>
            <el-table-column label="学号/考号" prop="serial_number"></el-table-column>
            <el-table-column label="姓名" prop="student_name"></el-table-column>
            <el-table-column label="原班级" prop="grade_info">
                <template #default="{ row }">
                    {{ row.grade_info.name }}{{ row.class_info?.name || '' }}
                </template>
            </el-table-column>
            <el-table-column label="座位号" prop="seat_number"></el-table-column>
            <el-table-column label="科目" prop="course_info" v-if="showCourse">
                <template #default="{ row }">
                    {{ row.course_info?.name }}
                </template>
            </el-table-column>
        </scTable>
    </el-dialog>
</template>

<script setup>
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()

const props = defineProps({
    params: {
        type: Object,
        default: () => { }
    },
    showCourse: {
        type: Boolean,
        default: false
    },
    courseList: {
        type: Array,
        default: []
    }
})
const defaultParams = () => {
    return {
        campus_id: campusId,
        tenant_id: tenantId,
        examine_id: Number(query.id),
        room_id: null,
        name: null,
        course_id: null,
    }
}
const visible = ref(false)
const title = ref('')
const params = ref(defaultParams())

const upsearch = () => {
    getOneRoom()
}
const open = (row) => {
    title.value = row.room_info.name
    params.value.room_id = row.room_id
    params.value.course_id = props.params.course_id
    getOneRoom()
    visible.value = true
}
const close = () => {
    visible.value = false
}

defineExpose({
    open,
    close
})
const studentList = ref([])
const getOneRoom = () => {
    globalPropValue.examine.roomStudentList.get(params.value).then(res => {
        if (res.code === 200) {
            studentList.value = res.data || []
            console.log(res)
        }
    })
}
</script>

<style lang="scss" scoped>
.search-box {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>