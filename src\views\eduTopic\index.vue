<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList.length > 1" label="">
						<el-select v-model="params.obj.campus_id" placeholder="校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.obj.grade_id" placeholder="请选择年级" filterable clearable>
							<el-option v-for="item in myGrade.list" :key="item.id" :label="item.grade_name" :value="item.id" />
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.obj.course_id" placeholder="请选择学科" filterable clearable>
							<el-option
								v-for="item in course.list"
								:key="item.id"
								:label="item.course_name"
								:value="item.id"
							></el-option>
						</el-select>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-plus" @click="add">新增题目</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="myScTable" row-key="id" :params="params.obj" :apiObj="apiObj">
				<el-table-column label="类型" prop="topic_type">
					<template #default="{ row }">
						<span v-if="row.topic_type === 1">判断题</span>
						<span v-if="row.topic_type === 2">单选题</span>
						<span v-if="row.topic_type === 3">多选题</span>
					</template>
				</el-table-column>
				<el-table-column label="题目" prop="topic_name">
					<template #default="{ row }">
						<div v-html="row.topic_name"></div>
					</template>
				</el-table-column>
				<el-table-column label="分值" prop="topic_score"></el-table-column>
				<el-table-column label="年级" prop="grade_name"></el-table-column>
				<el-table-column label="学科" prop="course_name"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center">
					<template #default="{ row }">
						<el-button-group>
							<el-button text type="primary" size="small" @click="table_edit(row)">编辑</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(row)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<addTopic ref="addTopicRef" :course="course.list" @suc="success"></addTopic>
	</el-container>
</template>

<script setup>
import { reactive, ref, toRef, computed, onMounted, getCurrentInstance, watch } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage, ElAlert } from 'element-plus'
import addTopic from './addTopic.vue'
const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		grade_id: '',
		discipline_id: '',
		course_id: '',
		topic_type: ''
	}
}
const { proxy } = getCurrentInstance()
let apiObj = proxy.$API.eduWork.storeList
const myScTable = ref(null)
const addTopicRef = ref(null)
let params = reactive({
	obj: defaultParams()
})
let CampusManagementList = reactive(campusInfo)
let myGrade = reactive({
	list: []
})
const disciplineOptions = reactive({
	list: []
})
const course = reactive({
	list: []
})
let getGrade = computed(() => {
	return myGrade.list.filter(
		(v) => v.parent_id != 0 && v.semester_id == params.obj.semester_id && v.campus_id == params.obj.campus_id
	)
})
let getCourse = computed(() => {
	return course.list.filter((item) => {
		return item.campus_id == params.obj.campus_id && item.discipline_id == params.obj.discipline_id
	})
})
const getGradeData = async () => {
	var res = await proxy.$API.eduGradeClass.grade.all.get(params.obj)
	myGrade.list = res.data
	console.log(myGrade.list)
}
const getDisciplineOptions = async () => {
	let { data } = await proxy.$API.eduDiscipline.discipline.all.get({ tenant_id: tenantId, campus_id: campusId })
	disciplineOptions.list = data
}
const getEduCourse = async () => {
	let { data } = await proxy.$API.eduCourseSet.course.all.get({ tenant_id: tenantId, campus_id: campusId })
	course.list = data
}
const upsearch = () => {
	myScTable.value.upData(params.obj)
}
const refresh = () => {
	params.obj = defaultParams()
	upsearch()
}
const success = () => {
	myScTable.value.refresh()
}
const add = () => {
	addTopicRef.value.show('add')
}
const table_edit = (row) => {
	addTopicRef.value.show('edit', row)
}
const table_del = async (row) => {
	var reqData = { id: row.id, tenant_id: params.obj.tenant_id, campus_id: params.obj.campus_id }
	var res = await proxy.$API.eduWork.delStore.post(reqData)
	if (res.code === 200) {
		ElMessage.success('删除成功')
		success()
	} else {
		ElAlert(res.message, '提示', { type: 'error' })
	}
}
onMounted(() => {
	getGradeData()
	getDisciplineOptions()
	getEduCourse()
})
</script>

<style lang="scss" scoped></style>
