<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script setup>
import set from './set'
import panel from './panel'
import { ref } from 'vue'
const activeName = ref('panel')
const currComponent = ref({
	name: 'panel',
	component: panel
})
const tabs = [
	 {
		name: 'panel',
		label: '教室看板',
		component: panel
	}, 
	{
		name: 'set',
		label: '教室物联关联设置',
		component: set
	}
]
const handleClick = (name) => {
	currComponent.value = tabs.find((item) => item.name === name)
}
</script>
<style lang="scss" scoped></style>
