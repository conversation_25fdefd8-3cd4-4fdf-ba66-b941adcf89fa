/* 全局 */
#app,
body,
html {
	width: 100%;
	height: 100%;
	background-color: #f6f8f9;
	font-size: 12px;
}
#app, body{
	min-width: 1600px;
}
#app, body.mobileWd{
	min-width: unset;
}

a {
	color: #333;
	text-decoration: none;
}

a:hover,
a:focus {
	color: #000;
	text-decoration: none;
}

a:link {
	text-decoration: none;
}

a:-webkit-any-link {
	text-decoration: none;
}

a,
button,
input,
textarea {
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	box-sizing: border-box;
	outline: none !important;
	-webkit-appearance: none;
}

ol,
ul {
  list-style: none;
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	outline: none;
}

/* 大布局样式 */
.aminui {
	display: flex;
	flex-flow: column;
}

.aminui-wrapper {
	display: flex;
	flex: 1;
	overflow: auto;
	height: calc(100vh - 60px);
}
.layout-default .aminui-wrapper {
	height: 100vh;
}
.layout-menu .aminui-wrapper {
	height: calc(100vh - 60px);
}
.layout-dock .aminui-wrapper {
	height: calc(100vh - 60px);
}

/* 全局滚动条样式 */
.scrollable {
	-webkit-overflow-scrolling: touch;
}

::-webkit-scrollbar {
	width: 5px;
	height: 5px;
}

::-webkit-scrollbar-thumb {
	background-color: rgba(50, 50, 50, 0.3);
}

::-webkit-scrollbar-thumb:hover {
	background-color: rgba(50, 50, 50, 0.6);
}

::-webkit-scrollbar-track {
	background-color: rgba(50, 50, 50, 0.1);
}

::-webkit-scrollbar-track:hover {
	background-color: rgba(50, 50, 50, 0.2);
}

/*布局设置*/
.layout-setting {
	position: fixed;
	width: 40px;
	height: 40px;
	border-radius: 3px 0 0 3px;
	bottom: 100px;
	right: 0px;
	z-index: 100;
	background: #409eff;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.layout-setting i {
	font-size: 18px;
	color: #fff;
}

/* 头部 */
.adminui-header {
	height: 60px;
	background: #fff;
	color: #3b3e3f;
	display: flex;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: space-between;
}

.adminui-header-left {
	display: flex;
	align-items: center;
	padding-left: 15px;
}

.adminui-header-right {
	display: flex;
	align-items: center;
}

.adminui-header .logo-bar {
	font-size: 16px;
	font-weight: bold;
	display: flex;
	align-items: center;
	width: 180px;
	margin-right: 10px;
}

.adminui-header .logo-bar .logo {
	margin-right: 5px;
	width: 40px;
	height: 40px;
	background: #fff;
}

.adminui-header .nav {
	display: flex;
	//height: 100%;
	height: 45px;
}

.adminui-header .nav li {
	padding: 0 15px;
	margin: 0 5px 0 0;
	font-size: 15px;
	border-radius: 6px;
	color: #3b3e3f;
	list-style: none;
	height: 100%;
	display: flex;
	align-items: center;
	cursor: pointer;
}

.adminui-header .nav li i {
	margin-right: 5px;
}

.adminui-header .nav li:hover {
	color: #3b3e3f;
	background: #d9ecff;
}

.adminui-header .nav li.active {
	background: #c6e2ff;
	color: #409eff;
}

.adminui-header .user-bar .panel-item:hover {
	background: rgba(255, 255, 255, 0.1) !important;
}

.adminui-header .user-bar .user label {
	color: #3b3e3f;
}
.dockSubMenuModal{
	top: 60px;
}
.dockSubMenu{
	.el-drawer__body{
		padding-left: 205px !important;
		padding-right: 205px !important;
		padding-top: 20px !important;
		padding-bottom: 20px !important;
		background: #F6F8FA;
		height: auto;
	}
	.two_menus {
		width: 1000px;
		overflow: hidden;
		.two_menu_item {
			cursor: pointer;
			margin-bottom: 10px;
			margin-right: 25px;
			width: 85px;
			height: 95px;
			float: left;

			.icon_item {
				display: flex;
				justify-content: center; /* 水平居中 */
				align-items: center;    /* 垂直居中 */
				height: 50px;
				width: 50px;
				line-height: 50px;
				margin: 0 auto;
				font-size: 30px;
				border-radius: 50%;
				background-color: var(--el-color-primary-light-8);
				color: var(--el-color-primary);
			}

			.secondary_menu_title_box {
				margin-top: 10px;
				height: 20px;
				line-height: 20px;
				font-size: 13px;
				//font-weight: bold;
				width: 85px;
				color: #606266;
				text-align: center;
			}
		}
	}

}


/* 左侧菜单 */
.aminui-side-split {
	width: 70px;
	flex-shrink: 0;
	background: #fff;
	display: flex;
	flex-flow: column;
	border-right: 1px solid var(--el-border-color-light);
}

.aminui-side-split-top {
	height: 49px;
}

.aminui-side-split-top a {
	display: inline-block;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.aminui-side-split-top .logo {
	height: 45px;
	width: 45px;
	border-radius: 50%;
	background: #fff;
	vertical-align: bottom;
}

.adminui-side-split-scroll {
	overflow: auto;
	overflow-x: hidden;
	height: 100%;
	flex: 1;
}

.aminui-side-split li {
	cursor: pointer;
	width: 60px;
	height: 60px;
	color: #303133;
	border-radius: 6px;
	margin: 5px 5px;
	font-size: 13px;
	text-align: center;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.aminui-side-split li i {
	font-size: 18px;
}

.aminui-side-split li p {
	margin-top: 5px;
}

.aminui-side-split li:hover {
	background: rgba(255, 255, 255, 0.1);
}

.aminui-side-split li.active {
	background: #e8f3ff;
	color: #409eff;
}

.adminui-side-split-scroll::-webkit-scrollbar-thumb {
	background-color: rgba(255, 255, 255, 0.4);
	border-radius: 5px;
}

.adminui-side-split-scroll::-webkit-scrollbar-thumb:hover {
	background-color: rgba(255, 255, 255, 0.5);
}

.adminui-side-split-scroll::-webkit-scrollbar-track {
	background-color: rgba(255, 255, 255, 0);
}

.adminui-side-split-scroll::-webkit-scrollbar-track:hover {
	background-color: rgba(255, 255, 255, 0);
}

.aminui-side {
	display: flex;
	flex-flow: column;
	flex-shrink: 0;
	width: 200px;
	background: #fff;
	border-right: 1px solid var(--el-border-color-light);
	transition: width 0.3s;
}

.adminui-side-top {
	border-bottom: 1px solid var(--el-border-color-light);
	height: 40px;
	line-height: 40px;
}

.adminui-side-top h2 {
	padding: 0 20px;
	font-size: 14px;
	font-weight: bold;
	color: #3c4a54;
}

.adminui-side-scroll {
	overflow: auto;
	overflow-x: hidden;
	flex: 1;
	padding-top: 10px;
}

.adminui-side-bottom {
	border-top: 1px solid var(--el-border-color-light);
	height: 40px;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
}

.adminui-side-bottom i {
	font-size: 16px;
}

.adminui-side-bottom:hover {
	color: var(--el-color-primary);
}

.aminui-side.isCollapse {
	width: 65px;
}

.el-menu .menu-tag {
	position: absolute;
	height: 18px;
	line-height: 18px;
	background: var(--el-color-danger);
	font-size: 12px;
	color: #fff;
	right: 20px;
	border-radius: 18px;
	padding: 0 6px;
}

.el-menu .el-sub-menu__title .menu-tag {
	right: 40px;
}

.el-menu--horizontal > li .menu-tag {
	display: none;
}
.el-menu--horizontal {
	height: unset;
}

/* 右侧内容 */
.aminui-body {
	flex: 1;
	display: flex;
	flex-flow: column;
}

.adminui-topbar {
	height: 50px;
	border-bottom: 1px solid var(--el-border-color-light);
	background: #fff;
	//box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
	display: flex;
	justify-content: space-between;
}

.adminui-topbar-menu {
	height: 50px;
	border-bottom: unset;
	color: #3b3e3f;
	//box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
	display: flex;
	background: unset;
	justify-content: space-between;
}

.adminui-topbar-menu .el-breadcrumb {
	margin-left: 0px;
}

.adminui-topbar-menu .el-breadcrumb__inner,
.adminui-topbar-menu .el-breadcrumb__separator {
	color: #606266;
}

.adminui-topbar-menu .el-breadcrumb__item:last-child .el-breadcrumb__inner,
.adminui-topbar-menu .el-breadcrumb__item:last-child .el-breadcrumb__inner a,
.adminui-topbar-menu .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover,
.adminui-topbar-menu .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
	color: #606266;
}

.adminui-topbar .left-panel {
	display: flex;
	align-items: center;
}

.adminui-topbar .right-panel {
	display: flex;
	align-items: center;
}

.right-panel-search {
	display: flex;
	align-items: center;
}

.right-panel-search > * + * {
	margin-left: 10px;
}

.adminui-tags {
	height: 35px;
	padding-top: 4px;
	background: #fff;
	border-bottom: 1px solid var(--el-border-color-light);
}

.adminui-tags ul {
	display: flex;
	overflow: hidden;
}

.adminui-tags li {
	cursor: pointer;
	display: inline-block;
	height: 25px;
	line-height: 25px;
	position: relative;
	border-radius: 5px;
	margin-left: 8px;
	flex-shrink: 0;
}

.adminui-tags li::after {
	content: ' ';
	width: 1px;
	height: 100%;
	position: absolute;
	right: 0px;
	background-image: linear-gradient(#fff, #e6e6e6);
}

.adminui-tags li a {
	display: inline-block;
	padding: 0 10px;
	width: 100%;
	height: 100%;
	color: #999;
	text-decoration: none;
	display: flex;
	align-items: center;
}

.adminui-tags li i {
	margin-left: 3px;
	border-radius: 3px;
	width: 15px;
	height: 15px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.adminui-tags li i:hover {
	background: rgba(0, 0, 0, 0.2);
	color: #fff;
}

.adminui-tags li:hover {
	background: #ecf5ff;
}

.adminui-tags li.active {
	background: #409eff;
}

.adminui-tags li.active a {
	color: #fff;
}

.adminui-tags li.sortable-ghost {
	opacity: 0;
}

.adminui-main {
	overflow: auto;
	background-color: #f6f8f9;
	flex: 1;
}

/*页面最大化*/
.aminui.main-maximize {
	.main-maximize-exit {
		display: block;
	}

	.aminui-side-split,
	.aminui-side,
	.adminui-header,
	.adminui-topbar,
	.adminui-tags {
		display: none;
	}
}

.main-maximize-exit {
	display: none;
	position: fixed;
	z-index: 3000;
	top: -20px;
	left: 50%;
	margin-left: -20px;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	cursor: pointer;
	background: rgba(0, 0, 0, 0.2);
	text-align: center;
}

.main-maximize-exit i {
	font-size: 14px;
	margin-top: 22px;
	color: #fff;
}

.main-maximize-exit:hover {
	background: rgba(0, 0, 0, 0.4);
}

/*定宽页面*/
.sc-page {
	width: 1230px;
	margin: 0 auto;
}

.mobile_place {
	width: 100%;
	height: 40px;
}

.aminui-side .el-menu-item {
	height: 40px;
	line-height: 40px;
}
.aminui-side .el-sub-menu__title {
	height: 40px;
	line-height: 40px;
}
.aminui-side .el-menu-item.is-active {
	color: var(--el-menu-active-color);
	background: var(--el-color-primary-light-8);
}
.el-button.is-text{
	padding: 0 6px;
}
.el-input__validateIcon {
	display: none;
}
