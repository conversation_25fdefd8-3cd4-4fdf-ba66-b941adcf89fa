import MY_CONFIG from './myConfig'
const DEFAULT_CONFIG = {
	//标题
	APP_NAME: import.meta.env.VITE_APP_TITLE,

	//首页地址
	DASHBOARD_URL: '/work_dashboard',

	//版本号
	APP_VER: 'v1.0',

	//内核版本号
	CORE_VER: 'v1.0',

	//接口地址
	//API_URL: '/api',
	API_URL:
		import.meta.env.VITE_NODE_ENV === 'development' && import.meta.env.VITE_APP_PROXY === 'true'
			? '/api'
			: import.meta.env.VITE_APP_API_BASEURL,

	MOCK_URL:
		import.meta.env.VITE_NODE_ENV === 'development' && import.meta.env.VITE_APP_PROXY === 'true'
			? '/mock'
			: import.meta.env.VITE_APP_MOCK_BASEURL,

	//请求超时
	TIMEOUT: 10000,

	//TokenName
	TOKEN_NAME: 'Authorization',

	//Token前缀，注意最后有个空格，如不需要需设置空字符串
	TOKEN_PREFIX: 'bearer ',

	//追加其他头
	HEADERS: {},

	//请求是否开启缓存
	REQUEST_CACHE: false,

	//布局 默认：default | 通栏：header | 经典：menu | 功能坞：dock
	//dock将关闭面包屑栏
	LAYOUT: 'header',

	LAYOUT_SIZE: 'default',
	//菜单是否折叠
	MENU_IS_COLLAPSE: false,

	//菜单是否启用手风琴效果
	MENU_UNIQUE_OPENED: true,

	//是否开启多标签
	LAYOUT_TAGS: true,

	//是否开启水印
	LAYOUT_WATER_MARK: false,

	LAYOUT_WATER_MARK_TEXT: '智慧校园',
	LAYOUT_WATER_MARK_SUBTEXT: '',
	//语言
	LANG: 'zh-cn',

	//主题颜色
	COLOR: '#165DFF', //#0960BD

	//是否加密localStorage, 为空不加密，可填写AES(模式ECB,移位Pkcs7)加密
	LS_ENCRYPTION: import.meta.env.VITE_NODE_ENV === 'development' ? '' : 'AES',

	//localStorageAES加密秘钥，位数建议填写8的倍数
	LS_ENCRYPTION_key: 'USMEOX5V8N3LOJQM',

	//控制台首页默认布局
	DEFAULT_GRID: {
		//默认分栏数量和宽度 例如 [24] [18,6] [8,8,8] [6,12,6]
		layout: [18, 6],
		//小组件分布，com取值:views/home/<USER>
		copmsList: [['echarts'], ['time']]
	}
}

//合并业务配置
Object.assign(DEFAULT_CONFIG, MY_CONFIG)

// 如果生产模式，就合并动态的APP_CONFIG
// public/config.js
if (process.env.NODE_ENV === 'production') {
	Object.assign(DEFAULT_CONFIG, APP_CONFIG)
}

export default DEFAULT_CONFIG
