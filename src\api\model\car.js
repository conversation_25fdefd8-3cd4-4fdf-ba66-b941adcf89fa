import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		//url: `${config.MOCK_URL}/system/user/list`,
		url: `${config.API_URL}/manapi/car/list`,
		name: '获取用户列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/manapi/car/save`,
		name: '新增用户/修改',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},

	del: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/manapi/car/del`,
		name: '删除用户',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	}
}
