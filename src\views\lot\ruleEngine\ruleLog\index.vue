<template>
	<el-container>
		<el-header>
			<div class="left-panel" style="width: 100%">
				<div class="left-panel-search">
					<el-form-item label="">
						<el-select
							v-if="CampusManagementList.length > 1"
							v-model="params.campus_id"
							placeholder="请选择校区"
							filterable
							style="width: 150px"
						>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="请输入内容搜索" clearable></el-input>
					</el-form-item>
					<el-form-item label="">
						<el-date-picker
							v-model="params.time"
							type="datetimerange"
							range-separator="至"
							start-placeholder="开始时间"
							end-placeholder="结束时间"
							value-format="YYYY-MM-DD HH:mm:ss"
						/>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.status" style="width: 150px" placeholder="执行状态" clearable>
							<el-option label="执行成功" :value="1"></el-option>
							<el-option label="执行失败" :value="-1"></el-option>
						</el-select>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<!--<div class="right-panel">
						<el-button type="primary" icon="el-icon-CirclePlus" @click="add">新增告警规则</el-button>
			</div>-->
		</el-header>
		<el-main>
			<scTable
				ref="table"
				size="small"
				row-key="id"
				:page-size="10"
				:pageSizes="[10, 20, 30, 50, 100]"
				:apiObj="list.apiObj"
				:params="params"
				:renderDataFunc="renderDataFunc"
				:hideDo="false"
			>
				<el-table-column label="编号" prop="log_id" width="130" fixed="left"></el-table-column>
				<el-table-column label="触发时间" prop="created_at" width="135"></el-table-column>
				<el-table-column label="规则名称" prop="engine_name" width="150" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.engine_info">{{ scope.row.engine_info.engine_name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="触发方式" prop="engine_type" width="80">
					<template #default="scope">
						<span v-if="scope.row.engine_info?.engine_type === 1">设备触发</span>
						<span v-if="scope.row.engine_info?.engine_type === 2">定时触发</span>
					</template>
				</el-table-column>
				<el-table-column label="触发场室" prop="room" width="180" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.room_info">{{ scope.row.room_info.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="触发情况" prop="name" min-width="200">
					<template #default="scope">
						<el-tooltip v-if="scope.row.engine_info?.engine_type === 1" :raw-content="true">
							<template #content>
								<p>设备：{{ scope.row.name.deviceName }}({{ scope.row.name.deviceKey }})</p>
								<p>触发规则：{{ scope.row.name.engineName }}</p>
								<template v-if="scope.row.name.triggerMode === 1">
									<p>触发属性：{{ scope.row.name.tslName }}({{ scope.row.name.tslCode }})</p>
									<p>属性值：{{ scope.row.name.valueFormate }}</p>
								</template>
								<template v-if="scope.row.name.triggerMode === 2">
									<p>触发事件：{{ scope.row.name.tslName }}({{ scope.row.name.tslCode }})</p>
									<p>触发参数：{{ scope.row.name.valueFormate }}</p>
								</template>
								<template v-if="scope.row.name.triggerMode === 3">
									<p>触发原因：{{ scope.row.name.statusDesc }}</p>
								</template>
							</template>
							<div class="overflowTips">
								设备：{{ scope.row.name.deviceName }}({{ scope.row.name.deviceKey }})；触发规则：{{
									scope.row.name.engineName
								}}；
								<template v-if="scope.row.name.triggerMode === 1">
									触发属性：{{ scope.row.name.tslName }}({{ scope.row.name.tslCode }})； 属性值：{{
										scope.row.name.valueFormate
									}}；
								</template>
								<template v-if="scope.row.name.triggerMode === 2">
									触发事件：{{ scope.row.name.tslName }}({{ scope.row.name.tslCode }})；
								</template>
								<template v-if="scope.row.name.triggerMode === 3">
									触发原因：{{ scope.row.name.statusDesc }}；
								</template>
							</div>
						</el-tooltip>
						<template v-if="scope.row.engine_info?.engine_type === 2">
							<el-tooltip :raw-content="true">
								<template #content>
									{{ scope.row.name }}
								</template>
								<div class="overflowTips">{{ scope.row.name }}</div>
							</el-tooltip>
						</template>
					</template>
				</el-table-column>
				<el-table-column label="状态" prop="status" width="120">
					<template #default="scope">
						<span v-if="scope.row.status === -1">
							<sc-status-indicator type="danger"> </sc-status-indicator> 执行失败
						</span>
						<span v-if="scope.row.status === 1">
							<sc-status-indicator type="success"> </sc-status-indicator> 执行成功
						</span>
					</template>
				</el-table-column>
				<el-table-column label="执行详情" prop="message" min-width="300">
					<template #default="scope">
						<el-tooltip v-if="scope.row.status === 1" :raw-content="true">
							<template #content>
								<div v-for="(item, index) in scope.row.message" :key="index">
									<p>
										设备：{{ item.deviceName }}({{ item.deviceKey }})；设置属性：{{ item.tslName }}({{
											item.tslCode
										}})；设置值：{{ item.valueFormate }}
									</p>
								</div>
							</template>
							<div class="overflowTips">
								<span v-for="(item, index) in scope.row.message" :key="index">
									设备：{{ item.deviceName }}({{ item.deviceKey }})；设置属性：{{ item.tslName }}({{
										item.tslCode
									}})；设置值：{{ item.valueFormate }}
								</span>
							</div>
						</el-tooltip>
						<span v-else>{{ scope.row.message }}</span>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
</template>

<script>
import ScTable from '@/components/scTable/index.vue'
import { ElMessageBox } from 'element-plus'
import cusTom from '@/utils/cusTom'
import ScStatusIndicator from '@/components/scMini/scStatusIndicator.vue'
import dayjs from 'dayjs'
const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		campus_id: campusId,
		alert_level: null,
		status: null,
		time: null,
		begin_time: null,
		end_time: null
	}
}

export default {
	name: 'alertList',
	data() {
		return {
			groupFilterText: '',
			groupData: [],
			CampusManagementList: [],
			enumConfig: [],
			alertLevelStatus: {
				1: 'danger',
				2: 'warning',
				3: 'primary',
				4: 'info'
			},
			alertLevelMap: {
				1: '紧急',
				2: '重要',
				3: '次要',
				4: '提示'
			},
			list: {
				apiObj: this.$LotApi.ruleEngine.getLog
			},
			alertTotal: 0,
			chartDataMap: {
				1: 0,
				2: 0,
				3: 0,
				4: 0
			},
			params: defaultParams(),
			search: {
				name: null
			},
			dialog: {
				save: false,
				ruleSet: false
			}
		}
	},
	components: {
		ScStatusIndicator,
		ScTable
	},
	watch: {},
	created() {
		/*this.params.time = [dayjs().add(-7, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
		this.params.begin_time = this.params.time ? this.params.time[0] : null
		this.params.end_time = this.params.time ? this.params.time[1] : null*/
		this.CampusManagementList = campusInfo
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	computed: {},
	methods: {
		renderDataFunc(data) {
			data.map((v) => {
				if (v.engine_info?.engine_type === 1) {
					v.name = JSON.parse(v.name)
				}
				if (v.status === 1 && v.message) {
					v.message = JSON.parse(v.message)
				}
			})
			return data
		},
		//搜索
		upsearch() {
			this.params.begin_time = this.params.time ? this.params.time[0] : null
			this.params.end_time = this.params.time ? this.params.time[1] : null
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.params.time = [dayjs().add(-7, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
			this.params.begin_time = this.params.time ? this.params.time[0] : null
			this.params.end_time = this.params.time ? this.params.time[1] : null
			this.upsearch()
		},
		//新增职位
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', this.params.campus_id)
			})
		},
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.$refs.table.refresh()
			}
		},
		//编辑
		table_edit(row) {
			ElMessageBox.prompt('处理结果', '处理', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputPlaceholder: '请输入处理结果',
				inputType: 'textarea'
			})
				.then(async ({ value }) => {
					var reqData = {
						alert_id: row.id,
						tenant_id: row.tenant_id,
						campus_id: row.campus_id,
						status: 1,
						remark: value
					}
					var res = await this.$LotApi.alertRule.treatedAlert.post(reqData)
					if (res.code === 200) {
						this.$message.success('操作成功')
						this.upsearch()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
				.catch(() => {})
		},
		table_rule(row) {
			this.dialog.ruleSet = true
			this.$nextTick(() => {
				this.$refs.ruleSetDialog.open(row)
			})
		},
		//查看
		table_show(row) {
			/*this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})*/
			this.$router.push({
				name: 'lotSetTemplateTsl',
				params: {
					id: row.id
				}
			})
		},
		//删除
		async table_del(row) {
			var reqData = { alert_id: row.id, tenant_id: row.tenant_id, campus_id: row.campus_id, status: 2 }
			ElMessageBox.confirm('确定要忽略此告警吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.alertRule.treatedAlert.post(reqData)
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.el-header {
	display: unset;
	.plate {
		margin-bottom: 10px;
		width: 100%;
		height: 150px;
		display: flex;
	}
	.chart {
		width: 220px;
		padding-left: 10px;
	}
	.chartData {
		padding-top: 20px;
		width: 500px;
	}
	.chartData-item {
		padding-bottom: 15px;
	}
}
.left-panel {
}
.overflowTips {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
</style>
