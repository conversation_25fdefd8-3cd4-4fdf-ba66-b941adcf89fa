import config from '@/config'
import http from '@/utils/request'

export default {
	period: {
		url: `${config.API_URL}/eduapi/periods/list`,
		name: '获取课程时段名称列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	periodAdd: {
		url: `${config.API_URL}/eduapi/periods/creat`,
		name: '添加课程时段名称',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	periodEdit: {
		url: `${config.API_URL}/eduapi/periods/edit`,
		name: '编辑课程时段名称',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	periodDel: {
		url: `${config.API_URL}/eduapi/periods/del`,
		name: '删除课程时段名称',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	coursePeriod: {
		url: `${config.API_URL}/eduapi/course_periods/all`,
		name: '获取所有课程时段列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	coursePeriodAdd: {
		url: `${config.API_URL}/eduapi/course_periods/creat`,
		name: '添加课程时段',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	coursePeriodEdit: {
		url: `${config.API_URL}/eduapi/course_periods/edit`,
		name: '编辑课程时段',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	coursePeriodDel: {
		url: `${config.API_URL}/eduapi/course_periods/del`,
		name: '删除课程时段',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	rules: {
		list: {
			url: `${config.API_URL}/eduapi/class_rule/list`,
			name: '获取课程规则列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/eduapi/class_rule/all`,
			name: '获取所有课程规则列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add: {
			url: `${config.API_URL}/eduapi/class_rule/creat`,
			name: '添加课程规则',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		edit: {
			url: `${config.API_URL}/eduapi/class_rule/edit`,
			name: '编辑课程规则',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		del: {
			url: `${config.API_URL}/eduapi/class_rule/del`,
			name: '删除课程规则',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		copy: {
			url: `${config.API_URL}/eduapi/class_rule/copy`,
			name: '复制课程规则',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		detail: {
			url: `${config.API_URL}/eduapi/class_rule/one`,
			name: '获取课程规则详情',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		set: {
			url: `${config.API_URL}/eduapi/class_rule/set`,
			name: '保存课程规则',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		}
	},
	schedule: {
		myschedule: {
			url: `${config.API_URL}/api/common/schedule`,
			name: '我的课表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		list: {
			url: `${config.API_URL}/eduapi/class_schedule/list`,
			name: '获取课程排课列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add: {
			url: `${config.API_URL}/eduapi/class_schedule/creat`,
			name: '新增排课',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		sync: {
			url: `${config.API_URL}/eduapi/class_schedule/sync`,
			name: '同步课表',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		edit: {
			url: `${config.API_URL}/eduapi/class_schedule/edit`,
			name: '修改排课',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		},
		del: {
			url: `${config.API_URL}/eduapi/class_schedule/del`,
			name: '删除排课',
			post: async function (params) {
				return await http.post(this.url, params)
			}
		},
		smart: {
			url: `${config.API_URL}/eduapi/class_schedule/smart`,
			name: '一键排课',
			post: async function (params, fig) {
				return await http.post(this.url, params, fig)
			}
		},
		detail: {
			url: `${config.API_URL}/eduapi/class_schedule/detail`,
			name: '排课详情',
			get: async function (params) {
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/class_schedule/save`,
			name: '保存排课',
			post: async function (params) {
				return await http.post(this.url, params)
			}
		},
		check: {
			url: `${config.API_URL}/eduapi/class_schedule/check`,
			name: '排课冲突检测',
			post: async function (params) {
				return await http.post(this.url, params)
			}
		}
	}
}
