<template>
	<div class="left-panel" style="width: 100%">
		<div class="left-panel-search">
			<el-form-item label="">
				<el-select v-model="params.tsl_id" style="width: 150px" placeholder="请选择服务属性" clearable filterable>
					<el-option v-for="(item, index) in tslList" :key="index" :label="item.name" :value="item.id"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="">
				<el-date-picker
					v-model="params.time"
					type="datetimerange"
					:shortcuts="shortcuts"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					value-format="YYYY-MM-DD hh:mm:ss"
				/>
			</el-form-item>
			<el-form-item label="">
				<el-button type="primary" icon="el-icon-search" @click="upsearch">查询</el-button>
				<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
			</el-form-item>
		</div>
	</div>
	<el-table ref="table" empty-text="当前所选日期时间内无数据" row-key="id" :data="logData" size="small">
		<el-table-column label="时间" prop="report_time" width="180"></el-table-column>
		<el-table-column label="服务名称" prop="tsl_info" width="150">
			<template #default="scope">
				{{ scope.row.tsl_info?.name }}
			</template>
		</el-table-column>
		<el-table-column label="服务标识符" prop="tsl_info" width="150">
			<template #default="scope">
				{{ scope.row.tsl_info?.code }}
			</template>
		</el-table-column>
		<el-table-column label="输入参数" prop="input_params" min-width="200" show-overflow-tooltip> </el-table-column>
		<el-table-column label="输出参数" prop="output_params" min-width="200" show-overflow-tooltip> </el-table-column>
	</el-table>
	<div class="page">
		<el-pagination
			v-if="total > 0"
			v-model:current-page="params.page"
			:page-sizes="[10, 20, 30, 50, 100]"
			:page-size="params.pageSize"
			size="small"
			background
			layout="total,sizes, prev, pager, next,jumper"
			:total="total"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		/>
	</div>
</template>

<script>
import cusTom from '@/utils/cusTom'

const defaultParams = () => {
	return {
		tenant_id: null,
		campus_id: null,
		device_id: null,
		tsl_id: null,
		time: null,
		begin_time: null,
		end_time: null,
		page: 1,
		pageSize: 10
	}
}

export default {
	name: 'serviceList',
	props: {
		deviceInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			currentPage1: 1,
			total: 0,
			logData: [],
			tslList: [],
			enumConfig: {},
			params: defaultParams(),
			shortcuts: [
				{
					text: '最近1小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 1)
						return [start, end]
					}
				},
				{
					text: '最近12小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 12)
						return [start, end]
					}
				},
				{
					text: '最近24小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 24)
						return [start, end]
					}
				}
			]
		}
	},
	components: {},
	watch: {},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
		if (this.deviceInfo) {
			this.params.device_id = this.deviceInfo.id
			this.params.tenant_id = this.deviceInfo.tenant_id
			this.params.campus_id = this.deviceInfo.campus_id
			this.getList()
			this.getTslList()
		}
	},
	computed: {},
	methods: {
		handleSizeChange(page_size) {
			console.log(page_size)
			this.params.pageSize = page_size
			this.getList()
		},
		handleCurrentChange(page) {
			console.log(page)
			this.params.page = page
			this.getList()
		},
		//搜索
		upsearch() {
			this.params.page = 1
			this.getList()
		},
		async getList() {
			if (this.params.time) {
				this.params.begin_time = this.params.time[0]
				this.params.end_time = this.params.time[1]
			} else {
				this.params.begin_time = null
				this.params.end_time = null
			}
			const res = await this.$LotApi.device.getDeviceServiceLog.get(this.params)
			if (res.code === 200) {
				this.logData = res.data.rows
				this.total = res.data.total
			}
		},
		getTslList() {
			this.$LotApi.productTsl.list
				.get({
					product_id: this.deviceInfo.product_id,
					tenant_id: this.deviceInfo.tenant_id,
					tsl_type: 3
				})
				.then((res) => {
					if (res.code === 200) {
						this.tslList = res.data
					}
				})
		},
		refresh() {
			this.params.code = null
			this.params.page = 1
			this.params.time = null
			this.getList()
		}
	}
}
</script>

<style lang="scss" scoped>
.left-panel {
	padding-bottom: 15px;
}

.page {
	padding: 20px;
	display: flex;
	justify-content: flex-end;
}
</style>
