<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :name="item.name" :label="item.label" :key="item.name"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script>
import RepairAcceptance from './RepairAcceptance'
import MyRepair from './MyRepair'
import RepairConfiguration from './RepairConfiguration'
import RepairTotal from './RepairTotal'
import AcceptanceConfig from './AcceptanceConfig'
import { shallowRef } from 'vue'
export default {
	name: '',
	components: {
		RepairAcceptance,
		MyRepair,
		RepairConfiguration,
		RepairTotal,
		AcceptanceConfig
	},
	data() {
		return {
			activeName: 'MyRepair',
			tabs: [
				{
					label: '我的报修',
					name: 'MyRepair',
					component: shallowRef(MyRepair)
				},
				{
					label: '报修受理',
					name: 'RepairAcceptance',
					component: shallowRef(RepairAcceptance)
				},
				{
					label: '报修配置',
					name: 'RepairConfiguration',
					component: shallowRef(RepairConfiguration)
				},
				{
					label: '受理配置',
					name: 'AcceptanceConfig',
					component: shallowRef(AcceptanceConfig)
				},
				{
					label: '报修统计',
					name: 'RepairTotal',
					component: shallowRef(RepairTotal)
				}
			],
			currComponent: {}
		}
	},
	created() {
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
		}
	}
}
</script>

<style></style>
