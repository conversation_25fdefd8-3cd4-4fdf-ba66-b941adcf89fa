<template>
	<el-drawer v-model="addVisible" title="新增领导视察信息" size="30%" destroy-on-close>
		<el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
			<el-form-item label="信息类型" prop="display_type">
				<el-radio-group v-model="form.display_type" size="large">
					<el-radio label="1" :value="1">图片</el-radio>
					<el-radio label="2" :value="2">视频</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.display_type == 2" label="封面图" prop="cover_url">
				<sc-upload
					v-model="form.cover_url"
					:width="120"
					:height="120"
					icon="el-icon-pictureFilled"
					title="封面图"
					:cropper="false"
					fileTypeTag="platoonHotspot"
				></sc-upload>
			</el-form-item>

			<el-form-item label="资源地址" prop="hotspot_url">
				<sc-upload-file v-model="form.hotspot_url" :limit="1" fileTypeTag="platoonHotspot"></sc-upload-file>
			</el-form-item>
			<el-form-item label="同步所有班牌" prop="is_all">
				<el-switch
					v-model="form.is_all"
					:active-value="1"
					:inactive-value="0"
					class="ml-2"
					style="--el-switch-on-color: #13ce66; --el-switch-off-color: #909399"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="close">取消</el-button>
			<el-button type="primary" @click="confirm">确定</el-button>
		</template>
	</el-drawer>
</template>
<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const addVisible = ref(false)
const currentSn = ref('')
const form = ref({
	display_type: 1,
	cover_url: '',
	is_all: 0,
	hotspot_url: ''
})
const formRef = ref()
const rules = reactive({
	cover_url: [{ required: true, message: '请上传封面图', trigger: 'change' }],
	display_type: [{ required: true, message: '请选择信息类型', trigger: 'change' }],
	hotspot_url: [{ required: true, message: '请上传资源', trigger: 'change' }]
})
const emit = defineEmits(['addSuccess'])
const confirm = () => {
	formRef.value.validate((valid) => {
		if (valid) {
			globalPropValue.platoonhotspot.add_leader_inspection_mode
				.post({
					...form.value,
					device_sn: currentSn.value,
					tenant_id: tenantId,
					campus_id: campusId
				})
				.then((res) => {
					if (res.code === 200) {
						ElMessage.success('新增领导视察信息成功')
						emit('addSuccess', currentSn.value)
						form.value = {
							display_type: 1,
							cover_url: '',
							is_all: 0,
							hotspot_url: ''
						}
						addVisible.value = false
					}
				})
				.catch((err) => {
					console.log(`新增领导视察信息失败： ${err.message}`)
				})
		}
	})
}

const show = (sn) => {
	currentSn.value = sn
	addVisible.value = true
}
const close = () => {
	addVisible.value = false
}

defineExpose({
	show,
	close
})
</script>
