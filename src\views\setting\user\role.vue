<template>
	<el-dialog v-model="visible" title="批量分配角色" :width="500" destroy-on-close @closed="$emit('closed')">
		<el-form
			ref="dialogForm"
			:model="form"
			:rules="rules"
			label-width="100px"
			label-position="left"
		>
			<el-form-item label="选择账号" prop="user_name">
				<el-tag v-for="(item,index) in selection" :key="index" style="margin-bottom: 5px">{{ item.nickname }}</el-tag>
			</el-form-item>
			<el-form-item label="分配角色" prop="roles">
				<el-select
					v-model="form.roles"
					multiple
					filterable
					:max-collapse-tags="2"
					collapse-tags
					collapse-tags-tooltip
					style="width: 100%"
				>
					<el-option v-for="item in allRoles" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="校区权限" prop="campusIds">
				<el-select
					v-model="form.campusIds"
					multiple
					filterable
					:max-collapse-tags="2"
					collapse-tags
					collapse-tags-tooltip
					style="width: 100%"
				>
					<el-option label="全部校区" :value="0" />
					<el-option v-for="item in allCampus" :key="item.id" :label="item.campus_name" :value="item.id" />
				</el-select>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增用户',
				edit: '编辑用户',
				show: '查看'
			},
			form: {
				roles: [],
				userIds: [],
				campusIds: []
			},
			visible: false,
			isSaveing: false,
			//表单数据
			selection: [],
			selectionIds: [],
			//验证规则
			rules: {
				roles: [{ required: true, message: '请选择所属角色', trigger: 'change' }]
			},
			//所需数据选项
			allRoles: [],
			allCampus: [],
			depts: [],
			params: {
				tenant_id: null
			},
			deptsProps: {
				value: 'id',
				checkStrictly: true
			}
		}
	},
	mounted() {
		this.params.tenant_id = this.$TOOL.data.get('USER_INFO').tenant_id
		this.getAllRoles()
		this.getAllCampus()
		//this.getDept()
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			return this
		},
		async getAllCampus() {
			var res = await this.$API.CampusManagement.all.get(this.params)
			this.allCampus = res.data
		},
		async getAllRoles() {
			var res = await this.$API.role.all.get()
			this.allRoles = res.data
		},
		async getDept() {
			var res = await this.$API.system.dept.list.get()
			this.depts = res.data
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					this.form.mode = this.mode
					var res = await this.$API.user.setRoles.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				} else {
					return false
				}
			})
		},
		//表单注入数据
		setData(data) {
			this.selection = data
			console.log(this.selection)
			data.map((item) => {
				this.form.userIds.push(item.id)
			})
		}
	}
}
</script>

<style></style>
