<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @row-click="rowClick">
				<el-table-column label="操作用户" prop="name"></el-table-column>
				<el-table-column label="校区名称" prop="campus_name"></el-table-column>
				<el-table-column label="IP地址" prop="ip"></el-table-column>
				<el-table-column label="请求id" prop="trace_id" width="300"></el-table-column>
				<el-table-column label="请求地址" prop="uri" width="300"></el-table-column>
				<el-table-column label="请求时间" prop="request_time"></el-table-column>
				<el-table-column label="请求方式" prop="method"></el-table-column>
				<el-table-column label="耗时（毫秒）" prop="duration"></el-table-column>
				<el-table-column label="请求状态" prop="status">
					<template #default="scope">
						<el-tag :type="['success', 'danger'][scope.row.status - 1]">
							{{ $formatDictionary(statusMap, scope.row.status) }}
						</el-tag>
					</template>
				</el-table-column>
			</scTable>
		</el-main>

		<el-drawer v-model="infoDrawer" title="日志详情" :size="600" destroy-on-close>
			<info ref="info"></info>
		</el-drawer>
	</el-container>
</template>

<script>
import info from './info.vue'
import cusTom from '@/utils/cusTom'
const {
	campusId,
	tenantId,
	campusInfo,
	tenantInfo,
	attendanceStatusMap,
	semesterInfo,
	attendanceTypeMap,
	attendanceModeMap
} = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		begin_time: null,
		end_time: null,
		date: [],
		name: null
	}
}
export default {
	name: 'dept',
	components: {
		info
	},
	data() {
		return {
			apiObj: this.$API.log.action,
			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			statusMap: [
				{ name: '成功', value: 1 },
				{ name: '失败', value: 2 }
			],
			attendanceStatusMap,
			attendanceTypeMap,
			attendanceModeMap,
			infoDrawer: false,
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'date',
						value: null,
						component: 'cusDate',
						options: {
							placeholder: '请选择校区',
							type: 'daterange'
						}
					},
					{
						label: null,
						name: 'name',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入操作用户'
						}
					}
				]
			}
		}
	},
	watch: {},
	computed: {},
	async created() {},
	methods: {
		rowClick(row) {
			this.infoDrawer = true
			this.$nextTick(() => {
				this.$refs.info.setData(row)
			})
			console.log(row)
		}, //搜索
		upsearch() {
			this.params.begin_time = this.params.date ? this.params.date[0] : null
			this.params.end_time = this.params.date ? this.params.date[1] : null
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped>
.roomItem {
	& + & {
		margin-top: 5px;
	}
}
</style>
