<template>
	<el-dialog v-model="visible" :width="500" destroy-on-close @closed="$emit('closed')">
		<template #header="{ close, titleId, titleClass }">
			<div class="my-header">
				<h4 :id="titleId" :class="titleClass">
					{{ titleMap[mode] }}
					<span v-if="mode != 'add'" class="ExistingStudents">已安排学生：{{ ExistingStudents }}</span>
				</h4>
			</div>
		</template>

		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'">
			<el-form-item label="宿舍楼" prop="building_id">
				<el-select v-model="form.building_id" placeholder="请选择" filterable clearable @change="changebuild">
					<el-option v-for="item in buildingOptions" :key="item.id" :label="item.building_name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="楼层" prop="floor_id">
				<el-select v-model="form.floor_id" placeholder="请选择" filterable clearable>
					<el-option v-for="item in floorOptions" :key="item.id" :label="item.building_name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="房间名称" prop="room_name">
				<el-input v-model="form.room_name" placeholder="请输入名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="房间号" prop="room_code">
				<el-input v-model="form.room_code" placeholder="请输入名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="床位数" prop="room_capacity">
				<el-input-number v-model="form.room_capacity" :min="1" />
			</el-form-item>
			<el-form-item label="热水器" prop="has_geyser">
				<el-radio-group v-model="form.has_geyser">
					<el-radio :label="-1">无</el-radio>
					<el-radio :label="1">有</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="空调" prop="has_air_conditioner">
				<el-radio-group v-model="form.has_air_conditioner">
					<el-radio :label="-1">无</el-radio>
					<el-radio :label="1">有</el-radio>
				</el-radio-group>
			</el-form-item>
			<!-- <el-form-item label="状态" prop="status">
				<el-switch v-model="form.status" :active-value="1" :inactive-value="-1"></el-switch>
			</el-form-item> -->
			<!-- <el-form-item label="排序" prop="listorder">
				<el-input-number v-model="form.listorder" controls-position="right" :min="1"></el-input-number>
			</el-form-item> -->
			<el-form-item label="备注" prop="remark">
				<el-input
					v-model="form.remark"
					placeholder="请输入"
					clearable
					type="textarea"
					:autosize="{ minRows: 4, maxRows: 6 }"
				></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const defaultData = () => {
	return {
		tenant_id: '',
		campus_id: '',
		building_id: '',
		floor_id: '',
		room_name: '',
		room_code: '',
		has_geyser: -1,
		has_air_conditioner: -1,
		listorder: 1,
		status: 1,
		room_capacity: 1
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		groupData: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			mode: 'add',

			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			titleMap: {
				add: '新增房间',
				edit: '编辑房间'
			},
			//验证规则
			rules: {
				listorder: [{ required: true, message: '请输入排序', trigger: 'change' }],
				room_name: [{ required: true, message: '请输入房间名称' }],
				room_code: [{ required: true, message: '请输入房间号' }],
				room_capacity: [{ required: true, message: '床位数不能为空' }],
				has_geyser: [{ required: true, message: '请选择' }],
				has_air_conditioner: [{ required: true, message: '请选择' }],
				building_id: [{ required: true, message: '请选择宿舍楼' }],
				floor_id: [{ required: true, message: '请选择楼层' }]
			},
			metaData: [],
			buildingOptions: [],
			floorOptions: []
		}
	},
	mounted() {},
	created() {
		this.getLou()
	},
	computed: {
		buildingOptions() {
			return this.metaData.filter((item) => item.parent_id <= 0 && item.building_type == 1)
		},
		floorOptions() {
			return this.metaData.filter((item) => item.parent_id == this.form.building_id && item.building_type == 2)
		},
		ExistingStudents() {
			if (this.form.room_empty_num) {
				return this.form.room_capacity - this.form.room_empty_num
			} else {
				return 0
			}
		}
	},
	watch: {},
	methods: {
		changebuild() {
			this.form.floor_id = ''
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.campus_id = this.params.campus_id
			this.form.tenant_id = this.params.tenant_id
			return this
		},

		//获取宿舍楼
		async getLou() {
			this.metaData = cusTom.treeToArray(this.groupData)
			this.delRoom
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true

					var res = await this.$API.buildingRooms.rooms.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},

		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
