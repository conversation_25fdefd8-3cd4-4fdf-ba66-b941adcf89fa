<template>
	<div v-for="item in comments" :key="item.id" class="workComment">
		<div class="publisher">
			<div class="left">
				<el-avatar :size="40" :src="item.comment_user_head" />
			</div>
			<div class="right">
				<p>{{ item.comment_user_name }}</p>
				<p>
					<span style="margin-right: 10px">{{ item.created_at }}</span>
					<el-popconfirm v-if="onlyShow === false" title="确定删除吗？" @confirm="delComment(item)">
						<template #reference>
							<el-button text type="danger" size="small">删除</el-button>
						</template>
					</el-popconfirm>
					<el-popover
						v-if="item.audited === 1 && onlyShow === false"
						:visible="item.replyPopover"
						trigger="click"
						placement="right"
						:width="300"
					>
						<template #reference>
							<el-button
								type="warning"
								text
								size="small"
								style="margin-left: 5px"
								@click="item.replyPopover = !item.replyPopover"
								>审核</el-button
							>
						</template>
						<el-form label-position="left" label-width="50px">
							<el-form-item label="操作">
								<el-radio-group v-model="item.auditAction">
									<el-radio :label="2">通过</el-radio>
									<el-radio :label="3">不通过</el-radio>
								</el-radio-group>
							</el-form-item>
							<el-form-item label="备注">
								<el-input v-model="item.auditRemark" type="textarea"></el-input>
							</el-form-item>
						</el-form>
						<el-button size="small" @click="item.replyPopover = false">取消</el-button>
						<el-button type="primary" size="small" @click="audited(item)">提交</el-button>
					</el-popover>
					<el-popover
						v-if="reply === true && onlyShow === false && item.audited === 2"
						:visible="item.replyPopover"
						trigger="click"
						placement="right"
						:width="300"
					>
						<template #reference>
							<el-button
								type="primary"
								text
								size="small"
								style="margin-left: 5px"
								@click="item.replyPopover = !item.replyPopover"
								>回复</el-button
							>
						</template>
						<el-form label-position="left" label-width="50px">
							<el-form-item label="分值">
								<!-- <el-input type="number" v-model="item.comment_score2"></el-input> -->
								<el-rate v-model="item.comment_score2" text-color="#ff9900" clearable />
							</el-form-item>
							<el-form-item label="内容">
								<el-input v-model="item.comment_desc2" type="textarea"></el-input>
							</el-form-item>
						</el-form>
						<el-button size="small" @click="item.replyPopover = false">取消</el-button>
						<el-button type="primary" size="small" @click="replyComment(item)">确定</el-button>
					</el-popover>
				</p>
			</div>
			<el-rate v-model="item.comment_score" class="score" size="small" disabled-void-color="#ccc" disabled />
			<!-- <el-tag class="score" type="primary">{{ item.comment_score }}</el-tag> -->
		</div>
		<div class="content">
			{{ item.comment_desc }}
		</div>
		<div v-if="item.child && item.child.length" class="reply">
			<div class="replyContent">
				<childComment :comments="item.child" :reply="false" @suc="sucEmit"></childComment>
				<!-- <ul>
              <li v-for="(reply, index) in item.child" :key="index">
                <childComment :comments="item.child"></childComment>
              </li>
            </ul> -->
			</div>
		</div>
	</div>
</template>
<script>
export default {
	name: 'childComment' //给组件命名
}
</script>

<script setup>
import { getCurrentInstance, defineProps, defineEmits, inject } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
const { campusId, tenantId } = cusTom.getBaseQuery()
const { proxy } = getCurrentInstance()
const props = defineProps({
	comments: {
		type: Array,
		default: []
	},
	reply: {
		type: Boolean,
		default: true
	}
})
const onlyShow = inject('onlyShow')
const emit = defineEmits(['suc'])
const delComment = async (item) => {
	let p = {
		id: item.id,
		campus_id: item.campusId,
		tenant_id: item.tenantId
	}
	let res = await proxy.$API.famous.comment.del.post(p)
	if (res.code === 200) {
		sucEmit()
	}
}

const audited = async (item) => {
	if (!item.auditAction) {
		ElMessage.warning('请选择操作')
		return
	}
	let p = {
		id: item.id,
		campus_id: item.campus_id,
		tenant_id: item.tenant_id,
		resources_id: item.resources_id,
		audit_remark: item.auditRemark,
		audit: item.auditAction
	}
	console.log(p)
	let res = await proxy.$API.famous.comment.audit.post(p)
	if (res.code === 200) {
		sucEmit()
	}
}
const replyComment = async (item) => {
	if (!item.comment_desc2) {
		ElMessage.warning('请输入内容')
		return
	}
	let p = {
		id: 0,
		campus_id: item.campus_id,
		tenant_id: item.tenant_id,
		resources_id: item.resources_id,
		parent_id: item.id,
		comment_desc: item.comment_desc2,
		comment_score: Number(item.comment_score2)
	}
	let res = await proxy.$API.famous.comment.save.post(p)
	if (res.code === 200) {
		item.replyPopover = false
		sucEmit()
	}
}
const sucEmit = () => {
	emit('suc')
}
</script>

<style scoped lang="scss">
ul,
li {
	list-style: none;
}
.workComment {
	color: #303133;
	padding: 8px 0;
	font-size: 12px;
	border-bottom: 1px solid var(--el-border-color-light);
	.publisher {
		display: flex;
		position: relative;
		.left {
			width: 40px;
			height: 40px;
			border: 1px solid #e8e8e8;
			border-radius: 50%;
			margin-right: 10px;
			overflow: hidden;
		}
		.right {
			p {
				&:last-child {
					display: flex;
					align-items: center;
					font-size: 12px;
					color: #909399;
				}
			}
		}
		.score {
			position: absolute;
			top: 8px;
			right: 5px;
		}
	}
	.content {
		position: relative;
		padding: 10px 50px 10px;
		white-space: pre-wrap;
		.score {
			position: absolute;
			right: 5px;
		}
	}
	.reply {
		margin: 0 0 0 50px;
		.replyContent {
			border-radius: 6px;
			padding: 5px 10px;
			background: #f7f8f9;
		}
	}
}
</style>
