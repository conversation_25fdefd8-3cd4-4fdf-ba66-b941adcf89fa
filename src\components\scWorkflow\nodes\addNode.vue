<template>
	<div class="add-node-btn-box">
		<div class="add-node-btn">
			<!--				:width="hasNode.length * 93"-->
			<el-popover
				:width="270"
				placement="right-start"
				trigger="click"
				:hide-after="0"
				:show-after="0"
				@before-enter="show"
			>
				<template #reference>
					<!--						v-if="hasNode.length > 0 && allowNode.length > 0"-->
					<el-button type="primary" icon="el-icon-plus" circle></el-button>
				</template>
				<div class="add-node-popover-body">
					<ul>
						<!--						v-if="hasNode.includes('approver') && allowNode.includes('approver')"-->
						<li>
							<el-icon style="color: #ff943e" @click="addType(1)">
								<el-icon-user-filled />
							</el-icon>
							<p>审批节点</p>
						</li>
						<!--						v-if="hasNode.includes('send') && allowNode.includes('send')"-->
						<li>
							<el-icon style="color: #3296fa" @click="addType(2)">
								<el-icon-promotion />
							</el-icon>
							<p>抄送节点</p>
						</li>
						<!--						v-if="hasNode.includes('branch') && allowNode.includes('branch')"-->
						<li>
							<el-icon style="color: #15bc83" @click="addType(4)">
								<el-icon-share />
							</el-icon>
							<p>条件分支</p>
						</li>
					</ul>
				</div>
			</el-popover>
		</div>
	</div>
</template>

<script>
import tool from '@/utils/tool'
import { ElNotification } from 'element-plus'

export default {
	props: {
		modelValue: {
			type: Object,
			default: () => {}
		},
		isBranch: {
			type: Boolean,
			default: false
		},
		prentNode: {
			type: Object,
			default: () => {}
		},
		allowNode: {
			type: Array,
			default: () => {
				//'send',
				return ['branch', 'approver']
			}
		}
	},
	data() {
		return {
			//'send',
			hasNode: ['branch', 'approver']
		}
	},
	created() {},
	mounted() {
		this.hasNode = ['branch', 'approver']
		/*let sendStatus
		sendStatus = tool.data.get('SendStatus')
		if (sendStatus === true) {
			this.hasNode = this.hasNode.filter((item) => item !== 'send')
		}*/
		let branchStatus
		branchStatus = tool.data.get('BranchStatus')
		if (branchStatus === true) {
			this.hasNode = this.hasNode.filter((item) => item !== 'branch')
		}
	},
	methods: {
		show() {
			this.hasNode = ['branch', 'approver']
			/*let sendStatus
			sendStatus = tool.data.get('SendStatus')
			if (sendStatus === true) {
				this.hasNode = this.hasNode.filter((item) => item !== 'send')
			}*/
			let branchStatus
			branchStatus = tool.data.get('BranchStatus')
			if (branchStatus === true) {
				this.hasNode = this.hasNode.filter((item) => item !== 'branch')
			}
		},
		addType(type) {
			/*let isBranch = false
			if (this.isBranch === true || this.prentNode.isBranch === true) {
				isBranch = true
			}
			console.log(isBranch)*/
			var random = tool.getRandomString(8, 'alphanumeric')
			var node = {}
			if (type === 1) {
				node = {
					nodeName: '审核',
					nodeId: 'approval_' + random,
					type: 1, //节点类型
					setType: 1, //审核人类型
					nodeUserList: [], //审核人成员
					nodeRoleList: [], //审核角色
					nodePositionList: [], //审核角色
					examineLevel: 1, //指定主管层级
					directorLevel: 1, //自定义连续主管审批层级
					selectMode: 1, //发起人自选类型
					termAuto: false, //审批期限超时自动审批
					term: 0, //审批期限
					termMode: 1, //审批期限超时后执行类型
					examineMode: 3, //多人审批时审批方式
					directorMode: 0, //连续主管审批方式
					childNode: this.modelValue
				}
			} else if (type === 2) {
				/*let sendStatus
				sendStatus = tool.data.get('SendStatus')
				if (sendStatus === true) {
					ElNotification.error({
						title: '操作错误',
						message: '当前审批流程已有抄送节点，请删除后添加'
					})
					return
				}*/
				node = {
					nodeName: '抄送',
					nodeId: 'send_' + random,
					type: 2,
					userSelectFlag: false,
					nodeUserList: [],
					childNode: this.modelValue
				}
			} else if (type === 4) {
				/*let branchStatus
				branchStatus = tool.data.get('BranchStatus')
				if (branchStatus === true) {
					ElNotification.error({
						title: '操作错误',
						message: '当前审批流程已有条件路由，请删除后添加'
					})
					return
				}*/
				var conditionRandom = tool.getRandomString(8, 'alphanumeric')
				var conditionRandom2 = tool.getRandomString(8, 'alphanumeric')

				node = {
					nodeName: '条件路由',
					nodeId: 'branch_' + random,
					type: 4,
					conditionNodes: [
						{
							nodeName: '条件1',
							nodeId: 'condition_' + conditionRandom,
							type: 3,
							priorityLevel: 1,
							conditionMode: 1,
							conditionList: []
						},
						{
							nodeName: '条件2',
							nodeId: 'condition_' + conditionRandom2,
							type: 3,
							priorityLevel: 2,
							conditionMode: 1,
							conditionList: []
						}
					],
					childNode: this.modelValue
				}
			}
			this.$emit('update:modelValue', node)
			if (node.type === 4) {
				tool.data.set('BranchStatus', true)
			}
			if (node.type === 2) {
				tool.data.set('SendStatus', true)
			}
		}
	}
}
</script>

<style>
.el-popover.el-popper {
	min-width: 110px !important;
}
</style>
