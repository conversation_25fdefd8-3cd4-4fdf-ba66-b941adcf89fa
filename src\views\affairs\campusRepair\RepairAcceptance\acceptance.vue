<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<cusForm v-if="mode == 'edit'" ref="dialogForm" v-model="form" :config="paramsConfig"> </cusForm>
		<cusForm v-if="mode == 'result'" ref="dialogForm" v-model="form" :config="paramsConfig1"> </cusForm>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { repairStatusMap } = cusTom.getBaseQuery()

const repairStatusMapshift = repairStatusMap.slice(1, repairStatusMap.length)

const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		repair_status: null,
		deal_remark: null,
		deal_img: null,
		id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				edit: '报修受理',
				result: '报修受理'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),

			paramsConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: '状态',
						name: 'repair_status',
						value: null,
						component: 'radio',
						options: {
							placeholder: '请选择',
							items: repairStatusMapshift.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						},
						rules: [{ required: true, message: '请选择状态', trigger: 'blur' }]
					},
					{
						label: '备注',
						name: 'deal_remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请选择'
						},
						hideHandle: '!$.repair_status'
					},
					{
						label: '附件',
						name: 'deal_img',
						value: null,
						component: 'upload',
						options: {
							items: [
								{
									name: 'deal_img',
									label: '附件',
									type: 'repair'
								}
							]
						},
						hideHandle: '!$.repair_status'
					}
				]
			},
			paramsConfig1: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: '状态',
						name: 'repair_status',
						value: null,
						component: 'radio',
						options: {
							placeholder: '请选择',
							items: repairStatusMap.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						},
						rules: [{ required: true, message: '请选择状态', trigger: 'blur' }]
					},
					{
						label: '备注',
						name: 'deal_remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请选择'
						},
						hideHandle: '$.repair_status == 1'
					},
					{
						label: '附件',
						name: 'deal_img',
						component: 'upload',
						options: {
							items: [
								{
									name: 'deal_img',
									label: '附件',
									type: 'repair'
								}
							]
						},
						hideHandle: '$.repair_status == 1'
					}
				]
			}
		}
	},
	computed: {},
	mounted() {},
	created() {},
	watch: {
		'form.repair_status': {
			handler(val) {
				if (val == 1) {
					this.form.deal_remark = null
					this.form.deal_img = null
				}
			}
		}
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id

			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true

					var res = await this.$API.campusRepair.deal.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			if (this.mode === 'edit') {
				this.form.repair_status = 2
			}
		}
	}
}
</script>

<style></style>
