<template>
    <el-dialog v-if="dialogVisible" v-model="dialogVisible" width="500" :show-close="false" :close-on-press-escape="false"
        :close-on-click-modal="false" title="选择你的身份，登录后提交问卷" center align-center>
        
                <div class="btn">
                    <el-button type="primary" @click="getUser(2)" size="large" style="width: 80%;">我是老师</el-button>
                </div>
           
                <div class="btn">
                    <el-button type="primary" @click="getUser(3)" size="large" style="width: 80%;">我是学生</el-button>
                </div>
            
    </el-dialog>
</template>
<script setup>
const dialogVisible = ref(false)
const emit = defineEmits(['select'])
const getUser = (type) => {
    emit('select', type)
    close()
}
const open = () => {
    dialogVisible.value = true
}
const close = () => {
    dialogVisible.value = false
}
defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.btn{
    margin-top: 20px;
    margin-bottom: 10px;
    width: 100%;
    text-align: center;
}
</style>