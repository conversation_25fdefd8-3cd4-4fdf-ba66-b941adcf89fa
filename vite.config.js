import { defineConfig, loadEnv } from 'vite'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import * as path from 'path'
import vue from '@vitejs/plugin-vue'
import autoImport from 'unplugin-auto-import/vite'

// https://vitejs.dev/config/

export default ({ mode }) => {
	const env = loadEnv(mode, process.cwd())
	return defineConfig({
		base: './',
		plugins: [
			vue(),
			createSvgIconsPlugin({
				// Specify the icon folder to be cached
				iconDirs: [path.resolve(process.cwd(), 'src/assets/icons')],
				// Specify symbolId format
				symbolId: 'icon-[dir]-[name]'
			}),
			autoImport({
				imports: ['vue', 'vue-router', 'pinia'], //设置自动引入的插件/模块
				dts: 'src/auto-import.d.ts',
				eslintrc: {
					enabled: true
				}
			})
		],
		resolve: {
			alias: {
				// 设置路径
				'~': path.resolve(__dirname, './'),
				// 设置别名
				'@': path.resolve(__dirname, './src')
			},
			extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
		},
		define: { __VUE_I18N_FULL_INSTALL__: true, __VUE_I18N_LEGACY_API__: true, __INTLIFY_PROD_DEVTOOLS__: false },
		// vite 相关配置
		server: {
			port: env.VITE_APP_PORT || 2800,
			host: true,
			open: true,
			proxy: {
				'/api': {
					target: env.VITE_APP_API_BASEURL || 'https://www.fastmock.site/mock/5039c4361c39a7e3252c5b55971f1bd3/api',
					changeOrigin: true,
					rewrite: (p) => p.replace(/^\/api/, '')
				}
			},
			watch: {
				usePolling: true, // 实时监听
				interval: 1000 // 监听的间隔时间(ms)
			}
		},
		css: {
			postcss: {
				plugins: [
					{
						postcssPlugin: 'internal:charset-removal',
						AtRule: {
							charset: (atRule) => {
								if (atRule.name === 'charset') {
									atRule.remove()
								}
							}
						}
					}
				]
			}
		},
		build: {
			emptyOutDir: true,
			// 禁用 esModuleInterop
			esModuleInterop: false,
			outDir: 'dist',
			assetsDir: 'assets',
			minify: 'terser',
			terserOptions: {
				compress: {
					drop_console: true,
					drop_debugger: true
				}
			},
			rollupOptions: {
				// 拆包
				output: {
					chunkFileNames: 'js/[name]-[hash].js',
					entryFileNames: 'js/[name]-[hash].js',
					assetFileNames: '[ext]/[name]-[hash].[ext]',
					manualChunks(id) {
						if (id.includes('node_modules')) {
							return id.split('/node_modules/').pop()?.split('/')[0]
						}
					}
					// 第三方库拆包
					// manualChunks: {
					//   xgplayer: ['xgplayer'],
					//   xlsx: ['xlsx'],
					//   tinymce: ['tinymce'],
					//   elicons: ['@element-plus/icons-vue']
					// }
				}
			}
		}
	})
}
