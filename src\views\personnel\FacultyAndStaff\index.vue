<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList.length > 1" label="">
						<el-select v-model="params.campus_id" placeholder="校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="教职工姓名/手机号码/IC卡号" clearable></el-input>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.bind_face" placeholder="绑定人脸" clearable>
							<el-option :value="1" label="是"></el-option>
							<el-option :value="-1" label="否"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.bind_card" placeholder="绑卡" clearable>
							<el-option :value="1" label="是"></el-option>
							<el-option :value="-1" label="否"></el-option>
						</el-select>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
			
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :show-action="true" :apiObj="apiObj" @selection-change="selectionChange">
				<template #action>
					<div style="text-align: right;">
						<el-button type="primary" icon="el-icon-plus" @click="add">新增教职工</el-button>
						<el-button icon="el-icon-sort" @click="importExport">导入导出数据</el-button>
						<el-button icon="el-icon-switch" @click="sync">同步教职工数据</el-button>
					</div>
			</template>
				<el-table-column type="selection" width="50" fixed="left" />
				<el-table-column label="姓名" prop="name" width="100" fixed="left"></el-table-column>
				<el-table-column label="工号" prop="serial_number" width="120"></el-table-column>
				<el-table-column label="头像" prop="user_head" width="100" align="center">
					<template #default="scope">
						<cusHead
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.user_head"
							:preview-src-list="[scope.row.user_head]"
							preview-teleported
						>
						</cusHead>
					</template>
				</el-table-column>
				<el-table-column label="人脸识别" prop="face_img" width="75" align="center">
					<template #default="scope">
						<el-icon
							v-if="scope.row.face_img === ''"
							style="font-size: 30px; cursor: pointer; color: var(--el-color-info)"
							@click="setFace(scope.row)"
						>
							<sc-icon-face />
						</el-icon>
						<el-icon
							v-if="scope.row.face_img !== ''"
							style="font-size: 30px; cursor: pointer; color: var(--el-color-success)"
							@click="setFace(scope.row)"
						>
							<sc-icon-face />
						</el-icon>
					</template>
				</el-table-column>
				<el-table-column label="IC卡" prop="face_img" width="75" align="center">
					<template #default="scope">
						<el-icon
							v-if="scope.row.moredian_card_no === ''"
							style="font-size: 24px; cursor: pointer; color: var(--el-color-info)"
							@click="setCard(scope.row)"
						>
							<el-icon-Postcard />
						</el-icon>
						<el-icon
							v-if="scope.row.moredian_card_no !== ''"
							style="font-size: 24px; cursor: pointer; color: var(--el-color-success)"
							@click="setCard(scope.row)"
						>
							<el-icon-Postcard />
						</el-icon>
					</template>
				</el-table-column>
				<el-table-column label="性别" prop="sex" width="70">
					<template #default="scope">
						{{ formData(sexMap, scope.row.sex) }}
					</template>
				</el-table-column>
				<el-table-column label="身份证号" prop="idcard" width="170"></el-table-column>
				<el-table-column label="手机号" prop="phone" width="120"></el-table-column>
				<el-table-column label="邮箱" prop="email" width="120" show-overflow-tooltip></el-table-column>
				<!--				<el-table-column label="政治面貌" prop="political">
					<template #default="scope">
						{{ formData(politicalMap, scope.row.political) }}
					</template>
				</el-table-column>-->
				<el-table-column label="身份" prop="identity" width="120">
					<template #default="scope">
						{{ formData(identityMap, scope.row.identity) }}
					</template>
				</el-table-column>
				<el-table-column label="地址" prop="address" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="在职状态" prop="work_status">
					<template #default="scope">
						{{ formData(workStatusMap, scope.row.work_status) }}
					</template>
				</el-table-column>
				<el-table-column label="入职时间" prop="entry_time" width="120"></el-table-column>
				<el-table-column label="出生日期" prop="birthdate" width="120"></el-table-column>
				<el-table-column label="学科" prop="course_name" width="120"></el-table-column>
				<el-table-column label="是否学科负责人" prop="discipline_head" width="110">
					<template #default="scope">
						{{ scope.row.discipline_head == '1' ? '是' : '否' }}
					</template>
				</el-table-column>
				<!--
				<el-table-column label="课程" prop="course_name" width="140"></el-table-column>
-->
				<el-table-column label="职工类别" prop="staff_type">
					<template #default="scope">
						{{ formData(staffTypeMap, scope.row.staff_type) }}
					</template>
				</el-table-column>
				<el-table-column label="编制情况" prop="compile_type" width="140">
					<template #default="scope">
						{{ formData(compileTypeMap, scope.row.compile_type) }}
					</template>
				</el-table-column>
				<el-table-column label="学历情况" prop="academic_type" width="100">
					<template #default="scope">
						{{ formData(academicTypeMap, scope.row.academic_type) }}
					</template>
				</el-table-column>
				<el-table-column label="职称" prop="professional_type" width="100">
					<template #default="scope">
						{{ formData(professionalTypeMap, scope.row.professional_type) }}
					</template>
				</el-table-column>

				<el-table-column label="创建时间" prop="created_at" width="180"></el-table-column>
				<el-table-column label="备注" prop="remark" min-width="300" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="170">
					<template #default="scope">
						<el-button-group>
							<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-button text type="danger" size="small" @click="table_del(scope.row, scope.$index)">删除</el-button>
						</el-button-group>
					</template>
				</el-table-column>
				<template v-slot:selectAction>
					<el-button size="small" :disabled="delStatus !== true" type="danger" @click="tableBatchDel"
						>批量删除
					</el-button>
				</template>
			</scTable>
		</el-main>
	</el-container>

	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		:disciplineOptions="disciplineOptions"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
	<face-dialog
		v-if="dialog.face"
		ref="faceDialog"
		:params="params"
		:disciplineOptions="disciplineOptions"
		@success="handleSaveSuccess"
		@closed="dialog.face = false"
	></face-dialog>
	<card-dialog
		v-if="dialog.card"
		ref="cardDialog"
		@success="handleSaveSuccess"
		@closed="dialog.card = false"
	></card-dialog>
	<import-export-dialog
		v-if="dialog.importExport"
		ref="importExportDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.importExport = false"
	></import-export-dialog>
</template>

<script>
import saveDialog from './save'
import faceDialog from './face'
import cardDialog from './card'
import cusTom from '@/utils/cusTom'
import cusHead from '@/components/custom/cusStaffHead.vue'
import importExportDialog from './importExport.vue'
import { ElMessageBox } from 'element-plus'

const {
	campusId,
	tenantId,
	campusInfo,
	tenantInfo,
	sexMap,
	politicalMap,
	identityMap,
	workStatusMap,
	staffTypeMap,
	academicTypeMap,
	professionalTypeMap,
	compileTypeMap
} = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		name: null,
		bind_card: null,
		bind_face: null
	}
}
export default {
	name: 'dept',
	components: {
		importExportDialog,
		saveDialog,
		cusHead,
		cardDialog,
		faceDialog
	},
	data() {
		return {
			dialog: {
				save: false,
				importExport: false,
				face: false,
				card: false
			},
			apiObj: this.$API.personnel.staff.list,
			selection: [],
			delStatus: false,
			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			disciplineOptions: [],
			sexMap,
			politicalMap,
			identityMap,
			workStatusMap,
			staffTypeMap,
			academicTypeMap,
			professionalTypeMap,
			compileTypeMap
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.$SET_campus_id(val)
			},
			immediate: true
		}
	},
	computed: {},
	async created() {},
	methods: {
		sync() {
			ElMessageBox.confirm('确认将所有教职工信息数据同步到智能设备，用于刷卡刷脸签到、开门等功能', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
			var reqData = { tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			this.$API.CampusManagement.syncTeacher.post(reqData).then((res) => {
					if (res.code === 200) {
						this.$message.success('提交同步任务成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
			})
		},
		importExport() {
			this.dialog.importExport = true
			this.$nextTick(() => {
				this.$refs.importExportDialog.open('export')
			})
			// this.upsearch()
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		//添加
		add(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', row)
			})
			// this.upsearch()
		},
		setFace(row) {
			this.dialog.face = true
			this.$nextTick(() => {
				this.$refs.faceDialog.open('edit').setData(row)
			})
			// this.upsearch()
		},
		setCard(row) {
			this.dialog.card = true
			this.$nextTick(() => {
				this.$refs.cardDialog.open('edit').setData(row)
			})
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		tableBatchDel() {
			if (this.selection.length <= 0) {
				return
			}
			this.$confirm(
				`确定删除选中的 ${this.selection.length} 个教师吗？删除后所有相关的业务数据都将一并删除，确认删除吗？`,
				'提示',
				{
					type: 'warning'
				}
			)
				.then(async () => {
					const loading = this.$loading()
					var reqData = {
						id: this.selection.map((v) => v.id),
						tenant_id: this.params.tenant_id,
						campus_id: this.params.campus_id
					}
					var res = await this.$API.personnel.staff.del.post(reqData)
					if (res.code === 200) {
						this.$refs.table.refresh()
						loading.close()
						this.$message.success('操作成功')
					} else {
						loading.close()
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
				.catch(() => {})
		},
		//删除
		async table_del(row) {
			this.$confirm(`确定删除教师吗？删除后所有相关的业务数据都将一并删除，确认删除吗？`, '提示', {
				type: 'warning'
			})
				.then(async () => {
					var reqData = { id: [row.id], tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
					var res = await this.$API.personnel.staff.del.post(reqData)
					if (res.code === 200) {
						this.$message.success('删除成功')
						this.$refs.table.refresh()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
				.catch(() => {})
		},
		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			this.$API.personnel.staff.status.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
			if (selection.length > 0) {
				this.delStatus = true
			} else {
				this.delStatus = false
			}
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null

			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}

			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.dialog.save = false
			this.dialog.card = false
			this.dialog.face = false
			this.dialog.importExport = false
			if (mode === 'add') {
				this.upsearch()
			} else {
				this.$refs.table.refresh()
			}
		}
	}
}
</script>

<style lang="scss" scoped></style>
