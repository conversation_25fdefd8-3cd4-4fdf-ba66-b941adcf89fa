<template>
	<el-container>
		<el-header v-if="CampusManagementList.length > 1">
			<div class="left-panel">
				<div class="left-panel-search">
					<el-select
						v-if="CampusManagementList.length > 1"
						v-model="params.campus_id"
						placeholder="校区"
						filterable
						style="margin-right: 15px"
						@change="campusChange"
					>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</div>
			</div>
		</el-header>
		<el-main style="background-color: unset; padding: unset">
			<div v-loading="loading" class="echartsOut">
				<el-row :gutter="20">
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.product_num">
								<template #title>产品总数</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="totalDevice">
								<template #title>设备总数</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>

					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="deviceStatus[4]">
								<template #title>注册未激活设备</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>

					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="deviceStatus[2]">
								<template #title>当前在线设备</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>

					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="deviceStatus[3]">
								<template #title>当前离线设备</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>

					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.alert_untreated_num">
								<template #title>未处理告警数</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="6">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="deviceType" width="100%" height="300px"></scEcharts>
						</el-card>
					</el-col>
					<el-col :span="6">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="deviceAlarm" width="100%" height="300px"></scEcharts>
						</el-card>
					</el-col>
					<el-col :span="12">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="deviceAlarmLog" width="100%" height="300px"></scEcharts>
						</el-card>
					</el-col>
					<el-col :span="24">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="logData" width="100%" height="300px"></scEcharts>
						</el-card>
					</el-col>
				</el-row>
			</div>
		</el-main>
	</el-container>
</template>
<script>
import cusTom from '@/utils/cusTom'
import scEcharts from '@/components/scEcharts/index.vue'
import { ElMessage } from 'element-plus'

const { campusInfo, tenantId, campusId } = cusTom.getBaseQuery()

export default {
	name: 'index',
	components: {
		scEcharts
	},
	data() {
		return {
			params: {
				tenant_id: tenantId,
				campus_id: campusId
			},
			loading: false,
			overviewData: {},
			totalDevice: 0,
			deviceStatus: {
				1: 0,
				2: 0,
				3: 0,
				4: 0,
				5: 0
			},
			CampusManagementList: [],
			deviceType: {
				title: {
					text: '设备类型分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '0%'
				}
			},
			deviceAlarm: {
				color: ['#F56C6C', '#FF7D00', '#165DFF', '#909399'],
				title: {
					text: '设备告警分布',
					subtext: '7天内'
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},

			deviceAlarmLog: {
				color: ['#165DFF'],
				title: {
					text: '设备告警统计',
					subtext: '30天内分布，每日凌晨更新'
				},
				grid: {
					top: '60px',
					bottom: '10px'
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: [],
					boundaryGap: [0, '100%']
				} /*
				dataZoom: [
					{
						type: 'slider',
						start: 0,
						end: 100,
						height: 24
					}
				],*/,
				yAxis: {
					type: 'value',
					boundaryGap: [0, '100%']
				},
				series: [
					{
						name: '告警数量',
						data: [],
						type: 'line',
						smooth: true,
						showSymbol: false,
						lineStyle: {
							color: '#FF8900' //线条颜色
						},
						areaStyle: {
							// 使用方法二的写法
							color: {
								type: 'linear',
								x: 0, //右
								y: 0, //下
								x2: 0, //左
								y2: 1, //上
								colorStops: [
									{
										offset: 0,
										color: '#FF8900' // 0% 处的颜色
									},
									{
										offset: 1,
										color: '#E8F3FF' // 100% 处的颜色
									}
								]
							}
						}
					}
				]
			},
			logData: {
				color: ['#165DFF'],
				title: {
					text: '设备日志总线',
					subtext: '30天内分布，每日凌晨更新'
				},
				grid: {
					top: '60px',
					bottom: '10px'
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: [],
					boundaryGap: [0, '100%']
				} /*
				dataZoom: [
					{
						type: 'slider',
						start: 0,
						end: 100,
						height: 24
					}
				],*/,
				yAxis: {
					type: 'value',
					boundaryGap: [0, '100%']
				},
				series: [
					{
						name: '设备日志数量',
						data: [],
						type: 'line',
						smooth: true,
						showSymbol: false,
						lineStyle: {
							color: '#165DFF' //线条颜色
						},
						areaStyle: {
							// 使用方法二的写法
							color: {
								type: 'linear',
								x: 0, //右
								y: 0, //下
								x2: 0, //左
								y2: 1, //上
								colorStops: [
									{
										offset: 0,
										color: '#4080FF' // 0% 处的颜色
									},
									{
										offset: 1,
										color: '#E8F3FF' // 100% 处的颜色
									}
								]
							}
						}
					}
				]
			}
		}
	},
	computed: {},
	created() {
		//获取总览数据
		this.CampusManagementList = campusInfo
		this.getData()
	},
	methods: {
		campusChange(val) {
			this.params.campus_id = val
			this.deviceStatus = {
				1: 0,
				2: 0,
				3: 0,
				4: 0,
				5: 0
			}
			this.deviceAlarmLog.xAxis.data = []
			this.deviceAlarmLog.series[0].data = []
			this.deviceAlarm.series[0].data = []
			this.deviceType.series[0].data = []
			this.logData.xAxis.data = []
			this.logData.series[0].data = []
			this.getData()
		},
		async getData() {
			this.loading = true
			const res = await this.$LotApi.common.statistics.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.overviewData = res.data
				this.totalDevice = 0
				if (res.data.device_status) {
					res.data.device_status.map((v) => {
						this.deviceStatus[v.status] = v.num
						this.totalDevice += v.num
					})
				}

				if (res.data.device_type !== null) {
					this.deviceType.series[0].data = res.data.device_type.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.deviceType.series[0].data = []
				}

				if (res.data.device_alarm_info !== null) {
					let chartData = [
						{
							name: '紧急',
							value: 0
						},
						{
							name: '重要',
							value: 0
						},
						{
							name: '次要',
							value: 0
						},
						{
							name: '提示',
							value: 0
						}
					]
					res.data.device_alarm_info.map((v) => {
						if (chartData[v.alert_level - 1]) {
							chartData[v.alert_level - 1].value = v.count
						}
					})
					this.alertTotal = 0
					this.deviceAlarm.series[0].data = chartData
				} else {
					this.deviceAlarm.series[0].data = []
				}

				let categoryData = []
				let seriesData = []
				res.data.alart_log.map((item, index) => {
					categoryData.push(item.date.substring(5, 16).replace('-', ''))
					seriesData.push(item.num)
				})
				this.deviceAlarmLog.xAxis.data = categoryData
				this.deviceAlarmLog.series[0].data = seriesData

				let logCategoryData = []
				let logSeriesData = []
				res.data.device_log.map((item, index) => {
					logCategoryData.push(item.date.substring(5, 16).replace('-', ''))
					logSeriesData.push(item.num)
				})
				this.logData.xAxis.data = logCategoryData
				this.logData.series[0].data = logSeriesData
			} else {
				ElMessage({ type: 'error', message: res.message })
			}
		}
	}
}
</script>
<style scoped lang="scss">
.echartsOut {
	margin-top: 10px;
}

:deep(.el-statistic) {
	.el-statistic__number {
		font-size: 26px;
	}

	.el-statistic__head {
		font-size: 16px;
		font-weight: bold;
		line-height: 30px;
	}

	.el-statistic__content {
		text-align: center;
		line-height: 30px;
	}

	.el-statistic__suffix {
		color: var(--el-text-color-secondary);
		font-size: 14px;
	}
}
</style>
