<template>
    <div class="center-item-container drag-center">
        <div class="center-view" :class="{ active: selectWidget?.key === element.key }"
            v-if="element.type === 'divider'">
            <el-divider :direction='element.options.direction' :border-style='element.options.borderStyle'
                :content-position='element.options.contentPosition'>
                {{ element.options.content }}
            </el-divider>
        </div>
        <div class="center-view" :class="{ active: selectWidget?.key === element.key }" v-if="element.type === 'text'"
            style="min-height: 52px;">
            <div v-html="element.options.defaultValue"></div>
        </div>
        <div class="headImage" :class="{ active: selectWidget?.key === element.key }"
            :style="element.options.defaultValue ? 'height: 200px;' : ''" v-if="element.type === 'headImage'">
            <el-image :src="element.options.defaultValue" style="width: 100%;height: 100%;" fit="cover">
                <template #error v-if="!element.options.defaultValue">
                    点击设置头图
                </template>
            </el-image>
        </div>
        <div class="headImage title" :class="{ active: selectWidget?.key === element.key }"
            v-if="element.type === 'title'">
            <h3>{{ element.options.defaultValue }}</h3>
        </div>
        <!-- 占位用 -->
        <div class="" v-if="element.type === 'seat'"></div>

        <div class="center-view" style="text-align: center;padding: 10px;" v-if="element.type === 'insert-img'"
            :class="{ active: selectWidget?.key === element.key }">
            <el-image :src="element.options.defaultValue" style="width: 100%;height: 100%;" fit="cover">
                <template #error>
                    请上传需要插入的图片
                </template>
            </el-image>
        </div>
        <el-form-item class=" center-view"
            v-if="element && element.type !== 'divider' && element.type !== 'text' && element.type !== 'title' && element.type !== 'insert-img' && element.type !== 'headImage' && element.type !== 'seat'"
            :key="element.key" :class="{ active: selectWidget?.key === element.key }" :label="element.label"
            :rules="element.options.rules">
            <template v-if="element.type === 'input'">
                <el-input readonly :modelValue="element.options.defaultValue" :style="{ width: element.options.width }"
                    :placeholder="element.options.placeholder" :minlength="parseInt(element.options.minlength)"
                    :maxlength="parseInt(element.options.maxlength)" :clearable="element.options.clearable"
                    :disabled="element.options.disabled">
                    <template #prefix v-if="element.options.prefix">
                        {{ element.options.prefix }}
                    </template>
                    <template #suffix v-if="element.options.suffix">
                        {{ element.options.suffix }}
                    </template>
                    <template #prepend v-if="element.options.prepend">
                        {{ element.options.prepend }}
                    </template>
                    <template #append v-if="element.options.append">
                        {{ element.options.append }}
                    </template>
                </el-input>
            </template>

            <template v-if="element.type === 'textarea'">
                <el-input type="textarea" resize="none" readonly :rows="element.options.rows"
                    :modelValue="element.options.defaultValue" :style="{ width: element.options.width }"
                    :placeholder="element.options.placeholder" :minlength="parseInt(element.options.minlength)"
                    :maxlength="parseInt(element.options.maxlength)" :show-word-limit="element.options.showWordLimit"
                    :autosize="element.options.autosize" :clearable="element.options.clearable"
                    :disabled="element.options.disabled" />
            </template>

            <template v-if="element.type === 'number'">
                <el-input-number readonly :modelValue="element.options.defaultValue"
                    :style="{ width: element.options.width }" :max="element.options.max" :min="element.options.min"
                    :disabled="element.options.disabled" />
            </template>

            <template v-if="element.type === 'radio'">
                <el-radio-group readonly :modelValue="element.options.defaultValue"
                    :style="{ width: element.options.width, display: 'block' }" :disabled="element.options.disabled">
                    <el-radio v-for="item of element.options.options" :key="item.value" :label="item.value" :style="{
                        display: element.options.inline ? 'inline-block' : 'block'
                    }">{{ element.options.showLabel ? item.label : item.value }}</el-radio>
                </el-radio-group>
            </template>

            <template v-if="element.type === 'checkbox'">
                <el-checkbox-group :modelValue="element.options.defaultValue" :min="element.options.min"
                    :max="element.options.max" :style="{ width: element.options.width }" :disabled="true">
                    <el-checkbox v-for="item of element.options.options" :key="item.value" :label="item.value" :style="{
                        display: element.options.inline ? 'inline-block' : 'block'
                    }">{{ element.options.showLabel ? item.label : item.value }}
                    </el-checkbox>
                </el-checkbox-group>
            </template>

            <template v-if="element.type === 'time'">
                <el-time-picker :modelValue="element.options.defaultValue" :placeholder="element.options.placeholder"
                    :readonly="true" :editable="element.options.editable" :clearable="element.options.clearable"
                    :format="element.options.valueFormat" :value-format="element.options.format"
                    :disabled="element.options.disabled" :style="{ width: element.options.width }" />
            </template>

            <template v-if="element.type === 'date'">
                <el-date-picker readonly :type="element.options.type" :modelValue="element.options.defaultValue"
                    :placeholder="element.options.placeholder" :readonly="true" :editable="element.options.editable"
                    :clearable="element.options.clearable" :format="element.options.format"
                    :value-format="element.options.valueFormat" :disabled="element.options.disabled"
                    :style="{ width: element.options.width }" />
            </template>

            <template v-if="element.type === 'rate'">
                <el-rate readonly :modelValue="element.options.defaultValue" :max="element.options.max"
                    :allowHalf="element.options.allowHalf" :disabled="element.options.disabled" />
            </template>

            <template v-if="element.type === 'select'">
                <el-select readonly :modelValue="element.options.defaultValue" :multiple="element.options.multiple"
                    :collapseTags="element.options.collapseTags" :placeholder="element.options.placeholder"
                    :multipleLimit="element.options.multipleLimit" :clearable="element.options.clearable"
                    :filterable="element.options.filterable" :disabled="true" :style="{ width: element.options.width }">
                    <el-option v-for="item of element.options.options" :key="item.value" :value="item.value"
                        :label="element.options.showLabel ? item.label : item.value" />
                </el-select>
            </template>

            <template v-if="element.type === 'switch'">
                <el-switch readonly :modelValue="element.options.defaultValue" :active-text="element.options.activeText"
                    :inactive-text="element.options.inactiveText" :disabled="element.options.disabled" />
            </template>

            <!-- <template v-if="element.type == 'text'">
                <span>{{ element.options.defaultValue }}</span>
            </template> -->

            <template v-if="element.type === 'img-upload'">
                <sc-upload-Multiple :limit="element.options.limit" :file-list="element.options.defaultValue"
                    :name="element.options.file" :disabled="true"
                    :multiple="element.options.multiple"></sc-upload-Multiple>
            </template>

            <template v-if="element.type === 'file-upload'">
                <sc-upload-file :limit="element.options.limit" :file-list="element.options.defaultValue"
                    :name="element.options.file" :disabled="true" :multiple="element.options.multiple"></sc-upload-file>
            </template>

            <!-- <template v-if="element.type === 'divider'">
                <el-divider :direction='element.options.direction' :border-style='element.options.borderStyle'
                    :content-position='element.options.contentPosition'>
                    {{ element.options.content }}
                </el-divider>
            </template> -->
        </el-form-item>
        <div class="center-view-action"
            v-if="selectWidget?.key === element.key && element.key !== 'title' && element.key !== 'headImage'">
            <cusSvgIcon iconClass="copy" @click.stop="$emit('copy')" />
            <cusSvgIcon iconClass="delete" @click.stop="$emit('delete')" />
        </div>

        <!-- <div class="center-view-drag" v-if="selectWidget?.key === element.key">
            <cusSvgIcon iconClass="move" className="drag-center" />
        </div> -->
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import CusEditor from '@/components/custom/cusEditor.vue'
import { widgetForm } from '../components'
import CusSelectStudent from '@/components/custom/cusSelectStudent.vue';
import CusSelectTeacher from '@/components/custom/cusSelectTeacher.vue';
import CusSelectasset from '@/components/custom/cusSelectasset.vue';
import CusSelectField from '@/components/custom/cusSelectField.vue';
import CusSelectRoom from '@/components/custom/cusSelectRoom.vue';
import CusSelectTree from '@/components/custom/cusSelectTree.vue';
import CusSelectConsumables from '@/components/custom/cusSelectConsumables.vue';
import CusSelectDepartment from '@/components/custom/cusSelectDepartment.vue';
import CusSelectSemester from '@/components/custom/cusSelectSemester.vue';
import CusSelectClass from '@/components/custom/cusSelectClass.vue';
import CusSelectInfo from '@/components/custom/cusSelectInfo.vue';

export default defineComponent({
    name: 'CenterItem',
    components: {
        CusEditor,
        CusSelectStudent,
        CusSelectTeacher,
        CusSelectasset,
        CusSelectField,
        CusSelectRoom,
        CusSelectTree,
        CusSelectConsumables,
        CusSelectDepartment,
        CusSelectSemester,
        CusSelectClass,
        CusSelectInfo
    },
    props: {
        config: {
            type: Object,
            required: true
        },
        element: {
            type: Object,
            required: true
        },
        selectWidget: {
            type: Object
        }
    },
    emits: ['copy', 'delete']
})
</script>
<style lang="scss" scoped></style>
