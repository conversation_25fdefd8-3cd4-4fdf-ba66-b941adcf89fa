<template>
    <div class="addBtn">
        <el-button type="primary" icon="el-icon-plus" @click="table_add">新增时段</el-button>
    </div>
    <scTable ref="table" row-key="id" :data="timeList" :hideDo="true" hidePagination>
        <el-table-column prop="course_name" label="学科"></el-table-column>
        <el-table-column prop="examine_date" label="日期"></el-table-column>
        <el-table-column prop="sign_time" label="签到时间"></el-table-column>
        <el-table-column prop="begin_time" label="考试时间">
            <template #default="{ row }">
                {{ row.begin_time + '~' + row.end_time }}
            </template>
        </el-table-column>
        <el-table-column prop="duration" label="时长" width="150"></el-table-column>
        <el-table-column label="操作">
            <template #default="{ row }">
                <el-button type="text" @click="table_edit(row)">编辑</el-button>
                <el-popconfirm title="确定删除吗？" @confirm="table_del(row)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
            </template>
        </el-table-column>
    </scTable>
    <timeSave :courseList="courseList" ref="timeSaveRef" @saveSuccess="getTimeList"></timeSave>
</template>

<script setup>
import timeSave from './save.vue'
import { ElMessageBox, ElMessage } from 'element-plus'

import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()

const props = defineProps({
    courseList: {
        type: Array,
        default: () => []
    }
})

const params = ref({
    examine_id: query.id,
    tenant_id: tenantId,
    campus_id: campusId,
})

const timeSaveRef = ref()
const table_add = () => {
    timeSaveRef.value.open('add')
}
const table_edit = (row) => {
    timeSaveRef.value.open('edit', row)
}
const table_del = (row) => {
    
    globalPropValue.examine.timeDel.post({
        tenant_id: row.tenant_id,
        campus_id: row.campus_id,
        id: row.id
    }).then(res => {
        if (res.code === 200) {
            ElMessage.success('删除成功')
            getTimeList()
        }
    })
}


const timeList = ref([])
const getTimeList = () => {
    globalPropValue.examine.timeList.get({
        tenant_id: tenantId,
        campus_id: campusId,
        examine_id: query.id
    }).then(res => {
        if (res.code === 200) {
            timeList.value = res.data || []
        }
    })
}
getTimeList()
</script>

<style scoped>
.addBtn {
    text-align: right;
    margin-bottom: 10px;
}
</style>