import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import isBetween from 'dayjs/plugin/isBetween'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isToday from 'dayjs/plugin/isToday'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import weekday from 'dayjs/plugin/weekday'
import updateLocale from 'dayjs/plugin/updateLocale'
import localeData from 'dayjs/plugin/localeData'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import minMax from 'dayjs/plugin/minMax'
import duration from 'dayjs/plugin/duration'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.locale('zh-cn')
dayjs.extend(customParseFormat)
dayjs.extend(isBetween)
dayjs.extend(isSameOrBefore)
dayjs.extend(isToday)
dayjs.extend(weekday)
dayjs.extend(updateLocale)
dayjs.extend(localeData)
dayjs.extend(isSameOrAfter)
dayjs.extend(minMax)
dayjs.extend(duration)
dayjs.extend(relativeTime)

/**
 * 获取从n天前到今天的日期范围。
 * @param {number} n 天数。
 * @returns {Array} 返回一个数组，包含两个元素，分别是n天前的日期和今天的日期，格式为YYYY-MM-DD。
 */
export function getNDaysRange(n) {
	// 计算n天前的日期
	const startDate = dayjs().subtract(n, 'day').format('YYYY-MM-DD')
	// 获取今天的日期
	const endDate = dayjs().format('YYYY-MM-DD')

	return [startDate, endDate]
}
/**
 * 获取从n天前到今天的日期范围。
 * @param {number} n 天数。
 * @returns {Array} 返回一个数组，包含两个元素，分别是n天前的日期和今天的日期，格式为YYYY-MM-DD。
 */
export function getNDaysAfterRange(start,end) {
	// 计算n天前的日期
	const startDate = dayjs().add(start, 'day').format('YYYY-MM-DD')
	// 获取今天的日期
	const endDate = dayjs().add(end, 'day').format('YYYY-MM-DD')

	return [startDate, endDate]
}
/**
 * 获取本月第一天至最后一天的日期范围。
 * @returns {Array} 返回一个数组，包含两个元素，分别是本月的第一天和最后一天的日期，格式为YYYY-MM-DD。
 */
export function getMonthRange() {
	// 获取当前月份的第一天，无需指定日期，默认为每月的第一天
	const startDate = dayjs().startOf('month').format('YYYY-MM-DD')
	// 获取当前月份的最后一天，同样地，endOf会自动定位到月末
	const endDate = dayjs().endOf('month').format('YYYY-MM-DD')

	return [startDate, endDate]
}

// 使用示例：获取本月第一天至最后一天的日期范围
// console.log(getMonthRange())
export function getDaysLater() {
	// 获取当前日期
	const today = dayjs()

	// 初始化一个数组来存储日期和星期几
	const datesAndWeekdays = []

	// 循环6次，获取当前日期后的每一天
	for (let i = 0; i < 7; i++) {
		const date = today.add(i, 'day').format('YYYY-MM-DD') // 格式化日期
		const weekday = today.add(i, 'day').format('dddd')
		const id = today.add(i).valueOf() // 获取id
		datesAndWeekdays.push({ date, weekday, id })
	}
	return datesAndWeekdays
	// 打印结果
	console.log(datesAndWeekdays)
}

// 获取时间范围日期以及星期
export function getDateWeekArray(start, end) {
	const daysArray = []
	if (!start || !end) {
		return getDaysLater()
	}
	let current = dayjs(start)
	while (current.isBefore(end) || current.isSame(end, 'day')) {
		daysArray.push({
			date: current.format('YYYY-MM-DD'),
			weekday: current.format('dddd'),
			id: current.valueOf()
		})
		current = current.add(1, 'day')
	}

	return daysArray
}
// 获取当前日期之前
export function getDateBefore(time){
	return dayjs(time).fromNow()
}
// 判断时间是否是当前之后
export function isBeforeToday(date) {
	const today = dayjs()
	return dayjs(date).isBefore(today)
}
