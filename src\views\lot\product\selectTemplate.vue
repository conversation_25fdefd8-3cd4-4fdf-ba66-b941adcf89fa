<template>
	<el-drawer v-model="visible" title="选择品类" :size="650" destroy-on-close @closed="closed">
		<el-container>
			<el-header>
				<div class="left-panel">
					<div class="left-panel-search">
						<el-form-item label="">
							<el-input v-model="params.name" placeholder="请输入模板名称" clearable></el-input>
						</el-form-item>
						<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
						<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
					</div>
				</div>
			</el-header>
			<el-main>
				<scTable
					ref="templateTable"
					paginationLayout="total, prev, pager, next"
					row-key="id"
					:apiObj="apiObj"
					:page-size="10"
					:params="params"
					:hideDo="true"
				>
					<el-table-column label="品类名称" prop="template_name"></el-table-column>
					<el-table-column label="场景" prop="scene" width="100"></el-table-column>
					<el-table-column label="描述" prop="desc" width="200"></el-table-column>
					<el-table-column label="操作" fixed="right" align="center" width="150">
						<template #default="scope">
							<el-button text type="success" size="small" @click="submit(scope.row)">选择</el-button>
							<el-divider direction="vertical" />
							<el-button text type="primary" size="small" @click="showTsl(scope.row)">查看物模型 </el-button>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-drawer>
	<template-tsl v-if="showTslStatus" ref="templateTsl" @closed="closeTemplateTsl"></template-tsl>
</template>

<script>
import ScTable from '@/components/scTable/index.vue'
import TemplateTsl from '@/views/lot/product/templateTsl.vue'

const defaultParams = () => {
	return {
		name: null,
		status: 1
	}
}
export default {
	components: { TemplateTsl, ScTable },
	emits: ['success', 'closed', 'changeSize'],
	props: {},

	data() {
		return {
			mode: 'add',
			params: defaultParams(),
			apiObj: this.$LotApi.template.list,
			visible: false,
			showTslStatus: false,
			isSaveing: false,
			//表单数据
			enumConfig: []
		}
	},
	mounted() {},
	created() {},
	methods: {
		closeTemplateTsl() {
			this.showTslStatus = false
		},
		closed() {
			this.$emit('changeSize')
			this.$nextTick(() => {
				this.visible = false
				this.$emit('closed')
			})
		},
		//搜索
		upsearch() {
			this.$refs.templateTable.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//显示
		open() {
			this.visible = true
			return this
		},
		submit(row) {
			this.visible = false
			this.$emit('success', row)
		},
		showTsl(row) {
			this.showTslStatus = true
			this.$nextTick(() => {
				this.$refs.templateTsl.open(row)
			})
		}
	}
}
</script>

<style></style>
