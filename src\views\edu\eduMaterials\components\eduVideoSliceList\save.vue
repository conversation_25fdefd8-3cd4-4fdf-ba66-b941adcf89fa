<template>
	<el-dialog v-model="visible" :title="type[model]" destroy-on-close width="30%">
		<el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
			<el-form-item label="切片名称" prop="name" label-width="100px">
				<el-input v-model="form.name" placeholder="请输入"></el-input>
			</el-form-item>
			<el-form-item label="封面" label-width="100px">
				<scUpload v-model="form.cover_url" fileTypeTag="eduMaterials"></scUpload>
			</el-form-item>
			<el-form-item label="切片描述" label-width="100px">
				<el-input v-model="form.description" placeholder="请输入"></el-input>
			</el-form-item>
			<el-form-item label="开始时间" prop="time_begin" label-width="100px">
				<el-input-number v-model="form.time_begin" :min="1"></el-input-number>
				<span style="margin-left: 8px">s</span>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit">保 存</el-button>
		</template>
	</el-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus'

const defaultData = () => {
	return {
		id: null,
		tenant_id: 0,
		campus_id: 0,
		material_id: 0,
		name: '',
		description: '',
		time_begin: '',
		cover_url: ''
	}
}

import { ref, reactive, getCurrentInstance } from 'vue'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const emit = defineEmits(['success'])
const props = defineProps(['params'])
let visible = ref(false)
let formRef = ref(null)
const form = ref(defaultData())
let isSaveing = ref(false)
const rules = reactive({
	name: [{ required: true, message: '请输入切片名称', trigger: 'blur' }],
	time_begin: [{ required: true, message: '请输入切片开始时间', trigger: 'blur' }]
})
const type = reactive({ add: '新增', edit: '编辑' })
let model = ref('add')
const open = (mode = 'add') => {
	model.value = mode
	form.value = defaultData()
	form.value.tenant_id = Number(props.params.tenant_id)
	form.value.campus_id = Number(props.params.campus_id)
	form.value.material_id = Number(props.params.material_id)
	visible.value = true
}
// 保存按钮点击事件
const submit = async () => {
	await formRef.value.validate()
	isSaveing.value = true
	const res = await globalPropValue.eduMaterials.material.add_edit_video_slice.post(form.value)
	isSaveing.value = false
	if (res.code === 200) {
		emit('success', form.value, model.value)
		ElMessage({ type: 'success', message: `${model.value === 'add' ? '新增成功' : '编辑成功'}` })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
	visible.value = false
}
// 数据注入
const setData = (data) => {
	Object.assign(form.value, data)
}
defineExpose({
	open,
	setData
})
</script>

<style scoped></style>
