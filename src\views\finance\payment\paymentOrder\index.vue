<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<!-- <el-button type="primary" icon="el-icon-plus" @click="add">新增招生计划</el-button> -->
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<el-table-column label="订单号" width="180" fixed="left">
					<template #default="{ row }">
						<el-button link type="primary" size="small" @click="table_detail(row)">{{ row.order_sn }}</el-button>
					</template>
				</el-table-column>
				<el-table-column label="订单名称" prop="order_name" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="订单预计总收款" prop="total_amount" width="120"></el-table-column>
				<el-table-column label="订单已收款" prop="total_received" width="120"></el-table-column>
				<el-table-column label="总数量" prop="total_num" width="100"></el-table-column>
				<el-table-column label="已缴数量" prop="paid_num" width="100"></el-table-column>
				<el-table-column label="学年学期" prop="academic_name" width="180">
					<template #default="{ row }"> {{ row.academic_name }} - {{ row.semester_name }} </template>
				</el-table-column>
				<el-table-column label="关联班级" prop="class_list" width="180" show-overflow-tooltip>
					<template #default="{ row }">
						<span v-for="(item, index) in row.class_list" :key="item.id" type="primary" size="small"
							>{{ item.grade_name }}{{ item.class_name }}
							<span v-if="index < row.class_list.length - 1">&nbsp;|&nbsp;</span>
						</span>
					</template>
				</el-table-column>
				<!--				<el-table-column label="开始时间" prop="created_at" width="150"></el-table-column>
				<el-table-column label="截至时间" prop="end_time" width="150"></el-table-column>-->

				<el-table-column label="状态" prop="status" width="150">
					<template #default="scope">
						<el-tag v-if="scope.row.status === 1" type="danger">待发布</el-tag>
						<el-tag v-if="scope.row.status === 2" type="success">已发布</el-tag>
						<el-tag v-if="scope.row.status === -1" type="info">作废</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="210">
					<template #default="scope">
						<el-button-group>
							<el-popconfirm v-if="scope.row.status == 1" title="确定发布吗？" @confirm="table_publish(scope.row)">
								<template #reference>
									<el-button text type="success" size="small">发布</el-button>
								</template>
							</el-popconfirm>
							<el-button v-if="scope.row.status == 1" text type="primary" size="small" @click="table_edit(scope.row)"
								>编辑</el-button
							>
							<el-popconfirm title="确定作废吗？" @confirm="table_cancel(scope.row)">
								<template #reference>
									<el-button v-if="scope.row.status == 2" text type="info" size="small">作废</el-button>
								</template>
							</el-popconfirm>
							<el-popconfirm v-if="scope.row.status == 1" title="确定删除吗？" @confirm="table_del(scope.row)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<!-- 缴费单编辑弹层组件 -->
	<saveOrderDialog ref="dialog" :params="params" @success="handleSaveSuccess"></saveOrderDialog>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import cusTom from '@/utils/cusTom'
import saveOrderDialog from '../paymentItems/saveOrder.vue'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 获取路由实例
const router = useRouter()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
let apiObj = ref(globalPropValue.finance.recruit.orderList)
const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null
	}
}
let params = ref(defaultParams())
let selectionArr = ref([])
let searchConfig = ref({
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: null,
			name: 'campus_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择校区',
				noClearable: true,
				items: campusInfo.map((v) => {
					return {
						label: v.name,
						value: v.value
					}
				})
			}
		},
		{
			label: null,
			name: 'name',
			value: null,
			component: 'input',
			options: {
				placeholder: '请输入订单名称',
				items: []
			}
		},
		{
			label: null,
			name: 'academic_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学年',
				items: []
			}
		},
		{
			label: null,
			name: 'semester_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学期',
				items: []
			}
		}
	]
})
const handleSaveSuccess = (data, mode) => {
	if (mode === 'add') {
		upsearch()
	} else {
		table.value.refresh()
	}
}
watch(
	() => params.value.campus_id,
	(val) => {
		params.value.academic_id = null
		searchConfig.value.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
			.filter((v) => v.parent_id === 0 && v.campus_id === val)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
watch(
	() => params.value.academic_id,
	() => {
		params.value.semester_id = null
		searchConfig.value.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
			.filter(
				(v) => v.parent_id !== 0 && v.parent_id === params.value.academic_id && v.campus_id === params.value.campus_id
			)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
let dialog = ref()
let table = ref()
// let CampusManagementList = ref(campusInfo)
// 搜索按钮回调
const upsearch = () => {
	table.value.upData(params.value)
}
// 重置按钮回调
const refresh = () => {
	params.value = defaultParams()
	upsearch()
}
// 发布操作
const table_publish = async (row) => {
	const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.finance.recruit.orderPublish.post(reqData)
	if (res.code === 200) {
		// upsearch()
		table.value.refresh()
		ElMessage({ type: 'success', message: '发布成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
// 作废操作
const table_cancel = async (row) => {
	const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.finance.recruit.orderCancel.post(reqData)
	if (res.code === 200) {
		// upsearch()
		table.value.refresh()
		ElMessage({ type: 'success', message: '作废成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
// 编辑操作
const table_edit = (row) => {
	dialog.value.dialogFormVisible = true
	nextTick(() => {
		dialog.value.open('edit')
		dialog.value.setData(row)
	})
}
// 删除操作
const table_del = async (row) => {
	const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.finance.recruit.orderDel.post(reqData)
	if (res.code === 200) {
		// upsearch()
		table.value.refresh()
		ElMessage({ type: 'success', message: '删除成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
// 表格选择回调
const selectionChange = (selection) => {
	selectionArr.value = selection
}
// 表格点击订单号跳转订单明细
const table_detail = (row) => {
	router.push({
		path: '/payment/paymentDetails',
		query: {
			id: row.id,
			tenantId: row.tenant_id,
			campusId: row.campus_id
		}
	})
}
</script>

<style lang="scss" scoped></style>
