<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode"> </cusForm>
		<div class="tip">
			学校可以使用初始分值继承进行跨学期管理。
			选定其他学年学期后，该学期中各对象的总分数值将继承为当前学期中的初始分值。
		</div>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { semesterInfo } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		semester_id: null,
		tenant_id: null,
		campus_id: null,
		oth_academic_id: null,
		oth_semester_id: null,
		academic_id: null,
		overlay: -1
	}
}
import { ElMessageBox } from 'element-plus'

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '初始分继承',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				oth_semester_id: [{ required: true, message: '请选择学期' }],
				oth_academic_id: [{ required: true, message: '请选择学年' }]
			},
			//所需数据选项
			groups: [],
			groupsProps: {
				value: 'id',
				label: 'semester_name',
				emitPath: false
			},
			gradeOptions: [],
			semesterInfo,
			grade: [],
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				labelWidth: '80px',

				formItems: [
					{
						label: '学年',
						name: 'oth_academic_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择学年',
							items: []
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '学期',
						name: 'oth_semester_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择学期',
							items: []
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					}
				]
			}
		}
	},
	mounted() {},
	watch: {
		'form.campus_id': {
			handler(val) {
				this.formConfig.formItems[0].options.items = this.semesterInfo
					.filter((v) => v.parent_id == 0 && v.campus_id == this.form.campus_id)
					.map((v) => {
						return {
							label: v.name,
							value: v.value
						}
					})
			},
			immediate: true
		},
		'form.oth_academic_id': {
			handler(val) {
				this.formConfig.formItems[1].options.items = this.semesterInfo
					.filter(
						(v) => v.parent_id != 0 && v.parent_id == this.form.oth_academic_id && v.campus_id == this.form.campus_id
					)
					.map((v) => {
						return {
							label: v.name,
							value: v.value
						}
					})
			}
		}
	},
	computed: {},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.form.mode = mode
			if (mode == 'add') {
				this.form.campus_id = this.params.campus_id
				this.form.tenant_id = this.params.tenant_id
				this.form.academic_id = this.params.academic_id
				this.form.semester_id = this.params.semester_id
			}
			this.visible = true
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					ElMessageBox.confirm('继承后，将直接覆盖当前的所有初始分值，确定要继承吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(async () => {
							this.form.overlay = 1
							var res = await this.$API.buildingRooms.scoreSetting.copy.post(this.form)
							this.isSaveing = false
							if (res.code === 200) {
								this.$emit('success', this.form, this.mode)
								this.visible = false
								this.$message.success('操作成功')
							} else {
								this.$alert(res.message, '提示', { type: 'error' })
							}
						})
						.finally(() => {
							this.isSaveing = false
						})
				}
			})
		},
		//表单注入数据
		setData(data) {
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style scoped lang="scss">
.tip {
	color: #808080;
	background-color: #f6f8fa;
	padding: 20px;
}
</style>
