<template>
	<div>
		<el-cascader
			v-model="value"
			:placeholder="placeholder"
			:disabled="disabled"
			:options="options"
			:props="prop"
			clearable
			:style="{ width: width }"
			@change="handleChange"
		/>
	</div>
</template>

<script>
export default {
	props: {
		prop: {
			type: Object,
			default: () => {
				return {
					emitPath: false,
					value: 'value',
					label: 'name',
					checkStrictly: false
				}
			}
		},
		disabled: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: String,
			default: '请选择'
		},
		modelValue: {
			type: [String, Number, Object],
			default: ''
		},
		options: {
			type: Array,
			default: () => {
				return []
			}
		},
		width: {
			type: String,
			default: '200px'
		}
	},
	data() {
		return {
			value: null
		}
	},
	watch: {
		modelValue: {
			handler(val) {
				this.value = val
			},
			immediate: true
		},
		value() {
			this.$emit('update:modelValue', this.value)
		}
	},
	methods: {
		handleChange(val) {
			this.$emit('valChange', val)
		}
	}
}
</script>

<style scoped></style>
