<template>
	<el-drawer
		v-model="visible"
		:title="'教室看板设置 - ' + roomData.room_name"
		size="900"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="环境数据" name="environment">
				<el-card>
					<div style="display: flex; align-items: center">
						<div style="font-size: 14px">选择展示数据：</div>
						<div>
							<el-checkbox-group v-model="argumentList">
								<el-checkbox-button label="温度" value="temperature" />
								<el-checkbox-button label="湿度" value="humidity" />
								<el-checkbox-button label="PM2.5" value="pm25" />
								<el-checkbox-button label="二氧化碳" value="co2" />
								<el-checkbox-button label="甲醛" value="hcho" />
								<el-checkbox-button label="光照度" value="light" />
								<el-checkbox-button label="是否有人" value="isPerson" />
								<el-checkbox-button label="烟雾状态" value="smokeStatus" />
								<el-checkbox-button label="水浸状态" value="waterStatus" />
							</el-checkbox-group>
						</div>
					</div>
					<div class="card-item" v-if="argumentList.includes('temperature')">
						<div class="card-item-title">温度：</div>
						<div>
							<el-row :gutter="10">
								<!-- <el-col :span="4">
							<el-input v-model="temperature" placeholder="显示名称" />
						</el-col> -->
								<el-col :span="6">
									<el-select
										v-model="dataList.temperature.device_id"
										placeholder="设备"
										@change="
											(val) => {
												getDeviceTsl(val, 'temperature')
											}
										"
									>
										<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
									</el-select>
								</el-col>
								<el-col :span="4">
									<el-select v-model="dataList.temperature.tsl_type" placeholder="类型">
										<el-option label="设备属性" :value="1" />
										<el-option label="设备事件" :value="3" />
									</el-select>
								</el-col>
								<el-col :span="6">
									<el-select
										v-model="dataList.temperature.tsl_id"
										v-if="dataList.temperature.tsl_type === 1"
										placeholder="属性"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.temperature.device_id]?.readTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
									<el-select
										v-model="dataList.temperature.tsl_id"
										v-if="dataList.temperature.tsl_type === 3"
										placeholder="事件"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.temperature.device_id]?.eventTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-col>
								<el-col :span="6" v-if="dataList.temperature.tsl_type === 3">
									<el-select v-model="dataList.temperature.event_argument" placeholder="事件参数" clearable>
										<el-option
											v-for="item in deviceTsl[dataList.temperature.device_id]?.eventTslList.filter(
												(v) => v.id === dataList.temperature.tsl_id
											)[0]?.output_params"
											:key="item.id"
											:label="item.name"
											:value="item.code"
										/>
									</el-select>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="card-item" v-if="argumentList.includes('humidity')">
						<div class="card-item-title">湿度：</div>
						<div>
							<el-row :gutter="10">
								<!-- <el-col :span="4">
							<el-input v-model="temperature" placeholder="显示名称" />
						</el-col> -->
								<el-col :span="6">
									<el-select
										v-model="dataList.humidity.device_id"
										placeholder="设备"
										@change="
											(val) => {
												getDeviceTsl(val, 'humidity')
											}
										"
									>
										<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
									</el-select>
								</el-col>
								<el-col :span="4">
									<el-select v-model="dataList.humidity.tsl_type" placeholder="类型">
										<el-option label="设备属性" :value="1" />
										<el-option label="设备事件" :value="3" />
									</el-select>
								</el-col>
								<el-col :span="6">
									<el-select
										v-model="dataList.humidity.tsl_id"
										v-if="dataList.humidity.tsl_type === 1"
										placeholder="属性"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.humidity.device_id]?.readTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
									<el-select
										v-model="dataList.humidity.tsl_id"
										v-if="dataList.humidity.tsl_type === 3"
										placeholder="事件"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.humidity.device_id]?.eventTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-col>
								<el-col :span="6" v-if="dataList.humidity.tsl_type === 3">
									<el-select v-model="dataList.humidity.event_argument" placeholder="事件参数" clearable>
										<el-option
											v-for="item in deviceTsl[dataList.humidity.device_id]?.eventTslList.filter(
												(v) => v.id === dataList.humidity.tsl_id
											)[0]?.output_params"
											:key="item.id"
											:label="item.name"
											:value="item.code"
										/>
									</el-select>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="card-item" v-if="argumentList.includes('pm25')">
						<div class="card-item-title">PM2.5：</div>
						<div>
							<el-row :gutter="10">
								<!-- <el-col :span="4">
							<el-input v-model="temperature" placeholder="显示名称" />
						</el-col> -->
								<el-col :span="6">
									<el-select
										v-model="dataList.pm25.device_id"
										placeholder="设备"
										@change="
											(val) => {
												getDeviceTsl(val, 'pm25')
											}
										"
									>
										<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
									</el-select>
								</el-col>
								<el-col :span="4">
									<el-select v-model="dataList.pm25.tsl_type" placeholder="类型">
										<el-option label="设备属性" :value="1" />
										<el-option label="设备事件" :value="3" />
									</el-select>
								</el-col>
								<el-col :span="6">
									<el-select
										v-model="dataList.pm25.tsl_id"
										v-if="dataList.pm25.tsl_type === 1"
										placeholder="属性"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.pm25.device_id]?.readTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
									<el-select
										v-model="dataList.pm25.tsl_id"
										v-if="dataList.pm25.tsl_type === 3"
										placeholder="事件"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.pm25.device_id]?.eventTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-col>
								<el-col :span="6" v-if="dataList.pm25.tsl_type === 3">
									<el-select v-model="dataList.pm25.event_argument" placeholder="事件参数" clearable>
										<el-option
											v-for="item in deviceTsl[dataList.pm25.device_id]?.eventTslList.filter(
												(v) => v.id === dataList.pm25.tsl_id
											)[0]?.output_params"
											:key="item.id"
											:label="item.name"
											:value="item.code"
										/>
									</el-select>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="card-item" v-if="argumentList.includes('co2')">
						<div class="card-item-title">二氧化碳：</div>
						<div>
							<el-row :gutter="10">
								<!-- <el-col :span="4">
							<el-input v-model="temperature" placeholder="显示名称" />
						</el-col> -->
								<el-col :span="6">
									<el-select
										v-model="dataList.co2.device_id"
										placeholder="设备"
										@change="
											(val) => {
												getDeviceTsl(val, 'co2')
											}
										"
									>
										<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
									</el-select>
								</el-col>
								<el-col :span="4">
									<el-select v-model="dataList.co2.tsl_type" placeholder="类型">
										<el-option label="设备属性" :value="1" />
										<el-option label="设备事件" :value="3" />
									</el-select>
								</el-col>
								<el-col :span="6">
									<el-select
										v-model="dataList.co2.tsl_id"
										v-if="dataList.co2.tsl_type === 1"
										placeholder="属性"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.co2.device_id]?.readTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
									<el-select
										v-model="dataList.co2.tsl_id"
										v-if="dataList.co2.tsl_type === 3"
										placeholder="事件"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.co2.device_id]?.eventTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-col>
								<el-col :span="6" v-if="dataList.co2.tsl_type === 3">
									<el-select v-model="dataList.co2.event_argument" placeholder="事件参数" clearable>
										<el-option
											v-for="item in deviceTsl[dataList.co2.device_id]?.eventTslList.filter(
												(v) => v.id === dataList.co2.tsl_id
											)[0]?.output_params"
											:key="item.id"
											:label="item.name"
											:value="item.code"
										/>
									</el-select>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="card-item" v-if="argumentList.includes('hcho')">
						<div class="card-item-title">甲醛：</div>
						<div>
							<el-row :gutter="10">
								<!-- <el-col :span="4">
							<el-input v-model="temperature" placeholder="显示名称" />
						</el-col> -->
								<el-col :span="6">
									<el-select
										v-model="dataList.hcho.device_id"
										placeholder="设备"
										@change="
											(val) => {
												getDeviceTsl(val, 'hcho')
											}
										"
									>
										<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
									</el-select>
								</el-col>
								<el-col :span="4">
									<el-select v-model="dataList.hcho.tsl_type" placeholder="类型">
										<el-option label="设备属性" :value="1" />
										<el-option label="设备事件" :value="3" />
									</el-select>
								</el-col>
								<el-col :span="6">
									<el-select
										v-model="dataList.hcho.tsl_id"
										v-if="dataList.hcho.tsl_type === 1"
										placeholder="属性"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.hcho.device_id]?.readTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
									<el-select
										v-model="dataList.hcho.tsl_id"
										v-if="dataList.hcho.tsl_type === 3"
										placeholder="事件"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.hcho.device_id]?.eventTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-col>
								<el-col :span="6" v-if="dataList.hcho.tsl_type === 3">
									<el-select v-model="dataList.hcho.event_argument" placeholder="事件参数" clearable>
										<el-option
											v-for="item in deviceTsl[dataList.hcho.device_id]?.eventTslList.filter(
												(v) => v.id === dataList.hcho.tsl_id
											)[0]?.output_params"
											:key="item.id"
											:label="item.name"
											:value="item.code"
										/>
									</el-select>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="card-item" v-if="argumentList.includes('light')">
						<div class="card-item-title">光照度：</div>
						<div>
							<el-row :gutter="10">
								<!-- <el-col :span="4">
							<el-input v-model="temperature" placeholder="显示名称" />
						</el-col> -->
								<el-col :span="6">
									<el-select
										v-model="dataList.light.device_id"
										placeholder="设备"
										@change="
											(val) => {
												getDeviceTsl(val, 'light')
											}
										"
									>
										<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
									</el-select>
								</el-col>
								<el-col :span="4">
									<el-select v-model="dataList.light.tsl_type" placeholder="类型">
										<el-option label="设备属性" :value="1" />
										<el-option label="设备事件" :value="3" />
									</el-select>
								</el-col>
								<el-col :span="6">
									<el-select
										v-model="dataList.light.tsl_id"
										v-if="dataList.light.tsl_type === 1"
										placeholder="属性"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.light.device_id]?.readTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
									<el-select
										v-model="dataList.light.tsl_id"
										v-if="dataList.light.tsl_type === 3"
										placeholder="事件"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.light.device_id]?.eventTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-col>
								<el-col :span="6" v-if="dataList.light.tsl_type === 3">
									<el-select v-model="dataList.light.event_argument" placeholder="事件参数" clearable>
										<el-option
											v-for="item in deviceTsl[dataList.light.device_id]?.eventTslList.filter(
												(v) => v.id === dataList.light.tsl_id
											)[0]?.output_params"
											:key="item.id"
											:label="item.name"
											:value="item.code"
										/>
									</el-select>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="card-item" v-if="argumentList.includes('isPerson')">
						<div class="card-item-title">是否有人：</div>
						<div>
							<el-row :gutter="10">
								<!-- <el-col :span="4">
							<el-input v-model="temperature" placeholder="显示名称" />
						</el-col> -->
								<el-col :span="6">
									<el-select
										v-model="dataList.isPerson.device_id"
										placeholder="设备"
										@change="
											(val) => {
												getDeviceTsl(val, 'isPerson')
											}
										"
									>
										<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
									</el-select>
								</el-col>
								<el-col :span="4">
									<el-select v-model="dataList.isPerson.tsl_type" placeholder="类型">
										<el-option label="设备属性" :value="1" />
										<el-option label="设备事件" :value="3" />
									</el-select>
								</el-col>
								<el-col :span="6">
									<el-select
										v-model="dataList.isPerson.tsl_id"
										v-if="dataList.isPerson.tsl_type === 1"
										placeholder="属性"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.isPerson.device_id]?.readTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
									<el-select
										v-model="dataList.isPerson.tsl_id"
										v-if="dataList.isPerson.tsl_type === 3"
										placeholder="事件"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.isPerson.device_id]?.eventTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-col>
								<el-col :span="6" v-if="dataList.isPerson.tsl_type === 3">
									<el-select v-model="dataList.isPerson.event_argument" placeholder="事件参数" clearable>
										<el-option
											v-for="item in deviceTsl[dataList.isPerson.device_id]?.eventTslList.filter(
												(v) => v.id === dataList.isPerson.tsl_id
											)[0]?.output_params"
											:key="item.id"
											:label="item.name"
											:value="item.code"
										/>
									</el-select>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="card-item" v-if="argumentList.includes('smokeStatus')">
						<div class="card-item-title">烟雾状态：</div>
						<div>
							<el-row :gutter="10">
								<!-- <el-col :span="4">
							<el-input v-model="temperature" placeholder="显示名称" />
						</el-col> -->
								<el-col :span="6">
									<el-select
										v-model="dataList.smokeStatus.device_id"
										placeholder="设备"
										@change="
											(val) => {
												getDeviceTsl(val, 'smokeStatus')
											}
										"
									>
										<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
									</el-select>
								</el-col>
								<el-col :span="4">
									<el-select v-model="dataList.smokeStatus.tsl_type" placeholder="类型">
										<el-option label="设备属性" :value="1" />
										<el-option label="设备事件" :value="3" />
									</el-select>
								</el-col>
								<el-col :span="6">
									<el-select
										v-model="dataList.smokeStatus.tsl_id"
										v-if="dataList.smokeStatus.tsl_type === 1"
										placeholder="属性"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.smokeStatus.device_id]?.readTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
									<el-select
										v-model="dataList.smokeStatus.tsl_id"
										v-if="dataList.smokeStatus.tsl_type === 3"
										placeholder="事件"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.smokeStatus.device_id]?.eventTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-col>
								<el-col :span="6" v-if="dataList.smokeStatus.tsl_type === 3">
									<el-select v-model="dataList.smokeStatus.event_argument" placeholder="事件参数" clearable>
										<el-option
											v-for="item in deviceTsl[dataList.smokeStatus.device_id]?.eventTslList.filter(
												(v) => v.id === dataList.smokeStatus.tsl_id
											)[0]?.output_params"
											:key="item.id"
											:label="item.name"
											:value="item.code"
										/>
									</el-select>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="card-item" v-if="argumentList.includes('waterStatus')">
						<div class="card-item-title">水浸状态：</div>
						<div>
							<el-row :gutter="10">
								<el-col :span="6">
									<el-select
										v-model="dataList.waterStatus.device_id"
										placeholder="设备"
										@change="
											(val) => {
												getDeviceTsl(val, 'waterStatus')
											}
										"
									>
										<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
									</el-select>
								</el-col>
								<el-col :span="4">
									<el-select v-model="dataList.waterStatus.tsl_type" placeholder="类型">
										<el-option label="设备属性" :value="1" />
										<el-option label="设备事件" :value="3" />
									</el-select>
								</el-col>
								<el-col :span="6">
									<el-select
										v-model="dataList.waterStatus.tsl_id"
										v-if="dataList.waterStatus.tsl_type === 1"
										placeholder="属性"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.waterStatus.device_id]?.readTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
									<el-select
										v-model="dataList.waterStatus.tsl_id"
										v-if="dataList.waterStatus.tsl_type === 3"
										placeholder="事件"
										clearable
									>
										<el-option
											v-for="item in deviceTsl[dataList.waterStatus.device_id]?.eventTslList"
											:key="item.id"
											:label="item.name"
											:value="item.id"
										/>
									</el-select>
								</el-col>
								<el-col :span="6" v-if="dataList.waterStatus.tsl_type === 3">
									<el-select v-model="dataList.waterStatus.event_argument" placeholder="事件参数" clearable>
										<el-option
											v-for="item in deviceTsl[dataList.waterStatus.device_id]?.eventTslList.filter(
												(v) => v.id === dataList.waterStatus.tsl_id
											)[0]?.output_params"
											:key="item.id"
											:label="item.name"
											:value="item.code"
										/>
									</el-select>
								</el-col>
							</el-row>
						</div>
					</div>
				</el-card>
			</el-tab-pane>
			<el-tab-pane label="空调数据" name="airConditioner">
				<el-card>
					<template #header>
						<div style="display: flex; justify-content: space-between">
							<div>
								<el-button type="primary" size="small" plain @click="addAirConditioner">添加空调</el-button>
							</div>
						</div>
					</template>
					<div v-for="(item, index) in dataList.airConditioner" :key="index" class="card-item">
						<div class="card-item-title">
							<div>空调名称：{{ item.group_name }}</div>
							<div style="display: flex; align-items: center; padding-left: 20px">
								<el-button icon="el-icon-edit" size="small" circle type="primary" @click="editAirConditioner(index)" />
								<el-button
									icon="el-icon-delete"
									size="small"
									circle
									type="danger"
									@click="deleteAirConditioner(index)"
								/>
							</div>
						</div>
						<div v-for="(item2, index2) in item.list" :key="index2" style="padding-left: 20px">
							<div class="item-title">{{ item2.show_name }}：</div>
							<div>
								<el-row :gutter="10">
									<el-col :span="6">
										<el-select
											v-model="item2.device_id"
											placeholder="设备"
											@change="
												(val) => {
													getDeviceTsl(val, 'airConditioner')
												}
											"
										>
											<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
										</el-select>
									</el-col>
									<el-col :span="4">
										<el-select v-model="item2.tsl_type" placeholder="类型">
											<el-option label="设备属性" :value="1" />
											<el-option label="设备事件" :value="3" />
										</el-select>
									</el-col>
									<el-col :span="6">
										<el-select v-model="item2.tsl_id" v-if="item2.tsl_type === 1" placeholder="属性" clearable>
											<el-option
												v-for="item in deviceTsl[item2.device_id]?.readTslList"
												:key="item.id"
												:label="item.name"
												:value="item.id"
											/>
										</el-select>
										<el-select v-model="item2.tsl_id" v-if="item2.tsl_type === 3" placeholder="事件" clearable>
											<el-option
												v-for="item in deviceTsl[item2.device_id]?.eventTslList"
												:key="item.id"
												:label="item.name"
												:value="item.id"
											/>
										</el-select>
									</el-col>
									<el-col :span="6" v-if="item2.tsl_type === 3">
										<el-select v-model="item2.event_argument" placeholder="事件参数" clearable>
											<el-option
												v-for="item in deviceTsl[item2.device_id]?.eventTslList.filter((v) => v.id === item2.tsl_id)[0]
													?.output_params"
												:key="item.id"
												:label="item.name"
												:value="item.code"
											/>
										</el-select>
									</el-col>
								</el-row>
							</div>
						</div>
					</div>
				</el-card>
			</el-tab-pane>
			<el-tab-pane label="灯光数据" name="lamplight">
				<el-card>
					<template #header>
						<div style="display: flex; align-items: center; justify-content: space-between">
							<div>
								<el-button type="primary" size="small" plain @click="addLamplight">添加灯光</el-button>
							</div>
						</div>
					</template>
					<div v-for="(item, index) in dataList.lamplight" :key="index" class="card-item">
						<div class="card-item-title">
							<div>灯光名称：{{ item.group_name }}</div>
							<div style="display: flex; align-items: center; padding-left: 20px">
								<el-button icon="el-icon-edit" size="small" circle type="primary" @click="editLamplight(index)" />
								<el-button icon="el-icon-delete" size="small" circle type="danger" @click="deleteLamplight(index)" />
							</div>
						</div>
						<div v-for="(item2, index2) in item.list" :key="index2" style="padding-left: 20px">
							<div class="item-title">{{ item2.show_name }}：</div>
							<div>
								<el-row :gutter="10">
									<el-col :span="6">
										<el-select
											v-model="item2.device_id"
											placeholder="设备"
											clearable
											@change="
												(val) => {
													getDeviceTsl(val, 'light')
												}
											"
										>
											<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
										</el-select>
									</el-col>
									<el-col :span="4">
										<el-select v-model="item2.tsl_type" placeholder="类型">
											<el-option label="设备属性" :value="1" />
											<el-option label="设备事件" :value="3" />
										</el-select>
									</el-col>
									<el-col :span="6">
										<el-select v-model="item2.tsl_id" v-if="item2.tsl_type === 1" placeholder="属性" clearable>
											<el-option
												v-for="item in deviceTsl[item2.device_id]?.readTslList"
												:key="item.id"
												:label="item.name"
												:value="item.id"
											/>
										</el-select>
										<el-select v-model="item2.tsl_id" v-if="item2.tsl_type === 3" placeholder="事件" clearable>
											<el-option
												v-for="item in deviceTsl[item2.device_id]?.eventTslList"
												:key="item.id"
												:label="item.name"
												:value="item.id"
											/>
										</el-select>
									</el-col>
									<el-col :span="6" v-if="item2.tsl_type === 3">
										<el-select v-model="item2.event_argument" placeholder="事件参数" clearable>
											<el-option
												v-for="item in deviceTsl[item2.device_id]?.eventTslList.filter((v) => v.id === item2.tsl_id)[0]
													?.output_params"
												:key="item.id"
												:label="item.name"
												:value="item.code"
											/>
										</el-select>
									</el-col>
								</el-row>
							</div>
						</div>
					</div>
				</el-card>
			</el-tab-pane>
			<el-tab-pane label="窗帘数据" name="curtain">
				<el-card>
					<template #header>
						<div style="display: flex; align-items: center; justify-content: space-between">
							<div>
								<el-button type="primary" size="small" plain @click="addCurtain">添加窗帘</el-button>
							</div>
						</div>
					</template>
					<div v-for="(item, index) in dataList.curtain" :key="index" class="card-item">
						<div class="card-item-title">
							<div>窗帘名称：{{ item.group_name }}</div>
							<div style="display: flex; align-items: center; padding-left: 20px">
								<el-button icon="el-icon-edit" size="small" circle type="primary" @click="editCurtain(index)" />
								<el-button icon="el-icon-delete" size="small" circle type="danger" @click="deleteCurtain(index)" />
							</div>
						</div>
						<div v-for="(item2, index2) in item.list" :key="index2" style="padding-left: 20px">
							<div class="item-title">{{ item2.show_name }}：</div>
							<div>
								<el-row :gutter="10">
									<el-col :span="6">
										<el-select
											v-model="item2.device_id"
											placeholder="设备"
											clearable
											@change="
												(val) => {
													getDeviceTsl(val, 'curtain')
												}
											"
										>
											<el-option v-for="item in deviceList" :key="item.id" :label="item.device_name" :value="item.id" />
										</el-select>
									</el-col>
									<el-col :span="4">
										<el-select v-model="item2.tsl_type" placeholder="类型">
											<el-option label="设备属性" :value="1" />
											<el-option label="设备事件" :value="3" />
										</el-select>
									</el-col>
									<el-col :span="6">
										<el-select v-model="item2.tsl_id" v-if="item2.tsl_type === 1" placeholder="属性" clearable>
											<el-option
												v-for="item in deviceTsl[item2.device_id]?.readTslList"
												:key="item.id"
												:label="item.name"
												:value="item.id"
											/>
										</el-select>
										<el-select v-model="item2.tsl_id" v-if="item2.tsl_type === 3" placeholder="事件" clearable>
											<el-option
												v-for="item in deviceTsl[item2.device_id]?.eventTslList"
												:key="item.id"
												:label="item.name"
												:value="item.id"
											/>
										</el-select>
									</el-col>
									<el-col :span="6" v-if="item2.tsl_type === 3">
										<el-select v-model="item2.event_argument" placeholder="事件参数" clearable>
											<el-option
												v-for="item in deviceTsl[item2.device_id]?.eventTslList.filter((v) => v.id === item2.tsl_id)[0]
													?.output_params"
												:key="item.id"
												:label="item.name"
												:value="item.code"
											/>
										</el-select>
									</el-col>
								</el-row>
							</div>
						</div>
					</div>
				</el-card>
			</el-tab-pane>
		</el-tabs>

		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import cusTom from '@/utils/cusTom'
import { ElMessageBox } from 'element-plus'

const { tenantId, campusId } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		engine_name: '',
		engine_desc: '',
		status: 1
	}
}

export default {
	emits: ['success', 'closed'],
	props: {},

	data() {
		return {
			activeName: 'environment',
			visible: false,
			isSaveing: false,
			enumConfig: [],
			dataList: {
				temperature: {
					show_name: '温度',
					device_id: null,
					tsl_type: 1,
					tsl_id: null,
					event_argument: ''
				},
				humidity: {
					show_name: '湿度',
					device_id: null,
					tsl_type: 1,
					tsl_id: null,
					event_argument: ''
				},
				pm25: {
					show_name: 'PM2.5',
					device_id: null,
					tsl_type: 1,
					tsl_id: null,
					event_argument: ''
				},
				co2: {
					show_name: '二氧化碳',
					device_id: null,
					tsl_type: 1,
					tsl_id: null,
					event_argument: ''
				},
				hcho: {
					show_name: '甲醛',
					device_id: null,
					tsl_type: 1,
					tsl_id: null,
					event_argument: ''
				},
				light: {
					show_name: '光照度',
					device_id: null,
					tsl_type: 1,
					tsl_id: null,
					event_argument: ''
				},
				isPerson: {
					show_name: '是否有人',
					device_id: null,
					tsl_type: 1,
					tsl_id: null,
					event_argument: ''
				},
				smokeStatus: {
					show_name: '烟雾状态',
					device_id: null,
					tsl_type: 1,
					tsl_id: null,
					event_argument: ''
				},
				waterStatus: {
					show_name: '水浸状态',
					device_id: null,
					tsl_type: 1,
					tsl_id: null,
					event_argument: ''
				},
				airConditioner: [],
				lamplight: [],
				curtain: []
			},
			argumentList: [
				'temperature',
				'humidity',
				'pm25',
				'co2',
				'hcho',
				'light',
				'isPerson',
				'smokeStatus',
				'waterStatus'
			],
			deviceList: [],
			roomData: {},
			deviceTsl: {},
			//表单数据
			form: defaultData()
		}
	},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	mounted() {},
	methods: {
		deleteAirConditioner(index) {
			this.dataList.airConditioner.splice(index, 1)
		},
		deleteLamplight(index) {
			this.dataList.lamplight.splice(index, 1)
		},
		deleteCurtain(index) {
			this.dataList.curtain.splice(index, 1)
		},
		editAirConditioner(index) {
			ElMessageBox.prompt('请输入空调名称', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputValue: this.dataList.airConditioner[index].group_name
			})
				.then(({ value }) => {
					this.dataList.airConditioner[index].group_name = value
				})
				.catch(() => {})
		},
		editLamplight(index) {
			ElMessageBox.prompt('请输入灯光名称', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputValue: this.dataList.lamplight[index].group_name
			})
				.then(({ value }) => {
					this.dataList.lamplight[index].group_name = value
				})
				.catch(() => {})
		},
		editCurtain(index) {
			ElMessageBox.prompt('请输入窗帘名称', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputValue: this.dataList.curtain[index].group_name
			})
				.then(({ value }) => {
					this.dataList.curtain[index].group_name = value
				})
				.catch(() => {})
		},

		addAirConditioner() {
			const len = this.dataList.airConditioner.length
			if (len >= 4) {
				ElMessageBox.alert('最多只能添加4个空调', '提示', { type: 'warning' })
				return
			}
			this.dataList.airConditioner.push({
				group_name: '空调-' + (len + 1),
				list: [
					{
						show_name: '空调状态',
						child_argument: 'powerSwitch',
						device_id: null,
						tsl_type: 1,
						tsl_id: null,
						event_argument: ''
					},
					{
						show_name: '工作模式',
						child_argument: 'systemMode',
						device_id: null,
						tsl_type: 1,
						tsl_id: null,
						event_argument: ''
					},
					{
						show_name: '风速模式',
						child_argument: 'fanMode',
						device_id: null,
						tsl_type: 1,
						tsl_id: null,
						event_argument: ''
					},
					{
						show_name: '空调温度',
						child_argument: 'targetTemperature',
						device_id: null,
						tsl_type: 1,
						tsl_id: null,
						event_argument: ''
					}
				]
			})
		},
		addLamplight() {
			const len = this.dataList.lamplight.length
			if (len >= 10) {
				ElMessageBox.alert('最多只能添加10个灯光', '提示', { type: 'warning' })
				return
			}
			this.dataList.lamplight.push({
				group_name: '灯光-' + (len + 1),
				list: [
					{
						show_name: '灯光状态',
						child_argument: 'powerSwitch',
						device_id: null,
						tsl_type: 1,
						tsl_id: null,
						event_argument: ''
					}
				]
			})
		},
		addCurtain() {
			const len = this.dataList.curtain.length
			if (len >= 10) {
				ElMessageBox.alert('最多只能添加10个窗帘', '提示', { type: 'warning' })
				return
			}
			this.dataList.curtain.push({
				group_name: '窗帘-' + (len + 1),
				list: [
					{
						show_name: '开启比例',
						child_argument: 'positionPercentage',
						device_id: null,
						tsl_type: 1,
						tsl_id: null,
						event_argument: ''
					}
				]
			})
		},
		//显示
		open(item) {
			console.log(item)
			this.roomData = item
			this.getRoomDeviceList()
			this.getRoomConf()
			this.visible = true
			return this
		},
		//表单提交方法
		async submit() {
			let subForm = []
			Object.keys(this.dataList).forEach((key) => {
				if (this.argumentList.includes(key)) {
					if (this.dataList[key].device_id && this.dataList[key].tsl_id) {
						subForm.push({
							mode: 1,
							group_name: '',
							argument: key,
							child_argument: this.dataList[key].child_argument,
							show_name: this.dataList[key].show_name,
							device_id: this.dataList[key].device_id,
							tsl_type: this.dataList[key].tsl_type,
							tsl_id: this.dataList[key].tsl_id,
							event_argument: this.dataList[key].event_argument
						})
					}
				} else if (key === 'airConditioner') {
					this.dataList[key].forEach((item) => {
						item.list.forEach((item2) => {
							if (item2.device_id && item2.tsl_id) {
								subForm.push({
									mode: 2,
									child_argument: item2.child_argument,
									group_name: item.group_name,
									argument: key,
									show_name: item2.show_name,
									device_id: item2.device_id,
									tsl_type: item2.tsl_type,
									tsl_id: item2.tsl_id,
									event_argument: item2.event_argument
								})
							}
						})
					})
				} else if (key === 'lamplight') {
					this.dataList[key].forEach((item) => {
						item.list.forEach((item2) => {
							if (item2.device_id && item2.tsl_id) {
								subForm.push({
									mode: 4,
									group_name: item.group_name,
									child_argument: item2.child_argument,
									argument: key,
									show_name: item2.show_name,
									device_id: item2.device_id,
									tsl_type: item2.tsl_type,
									tsl_id: item2.tsl_id,
									event_argument: item2.event_argument
								})
							}
						})
					})
				} else if (key === 'curtain') {
					this.dataList[key].forEach((item) => {
						item.list.forEach((item2) => {
							if (item2.device_id && item2.tsl_id) {
								subForm.push({
									mode: 3,
									group_name: item.group_name,
									argument: key,
									show_name: item2.show_name,
									child_argument: item2.child_argument,
									device_id: item2.device_id,
									tsl_type: item2.tsl_type,
									tsl_id: item2.tsl_id,
									event_argument: item2.event_argument
								})
							}
						})
					})
				}
			})
			var res = await this.$LotApi.room.setConf.post({
				tenant_id: this.roomData.tenant_id,
				campus_id: this.roomData.campus_id,
				room_id: this.roomData.id,
				conf: subForm
			})
			this.isSaveing = false
			if (res.code === 200) {
				this.visible = false
				this.$emit('closed')
				this.$message.success('操作成功')
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}

			/* this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let subForm
					if (this.mode === 'add') {
						subForm = { ...this.params, ...this.form }
					} else if (this.mode === 'edit') {
						subForm = { ...this.form }
					}
					var res = await this.$LotApi.ruleEngine.save.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			}) */
		},
		getRoomConf() {
			this.$LotApi.room.getConf
				.get({
					tenant_id: this.roomData.tenant_id,
					campus_id: this.roomData.campus_id,
					room_id: this.roomData.id
				})
				.then((res) => {
					if (res.code === 200) {
						if (res.data.length <= 0) {
							return
						}
						// 初始化数据结构
						let convertedData = {
							temperature: { show_name: '温度', device_id: null, tsl_type: 1, tsl_id: null, event_argument: '' },
							humidity: { show_name: '湿度', device_id: null, tsl_type: 1, tsl_id: null, event_argument: '' },
							pm25: { show_name: 'PM2.5', device_id: null, tsl_type: 1, tsl_id: null, event_argument: '' },
							co2: { show_name: '二氧化碳', device_id: null, tsl_type: 1, tsl_id: null, event_argument: '' },
							hcho: { show_name: '甲醛', device_id: null, tsl_type: 1, tsl_id: null, event_argument: '' },
							light: { show_name: '光照度', device_id: null, tsl_type: 1, tsl_id: null, event_argument: '' },
							isPerson: { show_name: '是否有人', device_id: null, tsl_type: 1, tsl_id: null, event_argument: '' },
							smokeStatus: { show_name: '烟雾状态', device_id: null, tsl_type: 1, tsl_id: null, event_argument: '' },
							waterStatus: { show_name: '水浸状态', device_id: null, tsl_type: 1, tsl_id: null, event_argument: '' },
							airConditioner: [],
							lamplight: [],
							curtain: []
						}

						// 临时存储分组数据
						let airConditionerGroups = {}
						let lamplightGroups = {}
						let curtainGroups = {}
						let deviceIds = []
						// 遍历数据进行转换
						res.data.forEach((item) => {
							deviceIds.push(item.device_id)
							// 处理环境数据 (mode = 1)
							if (item.mode === 1) {
								if (convertedData[item.argument]) {
									convertedData[item.argument] = {
										show_name: item.show_name,
										device_id: item.device_id,
										tsl_type: item.tsl_type,
										tsl_id: item.tsl_id,
										event_argument: item.event_argument || ''
									}
								}
							}
							// 处理空调数据 (mode = 2)
							else if (item.mode === 2) {
								if (!airConditionerGroups[item.group_name]) {
									airConditionerGroups[item.group_name] = {
										group_name: item.group_name,
										list: []
									}
								}
								airConditionerGroups[item.group_name].list.push({
									show_name: item.show_name,
									device_id: item.device_id,
									tsl_type: item.tsl_type,
									tsl_id: item.tsl_id,
									event_argument: item.event_argument || '',
									child_argument: item.child_argument
								})
							}
							// 处理窗帘数据 (mode = 3)
							else if (item.mode === 3) {
								if (!curtainGroups[item.group_name]) {
									curtainGroups[item.group_name] = {
										group_name: item.group_name,
										list: []
									}
								}
								curtainGroups[item.group_name].list.push({
									show_name: item.show_name,
									device_id: item.device_id,
									tsl_type: item.tsl_type,
									tsl_id: item.tsl_id,
									event_argument: item.event_argument || '',
									child_argument: item.child_argument
								})
							}
							// 处理灯光数据 (mode = 4)
							else if (item.mode === 4) {
								if (!lamplightGroups[item.group_name]) {
									lamplightGroups[item.group_name] = {
										group_name: item.group_name,
										list: []
									}
								}
								lamplightGroups[item.group_name].list.push({
									show_name: item.show_name,
									device_id: item.device_id,
									tsl_type: item.tsl_type,
									tsl_id: item.tsl_id,
									event_argument: item.event_argument || '',
									child_argument: item.child_argument
								})
							}
						})

						// 将分组数据转换为数组
						convertedData.airConditioner = Object.values(airConditionerGroups)
						convertedData.lamplight = Object.values(lamplightGroups)
						convertedData.curtain = Object.values(curtainGroups)
						console.log(convertedData)
						// 更新组件数据
						this.dataList = convertedData
						// 更新选中的环境数据参数
						this.argumentList = Object.keys(convertedData).filter(
							(key) =>
								[
									'temperature',
									'humidity',
									'pm25',
									'co2',
									'hcho',
									'light',
									'isPerson',
									'smokeStatus',
									'waterStatus'
								].includes(key) && convertedData[key].device_id !== null
						)
						this.deviceList.forEach((item) => {
							this.getDeviceTsl(item.id)
						})
					}
				})
		},
		getRoomDeviceList() {
			this.$LotApi.device.all
				.get({
					tenant_id: this.roomData.tenant_id,
					campus_id: this.roomData.campus_id,
					room_id: this.roomData.id
				})
				.then((res) => {
					if (res.code === 200) {
						this.deviceList = res.data
						if (!this.deviceList || this.deviceList.length <= 0) {
							ElMessageBox.alert('当前场室无关联设备', '提示', { type: 'warning' })
						}
					}
				})
		},
		getDeviceTsl(val, type = null) {
			console.log(this.dataList,'val')
			let product_id = 0
			if (type) {
				this.dataList[type].tsl_id = null
				this.dataList[type].event_argument = null
			}
			this.deviceList.forEach((item) => {
				if (item.id === val) {
					product_id = item.product_id
				}
			})
			if (this.deviceTsl[val]) {
				return
			}
			this.deviceTsl[val] = {
				eventTslList: [],
				writerTslList: []
			}
			var reqData = {
				product_id: product_id,
				tenant_id: this.roomData.tenant_id
			}
			this.$LotApi.productTsl.list.get(reqData).then((res) => {
				if (res.code === 200) {
					let readTslList = []
					let eventTslList = []
					res.data.forEach((item) => {
						if (item.type_spec.specs) {
							item.type_spec.specs = JSON.parse(item.type_spec.specs)
						}
						if (item.type_spec.type === 'date' || item.type_spec.type === 'struct' || item.type_spec.type === 'array') {
							return
						}
						if (item.tsl_type === 1 && (item.access_mode === 1 || item.access_mode === 2)) {
							readTslList.push(item)
						}
						if (item.tsl_type === 2) {
							eventTslList.push(item)
						}
					})
					this.deviceTsl[val].eventTslList = eventTslList
					this.deviceTsl[val].readTslList = readTslList
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
	padding: 15px;
}
:deep(.el-card__header) {
	padding: 10px 10px 0px 10px;
}
.card-item {
	border: 1px dashed var(--el-color-primary);
	padding: 10px;
	border-radius: 5px;
	margin-top: 10px;
	margin-bottom: 10px;
	.card-item-title {
		font-size: 14px;
		font-weight: bold;
		height: 30px;
		color: var(--el-color-primary);
		line-height: 30px;
		display: flex;
		align-items: center;
	}
	.el-select {
		width: 100%;
		min-width: unset;
	}
	.item-title {
		font-size: 12px;
		font-weight: bold;
		height: 30px;
		line-height: 30px;
	}
}
</style>
