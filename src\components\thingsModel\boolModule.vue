<template>
	<el-form-item label="布尔值" prop="type_spec.specs">
		<el-row style="width: 100%" gutter="20">
			<el-col v-for="(item, index) in modelValue" :span="12">
				<el-form-item>
					<el-input v-model="modelValue[index]" :placeholder="'请输入' + index + '值定义'" clearable>
						<template #prepend>
							<span>{{ index }}</span>
						</template>
					</el-input>
				</el-form-item>
			</el-col>
		</el-row>
	</el-form-item>
</template>
<script>
export default {
	name: 'boolModule',
	props: {
		modelValue: {
			type: Array,
			default: () => {
				return ['关', '开']
			}
		}
	},
	data() {
		return {
			unitConfig: []
		}
	},
	emits: ['update:modelValue'], // 明确声明该组件会触发 update:modelValue 事件
	watch: {
		modelValue: {
			handler(val) {
				this.$emit('update:modelValue', val)
			},
			deep: true
		}
	},
	created() {},
	methods: {}
}
</script>

<style scoped lang="scss">
.enum_header {
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.enum_item {
	margin: 5px 0;
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.text-center {
	text-align: center;
}
</style>
