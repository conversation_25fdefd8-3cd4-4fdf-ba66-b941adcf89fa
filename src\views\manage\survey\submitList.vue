<template>
    <el-container>
        <el-header>
            <el-page-header content="问卷提交列表" @back="goBack"></el-page-header>
        </el-header>
        <el-main>
            <div class="left-panel" style="margin-bottom: 10px;">
                <div class="left-panel-search">
                    <cusForm ref="formRef" v-model="params" :config="searchConfig" :inline="true"></cusForm>
                    <el-button type="primary" icon="el-icon-search" @click="upSearch">搜索</el-button>
                    <el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
                </div>
            </div>
            <scTable ref="table" row-key="id" :data="submitList" height="calc(100% - 52px)" hide-pagination>
                <el-table-column label="提交人" align="center">
                    <template #default="{ row }">
                        {{ row.user_info?.name || '游客' }}
                    </template>
                </el-table-column>
                <el-table-column label="提交时间" prop="created_at" align="center">
                </el-table-column>
                <el-table-column label="提交信息" align="center">
                    <el-table-column
                        v-for="(item, index) in surveyInfo.form_config?.list.filter(item => !['text', 'headImage', 'title', 'insert-img'].includes(item.type))"
                        :key="item.key" :label="item.label" align="center" show-overflow-tooltip>
                        <template #default="{ row }">
                            <template
                                v-if="item.type === 'img-upload' && row.formItems?.list.find(i => i.model === item.model)?.options.defaultValue?.length">
                                <el-image
                                    :src="row.formItems.list.find(i => i.model === item.model).options.defaultValue.split(',')[0]"
                                    :preview-src-list="row.formItems.list.find(i => i.model === item.model).options.defaultValue.split(',')"
                                    preview-teleported style="width: 100px;height: 100px;" fit="contain" />
                            </template>
                            <template v-else>
                                {{formatDefaultValue(row.formItems?.list.find(i => i.model ===
                                    item.model)?.options.defaultValue)}}
                            </template>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="操作" fixed="right" align="center" width="100">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="handleView(row)">查看</el-button>
                    </template>
                </el-table-column>
            </scTable>
        </el-main>
        <el-footer style="display: flex;justify-content: flex-end;border: none;padding: 0 15px;">
            <el-pagination v-model:current-page="params.page" :page-sizes="[10, 20, 30, 50, 100]"
                :page-size="params.pageSize" size="small" background layout="total,sizes, prev, pager, next,jumper"
                :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </el-footer>
    </el-container>
    <!-- 预览弹窗 -->
    <el-dialog v-model="previewVisible" title="查看" :width="500" align-center>
        <div style="min-height: 450px;max-height: 650px;overflow-y: auto;background: #f6f8f9;">
            <designerFormPreview v-if="previewVisible" ref="designerFormPreviewRef" :data="previewData" />
        </div>
    </el-dialog>
</template>

<script setup>
import designerFormPreview from './formDesigner/designerFormPreview.vue'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { go } = useRouter()
const { query } = useRoute()
const defaultParams = () => {
    return {
        survey_id: query.id,
        time: null,
        begin_time: null,
        end_time: null,
        tenant_id: tenantId,
        campus_id: campusId,
        page: 1,
        pageSize: 20
    }
}
const searchConfig = ref({
    labelPosition: 'right',
    size: 'medium',
    formItems: [
        {
            label: null,
            name: 'campus_id',
            value: null,
            component: 'select',
            options: {
                placeholder: '请选择校区',
                noClearable: true,
                width: '200px',
                items: campusInfo.map((v) => {
                    return {
                        label: v.name,
                        value: v.value
                    }
                })
            }
        },
        {
            label: null,
            name: 'time',
            value: null,
            component: 'date',
            options: {
                startPlaceholder: '开始时间',
                endPlaceholder: '结束时间',
                // width: '240px',
                type: 'datetimerange',
                valueFormat: 'YYYY-MM-DD HH:mm:ss'
            }
        },
    ]
})
const params = ref(defaultParams())
const surveyInfo = ref({})
const previewVisible = ref(false)
const previewData = ref({})
const submitList = ref([])
const total = ref(0)
const handleView = async (data) => {
    // 直接使用处理好的formItems数据
    previewData.value = data.formItems
    previewVisible.value = true
}

const getSurveyInfo = async () => {
    const res = await globalPropValue.survey.one.get({
        tenant_id: tenantId,
        campus_id: campusId,
        id: query.id
    })
    if (res.code === 200) {
        surveyInfo.value = res.data
        if (surveyInfo.value.form_config) {
            surveyInfo.value.form_config = JSON.parse(surveyInfo.value.form_config)
        }
    }
}

const formatDefaultValue = (value) => {
    if (value === undefined || value === null) return ''
    if (value === 0 || value === '0') return '0'
    return Array.isArray(value) ? value.join(',') : value
}

const getSubmitList = async () => {
    const res = await globalPropValue.survey.submitList.get(params.value)
    if (res.code === 200) {
        if (res.data.rows) {
            submitList.value = res.data.rows.map(item => {
                const submitInfo = JSON.parse(item.submit_info)
                const formItems = surveyInfo.value.form_config.list.map(formItem => ({
                    ...formItem,
                    options: {
                        ...formItem.options,
                        defaultValue: submitInfo[formItem.model] ?? formItem.options.defaultValue,
                        disabled: true
                    }
                }))
                return {
                    ...item,
                    formItems: {
                        list: formItems,
                        config: surveyInfo.value.form_config.config
                    }
                }
            })
            total.value = res.data.total
        } else {
            submitList.value = []
            total.value = 0
        }
    }
}

const upSearch = () => {
    if (params.value.time) {
        params.value.begin_time = params.value.time[0]
        params.value.end_time = params.value.time[1]
    }
    // delete params.value.time
    params.value.page = 1
    getSubmitList()
}

const refresh = () => {
    params.value = defaultParams()
    getSubmitList()
}

const handleSizeChange = (size) => {
    params.value.pageSize = size
    getSubmitList()
}

const handleCurrentChange = (page) => {
    params.value.page = page
    getSubmitList()
}
onMounted(async () => {
    await getSurveyInfo()
    await getSubmitList()
})
const goBack = () => {
    go(-1)
}

</script>