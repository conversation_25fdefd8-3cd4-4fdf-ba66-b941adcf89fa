<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"></cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" @click="add_items">新增表单</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
				<el-table-column label="表单标题" prop="title"></el-table-column>
				<el-table-column label="表单类型" prop="form_type">
					<template #default="{ row }"> {{ formData(type, row.form_type) }}</template>
				</el-table-column>
				<el-table-column label="表单封面图" prop="cover_url" width="120">
					<template #default="scope">
						<!-- <el-image
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.cover_url"
							:preview-src-list="[scope.row.cover_url]"
							preview-teleported
						/> -->
						<cusImage loading="lazy" :lazy="true" fit="contain" style="width: 50px; height: 50px"
							:src="scope.row.cover_url" :preview-src-list="[scope.row.cover_url]" preview-teleported>
						</cusImage>
					</template>
				</el-table-column>
				<el-table-column label="创建人" prop="created_user_name"></el-table-column>
				<el-table-column label="开始时间" prop="begin_time"></el-table-column>
				<el-table-column label="截止时间" prop="end_time"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="180">
					<template #default="scope">
						<el-button-group>
							<el-button v-if="scope.row.is_sys !== 1" text type="primary" size="small"
								@click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-button text type="primary" size="small" @click="form_set(scope.row)">配置</el-button>
							<el-popconfirm v-if="scope.row.is_sys !== 1" title="确定删除吗？"
								@confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button v-if="scope.row.is_sys !== 1" text type="danger"
										size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>

		<saveDialog ref="dialogForm" :params="params" @success="saveDataSuccess"></saveDialog>

		<el-dialog v-model="dialog" title=" " fullscreen :show-close="false" :close-on-press-escape="false"
			append-to-body class="designer-form">
			<template #header>
				<div class="my-header">
					<h2>{{ dialogFormTitle }}</h2>
					<div class="header-btn">
						<el-button type="primary" @click="handleSaveForm">
							<template #icon>
								<cusSvgIcon iconClass="save" />
							</template>
							保存
						</el-button>
						<el-button type="danger" :icon="CircleClose" @click="designerFormClose"> 关闭</el-button>
					</div>
				</div>
			</template>
			<designerForm ref="designerFormD" v-loading="loading" @saveForm="saveSuccess"></designerForm>
		</el-dialog>
	</el-container>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick, watch, computed } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CircleClose } from '@element-plus/icons-vue'
import designerForm from './formDesigner/designerForm.vue'
import saveDialog from './save.vue'
import cusImage from '@/components/custom/cusImage.vue'

const { campusId, tenantId, campusInfo, semesterInfo, formTypeMap } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null
	}
}
let defaultForm = () => {
	return {
		id: '',
		tenant_id: tenantId,
		campus_id: campusId,
		label_position: '',
		lable_width: '',
		size: '',
		form_content: ''
	}
}
let apiObj = ref(globalPropValue.form.form.list)
let params = ref(defaultParams())
let formSetData = ref(defaultForm())
let searchConfig = ref({
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: null,
			name: 'campus_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择校区',
				noClearable: true,
				items: campusInfo.map((v) => {
					return {
						label: v.name,
						value: v.value
					}
				})
			}
		},
		{
			label: null,
			name: 'academic_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学年',
				items: []
			}
		},
		{
			label: null,
			name: 'semester_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学期',
				items: []
			}
		},
		{
			label: null,
			name: 'name',
			value: null,
			component: 'input',
			options: {
				placeholder: '请输入表单名称',
				items: []
			}
		}
	]
})
let saveStatus = ref(false)
let dialog = ref()
let dialogFormTitle = ref('')
let dialogForm = ref()
let designerFormD = ref()
let table = ref()
let loading = ref(false)
let type = ref(formTypeMap)
const positionMap = {
	left: 1,
	right: 2,
	top: 3
}

const handleSaveForm = async () => {
	let designerData = designerFormD.value.getJson()
	formSetData.value.label_position = positionMap[designerData.config.labelPosition]
	formSetData.value.lable_width = designerData.config.labelWidth
	formSetData.value.size = designerData.config.size
	// formSetData.value.form_content = JSON.stringify(designerData.list)
	// 防止提交时，时间格式错误
	formSetData.value.form_content = JSON.stringify(
		designerData.list.map((item) => {
			if (item.options.valueFormat) {
				item.options.valueFormat = item.options.format
			}
			//如果必填,设置rules.message
			if (item.options.rules?.required) {
				item.options.rules.message = item.label + '不能为空'
			}
			return item
		})
	)
	console.log(designerData, formSetData.value, '拿到表单数据')
	// const reqData = { }
	const res = await globalPropValue.form.form.set.post(formSetData.value)
	if (res.code === 200) {
		saveStatus.value = true
		upsearch()
		ElMessage({ type: 'success', message: '保存成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
const designerFormClose = () => {
	console.log('关闭')
	if (saveStatus.value === false) {
		ElMessageBox.confirm('关闭后不会保存你的更改，确定要关闭吗？', '警告', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		})
			.then(() => {
				designerFormD.value.clear()
				dialog.value = false
			})
			.catch(() => { })
	} else {
		designerFormD.value.clear()
		dialog.value = false
	}
}

// 表单基本数据
const saveDataSuccess = async (data) => {
	console.log(data, 'saveDataSuccess')
	// designerFormD.value.clear()
	formSetData.value.id = data.data.id
	console.log(formSetData.value, 'formSetData.value')
	if (data.mode1 == 'add') {
		dialogFormTitle.value = data.data.title
		dialog.value = true
		upsearch()
	} else {
		table.value.refresh()
	}
}

const saveSuccess = async (data) => {
	console.log(designerFormD.value.setJson(data), 'index')
	const addData = { tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.form.form.add.post(addData)
	if (res.code === 200) {
		ElMessage.success('保存成功')
		dialog.value = false
		upsearch()
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}

watch(
	() => params.value.campus_id,
	(val) => {
		params.value.academic_id = null
		searchConfig.value.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
			.filter((v) => v.parent_id === 0 && v.campus_id === val)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
watch(
	() => params.value.academic_id,
	() => {
		params.value.semester_id = null
		searchConfig.value.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
			.filter(
				(v) => v.parent_id !== 0 && v.parent_id === params.value.academic_id && v.campus_id === params.value.campus_id
			)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
// let CampusManagementList = ref(campusInfo)
// 搜索按钮回调
const upsearch = () => {
	table.value.upData(params.value)
}
// 重置按钮回调
const refresh = () => {
	params.value = defaultParams()
	upsearch()
}
// 新增表单
const add_items = () => {
	dialogForm.value.dialogFormVisible = true
	nextTick(() => {
		dialogForm.value.open('add')
	})
}
// 编辑操作
const table_edit = (row) => {
	dialogForm.value.dialogFormVisible = true
	nextTick(() => {
		dialogForm.value.open('edit')
		dialogForm.value.setData(row)
	})
}
// 配置操作
const form_set = async (row) => {
	nextTick(() => {
		loading.value = true
		formSetData.value.id = row.id
		dialogFormTitle.value = row.title
		dialog.value = true
	})
	setTimeout(() => {
		if (row.form_content) {
			designerFormD.value.setJson(row)
		}
		loading.value = false
		saveStatus.value = false
	}, 200)
}
// 删除操作
const table_del = async (row) => {
	const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.form.form.del.post(reqData)
	if (res.code === 200) {
		// upsearch()
		table.value.refresh()
		ElMessage({ type: 'success', message: '删除成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
//数据回显格式化
const formData = (arr, val) => {
	return arr.find((v) => v.value == val)?.name || '-'
}
</script>

<style lang="scss">
.my-header {
	display: flex;
	justify-content: space-between;

	h2 {
		// text-decoration: underline;
	}
}

.designer-form {
	>.el-dialog__body {
		padding: 0;
	}
}
</style>
