<template>
	<el-drawer v-model="visible" title="资产管理" destroy-on-close @closed="$emit('closed')">
		<div class="rk">
			<cusForm ref="formref" v-model="form" :config="formConfig"> </cusForm>
		</div>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, assetsStatusMap } = cusTom.getBaseQuery()
import CusCascader from '@/components/custom/cusCascader.vue'
const defaultData = () => {
	return {
		tenant_id: null,
		campus_id: null,
		name: null,
		serial_number: null,
		type_id: null,
		storage_time: null,
		num: 1,
		unit: null,
		room_id: [],
		img: [],
		amount: 0,
		storage_remark: null,
		status: 1,
		borrow_user: null,
		borrow_userObj: []
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		groupData: {
			type: Array,
			default: () => {
				return []
			}
		}
	},
	data() {
		return {
			mode: 'add',
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			titleMap: {
				edit: '离校学生-'
			},
			type: '新资产入库',
			//验证规则
			rules: {
				name: [{ required: true, message: '请输入' }],
				serial_number: [{ required: true, message: '请输入' }],
				type_id: [{ required: true, message: '请选择' }],

				num: [{ required: true, message: '请输入' }],
				unit: [{ required: true, message: '请输入' }],
				room_id: [{ required: true, message: '请选择' }],
				borrow_userObj: [{ required: true, message: '请选择' }],
				borrow_time: [{ required: true, message: '请选择' }],
				scrap_time: [{ required: true, message: '请选择' }],
				status: [{ required: true, message: '请选择' }]
			},
			//所需数据选项
			assetsStatusMap,
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				labelWidth: '130px',
				formItems: [
					{
						label: '资产类别',
						name: 'type_id',
						value: null,
						component: 'cascader',
						options: {
							placeholder: '请选择资产类别',
							prop: {
								emitPath: false,
								value: 'id',
								label: 'name',
								children: 'child'
							},
							items: this.groupData
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					},
					{
						label: '资产名称',
						name: 'name',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入'
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '资产编号',
						name: 'serial_number',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入'
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},

					{
						label: '入库时间',
						name: 'storage_time',
						value: null,
						component: 'cusDate',
						options: {
							type: 'datetime'
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					},
					{
						label: '状态',
						name: 'status',
						value: null,
						component: 'select',
						options: {
							items: assetsStatusMap.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					},
					{
						label: '领用人',
						name: 'borrow_userObj',
						value: null,
						component: 'cusSelectTeacher',
						options: {},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }],
						hideHandle: '$.status != "2"'
					},
					{
						label: '领用时间',
						name: 'borrow_time',
						value: null,
						component: 'cusDate',
						options: {
							type: 'datetime'
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }],
						hideHandle: '$.status != "2"'
					},
					{
						label: '领用备注',
						name: 'storage_remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入'
						},
						hideHandle: '$.status != "2"'
					},
					{
						label: '报废时间',
						name: 'scrap_time',
						value: null,
						component: 'cusDate',
						options: {
							type: 'datetime'
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }],
						hideHandle: '$.status != "3"'
					},
					{
						label: '报废备注',
						name: 'scrap_remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入'
						},
						hideHandle: '$.status != "3"'
					},

					{
						label: '位置',
						name: 'room',
						value: null,
						component: 'cusSelectField',
						options: {},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					},
					{
						label: '资产图片',
						component: 'uploadMultiple',
						options: {
							items: [
								{
									name: 'img',
									label: '资产图片',
									type: 'asset'
								}
							]
						}
					},
					{
						label: '金额',
						name: 'amount',
						value: null,
						component: 'number',
						options: {
							placeholder: '请输入',
							controlsPosition: '1'
						}
					},
					{
						label: '入库备注',
						name: 'storage_remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入'
						}
					}
				]
			}
		}
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		statusChange() {
			this.form.borrow_userObj = []
			this.form.borrow_time = null
			this.form.borrow_remark = null

			this.form.scrap_time = null
			this.form.scrap_remark = null
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.campus_id = this.params.campus_id
			this.form.tenant_id = this.params.tenant_id
			return this
		},
		//表单提交方法
		submit() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true

					let formCopy = JSON.parse(JSON.stringify(this.form))
					if (formCopy.img) {
						formCopy.img = formCopy.img.map((v) => v.url).toString()
					} else {
						formCopy.img = ''
					}
					console.log(formCopy)

					formCopy.room_id = formCopy.room.map((v) => v.id).toString()

					formCopy.room_id = Number(formCopy.room_id)
					formCopy.amount = Number(formCopy.amount)
					formCopy.borrow_user = formCopy.borrow_userObj.map((v) => v.id).toString()
					formCopy.borrow_user = Number(formCopy.borrow_user)
					// formCopy.serial_number = Number(formCopy.serial_number)//

					var res = await this.$API.assets.rooms.save.post(formCopy)

					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			console.log(this.form)
			if (data.room) {
				this.form.room = [
					{
						id: data.room.id,
						label: data.room.name
					}
				]
			}
			if (data.img) {
				this.form.img = data.img.split(',').map((v) => {
					return {
						url: v
					}
				})
			}
			if (data.borrow_user) {
				this.$nextTick(() => {
					this.form.borrow_userObj = [
						{
							label: data.borrow_user_name,
							id: data.borrow_user
						}
					]
				})
			}
		}
	},
	components: { CusCascader }
}
</script>

<style scoped lang="scss"></style>
