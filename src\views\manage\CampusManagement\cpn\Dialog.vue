<template>
	<sc-dialog v-model="dialogShow" draggable :title="title" width="30%">
		<cusForm ref="formref" v-model="formData" :config="config" :loading="loading"> </cusForm>

		<template #footer>
			<el-button @click="dialogShow = false">取 消</el-button>
			<el-button type="primary" @click="confirm">确 定</el-button>
		</template>
	</sc-dialog>
</template>

<script>
import { ElMessage } from 'element-plus'

const defaultData = () => {
	return {
		id: null,
		tenant_id: null,
		campus_name: null,
		campus_code: null,
		address: null,
		contacts: null,
		phone: null,
		email: null,
		status: null,
		remark: null
	}
}

const config = {
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: '校区名称',
			name: 'campus_name',
			value: '',
			component: 'input',
			options: {
				placeholder: '请输入校区名称'
			},
			rules: [{ required: true, message: '请输入', trigger: 'blur' }]
		},
		{
			label: '校区编码',
			name: 'campus_code',
			value: '',
			component: 'input',
			options: {
				placeholder: '请输入校区编码'
			},
			rules: [{ required: true, message: '请输入', trigger: 'blur' }]
		},
		{
			label: '地址',
			name: 'address',
			value: '',
			component: 'input',
			options: {
				placeholder: '请输入'
			},
			rules: [{ required: true, message: '请输入', trigger: 'blur' }],
			requiredHandle: '$.required==true'
		},
		{
			label: '联系人',
			name: 'contacts',
			value: '',
			component: 'input',
			options: {
				placeholder: '请输入'
			},
			rules: [{ required: true, message: '请输入', trigger: 'blur' }],
			requiredHandle: '$.required==true'
		},
		{
			label: '联系号码',
			name: 'phone',
			value: '',
			component: 'input',
			options: {
				placeholder: '请输入'
			},
			rules: [
				{ required: true, message: '请输入', trigger: 'blur' },
				{
					pattern: /^1[3456789]\d{9}$/,
					message: '请输入正确的手机号格式',
					trigger: 'blur'
				}
			],
			requiredHandle: '$.required==true'
		},
		{
			label: '联系邮箱',
			name: 'email',
			value: '',
			component: 'input',
			options: {
				placeholder: '请输入'
			},
			rules: [{ required: true, message: '请输入', trigger: 'blur' }],
			requiredHandle: '$.required==true'
		},
		{
			label: '启用状态',
			name: 'status',
			message: '',
			options: {
				inactiveValue: -1,
				activeValue: 1
			},
			value: false,
			component: 'switch'
		},
		{
			label: '备注',
			name: 'remark',
			value: '',
			component: 'textarea',
			options: {
				placeholder: '请输入'
			},
			rules: [{ required: true, message: '请输入', trigger: 'blur' }],
			requiredHandle: '$.required==true'
		}
	]
}

export default {
	name: '',
	data() {
		return {
			title: null,
			openType: null,
			dialogShow: false,
			formData: defaultData(),
			loading: false,
			config
		}
	},
	methods: {
		async confirm() {
			const isSub = await this.$refs.formref.validate()
			if (!isSub) return
			if (this.openType == 'create') {
				this.addData()
			} else {
				this.editData()
			}
		},
		async addData() {
			let tId = this.$TOOL.data.get('USER_INFO').tenant_id
			const { code, message } = await this.$API.CampusManagement.save.post({
				...this.formData,
				tenant_id: tId
			})

			if (code == 200) {
				ElMessage.success('新增成功')
				this.close()
			} else {
				ElMessage.error({
					message
				})
			}
		},
		async editData() {
			const { code } = await this.$API.CampusManagement.save.post({
				...this.formData
			})

			if (code == 200) {
				ElMessage.success('修改成功')
				this.close()
			}
		},
		close() {
			this.dialogShow = false
			this.$emit('close')
		},
		open(row) {
			this.formData = defaultData()
			if (row) {
				this.openType = 'edit'
				this.title = '编辑校区'
				this.formData = row
			} else {
				this.openType = 'create'
				this.title = '新增校区'
			}
			this.dialogShow = true
		}
	}
}
</script>

<style scoped></style>
