<template>
	<el-form ref="loginForm" :model="form" :rules="rules" label-width="0" size="large" @keyup.enter="login">
		<el-form-item v-if="type === 2" prop="tenant_id">
			<el-input v-model="form.tenant_code" prefix-icon="el-icon-school" clearable placeholder="请输入学校代号" />
		</el-form-item>
		<el-form-item prop="user">
			<el-input v-model="form.user" prefix-icon="el-icon-user" clearable :placeholder="$t('login.userPlaceholder')">
				<!--				<template #append>
									<el-select v-model="userType" style="width: 130px">
										<el-option :label="$t('login.admin')" value="admin"></el-option>
										<el-option :label="$t('login.user')" value="user"></el-option>
									</el-select>
								</template>-->
			</el-input>
		</el-form-item>
		<el-form-item prop="password">
			<el-input
				v-model="form.password"
				prefix-icon="el-icon-lock"
				clearable
				show-password
				:placeholder="$t('login.PWPlaceholder')"
			></el-input>
		</el-form-item>
		<el-form-item prop="isPassing" style="margin-bottom: 10px; user-select: none">
			<drag-verify
				ref="dragVerify"
				v-model:isPassing="isPassing"
				text="请按住滑块拖动"
				successText="验证通过"
				handlerIcon="el-icon-DArrowRight"
				successIcon="el-icon-CircleCheck"
			>
			</drag-verify>
		</el-form-item>
		<el-form-item v-if="false" prop="captchaCode" style="margin-bottom: 10px">
			<div class="login-msg-yzm">
				<el-input
					v-model="form.captchaCode"
					prefix-icon="el-icon-unlock"
					clearable
					:placeholder="$t('login.captchaPlaceholder')"
					style="margin-right: 15px"
				></el-input>
				<el-image style="cursor: pointer" :src="captchaBase64Blob" @click="getCaptcha" />
			</div>
		</el-form-item>
		<el-form-item style="margin-bottom: 10px">
			<el-col :span="12">
				<!--				<el-checkbox v-model="form.autologin" :label="$t('login.rememberMe')"></el-checkbox>-->
			</el-col>
			<el-col v-if="$CONFIG.MY_SHOW_LOGIN_FORGET" :span="12" class="login-forgot">
				<router-link to="/reset_password">{{ $t('login.forgetPassword') }}？</router-link>
			</el-col>
		</el-form-item>
		<el-form-item>
			<el-button type="primary" style="width: 100%" :loading="islogin" round @click="login"
				>{{ $t('login.signIn') }}
			</el-button>
		</el-form-item>
		<div v-if="$CONFIG.MY_OPEN_REGISTER" class="login-reg">
			{{ $t('login.noAccount') }}
			<router-link to="/user_register">{{ $t('login.createAccount') }}</router-link>
		</div>
	</el-form>
</template>

<script>
import { useCommonStore } from '@/stores/common.js'
import DragVerify from '@/components/dragVerify/index.vue'

const commonStore = useCommonStore()

export default {
	components: { DragVerify },
	props: {
		type: {
			type: Number,
			default: () => {
				return 1
			}
		}
	},
	data() {
		return {
			userType: 'admin',
			isPassing: false,
			form: {
				user: '',
				password: '',
				captchaCode: '',
				autologin: false,
				type: this.type,
				tenant_code: null
			},
			captchaId: '',
			captchaBase64Blob: '',
			rules: {
				user: [{ required: true, message: this.$t('login.userError'), trigger: 'blur' }],
				password: [{ required: true, message: this.$t('login.PWError'), trigger: 'blur' }]
				//captchaCode: [{ required: true, message: this.$t('login.captchaCodeError'), trigger: 'blur' }]
			},
			islogin: false
		}
	},
	watch: {
		userType(val) {
			if (val === 'admin') {
				this.form.user = 'admin'
				this.form.password = '123456'
			} else if (val === 'user') {
				this.form.user = 'user'
				this.form.password = 'user'
			}
		}
	},
	mounted() {
		this.getCaptcha()
	},
	methods: {
		getCaptcha() {
			this.$API.common.getCaptcha
				.get()
				.then((res) => {
					if (res.code === 200) {
						this.captchaId = res.data.captchaId
						this.captchaBase64Blob = res.data.captchaBase64Blob
						//this.form.captchaCode = res.data.code
					} else {
						this.$message.warning(res.message)
						return false
					}
				})
				.catch((err) => {
					console.error(err)
				})

			/*var res = await this.$API.common.getCaptcha.get()
			if (res.code === 200) {
				this.captchaId = res.data.captchaId
				this.captchaBase64Blob = res.data.captchaBase64Blob
			} else {
				this.$message.warning(res.message)
				return false
			}*/
		},
		isIP(str) {
			// 使用正则表达式匹配 IPv4 地址的格式
			const pattern = /^(\d{1,3}\.){3}\d{1,3}$/
			return pattern.test(str)
		},
		async login() {
			if (!this.isPassing) {
				this.$message.warning('请拖动滑块验证')
				return false
			}
			var validate = await this.$refs.loginForm.validate().catch(() => {})
			if (!validate) {
				return false
			}
			this.islogin = true
			var data = {
				username: this.form.user,
				password: this.form.password,
				captchaCode: this.form.captchaCode,
				captchaId: this.captchaId
			}
			if (this.type === 2) {
				data.type = 2
				data.tenant_code = this.form.tenant_code
			}
			//获取token
			var user = await this.$API.auth.login.post(data)
			console.log(user)
			if (user.code === 200) {
				//this.$TOOL.data.set('token', user.data.token, this.form.autologin ? 24 * 60 * 60 : 12 * 60 * 60)
				let domain
				if (this.isIP(document.domain)) {
					domain = document.domain
				} else {
					domain = document.domain.split('.').slice(-2).join('.')
				}
				this.$TOOL.cookie.set('USER_TOKEN', user.data.token, {
					expires: this.form.autologin ? 24 * 60 * 60 : 12 * 60 * 60,
					domain: domain,
					path: '/'
				})
				this.$TOOL.data.set('USER_INFO', user.data.userInfo)
				window.localStorage.setItem('tenant_id', user.data.userInfo?.tenant_id)
				commonStore.saveCommonData() //保存公共数据 如校区列表信息
			} else if (user.code === 400101) {
				this.islogin = false
				this.$message.warning(user.message)
				this.getCaptcha()
				return false
			} else {
				this.islogin = false
				this.$message.warning(user.message)
				return false
			}
			//获取菜单
			var menu = null
			/*if (this.form.user === 'admin') {
				menu = await this.$API.system.menu.myMenus.get()
			} else {
				menu = await this.$API.demo.menu.get()
			}*/
			menu = await this.$API.system.menu.myMenus.get()
			if (menu.code === 200) {
				if (menu.data.menu === null || menu.data.menu.length === 0) {
					this.islogin = false
					this.$alert('当前用户无任何权限，请联系系统管理员', '无权限访问', {
						type: 'error',
						center: true
					})
					return false
				}
				this.$TOOL.data.set('MENU', menu.data.menu)
				this.$TOOL.data.set('PERMISSIONS', menu.data.permissions)
				if (menu.data.dashboardGrid) {
					this.$TOOL.data.set('DASHBOARDGRID', menu.data.dashboardGrid)
				}
				if (menu.data.dashboardCopmsList && menu.data.dashboardLayout) {
					this.$TOOL.data.set('GRID', {
						copmsList: menu.data.dashboardCopmsList,
						layout: menu.data.dashboardLayout
					})
				}
				if (menu.data.mods !== null) {
					this.$TOOL.data.set('my-mods', menu.data.mods)
				}
			} else {
				this.islogin = false
				this.$message.warning(menu.message)
				return false
			}

			this.$router.replace({
				path: '/'
			})
			this.$message.success({
				message: 'Login Success 登录成功',
				duration: 1000
			})
			this.islogin = false
		}
	}
}
</script>

<style scoped></style>
