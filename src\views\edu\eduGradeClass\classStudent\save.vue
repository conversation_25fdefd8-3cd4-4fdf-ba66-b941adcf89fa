<template>
	<el-dialog
		v-model="visible"
		:title="titleMap[mode] + student"
		:width="500"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode"> </cusForm>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { semesterInfo } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		tenant_id: null,
		campus_id: null,
		student_id: null,
		position: null,
		remark: null,
		mode: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				class_name: [{ required: true, message: '请输入名称' }],
				semester_id: [{ required: true, message: '请选择学期' }],
				grade_id: [{ required: true, message: '请选择年级' }]
			},
			//所需数据选项
			groups: [],
			groupsProps: {
				value: 'id',
				label: 'semester_name',
				emitPath: false
			},
			gradeOptions: [],
			semesterInfo,
			grade: [],
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				labelWidth: '80px',

				formItems: [
					{
						label: '班级',
						name: 'class_id',
						value: null,
						component: 'select',
						options: {
							items: []
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }],
						hideHandle: '$.mode == "edit" || $.mode == "show"'
					},
					{
						label: '学生',
						name: 'student_id',
						value: null,
						component: 'cusSelectStudent',
						rules: [{ required: true, message: '请输入', trigger: 'blur' }],
						hideHandle: '$.mode == "edit" || $.mode == "show"'
					},

					{
						label: '职位',
						name: 'position',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入',
						}
					},
					{
						label: '备注',
						name: 'remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入备注'
						}
					}
				]
			}
		}
	},
	mounted() {},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.form.campus_id),
				'value',
				'parent_id'
			)
		},
		student() {
			if (this.form.student_name) {
				return '-' + this.form.student_name
			} else {
				return ''
			}
		}
	},
	watch: {},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.form.mode = mode
			if (mode == 'add') {
				this.form.campus_id = this.params.campus_id
				this.form.tenant_id = this.params.tenant_id
				this.getClass()
			}
			this.visible = true
			return this
		},
		async getClass() {
			const res = await this.$API.eduGradeClass.class.all.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			})
			this.formConfig.formItems[0].options.items = res.data.map((v) => {
				return {
					label: v.class_name,
					value: v.id
				}
			})
		},
		//表单提交方法
		submit() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true

					var res = await this.$API.eduGradeClass.classStu.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			// this.form.id = data.id
			// this.form.label = data.label
			// this.form.status = data.status
			// this.form.sort = data.sort
			// this.form.parentId = data.parentId
			// this.form.remark = data.remark

			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
			this.form.student_id = data.user_id
		}
	}
}
</script>

<style></style>
