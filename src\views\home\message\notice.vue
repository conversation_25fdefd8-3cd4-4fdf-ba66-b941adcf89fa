<template>
	<el-container>
		<el-main>
			<scTable ref="table" :apiObj="apiObj" :params="params">
				<el-table-column label="标题" prop="title">
					<template #default="{ row }">
						<p v-if="row.importance == 1">
							<span style="color: #f56c6c; font-weight: bold; font-size: 14px">[重要] </span
							><el-link type="primary" style="vertical-align: initial" @click="noticeDetails(row)">{{
								row.title
							}}</el-link>
						</p>
						<p v-else>
							<el-link type="primary" @click="noticeDetails(row)">{{ row.title }}</el-link>
						</p>
					</template>
				</el-table-column>
				<el-table-column label="创建人" prop="created_user_name" width="200"></el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="200"></el-table-column>
				<!--                <el-table-column label="内容" prop="content">
                </el-table-column>-->
			</scTable>
		</el-main>
		<el-drawer v-model="showNotice" :title="noticeData.title" direction="rtl">
			<div class="notice-info">
				<div class="notice-title">
					<p>发布人：{{ noticeData.publisher_info.name + ' ' + noticeData.created_at }}</p>
				</div>
				<div class="notice-content" v-html="noticeData.content"></div>
				<div v-if="noticeData.file?.length > 0" class="notice-file">
					<h3>附件：</h3>
					<p v-for="row in noticeData.file">
						<el-link type="primary" :href="row.url" target="_blank" style="margin-right: 10px">{{ row.name }}</el-link>
					</p>
				</div>
			</div>
			<template #footer> </template>
		</el-drawer>
	</el-container>
</template>
<script>
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo } = cusTom.getBaseQuery()
export default {
	data() {
		return {
			apiObj: this.$API.notice.notice,
			params: {
				tenant_id: tenantId,
				campus_id: campusId
			},
			showNotice: false,
			noticeData: {}
		}
	},
	methods: {
		noticeDetails(data) {
			this.getNoticeData(data)
			console.log(data)
		},
		getNoticeData(data) {
			let params = {
				tenant_id: tenantId,
				campus_id: campusId,
				id: data.id
			}
			this.$API.notice.noticeDetail.get(params).then((res) => {
				if (res.code == 200) {
					this.noticeData = res.data
					if (res.data.file !== '') {
						this.noticeData.file = JSON.parse(res.data.file)
					}
					this.showNotice = true
				}
				console.log(res)
			})
		}
	}
}
</script>
<style lang="scss" scoped>
.notice-info {
	padding: 0 10px;

	.notice-title {
		h3 {
			font-size: 20px;
			font-weight: bold;
			margin-bottom: 10px;
		}

		p {
			font-size: 14px;
			color: #999;
			margin-bottom: 20px;
		}
	}
	.notice-content {
		padding-bottom: 15px;
		font-size: 14px;
		line-height: 30px;
	}
	.notice-file {
		padding-top: 15px;
		margin-bottom: 5px;
		border-top: 1px solid #eee;
		> p {
			padding-left: 10px;
			margin-bottom: 5px;
		}
	}
}
</style>
