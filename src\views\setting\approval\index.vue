<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script setup>
import flow from './flow'
import deploy from './deploy'
import { ref } from 'vue'
const activeName = ref('flow')
const currComponent = ref({
	name: 'flow',
	component: flow
})
const tabs = [
	{
		name: 'flow',
		label: '审批流程',
		component: flow
	},
	{
		name: 'deploy',
		label: '流程配置',
		component: deploy
	}
]
const handleClick = (name) => {
	currComponent.value = tabs.find((item) => item.name === name)
}
</script>
<style lang="scss" scoped></style>
