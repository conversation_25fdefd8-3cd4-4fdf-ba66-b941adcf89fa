import config from '@/config'
import http from '@/utils/request'

export default {
	getList: {
		url: `${config.API_URL}/lot/message/list`,
		name: '获取列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	read: {
		url: `${config.API_URL}/lot/message/read`,
		name: '设置已读',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	}
}
