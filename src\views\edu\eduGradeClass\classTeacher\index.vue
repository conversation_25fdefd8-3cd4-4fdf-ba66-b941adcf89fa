<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList.length > 1">
						<el-select v-model="params.campus_id" placeholder="请选择校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<cusCascader v-model="params.semester_id" placeholder="请选择学期" :options="getSemester"></cusCascader>
					</el-form-item>
					<el-form-item label="">
						<el-cascader
							v-model="params.class_id"
							placeholder="请选择班级"
							:options="classList"
							:props="{ multiple: false, label: 'name', value: 'id', children: 'class_list' }"
							clearable
						></el-cascader>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-plus" @click="add">新增班级教师</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<el-table-column label="教师姓名" prop="teacher_name" width="250">
					<template #default="scope">
						{{ scope.row.teacher_name }} <el-tag v-if="scope.row.is_head === 1" type="danger"> 班主任</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="教师工号" prop="serial_number" width="150"></el-table-column>
				<!-- <el-table-column label="头像" prop="user_head" width="100">
					<template #default="scope">
						<el-image
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.user_head"
							:preview-src-list="[scope.row.user_head]"
							preview-teleported
						/>
					</template>
				</el-table-column> -->
				<el-table-column label="授课课程" prop="course_name" width="150"></el-table-column>
				<el-table-column label="周课时数" prop="week_num" width="150"></el-table-column>
				<el-table-column label="班级名称" prop="class_name" width="200">
					<template #default="scope"> {{ scope.row.grade_name }} - {{ scope.row.class_name }} </template>
				</el-table-column>
				<el-table-column label="学年学期" prop="academic_name" width="200">
					<template #default="scope"> {{ scope.row.academic_name }} - {{ scope.row.semester_name }} </template>
				</el-table-column>

				<!-- <el-table-column label="学期名称" prop="semester_name" width="100"></el-table-column>
				<el-table-column label="年级名称" prop="grade_name" width="100"></el-table-column> -->
				<el-table-column label="职务" prop="position" width="150"></el-table-column>
				<el-table-column label="备注" prop="remark"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="210">
					<template #default="scope">
						<el-button-group>
							<!--							<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>-->
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm title="确定移除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">移除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
	<add-dialog
		v-if="dialog.add"
		ref="addDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.add = false"
	></add-dialog>
</template>

<script>
import saveDialog from './save'
import addDialog from './add'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, tenantInfo, attendanceStatusMap, semesterInfo, sexMap } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null,
		grade_id: null,
		class_id: null,
		begin_time: null,
		end_time: null,
		date: [],
		name: null
	}
}
export default {
	name: '',
	components: { saveDialog, addDialog },
	data() {
		return {
			dialog: {
				save: false,
				add: false
			},
			classList: [],
			apiObj: this.$API.eduGradeClass.classTeacher.list,
			selection: [],
			semesterInfo,
			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			entryExitMap: [
				{ name: '进', value: 1 },
				{ name: '出', value: 2 }
			],
			userTypeMap: [
				{ name: '学员', value: 1 },
				{ name: '教职工', value: 2 }
			],
			attendanceStatusMap,
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					}
				]
			},
			grade: [],
			class: [],
			sexMap,
			headMap: [
				{ name: '是', value: 1 },
				{ name: '不是', value: -1 }
			]
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.classList = []
				this.params.semester_id = null
			},
			immediate: true
		},
		'params.semester_id': {
			handler(val) {
				this.classList = []
				this.getClassData()
			},
			immediate: true
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	inject: ['classData_f', 'resetClassD'],
	async created() {
		const classD = this.classData_f()
		if (classD.id) {
			this.params.class_id = classD.id
		}
	},
	methods: {
		async getClassData() {
			let res = await this.$API.eduGradeClass.class.all.get(this.params)
			this.classList = res.data
			/*	this.searchConfig.formItems.find((v) => v.name === 'class_id').options.items = res.data.map((v) => {
				return {
					label: v.grade_name + ' - ' + v.class_name,
					value: v.id,
					grade_id: v.grade_id
				}
			})*/
			// console.log(this.searchConfig.formItems[2].options.items.map((v) => v.grade_id)[0])
			// this.params.grade_id = this.searchConfig.formItems[2]?.options.items.map((v) => v.grade_id)[0]
		},

		//添加
		add(row) {
			this.dialog.add = true
			this.$nextTick(() => {
				this.$refs.addDialog.open('add', row).setData({
					class_id: this.params.class_id
				})
			})
			// this.upsearch()
		},
		//编辑
		table_edit(row) {
			this.dialog.add = true
			this.$nextTick(() => {
				this.$refs.addDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.add = true
			this.$nextTick(() => {
				this.$refs.addDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = {
				id: row.id,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				class_id: row.class_id
			}
			var res = await this.$API.eduGradeClass.classTeacher.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('移出成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},

		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		//搜索
		upsearch() {
			this.params.begin_time = this.params.date ? this.params.date[0] : null
			this.params.end_time = this.params.date ? this.params.date[1] : null
			this.params.class_id = this.params.class_id ? this.params.class_id[1] : null
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.resetClassD()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped></style>
