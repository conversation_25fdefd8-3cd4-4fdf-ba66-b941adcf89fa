<template>
	<template v-if="type === 'int' || type === 'float'">
		<div>取值范围：{{ specsData.min }} - {{ specsData.max }}</div>
		<div>步长：{{ specsData.step }}</div>
		<div>单位：{{ specsData.unit_symbol }} ({{ specsData.unit_name }})</div>
	</template>

	<template v-if="type === 'text'">
		<div>数据长度：{{ specsData.len }}</div>
	</template>

	<template v-if="type === 'enum'">
		<div class="enum">
			<el-tag v-for="(item, index) in specsData" :key="index" size="small">{{ item.value }} - {{ item.name }}</el-tag>
		</div>
	</template>

	<template v-if="type === 'bool'">
		<div class="bool">
			<el-tag v-for="(item, index) in specsData" :key="index" size="small">{{ index }} - {{ item }}</el-tag>
		</div>
	</template>
	<template v-if="type === 'date'"> 整数类型Int64的UTC时间戳 (毫秒) </template>
	<template v-if="type === 'array'">
		<div>数组长度：{{ specsData.size }}</div>
	</template>
	<template v-if="type === 'struct'"> - </template>
</template>
<script>
export default {
	name: 'showSpecs',
	props: {
		specsData: {
			type: [Object, Array],
			default: () => {
				return null
			}
		},
		type: {
			type: String,
			default: ''
		}
	},
	data() {
		return {}
	},
	computed: {}
}
</script>
<style scoped lang="scss">
.enum {
}
</style>
