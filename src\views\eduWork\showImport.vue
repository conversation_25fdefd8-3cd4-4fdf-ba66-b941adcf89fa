<template>
	<el-drawer v-model="drawer" title="导入作业成绩" size="60%">
		<el-form>
			<el-form-item label="">
				<el-alert type="warning" :closable="false"
					>请先下载导入模板，然后按照模板格式填写数据，点击导入数据即可完成导入。<el-button
						type="text"
						plain
						@click="exportToExcel"
						>下载导入模板</el-button
					></el-alert
				>
			</el-form-item>
			<el-form-item label="导入数据">
				<el-alert type="info" :closable="false">请上传小于或等于 {{ maxSize }}M 的 .xls, .xlsx 格式文件</el-alert>
				<el-upload
					ref="uploader"
					accept=".xls, .xlsx"
					maxSize="10"
					:limit="1"
					style="margin-top: 10px"
					:data="data"
					:show-file-list="false"
					:before-upload="before"
					:on-change="handleChange"
				>
					<slot name="uploader">
						<el-button :loading="loading" :disabled="loading" type="primary" icon="el-icon-upload" plain
							>选择文件</el-button
						>
					</slot>
				</el-upload>
			</el-form-item>
		</el-form>
		<el-table :data="results.workResultData" style="margin-top: 20px">
			<el-table-column prop="student_name" label="学号" width="130">
				<template #default="{ row }">
					{{ row.serial_number }}
				</template>
			</el-table-column>
			<el-table-column prop="name" label="姓名" width="120">
				<template #default="{ row }">
					{{ row.student_name }}
				</template>
			</el-table-column>
			<el-table-column prop="score" label="分数" width="120">
				<template #default="{ row }">
					<el-input v-model="row.score" type="number"></el-input>
				</template>
			</el-table-column>
			<el-table-column prop="right_num" label="正确题数" width="120">
				<template #default="{ row }">
					<el-input v-model="row.right_num" type="number"></el-input>
				</template>
			</el-table-column>
			<el-table-column prop="error_num" label="错误题数" width="120">
				<template #default="{ row }">
					<el-input v-model="row.error_num" type="number"></el-input>
				</template>
			</el-table-column>
			<el-table-column prop="review_desc" label="评阅评价" max-width="200">
				<template #default="{ row }">
					<el-input v-model="row.review_desc" type="textarea" rows="1"></el-input>
				</template>
			</el-table-column>
		</el-table>
		<template #footer>
			<el-button plain @click="drawer = false">取消</el-button>
			<el-button type="primary" :loading="saveLoading" @click="over">保存成绩</el-button>
		</template>
	</el-drawer>
</template>

<script>
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

export default {
	emits: ['over'],
	data() {
		return {
			data: null,
			loading: false,
			saveLoading: false,
			drawer: false,
			maxSize: 10,
			results: {
				workDetail: [],
				workResultData: []
			}
		}
	},
	mounted() {},
	methods: {
		async geWorkDetail() {
			let { data } = await this.$API.eduWork.workDetail.get({
				work_id: this.results.workDetail.id,
				tenant_id: this.results.workDetail.tenant_id,
				campus_id: this.results.workDetail.campus_id
			})
			this.results.workResultData = data.map((item) => {
				return {
					id: item.id,
					answer_user: item.answer_user.student_id,
					serial_number: item.answer_user.serial_number,
					student_name: item.answer_user.student_name,
					score: item.score,
					right_num: item.right_num,
					error_num: item.error_num,
					review_desc: item.review_desc
				}
			})
		},

		show(data) {
			this.drawer = true
			this.results.workDetail = data
			console.log(data)
			this.geWorkDetail()
		},
		over() {
			this.results.workResultData = this.results.workResultData.map((item) => {
				return {
					id: item.id,
					answer_user: item.answer_user,
					serial_number: item.serial_number,
					student_name: item.student_name,
					score: parseFloat(item.score),
					right_num: parseInt(item.right_num),
					error_num: parseInt(item.error_num),
					review_desc: item.review_desc
				}
			})
			this.saveLoading = true
			//保存成绩
			this.$API.eduWork.saveResult
				.post({
					work_id: this.results.workDetail.id,
					tenant_id: this.results.workDetail.tenant_id,
					campus_id: this.results.workDetail.campus_id,
					result: this.results.workResultData
				})
				.then((res) => {
					this.saveLoading = false
					if (res.code === 200) {
						this.$message.success('保存成功')
						this.$emit('over')
						this.drawer = false
					} else {
						this.$message.error(res.message)
					}
				})
		},
		exportToExcel() {
			const data = [
				[
					'学号（请勿修改）',
					'学生姓名（请勿修改）',
					'分数（请填写数字）',
					'正确题数（请填写数字）',
					'错误题数（请填写数字）',
					'评阅评价'
				]
			]
			this.results.workResultData.forEach(function (value, index) {
				data.push([
					value.serial_number,
					value.student_name,
					value.score,
					value.right_num,
					value.error_num,
					value.review_desc
				])
			})
			// 将数据转换为工作表
			const worksheet = XLSX.utils.aoa_to_sheet(data)
			// 创建工作簿并添加工作表
			const workbook = XLSX.utils.book_new()
			XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
			// 生成Excel文件
			const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
			// 使用blob和FileReader创建一个Blob URL
			const dataBlob = new Blob([excelBuffer], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
			})
			const blobUrl = window.URL.createObjectURL(dataBlob)
			// 使用saveAs下载文件
			saveAs(dataBlob, '学生作业成绩导入模板 - ' + this.results.workDetail.work_name + '.xlsx')
			// 清理
			window.URL.revokeObjectURL(blobUrl)
		},
		open() {
			this.dialog = true
			this.formData = {}
		},
		close() {
			this.dialog = false
		},
		before(file) {
			const maxSize = file.size / 1024 / 1024 < this.maxSize
			if (!maxSize) {
				this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!`)
				return false
			}
		},
		handleChange(file) {
			if (!file) {
				this.$message.error('请选择要上传的文件')
				return false
			}
			this.loading = true
			const reader = new FileReader()
			reader.onload = (e) => {
				const data = new Uint8Array(e.target.result)
				const workbook = XLSX.read(data, { type: 'array' })
				const firstSheetName = workbook.SheetNames[0]
				const worksheet = workbook.Sheets[firstSheetName]
				const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
				const list = jsonData.slice(1)
				if (list.length <= 0) {
					this.$message.error('请导入正确格式的Excel文件')
					return false
				}
				const results = list.map((item) => {
					return {
						serial_number: item[0],
						student_name: item[1],
						score: parseFloat(item[2]),
						right_num: parseInt(item[3]),
						error_num: parseInt(item[4]),
						review_desc: item[5]
					}
				})
				this.results.workResultData.forEach((item) => {
					let index = results.findIndex((e) => e.serial_number === item.serial_number)
					item.score = results[index].score
					item.right_num = results[index].right_num
					item.error_num = results[index].error_num
					item.review_desc = results[index].review_desc
				})
				/*this.headers = jsonData[0]
				this.data = jsonData.slice(1)*/
			}
			reader.readAsArrayBuffer(file.raw)
			this.loading = false
		}
	}
}
</script>

<style lang="scss" scoped>
.workPreview {
	padding: 10px;
	border: 1px solid var(--el-border-color);
	font-size: 14px;

	.topic_item {
		padding: 8px;
		margin-bottom: 12px;

		&:last-child {
			margin-bottom: 0;
		}

		.el-icon {
			display: none;
		}

		.topic_top {
			display: flex;
			flex-wrap: wrap;
			margin-bottom: 10px;
		}

		.topic_name {
			display: inline-block;
			margin-left: 5px;
			font-weight: bold;
		}
		:deep(.topic_name img) {
			max-width: 500px;
			max-height: 300px;
		}
		.topicDesc {
			display: flex;
			margin-bottom: 8px;

			&:last-child {
				margin-bottom: 0;
			}
		}
		:deep(.topicDesc img) {
			max-width: 500px;
			max-height: 300px;
		}
	}
}
</style>
