<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formRef" v-model="params" :config="searchConfig" :inline="true"></cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upSearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" @click="add_items">新增问卷</el-button>
			</div>
		</el-header>
		<el-main>
			<div class="listBox">
				<div v-for="(item, index) in surveyList" :key="index" class="py-16 pt-16 pb-10 brs-8"
					style="border: 1px solid #eff1f7">
					<div class="flex-center flex-between">
						<div class="pr-20 flex-align-center flex-1">
							<el-tag :type="statusTypeMap[item.status]" effect="dark">
								{{ statusMap[item.status] }}
							</el-tag>
							<div class="font-16 pl-5 ellipsis-1 flex-1">{{ item.title }}</div>
						</div>
						<div class="flex-center" style="width: max-content">
							<text class="mr-5 opacity-5">问卷对象:</text>
							<text :style="`color: ${typeMap[item.object]}`">{{ formData(type, item.object) }}</text>
						</div>
					</div>
					<div class="mt-5">
						<div class="font-12">
							<text class="mr-5 opacity-5">创建时间:</text>
							<text>{{ item.created_at }}</text>
						</div>

						<div class="mt-5 flex">
							<div :class="`font-12 opacity-${item.begin_date ? 100 : 0}`">
								<text class="mr-5 opacity-5">开始日期:</text>
								<text>{{ item.begin_date }}</text>
							</div>
							<div :class="`font-12 ml-15 opacity-${item.end_date ? 100 : 0}`">
								<text class="mr-5 opacity-5">结束日期:</text>
								<text>{{ item.end_date }}</text>
							</div>
						</div>
					</div>
					<div class="text-center px-10 mt-10 brs-8 grid-columns-3" style="background: #f4f7ff">
						<div>
							<div class="font-13 opacity-5">浏览数</div>
							<div class="font-18 bold mt-5">{{ item.view_num }}</div>
						</div>
						<div>
							<div class="font-13 opacity-5">提交量</div>
							<div class="font-18 bold mt-5">{{ item.submit_num }}</div>
						</div>
						<div>
							<div class="font-13 opacity-5">数据详情</div>
							<el-link :underline="false" type="primary" class="mt-5" @click="submitList(item)">
								<el-text type="primary">查看</el-text>
							</el-link>
						</div>
					</div>

					<div class="mt-10 flex-justify-end">
						<el-space wrap :size="20">

							<el-button type="primary" link @click="showForm(item)">预览</el-button>

							<el-button type="primary" link v-if="item.status === 3" @click="shareForm(item)">
								分享
							</el-button>

							<el-dropdown @command="acHandleCommand">
								<el-button type="primary" link>更多操作<el-icon>
										<ArrowDownBold />
									</el-icon>
								</el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item :command="beforeHandleCommand('a', item)"
											v-if="item.status !== 3">
											<el-icon>
												<EditPen />
											</el-icon>
											编辑
										</el-dropdown-item>

										<el-dropdown-item v-if="item.status === 2 || item.status === 4"
											:command="beforeHandleCommand('c', item)">
											<el-icon>
												<Position />
											</el-icon>
											发布
										</el-dropdown-item>
										<el-dropdown-item v-if="item.status === 3"
											:command="beforeHandleCommand('e', item)">
											<el-icon>
												<SoldOut />
											</el-icon>
											下架
										</el-dropdown-item>
										<el-dropdown-item v-if="item.status !== 3"
											:command="beforeHandleCommand('d', item)">
											<el-icon>
												<Setting />
											</el-icon>
											配置
										</el-dropdown-item>
										<el-dropdown-item :command="beforeHandleCommand('b', item)">
											<el-icon>
												<Delete />
											</el-icon>
											删除
										</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</el-space>
					</div>
				</div>
			</div>
		</el-main>
		<el-footer class="py-15 flex-justify-end">
			<div>
				<el-pagination v-model:current-page="params.page" v-model:page-size="params.pageSize" :total="total"
					size="small" background layout="total, sizes, prev, pager, next, jumper"
					@current-change="getSurveyList" @size-change="getSurveyList">
				</el-pagination>
			</div>
		</el-footer>
		<!-- 编辑弹窗 -->
		<saveDialog ref="dialogForm" :params="params" @success="saveDataSuccess"></saveDialog>
		<!-- 分享弹窗 -->
		<shareCode ref="shareCodeRef"></shareCode>
		<!-- 配置弹窗 -->
		<el-dialog v-model="dialog" title=" " fullscreen :show-close="false" :close-on-press-escape="false"
			append-to-body class="designer-form">
			<template #header>
				<div class="my-header">
					<h2>{{ dialogFormTitle }}</h2>
					<div class="header-btn">
						<el-button type="primary" @click="handleSaveForm">
							<template #icon>
								<cusSvgIcon iconClass="save" />
							</template>
							保存
						</el-button>
						<el-button type="danger" :icon="CircleClose" @click="designerFormClose"> 关闭</el-button>
					</div>
				</div>
			</template>
			<designerForm ref="designerFormD" v-loading="loading" @saveForm="saveSuccess"></designerForm>
		</el-dialog>
	</el-container>
	<!-- 预览弹窗 -->
	<el-dialog v-model="previewVisible" title="预览" :width="500" align-center>
		<div style="min-height: 450px;max-height: 650px;overflow-y: auto;background: #f6f8f9;">
			<designerFormPreview v-if="previewVisible" ref="designerFormPreviewRef" :data="widgetForm" />
		</div>
	</el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick, watch, computed } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
	CircleClose, ArrowDownBold, EditPen, Position, Setting, SoldOut, Delete
} from '@element-plus/icons-vue'
import designerForm from './formDesigner/designerForm.vue'
import saveDialog from './components/save.vue'
import cusImage from '@/components/custom/cusImage.vue'
import shareCode from './components/shareCode.vue'
import designerFormPreview from './formDesigner/designerFormPreview.vue'

const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
const { push } = useRouter()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		title: null,
		status: null,
		tenant_id: tenantId,
		campus_id: campusId,
		page: 1,
		pageSize: 20
	}
}
let defaultForm = () => {
	return {
		id: '',
		tenant_id: tenantId,
		campus_id: campusId,
		cover: '',
		form_config: {
			list: '',
			config: {
				labelPosition: '',
				labelWidth: '',
				size: '',
			}
		}
	}
}
const statusTypeMap = {
	1: "info",
	2: "primary",
	3: "success",
	4: "warning",
}
const typeMap = {
	1: "#00C0A4",
	2: "#1E6EFF",
	3: "#6B63F7",
	4: '#FF7D00'
}
let params = ref(defaultParams())
let formSetData = ref(defaultForm())
let searchConfig = ref({
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: null,
			name: 'campus_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择校区',
				noClearable: true,
				items: campusInfo.map((v) => {
					return {
						label: v.name,
						value: v.value
					}
				})
			}
		},
		{
			label: null,
			name: 'title',
			value: null,
			component: 'input',
			options: {
				placeholder: '请输入问卷标题',
				items: []
			}
		},
		{
			label: null,
			name: 'status',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择问卷状态',
				width: '200px',
				items: [
					{
						label: '配置中',
						value: 1
					},
					{
						label: '待发布',
						value: 2
					},
					{
						label: '已发布',
						value: 3
					},
					{
						label: '已撤销',
						value: 4
					}
				]
			}
		}
	]
})
let saveStatus = ref(false)
let dialog = ref()
let dialogFormTitle = ref('')
let dialogForm = ref()
let designerFormD = ref()
let loading = ref(false)
let type = reactive([
	{ label: '游客', value: 1 },
	{ label: '老师', value: 2 },
	{ label: '学生', value: 3 },
	{ label: '学生+老师', value: 4 },
])
const statusMap = {
	1: '配置中',
	2: '待发布',
	3: '已发布',
	4: '已撤销',
}
onMounted(() => {
	getSurveyList()
})
const beforeHandleCommand = (item, row) => {
	return {
		command: item,
		row: row,
	};
}
const acHandleCommand = (command) => {
	switch (command.command) {
		case "a":
			table_edit(command.row);
			break;
		case "b":
			table_del(command.row);
			break;
		case "c":
			publishRow(command.row);
			break;
		case "d":
			form_set(command.row);
			break;
		case "e":
			setOffRow(command.row);
			break;
	}
}
const handleSaveForm = async () => {
	let designerData = designerFormD.value.getJson()
	formSetData.value.form_config.config = {
		labelPosition: designerData.config.labelPosition,
		labelWidth: designerData.config.labelWidth,
		size: designerData.config.size,
	}
	// formSetData.value.form_config = JSON.stringify(designerData.list)
	console.log(designerData, formSetData.value, '拿到表单数据')
	// 防止提交时，时间格式错误
	formSetData.value.form_config.list = designerData.list.map((item) => {
		console.log(item, 'item')
		if (item.type == 'headImage') {
			formSetData.value.cover = item.options.defaultValue
		}
		// 清除单选框和多选框的设置选项时误触生成的默认值
		if (item.type == 'radio') {
			item.options.defaultValue = ''
		}
		if (item.type == 'checkbox') {
			item.options.defaultValue = null
		}
		if (item.options.valueFormat) {
			item.options.valueFormat = item.options.format
		}
		//如果必填,设置rules.message
		if (item.options.rules?.required) {
			item.options.rules.message = '* 必填项，不能为空'
		}
		return item
	}).filter(item => item.type !== 'seat')
	// .filter(item => item.type !== 'headImage')
	const res = await globalPropValue.survey.setConf.post({ ...formSetData.value, form_config: JSON.stringify(formSetData.value.form_config) })
	if (res.code === 200) {
		saveStatus.value = true
		refresh()
		designerFormClose()
		ElMessage({ type: 'success', message: '保存成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
const designerFormClose = () => {
	console.log('关闭')
	if (saveStatus.value === false) {
		ElMessageBox.confirm('关闭后不会保存你的更改，确定要关闭吗？', '警告', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		})
			.then(() => {
				designerFormD.value.clear()
				dialog.value = false
			})
			.catch(() => { })
	} else {
		designerFormD.value.clear()
		dialog.value = false
	}
}

// 问卷基本数据
const saveDataSuccess = async (data) => {
	console.log(data, 'saveDataSuccess')
	// designerFormD.value.clear()
	formSetData.value.id = data.data.id
	console.log(formSetData.value, 'formSetData.value')
	if (data.mode1 == 'add') {
		dialogFormTitle.value = data.data.title
		// dialog.value = true
		upSearch()
	} else {
		refresh()
	}
}

const saveSuccess = async (data) => {
	console.log(designerFormD.value.setJson(data), 'index')
	const addData = { tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.survey.save.post(addData)
	if (res.code === 200) {
		ElMessage.success('添加成功，去配置问卷吧')
		//dialog.value = false
		upSearch()
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
// let CampusManagementList = ref(campusInfo)
// 搜索按钮回调
const upSearch = () => {
	getSurveyList()
}
// 重置按钮回调
const refresh = () => {
	params.value = defaultParams()
	upSearch()
}

// 提交列表
const submitList = (row) => {
	push({
		name: 'submitList',
		query: {
			id: row.id,
		}
	})
}

// 预览操作
const previewVisible = ref(false)
const widgetForm = ref({})
const showForm = (row) => {
	if (!row.form_config) {
		return ElMessage({ type: 'error', message: '请先配置该问卷' })
	}
	widgetForm.value = JSON.parse(row.form_config)
	previewVisible.value = true
}
//分享操作
const shareCodeRef = ref()
const shareForm = (row) => {
	shareCodeRef.value.open(row)
}
// 新增问卷
const add_items = () => {
	dialogForm.value.dialogFormVisible = true
	nextTick(() => {
		dialogForm.value.open('add')
	})
}
// 编辑操作
const table_edit = (row) => {
	dialogForm.value.dialogFormVisible = true
	nextTick(() => {
		dialogForm.value.open('edit')
		dialogForm.value.setData(row)
	})
}
// 配置操作
const form_set = async (row) => {
	nextTick(() => {
		loading.value = true
		formSetData.value.id = row.id
		dialogFormTitle.value = row.title
		dialog.value = true
	})
	setTimeout(() => {
		designerFormD.value.setJson(row)
		loading.value = false
		saveStatus.value = false
	}, 200)
}
// 发布操作
const publishRow = async (row) => {
	ElMessageBox.confirm('确定要发布吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
		const res = await globalPropValue.survey.publish.post(reqData)
		if (res.code === 200) {
			ElMessage.success('发布成功')
			refresh()
		} else {
			ElMessage({ type: 'error', message: res.message })
		}
	})
}
// 下架操作
const setOffRow = async (row) => {
	ElMessageBox.confirm('确定要下架吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
		const res = await globalPropValue.survey.cancel.post(reqData)
		if (res.code === 200) {
			refresh()
			ElMessage({ type: 'success', message: '下架成功' })
		} else {
			ElMessage({ type: 'error', message: res.message })
		}
	})
}
// 删除操作
const table_del = async (row) => {
	let msg = '确定要删除吗？'
	if (row.status === 3) {
		msg = '该问卷已发布，确定要删除吗？'
	}
	ElMessageBox.confirm(msg, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
		const res = await globalPropValue.survey.del.post(reqData)
		if (res.code === 200) {
			refresh()
			ElMessage({ type: 'success', message: '删除成功' })
		} else {
			ElMessage({ type: 'error', message: res.message })
		}
	})

}

// 获取问卷列表
const surveyList = ref([])
const total = ref(0)
const getSurveyList = async () => {
	const res = await globalPropValue.survey.list.get(params.value)
	if (res.code === 200) {
		surveyList.value = res.data.rows
		total.value = res.data.total
	}
}
//数据回显格式化
const formData = (arr, val) => {
	return arr.find((v) => v.value == val)?.label || '-'
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/global.scss";

.listBox {
	display: grid;
	grid-template-columns: repeat(1, 1fr);
	gap: 16px;
}

@media screen and (min-width: 768px) {
	.listBox {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media screen and (min-width: 1366px) {
	.listBox {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media screen and (min-width: 1600px) {
	.listBox {
		grid-template-columns: repeat(3, 1fr);
	}
}

@media screen and (min-width: 2000px) {
	.listBox {
		grid-template-columns: repeat(4, 1fr);
	}
}

@media screen and (min-width: 2500px) {
	.listBox {
		grid-template-columns: repeat(5, 1fr);
	}
}

.my-header {
	display: flex;
	justify-content: space-between;

	h2 {
		// text-decoration: underline;
	}
}

.designer-form {
	>.el-dialog__body {
		padding: 0;
	}
}
</style>
