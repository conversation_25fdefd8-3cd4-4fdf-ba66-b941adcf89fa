<template>
	<el-container>
		<el-container>
			<el-header style="border: none">
				<el-page-header style="width: 100%" @back="goBack">
					<template #content>
						<span class="text-large font-600 mr-3">{{ scheduleData.schedule_info?.schedule_name }} </span>
					</template>
					<template #extra>
						<div style="display: flex; align-items: center; justify-content: space-between">
							<el-button @click="smartSchedule">一键排课</el-button>
							<el-button type="primary" @click="save">保存</el-button>
						</div>
					</template>
				</el-page-header>
			</el-header>
			<el-main v-loading="isShowLoading" element-loading-text="排课操作中" style="display: flex; flex-wrap: wrap">
				<template v-for="(i,k) in scheduleData.class_list" :key="i.class_id">
					<div class="class-type-content">
						<ul class="table th">
							<li class="row">
								<div class="row-data">
									<span class="cell first-cell">{{ i.class_name }}</span>
									<span v-for="item in scheduleData.attend_day" :key="item" class="cell">{{ item }}</span>
								</div>
							</li>
						</ul>
						<ul class="table td">
							<li v-for="(item, u) in i.periods_list" :key="u" class="row">
								<div class="row-data">
									<span class="cell">{{ item.item_name }}<br />{{ item.begin_time + '~' + item.end_time }}</span>
									<template v-for="(items, index) in item.value" :key="index">
										<el-tooltip
											effect="dark"
											:content="tooltipContent"
											raw-content
											placement="top-start"
											:visible="
												focusData.class_id == i.class_id &&
												items.periods_item_id == focusData.periods_item_id &&
												focusData.attend_day == items.attend_day
											"
										>
											<span
												v-if="items.class_id === i.class_id"
												class="cell"
												:class="[
													{ ishover: selectTeacherData.teacher_id },
													{ noCLick: courseClash },
													{
														bg: teacherData.teacher_id === items.teacher_id && items.course_id == teacherData.course_id
													},
													{ warning: clashIds.includes(items.id + items.attend_day + items.periods_item_id) }
												]"
												@click="tableCellClick(item, items)"
												v-on:mouseenter="tableCellFocus(item, items, item.rules[index])"
												>{{
													item.rules[index]?.classroom_type !== 1
														? formData(classType, item.rules[index]?.classroom_type)
														: items.course_name
												}}
												<span>{{ item.rules[index]?.classroom_type == 1 ? items.teacher_name : '' }}</span>
												<span class="closed" v-if="items.teacher_name">
													<el-icon size="18" color="red" @click.stop="removeItem(k,u,index,items)"><el-icon-CircleClose/></el-icon>
												</span>
											</span>
										</el-tooltip>
									</template>
								</div>
							</li>
						</ul>
					</div>
				</template>
			</el-main>
		</el-container>
		<el-aside width="320px" style="border: none; margin-left: 10px">
			<el-container>
				<el-header style="border: none">
					<el-input
						v-model="teacherName"
						placeholder="输入名字搜索"
						prefix-icon="el-icon-Search"
						clearable
						style="margin: 0"
						@input="nameSearch"
					></el-input>
				</el-header>
				<el-main>
					<el-collapse v-model="activeNames" class="right-content">
						<el-collapse-item title="学科" name="1">
							<ul class="course-list">
								<el-space size="small" spacer="|" wrap>
									<li
										v-for="item in scheduleData.grade_course_info"
										:key="item.course_id"
										:class="{ active: item.course_id == selectCourse.course_id }"
										@click="courseSelect(item)"
									>
										{{ item.course_name }}
									</li>
								</el-space>
							</ul>
							<div class="teacher-list">
								<div
									v-for="item in selectCourse.teacher_list"
									:key="item.teacher_id"
									:class="{
										'teacher-active':
											item.teacher_id == selectTeacherData.teacher_id && item.course_id == selectTeacherData.course_id
									}"
									@click="selectTeacher(item)"
								>
									{{ item.teacher_name + '（' + item.course_name + '）' }}
									<!-- <span >{{ item.current_num + '/' + item.week_num }}</span> -->
								</div>
							</div>
						</el-collapse-item>
						<el-collapse-item title="班级" name="2">
							<ul class="class-list">
								<el-space size="small" spacer="|" wrap>
									<li
										v-for="item in scheduleData.class_list"
										:key="item.class_id"
										:class="{ active: item.class_id == selectClass.class_id }"
										@click="classSelect(item)"
									>
										{{ scheduleData.grade_info?.grade_name + item.class_name }}
									</li>
								</el-space>
							</ul>
							<div class="teacher-list">
								<div
									v-for="item in selectClass.teacher_list"
									:key="item.teacher_id"
									:class="{
										'teacher-active':
											item.teacher_id == selectTeacherData.teacher_id && item.course_id == selectTeacherData.course_id
									}"
									@click="selectTeacher(item)"
								>
									{{ item.teacher_name + '（' + item.course_name + '）' }}
									<!-- <span>{{ item.current_num + '/' + item.week_num }}</span> -->
								</div>
							</div>
						</el-collapse-item>
					</el-collapse>
				</el-main>
			</el-container>
		</el-aside>
		<el-aside v-if="false" width="80px" style="border: none; margin-left: 10px">
			<el-container>
				<el-main>
					<div class="active-list">
						<div class="active-item">
							<el-icon size="25">
								<Pointer />
							</el-icon>
							<p>一键排课</p>
						</div>
					</div>
				</el-main>
			</el-container>
		</el-aside>
	</el-container>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Pointer } from '@element-plus/icons-vue'
import { cloneDeep } from 'lodash'
import cusTom from '@/utils/cusTom'

const { tenantId, campusId, semesterInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const router = useRouter()
const activeNames = ref(['1', '2'])
const teacherName = ref('')
const scheduleData = ref({})
const selectCourse = ref({})
const selectClass = ref({})
const selectTeacherData = ref({})
const tooltipContent = ref(null)
const isShowLoading = ref(false)
const classType = ref([
	{
		label: '行政班',
		value: 1
	},
	// {
	//     label: '走班',
	//     value: 2
	// },
	{
		label: '班会',
		value: 3
	},
	{
		label: '自习',
		value: 4
	},
	{
		label: '活动',
		value: 5
	},
	{
		label: '劳动',
		value: 6
	}
])
//数据回显格式化
const formData = (arr, val) => {
	return arr.find((v) => v.value == val)?.label || val
}
const nameSearch = (val) => {
	let data = cloneDeep(scheduleData.value)
	if (!teacherName.value) {
		selectClass.value = scheduleData.value.class_list[0]
		selectCourse.value = scheduleData.value.grade_course_info[0]
		console.log(teacherName.value, data.class_list[0], data.grade_course_info[0], '!!!!')
	} else {
		const filterList = data.teacher_list.filter((item) =>
			item.teacher_name.toLowerCase().includes(teacherName.value.toLowerCase())
		)
		selectCourse.value.teacher_list = filterList
		selectClass.value.teacher_list = filterList
		console.log(teacherName.value, 'true')
	}
}
const courseSelect = (val) => {
	selectCourse.value = val
	console.log(val)
}
const classSelect = (val) => {
	selectClass.value = val
	console.log(val)
}
const selectTeacher = (val) => {
	if (selectTeacherData.value.teacher_id == val.teacher_id) {
		selectTeacherData.value = {}
		teacherData.value = {}
	} else {
		selectTeacherData.value = val
		teacherData.value = val
	}
	console.log(val)
}

const teacherData = ref({})
const tableCellClick = async (periods, course) => {
	if (!selectTeacherData.value.teacher_id) {
		if (teacherData.value.teacher_id == course.teacher_id) {
			teacherData.value = {}
		} else {
			teacherData.value = course
		}
		return
	}

	if (courseClash.value) {
		return
	}

	console.log(periods, course, focusData.value, 'tableCellClick')

	const updatedScheduleData = await scheduleData.value.class_list.map(async (i) => {
		if (i.class_id == course.class_id) {
			const updatedPeriodsList = await i.periods_list.map(async (item) => {
				if (item.periods_item_id == periods.periods_item_id) {
					const updatedItems = await item.value.map(async (items) => {
						if (
							items.class_id == course.class_id &&
							items.periods_item_id == periods.periods_item_id &&
							items.attend_day == course.attend_day
						) {
							items.teacher_id = selectTeacherData.value.teacher_id
							items.course_id = selectTeacherData.value.course_id
							items.course_name = selectTeacherData.value.course_name
							items.teacher_name = selectTeacherData.value.teacher_name

							const result = await courseCheck(items)
							items.clash = result.clash
							items.clash_detail = result.clash_detail

							return items
						}
					})
				}
			})
		}
		return i
	})

	scheduleData.value.class_list = await Promise.all(updatedScheduleData)

	console.log(scheduleData.value)
}
const courseCheck = async (val) => {
	const response = await globalPropValue.eduSchedule.schedule.check.post({
		...val,
		tenant_id: tenantId,
		campus_id: campusId,
		schedule_id: Number(router.currentRoute.value.query.id)
	})

	if (response.code == 200) {
		if (response.data.clash == 1 && response.data.clash_detail !== undefined) {
			clashIds.value.push(val.id + val.attend_day + val.periods_item_id)
			// tooltipContent.value = `<p>时间冲突</p><p>${JSON.parse(response.data.clash_detail).join('<br/>')}</p>`;
		} else {
			clashIds.value = clashIds.value.filter((item) => item !== val.id + val.attend_day + val.periods_item_id)
		}
		return response.data
	}
}
const focusData = ref({})
const courseClash = ref(false)
const tableCellFocus = (periods, course, rules) => {
	focusData.value = {
		class_id: course.class_id,
		periods_item_id: periods.periods_item_id,
		attend_day: course.attend_day
	}
	courseClash.value = false
	if (rules.is_adjust == -1 && rules.classroom_type !== 1) {
		courseClash.value = true
		tooltipContent.value = '该节次不可排，不可调整'
	} else if (rules.classroom_type == 1 && rules.is_adjust == -1 && rules.forbid_course != null) {
		if (selectTeacherData.value.teacher_id) {
			courseClash.value = rules.forbid_course.includes(selectTeacherData.value.course_id)
		} else {
			if (clashIds.value.includes(course.id + course.attend_day + course.periods_item_id)) {
				tooltipContent.value = `<p>时间冲突</p><p>${JSON.parse(course.clash_detail).join('<br/>')}</p>`
			}
			return
		}
		let courseData = rules.forbid_course_list.map((item) => item.name)
		tooltipContent.value = `<p>该节次不可排课程${courseData.join('，')}</p>`
	} else if (rules.classroom_type == 1 && rules.is_adjust == -1 && rules.forbid_user != null) {
		if (selectTeacherData.value.teacher_id) {
			courseClash.value = rules.forbid_user.includes(selectTeacherData.value.teacher_id)
		}
		let teacher = rules.forbid_user_list.map((item) => item.name)
		tooltipContent.value = `<p>该节次不可排人员${teacher.join('，')}</p>`
	} else if (
		rules.classroom_type == 1 &&
		rules.is_adjust == -1 &&
		rules.forbid_user != null &&
		rules.forbid_course != null
	) {
		if (selectTeacherData.value.teacher_id) {
			courseClash.value = rules.forbid_user.includes(selectTeacherData.value.teacher_id)
		}
		let courseData = rules.forbid_course_list.map((item) => item.name)
		let teacher = rules.forbid_user_list.map((item) => item.name)
		tooltipContent.value = `<p>该节次不可排课程${courseData.join('，')}</p><p>该节次不可排人员${teacher.join('，')}</p>`
	} else if (clashIds.value.includes(course.id + course.attend_day + course.periods_item_id)) {
		tooltipContent.value = `<p>时间冲突</p><p>${JSON.parse(course.clash_detail).join('<br/>')}</p>`
	} else {
		focusData.value = {}
		courseClash.value = false
		tooltipContent.value = ''
	}
	// console.log(periods, course, rules, 'tableCellFocus')
	// console.log(focusData.value, courseClash.value, 'tableCellFocus')
}

onMounted(() => {
	getData()
})
// 一键排课
const smartSchedule = () => {
	let id = router.currentRoute.value.query.id
	ElMessageBox.confirm('一键排课会重置此次排课数据，是否确定一键排课？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	})
		.then(() => {
			isShowLoading.value = true
			globalPropValue.eduSchedule.schedule.smart
				.post(
					{
						id: Number(id),
						tenant_id: tenantId,
						campus_id: campusId
					},
					{ timeout: 20000 }
				)
				.then((res) => {
					isShowLoading.value = false
					if (res.code === 200) {
						getData()
						console.log(res.data, '一键排课数据')
					} else {
						ElMessage({
							type: 'error',
							message: res.message
						})
					}
				})
		})
		.catch(() => {
			isShowLoading.value = false
			ElMessage({
				type: 'info',
				message: '已取消'
			})
		})

	console.log(scheduleData.value)
}
// 保存排课
const save = () => {
	let val = []
	scheduleData.value.class_list.forEach((item) => {
		item.periods_list.forEach((item) => {
			val.push(...item.value)
		})
	})
	console.log(val, '678')
	globalPropValue.eduSchedule.schedule.save
		.post({
			schedule_id: Number(router.currentRoute.value.query.id),
			tenant_id: tenantId,
			campus_id: campusId,
			schedule_detail: val
		})
		.then((res) => {
			if (res.code == 200) {
				ElMessage({
					type: 'success',
					message: '排课成功'
				})
				getData()
			} else {
				ElMessage({
					type: 'error',
					message: res.msg
				})
			}
		})
}
const goBack = () => {
	router.go(-1)
}
const transformArrays = (first, second, three) => {
	// 创建一个映射表，提高查找效率
	const idToMatchedItemMap = new Map()
	second.forEach((item) => {
		if (!idToMatchedItemMap.has(item.periods_item_id)) {
			idToMatchedItemMap.set(item.periods_item_id, [])
		}
		idToMatchedItemMap.get(item.periods_item_id).push(item)
	})
	const idToMatchedItemMap2 = new Map()
	three.forEach((item) => {
		if (!idToMatchedItemMap2.has(item.periods_item_id)) {
			idToMatchedItemMap2.set(item.periods_item_id, [])
		}
		idToMatchedItemMap2.get(item.periods_item_id).push(item)
	})

	return first.map((firstItem) => {
		const resultItem = { ...firstItem }
		const matchedItems = idToMatchedItemMap.get(firstItem.periods_item_id) || []
		const matchedItems2 = idToMatchedItemMap2.get(firstItem.periods_item_id) || []
		// 对 matchedItems 按照 attend_day 排序
		matchedItems.sort(compareDays)

		// 确保 value 字段的数据类型一致
		resultItem.value = matchedItems.length > 0 ? matchedItems : []
		resultItem.rules = matchedItems2.length > 0 ? matchedItems2 : []
		return resultItem
	})
}
const compareDays = (a, b) => {
	// 创建一个星期名称到索引的映射
	const dayOrder = {
		星期一: 1,
		星期二: 2,
		星期三: 3,
		星期四: 4,
		星期五: 5,
		星期六: 6,
		星期日: 7
	}
	// 使用映射来获取星期名称对应的索引，‌并根据索引进行比较
	return dayOrder[a.attend_day] - dayOrder[b.attend_day]
}
const clashIds = ref([])
const getData = () => {
	isShowLoading.value = true
	let id = router.currentRoute.value.query.id
	let params = {
		tenant_id: tenantId,
		campus_id: campusId,
		id: id
	}
	globalPropValue.eduSchedule.schedule.detail.get(params).then((res) => {
		if (res.code == 200) {
			isShowLoading.value = false
			scheduleData.value = res.data
			// 获取冲突集合
			clashIds.value = getClashIds(res.data.schedule_detail)
			// 默认
			selectClass.value = res.data.class_list[0]
			selectCourse.value = res.data.grade_course_info[0]
			// 统计当前排课老师的节数
			scheduleData.value.class_list = res.data.class_list.map((item) => {
				item.teacher_list = countTeacher(item.teacher_list, res.data.schedule_detail)
				return item
			})
			scheduleData.value.grade_course_info = res.data.grade_course_info.map((item) => {
				item.teacher_list = countTeacher(item.teacher_list, res.data.schedule_detail)
				return item
			})
			// 处理数据
			scheduleData.value.periods_list = transformArrays(
				res.data.periods_list,
				res.data.schedule_detail,
				scheduleData.value.rule_list
			)
			scheduleData.value.class_list = scheduleData.value.class_list
				.map((item) => {
					if (!item.class_id) {
						console.error('item缺少class_id:', item)
						return null
					}
					const filteredPeriodsList = filterPeriodsByClassId(item.class_id, scheduleData.value.periods_list)
					return {
						...item,
						periods_list: filteredPeriodsList
					}
				})
				.filter(Boolean)
			scheduleData.value.teacher_list = countTeacher(res.data.teacher_list, res.data.schedule_detail)
			console.log(scheduleData.value, clashIds.value)
		}
	})
}
// 统计每个老师的排课次数
const countTeacher = (teacher, detail) => {
	if (teacher === null) {
		return teacher
	}
	// 统计每个 teacher_id 的出现次数
	const teacherCount = detail.reduce((acc, item) => {
		const teacherId = item.teacher_id
		acc[teacherId] = (acc[teacherId] || 0) + 1
		return acc
	}, {})

	// 在原数组中添加 current_num 字段
	const updatedArray = teacher.map((item) => ({
		...item,
		current_num: teacherCount[item.teacher_id]
	}))
	return updatedArray
}
const filterPeriodsByClassId = (classId, periodsList) => {
	return periodsList.map((items) => ({
		...items,
		value: items.value.filter((i) => i.class_id === classId)
	}))
}

// 处理数据获得冲突集合
const getClashIds = (val) => {
	// 用于存储符合条件的 id
	const idSet = new Set()

	// 对数组进行分组
	const groupedData = val.reduce((acc, item) => {
		const key = `${item.attend_day}-${item.periods_item_id}`
		if (!acc[key]) {
			acc[key] = []
		}
		acc[key].push(item)
		return acc
	}, {})

	// 筛选符合条件的 id
	for (const key in groupedData) {
		const items = groupedData[key]
		if (items.some((item) => item.clash === 1)) {
			items.forEach((item) => {
				if (item.clash === 1) {
					idSet.add(item.id + item.attend_day + item.periods_item_id)
				}
			})
		}
	}
	return Array.from(idSet)
}

const removeItem=(k,u,index,items)=>{
	//console.log(k,u,index,items,'removeItem')
	scheduleData.value.class_list[k].periods_list[u].value[index].teacher_id=0
	scheduleData.value.class_list[k].periods_list[u].value[index].teacher_name=""
	scheduleData.value.class_list[k].periods_list[u].value[index].course_id=0
	scheduleData.value.class_list[k].periods_list[u].value[index].course_name=""
	//scheduleData.value.class_list[k].periods_list[u].value.splice(index,1)
	//console.log(scheduleData.value.class_list[k].periods_list[u].value,'removeItem')

}
</script>

<style lang="scss">
.right-content {
	.el-collapse-item {
		.el-collapse-item__header {
			height: 32px;
		}
	}
}
</style>
<style lang="scss" scoped>
.class-type-content {
	display: flex;
	flex-direction: column;
	margin-bottom: 20px;
	margin-right: 20px;

	// border: 1px solid var(--el-border-color-light);
	.table {
		width: 100%;
		display: flex;

		.row {
			border-left: 3px solid var(--el-border-color-light);
			border-right: 3px solid var(--el-border-color-light);
			border-bottom: 1px solid var(--el-border-color-light);

			.row-data {
				display: flex;
				text-align: center;

				.bg1.selected {
					color: var(--el-color-primary);
					background-color: var(--el-color-primary-light-8);
				}

				.bg3.selected {
					color: var(--el-color-success);
					background-color: var(--el-color-success-light-8);
				}

				.bg4.selected {
					color: var(--el-color-warning);
					background-color: var(--el-color-warning-light-8);
				}

				.bg5.selected {
					color: var(--el-color-info);
					background-color: var(--el-color-info-light-8);
				}

				.bg6.selected {
					color: var(--el-color-danger);
					background-color: var(--el-color-danger-light-8);
				}
			}

			.cell {
				flex: 1;
				text-align: center;
				min-width: 100px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				border-right: 1px solid var(--el-border-color-light);
				position: relative;
				&:first-child {
					flex: 0;
					min-width: 90px;
				}

				&:last-child {
					border-right: none;
				}
				&:hover{
					.closed{
						display: inline-block;
						position: absolute;
						right:5px;
						top:5px;
						width: 20px;
						height: 20px;
						border-radius: 50%;
						background: white;
					}
				}
				span {
					font-size: 12px;
					color: var(--el-color-info);
				}
				
				.closed{
					display: none;
					position: absolute;
						right:5px;
						top:5px;
						width: 20px;
						height: 20px;
						border-radius: 50%;
						background: white;
				}
			}

			.ishover {
				&:hover {
					background-color: var(--el-color-primary-light-9);
				}
			}

			.noCLick {
				cursor: not-allowed;
			}

			.bg {
				color: var(--el-color-primary);
				background-color: var(--el-color-primary-light-8);
			}

			.warning {
				color: var(--el-color-warning);
				background-color: var(--el-color-warning-light-8);
			}
		}
	}

	.th {
		.row {
			width: 100%;
			border-top: 3px solid var(--el-border-color-light);
			background-color: var(--el-border-color-extra-light);
		}

		.row-data {
			width: 100%;
			height: 35px;

			.first-cell {
				color: var(--el-color-primary);
				font-size: 12px;
				font-weight: bold;
			}
		}
	}

	.td {
		flex-direction: column;

		.row {
			&:last-child {
				border-bottom: 3px solid var(--el-border-color-light);
			}

			.row-data {
				height: 60px;

				.cell {
					&:not(:first-child) {
						font-size: 14px;
					}

					&:first-child {
						background-color: var(--el-border-color-extra-light);
					}
				}
			}
		}
	}
}

.course-list,
.class-list {
	display: flex;
	flex-wrap: wrap;
	padding: 15px 0;
	border-bottom: 5px solid var(--el-collapse-border-color);

	li {
		padding: 0px 10px;
		border-radius: 5px;
		cursor: pointer;
	}

	.active {
		color: var(--el-color-primary);
		background-color: var(--el-color-primary-light-8);
	}
}

.teacher-list {
	min-height: 120px;
	max-height: 160px;
	overflow-y: auto;

	div {
		box-sizing: border-box;
		width: 100%;
		padding: 5px 10px;
		cursor: pointer;
		display: flex;
		justify-content: space-between;

		span {
			font-size: 12px;
			color: var(--el-color-info);
		}

		&:hover {
			color: var(--el-color-primary);
			background-color: var(--el-color-primary-light-8);
		}
	}

	.teacher-active {
		color: var(--el-color-primary);
		background-color: var(--el-color-primary-light-8);
	}
}

.active-list {
	.active-item {
		width: 100%;
		height: 50px;
		text-align: center;
		cursor: pointer;

		p {
			line-height: 2;
		}
	}
}
</style>
