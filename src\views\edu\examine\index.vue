<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <div class="left-panel-search">
                    <el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区"
                        filterable style="margin-right: 15px" @change="campusChange">
                        <el-option v-for="item in CampusManagementList" :key="item.code" :label="item.name"
                            :value="item.value"></el-option>
                    </el-select>
                    <cusSelectSemester v-model="params.semester_id" :params="params" :show-default-value="true"
                        :width="'214px'" style="margin-right: 15px" @semesterChange="semesterChange" />
                    <el-select v-model="params.grade_id" placeholder="请选择考试年级" filterable clearable
                        style="margin-right: 15px">
                        <el-option v-for="item in gradeList" :key="item.id" :label="item.grade_name"
                            :value="item.id"></el-option>
                    </el-select>
                    <el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
                    <el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
                </div>
            </div>
            <div class="right-panel">
                <el-button type="primary" icon="el-icon-plus" @click="add">新增考试</el-button>
            </div>
        </el-header>
        <el-main>
            <scTable ref="table" row-key="id" :apiObj="apiObj" :params="params" :hideDo="true">
                <el-table-column label="考试名称" prop="examine_name" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column label="考试年级" prop="grade_info" width="100">
                    <template #default="{ row }">
                        {{ row.grade_info.name }}
                    </template>
                </el-table-column>
                <el-table-column label="考试类型" prop="examine_type" width="100">
                    <template #default="{ row }">
                        {{examineTypeMap.find((item) => item.value === row.examine_type).name}}
                    </template>
                </el-table-column>
                <el-table-column label="考试时间" prop="begin_date" width="180">
                    <template #default="{ row }">
                        {{ row.begin_date }} ~ {{ row.end_date }}
                    </template>
                </el-table-column>
                <el-table-column label="考生数量" prop="student_num" width="100">
                </el-table-column>
                <el-table-column label="是否需要签到" prop="sign" width="100">
                    <template #default="{ row }">
                        <el-tag :type="row.sign === 1 ? 'success' : 'info'">{{ row.sign === 1 ? '开启' : '不开启'
                            }}</el-tag>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="学生签到" prop="student_sign" width="100">
                    <template #default="{ row }">
                        <el-tag :type="row.student_sign === 1 ? 'success' : 'info'">{{ row.student_sign === 1 ? '开启' :
                            '不开启' }}</el-tag>
                    </template>
                </el-table-column> -->
                <el-table-column label="创建时间" prop="created_at" width="180">
                </el-table-column>
                <el-table-column label="状态" prop="status">
                    <template #default="{ row }">
                        <el-switch v-model="row.status" :active-value="1" :inactive-value="-1" active-text="启用"
                            inline-prompt style="--el-switch-on-color: #00b42a" inactive-text="关闭"
                            @change="statusChange(row)"></el-switch>


                    </template>
                </el-table-column>

                <el-table-column label="操作" min-width="300">
                    <template #default="{ row }">
                        <el-button type="warning" v-if="row.status == 1" size="small" plain
                            @click="sync(row)">同步到班牌</el-button>

                        <el-button text type="primary" @click="table_edit(row)">编辑</el-button>
                        <el-button text type="primary" @click="table_arrange(row)">考务安排</el-button>
                        <el-button text type="primary" @click="table_output(row)">考务输出</el-button>
                        <el-button text type="danger" @click="table_del(row)">删除</el-button>
                    </template>
                </el-table-column>
            </scTable>
        </el-main>
    </el-container>
    <save ref="saveRef" :params="params" @saveSuccess="upsearch" />
</template>
<script setup>
import save from './save.vue'

import cusSelectSemester from '@/components/custom/cusSelectSemester'
import { ElMessageBox, ElMessage } from 'element-plus'
const { push } = useRouter()
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
    return {
        campus_id: campusId,
        tenant_id: tenantId,
        semester_id: null,
        grade_id: null,
        name: null
    }
}
const CampusManagementList = ref(campusInfo)
const params = ref(defaultParams())
const apiObj = ref(globalPropValue.examine.list)
const currentSemester = ref('')
const semesterChange = (val) => {
    currentSemester.value = val
    params.value.semester_id = val
    getGradeList()
}

// 新增考试

const saveRef = ref()
const table = ref()
const add = () => {
    saveRef.value.open('add')
}

const table_edit = (row) => {
    saveRef.value.open('edit', row)
}

const sync = (row) =>{
    ElMessageBox.confirm('确认将当前考试安排同步到班牌设备？<br/>如果之前已同步过，将会全量覆盖数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
    }).then(() => {
        var reqData = { tenant_id: row.tenant_id, campus_id: row.campus_id,id: row.id }
        globalPropValue.examine.sync.post(reqData).then((res) => {
            if (res.code === 200) {
                ElMessageBox.alert('同步成功！', '提示', {
                    successButtonText: '确定',
                    type: 'success',
                })
                //this.$message.success('提交同步任务成功')
            } 
        })
    })
}

const statusChange = (val) => {
    console.log(val, 'statusChange')
    globalPropValue.examine.changeStatus.post(val).then((res) => {
        if (res.code == 200) {
            if (val.status == 1) {
                ElMessageBox.alert('启用成功！请重新同步考试到班牌设备', '提示', {
                    successButtonText: '确定',
                    type: 'success',
                })
            } else {
                ElMessage.success('禁用成功')
            }
            table.value.upData(params.value)
        }
    })
}

//考务安排
const table_arrange = (row) => {
    push({
        name: 'examineArrange',
        query: {
            id: row.id,
            name: row.examine_name,
            seat_type: row.seat_type,
        }
    })
}
// 考务输出
const table_output = (row) => {
    push({
        name: 'examineOutput',
        query: {
            id: row.id,
            name: row.examine_name,
            seat_type: row.seat_type,
        }
    })
}

const table_del = (row) => {
    ElMessageBox.confirm('确定删除该考试安排吗？<br/>删除后对应的监考安排和考生安排也会删除！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
    }).then(() => {
        globalPropValue.examine.del.post({
            id: row.id,
            campus_id: campusId,
            tenant_id: tenantId,
        }).then((res) => {
            if (res.code === 200) {
                ElMessage.success('删除成功')
                refresh()
            }
        })
    })
}

const upsearch = () => {
    table.value.upData(params.value)
}
const refresh = () => {
    params.value = defaultParams()
    params.value.semester_id = currentSemester.value
    upsearch()
}

// 获取年级列表
const gradeList = ref([])
const getGradeList = () => {
    globalPropValue.eduGradeClass.grade.all.get({
        tenant_id: tenantId,
        campus_id: campusId,
        semester_id: params.value.semester_id
    }).then(res => {
        if (res.code === 200) {
            gradeList.value = res.data
        }
    })
}
</script>