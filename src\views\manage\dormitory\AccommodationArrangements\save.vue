<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'">
			<el-form-item label="宿管" prop="staff_idObj">
				<cusSelectTeacher v-model="form.staff_idObj"></cusSelectTeacher>
			</el-form-item>

			<el-form-item label="管理范围" prop="room_idsObj">
				<cusSelectRoom v-model="form.room_idsObj" multiple> </cusSelectRoom>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const defaultData = () => {
	return {
		staff_id: null,
		remark: null,
		tenant_id: null,
		campus_id: null,
		staff_idObj: [],
		room_ids: null,
		room_idsObj: []
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				room_idsObj: [{ required: true, message: '请选择' }],
				staff_idObj: [{ required: true, message: '请选择' }]
			},
			//所需数据选项
			groups: [],
			groupsProps: {
				value: 'id',
				label: 'discipline_name'
			}
		}
	},
	mounted() {},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					if (this.form.staff_idObj) {
						this.form.staff_id = this.form.staff_idObj[0].id
					}
					if (this.form.room_idsObj) {
						this.form.room_ids = this.form.room_idsObj.map((i) => i.id).join(',')
					}
					var res = await this.$API.buildingRooms.AccommodationArrangements.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			// this.form.id = data.id
			// this.form.label = data.label
			// this.form.status = data.status
			// this.form.sort = data.sort
			// this.form.parentId = data.parentId
			// this.form.remark = data.remark

			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			if (data.staff_id) {
				this.form.staff_idObj = [
					{
						id: data.staff_id,
						label: data.staff_name
					}
				]
			}
			if (data.room) {
				let tempArr = []
				data.room.forEach((v) => {
					tempArr.push({
						id: v.id,
						label: v.name
					})
				})
				this.form.room_idsObj = tempArr
				//暂时更改
			}
			Object.assign(this.form, data)
			console.log(this.form, '123')
		}
	}
}
</script>

<style></style>
