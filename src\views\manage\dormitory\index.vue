<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :name="item.name" :label="item.label" :key="item.name"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script>
import buildingRooms from './buildingRooms'
import DormitoryView from './DormitoryView'
import PersonnelManagement from './PersonnelManagement'
import AccommodationArrangements from './AccommodationArrangements'
import EntryAndExitRecords from './EntryAndExitRecords'
import ScoreSetting from './ScoreSetting'
import EvaluationManagement from './EvaluationManagement'
import RegularEntry from './RegularEntry'
import score from './score'
import { shallowRef } from 'vue'
export default {
	name: 'layoutTCB',
	components: {
		buildingRooms,
		DormitoryView,
		PersonnelManagement,
		AccommodationArrangements,
		EntryAndExitRecords,
		score,
		ScoreSetting,
		EvaluationManagement
	},
	data() {
		return {
			activeName: 'DormitoryView',
			tabs: [
				{
					label: '宿舍视图',
					name: 'DormitoryView',
					component: shallowRef(DormitoryView)
				},
				{
					label: '楼栋房间管理',
					name: 'buildingRooms',
					component: shallowRef(buildingRooms)
				},
				{
					label: '宿舍人员安排',
					name: 'PersonnelManagement',
					component: shallowRef(PersonnelManagement)
				},
				{
					label: '宿管安排',
					name: 'AccommodationArrangements',
					component: shallowRef(AccommodationArrangements)
				},
				{
					label: '宿舍出入记录',
					name: 'EntryAndExitRecords',
					component: shallowRef(EntryAndExitRecords)
				},
				{
					label: '评分录入',
					name: 'RegularEntry',
					component: shallowRef(RegularEntry)
				},
				{
					label: '评分记录',
					name: 'score',
					component: shallowRef(score)
				},
				{
					label: '评价管理',
					name: 'EvaluationManagement',
					component: shallowRef(EvaluationManagement)
				},
				{
					label: '分值设置',
					name: 'ScoreSetting',
					component: shallowRef(ScoreSetting)
				}
			],
			currComponent: {}
		}
	},
	created() {
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
		}
	}
}
</script>

<style></style>
