<template>
	<el-form-item label="取值范围" prop="modelValue">
		<el-row>
			<el-col :span="11">
				<el-form-item prop="type_spec.specs.min">
					<el-input-number
						v-model="modelValue.min"
						type="number"
						style="width: 100%"
						controls-position="right"
						:controls="false"
						:precision="2"
						placeholder="最大值"
						clearable
					></el-input-number>
				</el-form-item>
			</el-col>
			<el-col :span="2" class="text-center">
				<span class="text-gray-500"> - </span>
			</el-col>
			<el-col :span="11">
				<el-form-item prop="type_spec.specs.max">
					<el-input-number
						v-model="modelValue.max"
						type="number"
						style="width: 100%"
						controls-position="right"
						:controls="false"
						:precision="2"
						placeholder="最大值"
						clearable
					></el-input-number>
				</el-form-item>
			</el-col>
		</el-row>
	</el-form-item>
	<el-form-item label="步长" prop="type_spec.specs.step">
		<el-input-number
			v-model="modelValue.step"
			type="number"
			controls-position="right"
			style="width: 45%"
			:controls="false"
			:precision="2"
			placeholder="请输入步长"
			clearable
		></el-input-number>
	</el-form-item>
	<el-form-item label="单位" prop="type_spec.specs.unit">
		<el-select
			v-model="modelValue.unit"
			placeholder="请选择"
			style="width: 100%; max-width: unset"
			filterable
			@change="unitChange"
		>
			<el-option
				v-for="item in unitConfig"
				:key="item.id"
				:label="item.name + ' (' + item.symbol + ')'"
				:value="item.id"
			>
				<span style="float: left">{{ item.name }}</span>
				<span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
					{{ item.symbol }}
				</span>
			</el-option>
		</el-select>
	</el-form-item>
</template>
<script>
import tool from '@/utils/tool'
import cusTom from '@/utils/cusTom'

export default {
	name: 'floatModule',
	props: {
		modelValue: {
			type: Object,
			default: () => {
				return {
					min: null,
					max: null,
					step: null,
					unit: null,
					unit_name: null,
					unit_symbol: null
				}
			}
		}
	},
	data() {
		return {
			unitConfig: []
		}
	},
	emits: ['update:modelValue'], // 明确声明该组件会触发 update:modelValue 事件
	watch: {
		modelValue: {
			handler(val) {
				this.$emit('update:modelValue', val)
			},
			deep: true
		}
	},
	created() {
		cusTom.getUnitConfig().then((res) => {
			this.unitConfig = res
		})
	},
	methods: {
		unitChange(id) {
			let unit = this.unitConfig.find((item) => item.id === id)
			let info = tool.objCopy(this.modelValue)
			info.unit_name = unit.name
			info.unit_symbol = unit.symbol
			this.$emit('update:modelValue', info)
		},
		getUnitConfig() {
			let unitConfig = tool.data.get('unitConfig')
			if (unitConfig) {
				this.unitConfig = unitConfig
			} else {
				this.$LotApi.common.unitList.get().then((res) => {
					if (res.code === 200) {
						this.unitConfig = res.data
						tool.data.set('unitConfig', res.data, 2 * 60 * 60)
					}
				})
			}
		}
	}
}
</script>

<style scoped lang="scss">
.enum_header {
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.enum_item {
	margin: 5px 0;
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.text-center {
	text-align: center;
}
</style>
