<template>
	<div class="msg-content">
		<ul v-if="paginatedArray?.length !== 0" class="msg-list">
			<li v-for="item in paginatedArray" v-bind:key="item.id" @click="showMessage">
				<a :href="item.link" target="_blank">
					<div class="msg-list__icon">
						<el-badge is-dot type="danger">
							<el-avatar>{{ item.title.substring(0, 1) }}</el-avatar>
						</el-badge>
					</div>
					<div class="msg-list__main">
						<h2>{{ item.title }}</h2>
						<!-- <p>{{ item.content }}</p> -->
					</div>
					<div class="msg-list__time">
						<p>{{ item.time }}</p>
					</div>
				</a>
			</li>
		</ul>
		<el-empty v-else description="暂无新消息" :image-size="100"></el-empty>
		<div class="pagination">
			<el-pagination
				layout="prev, pager, next"
				:total="total"
				:current-page="currentPage"
				:page-size="pageSize"
				@current-change="handlePageChange"
			/>
		</div>
	</div>
</template>
<script>
import { getDateBefore } from '@/utils/dayjs'
import { Message } from '@element-plus/icons-vue'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId } = cusTom.getBaseQuery()
export default {
	name: 'myNotice',
	props: {
		messageType: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			msgList: [],
			paginatedArray: [],
			total: 0,
			currentPage: 1,
			pageSize: 10,
			params: {
				tenant_id: tenantId,
				campus_id: campusId,
				message_type: 0
			},
			Message: 'el-icon-message'
		}
	},
	watch: {
		messageType(val) {
			this.params.message_type = val
			this.paginatedArray = []
			this.getUnread()
		}
	},
	created() {
		this.getUnread()
	},
	methods: {
		showMessage() {
			this.$router.push({ path: '/message' })
		},
		getUnread() {
			this.$API.notice.unread.get(this.params).then((res) => {
				if (res.code == 200) {
					this.msgList = res.data.map((item) => {
						item.time = getDateBefore(item.created_at)
						return item
					})
					this.total = this.msgList.length
					this.paginateArray(this.msgList)
				}
			})
		},
		paginateArray(arry) {
			if (!arry?.length) {
				return
			}
			const start = (this.currentPage - 1) * this.pageSize
			const end = start + this.pageSize
			this.paginatedArray = arry.slice(start, end)
		},
		handlePageChange(currentPage) {
			this.currentPage = currentPage
			this.paginateArray(this.msgList)
		}
	}
}
</script>

<style lang="scss" scoped>
.msg-content {
	position: relative;
	padding-bottom: 40px;
}

.msg-list {
	max-height: 600px;
	overflow: auto;
}

.msg-list li {
	/* border-top: 1px solid #eee; */
	border-bottom: 1px solid #eee;
	cursor: pointer;
}

.msg-list li:last-child {
	/* border-top: 1px solid #eee; */
	border-bottom: none;
}

.msg-list li a {
	display: flex;
	padding: 20px;
}

.msg-list li a:hover {
	background: #ecf5ff;
}

.msg-list__icon {
	width: 40px;
	margin-right: 15px;
}

.msg-list__main {
	flex: 1;
}

.msg-list__main h2 {
	font-size: 14px;
	font-weight: normal;
	color: #333;
}

.msg-list__main p {
	font-size: 12px;
	color: #999;
	line-height: 1.8;
	margin-top: 5px;
}

.msg-list__time {
	width: 100px;
	text-align: right;
	color: #999;
}

.pagination {
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
}
</style>
