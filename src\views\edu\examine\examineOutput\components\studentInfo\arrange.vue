<template>
	<div>
		<div class="add-btn">
			<p></p>
			<div>
				<el-input
					v-model="studentName"
					style="width: 200px"
					clearable
					placeholder="输入姓名搜索"
					prefix-icon="el-icon-search"
				></el-input>
				<el-button style="margin-left: 10px" type="primary" @click="exportExcel">导出</el-button>
			</div>
		</div>
		<el-table :data="paginatedSeatList" stripe style="width: 100%" min-height="400">
			<el-table-column prop="serial_number" label="学号/考号" align="center"></el-table-column>
			<el-table-column prop="student_name" label="姓名" align="center"></el-table-column>
			<el-table-column prop="grade" label="原班级" align="center">
				<template #default="{ row }">
					<div>{{ row.grade_info?.name || '' }} {{ row.class_info?.name || '' }}</div>
				</template>
			</el-table-column>
			<el-table-column v-for="(item, index) in dateList" :key="index" :label="item.date" align="center">
				<el-table-column
					v-for="items in item.course"
					:key="items.id"
					:prop="items.time"
					:label="`${items.course_name}`"
					align="center"
				>
					<template #header="{ column }">
						<div>{{ column.label }}</div>
						<div>{{ column.property }}</div>
					</template>
					<el-table-column label="地点" align="center">
						<template #default="{ row }">
							<div>{{ row.seat_info[items.id]?.room_info.name || '' }}</div>
						</template>
					</el-table-column>
					<el-table-column label="座位号" align="center">
						<template #default="{ row }">
							<div>{{ row.seat_info[items.id]?.seat_number || '' }}</div>
						</template>
					</el-table-column>
				</el-table-column>
			</el-table-column>
		</el-table>
		<el-pagination
			v-model:current-page="currentPage"
			v-model:page-size="pageSize"
			size="small"
			background
			:page-sizes="[10, 20, 30, 50]"
			:total="filteredArrangeList.length"
			layout="total, sizes, prev, pager, next, jumper"
			class="pagination"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		/>
	</div>
</template>
<script setup>
import { excelExport } from 'pikaz-excel-js'
import cusTom from '@/utils/cusTom'

const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		examine_id: query.id
	}
}
const params = ref(defaultParams())

// 导出
const exportExcel = async () => {
	let merges = ['1-2:1-6', '2-2:2-6', '3-2:3-6']
	const headers = [
		{ label: '学号/考号', key: 'serial_number' },
		{ label: '姓名', key: 'student_name' },
		{ label: '原班级', key: 'grade' }
	]

	let startColumn = 4 // 从第4列开始（前3列是固定的：学号、姓名、原班级）
	dateList.value.forEach((date, index) => {
		// 计算当前日期的课程占用的总列数（每个课程占2列：地点和座位号）
		const coursesColumns = date.course.length * 2
		// 合并日期行，从startColumn列开始，跨coursesColumns列
		merges.push(`${startColumn - 4 + 1 + 3}-2:${startColumn + coursesColumns - 1}-2`)

		date.course.forEach((course, courseIndex) => {
			// 计算当前课程的起始列
			const courseStartColumn = startColumn + courseIndex * 2
			// 合并课程行，每个课程合并2列（地点和座位号）
			merges.push(`${courseStartColumn}-3:${courseStartColumn + 1}-5`)

			headers.push(
				{
					label: `${course.course_name}\r\n${course.time}`,
					key: `room_${course.id}`,
					date: courseIndex == 0 ? date.date : ''
				},
				{ label: `${course.course_name}\r\n${course.time}`, key: `seat_${course.id}` }
			)
		})

		// 更新下一个日期的起始列
		startColumn += coursesColumns
	})

	const tableData = arrangeList.value.map((student) => {
		const data = {
			serial_number: student.serial_number,
			student_name: student.student_name,
			grade: `${student.grade_info?.name || ''}${student.class_info?.name || ''}`
		}

		dateList.value.forEach((date) => {
			date.course.forEach((course) => {
				data[`room_${course.id}`] = student.seat_info[course.id]?.room_info.name || ''
				data[`seat_${course.id}`] = student.seat_info[course.id]?.seat_number || ''
			})
		})

		return data
	})
	await excelExport({
		sheet: [
			{
				title: '考生安排表',
				globalStyle: { font: { sz: 14 }, alignment: { wrapText: true } },
				cellStyle: [{ cell: 'A1', font: { bold: true, sz: 18 } }],
				sheetName: '考生安排表',
				merges: merges,
				keys: headers.map((h) => h.key),
				table: [
					Object.fromEntries(headers.map((h, i) => [h.key, i < 3 ? h.label : h.date || ''])),
					Object.fromEntries(headers.map((h, i) => [h.key, i > 2 && h.key.includes('room') ? h.label : ''])),
					Object.fromEntries(headers.map((h, i) => [h.key, i < 3 ? h.label : ''])),
					Object.fromEntries(headers.map((h, i) => [h.key, i < 3 ? h.label : ''])),
					Object.fromEntries(headers.map((h, i) => [h.key, i < 3 ? '' : i % 2 == 0 ? '座位号' : '考场'])),
					...tableData
				]
			}
		],
		filename: query.name + '-考生安排表'
	})
}

//搜索
const studentName = ref('')
const filteredArrangeList = computed(() => {
	let list = studentName.value
		? arrangeList.value.filter((item) => item.student_name.toLowerCase().includes(studentName.value.toLowerCase()))
		: arrangeList.value
	return list
})

// 获取考场座位表
const arrangeList = ref([])
const getArrangeList = () => {
	globalPropValue.examine.studentArrangeList.get(params.value).then((res) => {
		if (res.code === 200) {
			arrangeList.value = res.data
			console.log(res.data)
		}
	})
}
// 获取时段
const dateList = ref([])
const getTime = () => {
	globalPropValue.examine.timeList.get(params.value).then((res) => {
		if (res.code === 200) {
			const groupedData = {}
			res.data.forEach((item) => {
				if (!groupedData[item.examine_date]) {
					groupedData[item.examine_date] = {
						date: item.examine_date,
						course: []
					}
				}
				groupedData[item.examine_date].course.push({
					id: item.id,
					course_name: item.course_name,
					time: item.begin_time + '-' + item.end_time
				})
			})
			dateList.value = Object.values(groupedData)
			console.log(dateList.value)
		}
	})
}

onMounted(() => {
	getArrangeList()
	getTime()
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

const paginatedSeatList = computed(() => {
	const start = (currentPage.value - 1) * pageSize.value
	const end = start + pageSize.value
	return filteredArrangeList.value.slice(start, end)
})

const handleCurrentChange = (val) => {
	currentPage.value = val
}

const handleSizeChange = (val) => {
	pageSize.value = val
	currentPage.value = 1
}
</script>

<style lang="scss" scoped>
.add-btn {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
}

.pagination {
	margin-top: 20px;
	display: flex;
	justify-content: flex-end;
}
</style>
