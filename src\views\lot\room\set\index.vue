<template>
	<el-container>
		<el-aside width="270px">
			<el-container>
				<el-header v-if="CampusManagementList.length > 1">
					<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-header>
				<el-main>
					<el-tree
						ref="group"
						class="menu"
						node-key="id"
						:data="groupsAdd"
						:highlight-current="true"
						:expand-on-click-node="false"
						:props="defaultProps"
						:default-expanded-keys="[0]"
						@node-click="groupClick"
					>
						<template #default="{ node, data }"> </template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<div class="left-panel">
					<el-input
						v-model="params.name"
						placeholder="请输入场室名称搜索"
						clearable
						style="width: 200px; margin-right: 15px"
					></el-input>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</el-header>
			<el-main>
				<scTable
					ref="table"
					default-expand-all
					row-key="id"
					stripe
					:apiObj="list.apiObj"
					:params="params"
					@dataChange="dataChange"
				>
					<el-table-column label="建筑" prop="building_name" width="200"></el-table-column>
					<el-table-column label="楼层" prop="floor_name" width="100"></el-table-column>

					<el-table-column label="房间名称" prop="room_name"></el-table-column>
					<el-table-column label="房间属性" prop="room_type" width="200">
						<template #default="scope">
							{{ formData(roomTypeMap, scope.row.room_type) }}
						</template>
					</el-table-column>
					<el-table-column label="操作" fixed="right" align="center" width="200">
						<template #default="scope">
							<el-button-group>
								<el-button type="primary" size="small" @click="handleShowSet(scope.row)">物联关联设置</el-button>
							</el-button-group>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-container>
	<save-dialog v-if="dialog.save" ref="saveDialog" @closed="dialog.save = false"></save-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import saveDialog from './save.vue'
const defaultProps = {
	children: 'floor',
	label: 'name'
}
const { campusId, tenantId, campusInfo, roomTypeMap, roomCapacityMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		building_id: null,
		floor_id: null
	}
}

export default {
	name: 'fieldRoom',
	data() {
		return {
			currentId: -1,
			groupFilterText: '',
			groupData: [],
			defaultProps,
			list: {
				apiObj: this.$API.fieldRoom.rooms.list
			},
			params: defaultParams(),
			CampusManagementList: campusInfo,
			JobData: [],
			dialog: {
				save: false
			},
			showTools: false,
			roomNameFilters: [],
			roomTypeMap,
			roomCapacityMap
		}
	},
	components: { saveDialog },
	watch: {
		'params.campus_id': {
			handler(val) {
				this.building_id = null
				this.floor_id = null
				this.getLou()
				this.getRoom()
			}
		}
	},
	computed: {
		floorFilters() {
			let res = cusTom
				.treeToArray(this.groupData)
				.filter((v) => {
					return v.parent_id > 0
				})
				.map((v) => {
					return {
						text: v.building_name,
						value: v.building_name
					}
				})

			return cusTom.uniqueByValue(res, 'text')
		},
		groupsAdd() {
			let arr = [
				{
					id: 0,
					name: '全部'
				},
				...this.groupData
			]
			return arr
		}
	},
	async created() {
		this.getLou()
	},
	methods: {
		handleShowSet(item) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open(item)
			})
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		handleShowTools(item) {
			this.currentId = item.id
		},
		dataChange(val) {
			if (!val.data.rows) return
			let arr = val.data.rows.map((v) => {
				return {
					text: v.room_name,
					value: v.room_name
				}
			})
			this.roomNameFilters = cusTom.uniqueByValue(arr, 'text')
		},
		filterHandler(value, row, column) {
			const property = column['property']
			return row[property] === value
		},
		//获取宿舍楼
		async getLou() {
			const res = await this.$API.fieldRoom.all.get({ ...this.params, building_type: null })
			if (!res.data) res.data = []
			let data1 = res.data.map((v) => {
				return {
					...v,
					showTools: false
				}
			})
			this.groupData = cusTom.arrayToTree(data1)
		},
		//获取房间列表
		async getRoom() {
			this.$refs.table.upData(this.params1)
		},

		//树点击事件
		groupClick(data) {
			if (data.parent_id <= 0 && data.id != 0) {
				this.params.building_id = data.id
				this.params.floor_id = null
			} else if (data.parent_id > 0 && data.id != 0) {
				this.params.building_id = data.parent_id
				this.params.floor_id = data.id
			} else {
				this.params.building_id = null
				this.params.floor_id = null
			}

			this.getRoom()
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		}
	}
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;
	.el-button {
		width: 80%;
		margin-bottom: 10px;
	}
}
.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;
	box-sizing: border-box;
	a {
		font-size: 18px;
	}
}
</style>
