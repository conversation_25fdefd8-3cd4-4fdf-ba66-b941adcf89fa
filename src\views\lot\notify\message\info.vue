<template>
	<el-main style="padding: 0 20px">
		<el-descriptions :column="1" border size="small">
			<el-descriptions-item label="请求ID">{{ data.trace_id }}</el-descriptions-item>
			<el-descriptions-item label="用户">{{ data.name }}</el-descriptions-item>
			<el-descriptions-item label="请求IP">{{ data.ip }}</el-descriptions-item>
			<el-descriptions-item label="请求时间">{{ data.request_time }}</el-descriptions-item>
			<el-descriptions-item label="请求接口">{{ data.uri }}</el-descriptions-item>
			<el-descriptions-item label="请求方法">{{ data.method }}</el-descriptions-item>
			<el-descriptions-item label="状态代码">{{ data.code }}</el-descriptions-item>
			<el-descriptions-item label="状态">
				<el-tag :type="['success', 'danger'][data.status - 1]">
					{{ $formatDictionary(statusMap, data.status) }}
				</el-tag></el-descriptions-item
			>
		</el-descriptions>
		<el-collapse v-model="activeNames" accordion style="margin-top: 20px">
			<el-collapse-item title="请求参数" name="1">
				<div class="code">
					{{ data.request }}
				</div>
			</el-collapse-item>
			<el-collapse-item title="响应内容" name="2">
				<div class="code">
					{{ data.response }}
				</div>
			</el-collapse-item>
			<el-collapse-item title="user_agent" name="3">
				<div class="code" v-html="data.user_agent">
				</div>
			</el-collapse-item>
		</el-collapse>
	</el-main>
</template>

<script>
export default {
	data() {
		return {
			data: {},
			activeNames: ['1'],
			statusMap: [
				{ name: '成功', value: 1 },
				{ name: '失败', value: 2 }
			]
		}
	},
	methods: {
		setData(data) {
			this.data = data
		}
	}
}
</script>

<style scoped>
.code {
	background: #848484;
	padding: 15px;
	color: #fff;
	font-size: 12px;
	border-radius: 4px;
	white-space: word-break;
	word-break: break-all;
}
</style>
