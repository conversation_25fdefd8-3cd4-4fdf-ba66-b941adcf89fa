<template>
	<el-dialog
		v-model="visible"
		title="证书预览"
		:width="mode === 1 ? '70%' : '50%'"
		:align-center="true"
		class="preview"
		:close-on-click-modal="true"
	>
		<el-container style="height: 80vh">
			<el-main class="box">
				<section class="content">
					<center-component
						v-if="previewStatus"
						:list="certTemplateConfig.list"
						:canvasConfig="certTemplateConfig.canvasConfig"
						:draggable="false"
						:resizable="false"
						:preventDeactivation="false"
					></center-component>
				</section>
				<section v-if="mode === 1" class="right">
					<el-form v-model="form" label-width="80px">
						<el-form-item label="学年">
							<el-input v-model="form.academic_name" placeholder="请输入学年"></el-input>
						</el-form-item>
						<el-form-item label="学期">
							<el-input v-model="form.semester_name" placeholder="请输入学期"></el-input>
						</el-form-item>
						<el-form-item v-if="certTemplate.object === 2" label="教师姓名">
							<el-input v-model="form.teacher_name" placeholder="请输入教师姓名"></el-input>
						</el-form-item>
						<el-form-item
							v-if="certTemplate.object === 1 || certTemplate.object === 4 || certTemplate.object === 5"
							label="年级"
						>
							<el-input v-model="form.grade_name" placeholder="请输入年级名称"></el-input>
						</el-form-item>
						<el-form-item v-if="certTemplate.object === 1 || certTemplate.object === 5" label="班级">
							<el-input v-model="form.class_name" placeholder="请输入班级名称"></el-input>
						</el-form-item>
						<el-form-item v-if="certTemplate.object === 1" label="学生姓名">
							<el-input v-model="form.student_name" placeholder="请输入学生姓名"></el-input>
						</el-form-item>

						<el-form-item v-if="certTemplate.object === 3" label="部门名称">
							<el-input v-model="form.department_name" placeholder="请输入部门名称"></el-input>
						</el-form-item>
						<el-form-item v-if="certTemplate.object === 6" label="学科名称">
							<el-input v-model="form.course_name" placeholder="请输入学科名称"></el-input>
						</el-form-item>
						<el-form-item label="获奖名称">
							<el-input v-model="form.award_name" placeholder="请输入获奖名称"></el-input>
						</el-form-item>
						<el-form-item label="获奖名次">
							<el-input v-model="form.award_ranking" placeholder="请输入获奖名次"></el-input>
						</el-form-item>
						<el-form-item label="获奖信息">
							<el-input v-model="form.award_desc" placeholder="请输入获奖信息"></el-input>
						</el-form-item>
						<el-form-item label="获奖日期">
							<el-date-picker
								v-model="form.award_date"
								type="date"
								placeholder="请选择获奖日期"
								format="YYYY年MM月DD日"
								value-format="YYYY年MM月DD日"
							/>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="generate">生成预览</el-button>
						</el-form-item>
					</el-form>
				</section>
			</el-main>
		</el-container>
	</el-dialog>
</template>
<script>
import centerComponent from './components/center.vue'
import cusTom from '@/utils/cusTom'
import { cloneDeep } from 'lodash'
import tool from '@/utils/tool'

export default {
	components: {
		centerComponent
	},
	data() {
		return {
			visible: false,
			campusId: '',
			previewStatus: true,
			tenantId: '',
			form: {
				academic_name: '',
				semester_name: '',
				teacher_name: '',
				grade_name: '',
				class_name: '',
				student_name: '',
				department_name: '',
				course_name: '',
				award_name: '',
				award_ranking: '',
				award_desc: '',
				award_date: '',
				cert_number: '',
				signed: ''
			},
			certTemplate: {},
			certTemplateConfig: {
				canvasConfig: {
					width: 700,
					height: 500,
					sizeType: 3, //1 a5 2 a4 3 自定义
					directionType: 1, //1 纵向 2 横向
					show: true,
					bgImg: ''
				},
				list: []
			},
			originalList: [],
			currentNode: {},
			mode: 1,
			saveLoading: false,
			showBgDialog: false
		}
	},
	created() {
		const { tenantId } = cusTom.getBaseQuery()
		this.campusId = this.$route.query.campusId
		this.tenantId = tenantId
	},
	watch: {},
	methods: {
		open(data, certTemplate, mode = 1, previewData = null) {
			this.mode = mode
			this.certTemplateConfig = cloneDeep(data)
			this.certTemplate = cloneDeep(certTemplate)
			this.form.signed = this.certTemplate.seal?.signed
			this.certTemplateConfig.list.forEach((item) => {
				item.active = false
				return item
			})
			this.originalList = cloneDeep(this.certTemplateConfig.list)
			this.$nextTick(() => {
				this.visible = true
			})
			if (previewData !== null && mode === 2) {
				this.form = previewData
				this.generate()
			}
		},
		generate() {
			this.previewStatus = false
			if (this.mode === 1) {
				this.form.cert_number = tool.getRandomString(18, 'number')
			}
			let list = cloneDeep(this.originalList)
			list.forEach((item) => {
				item.content = this.compiler(item.content, this.form)
			})
			this.certTemplateConfig.list = list
			console.log(this.certTemplateConfig.list)
			this.$nextTick(() => {
				this.previewStatus = true
			})
		},
		compiler(template, data) {
			let reg = /\{\{(.+?)\}\}/g
			template = template.replace(reg, function (_, g) {
				let key = g.trim()
				let value = data[key]
				return value
			})
			return template
		}
	}
}
</script>
<style lang="scss" scoped>
.box {
	display: flex;
	height: 100%;
}

.adminui-main > .el-container .el-main {
	background-color: unset;
	padding: unset;
}

.left {
	width: 300px;
	height: 100%;
	margin-right: 15px;
	background: #fff;

	border-radius: 6px;
}

.content {
	height: 100%;
	overflow: auto;
	flex: 1;
	user-select: none;
	background: #f2f3f5;
	border-radius: 6px;
	position: relative;
}

.right {
	width: 300px;
	height: 100%;
	margin-left: 15px;
	background: #fff;
	padding: 10px;
	border-radius: 6px;
}
</style>
