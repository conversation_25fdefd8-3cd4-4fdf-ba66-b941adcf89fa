/**
 * 设置  本地缓存
 */
export function setStorage(key, obj) {
	if (typeof obj === 'string') {
		localStorage.setItem(key, obj)
	} else {
		localStorage.setItem(key, JSON.stringify(obj))
	}
}

/**
 * 获取
 */
export function getStorage(key) {
	let val = localStorage.getItem(key)
	try {
		return JSON.parse(val)
	} catch (e) {
		console.log(e)
		return val
	}
}

//非json值
export function getStorageValue(key) {
	return localStorage.getItem(key)
}

/**
 * 删除， 如果不传值，删除所有
 */
export function clearStorage(key) {
	if (key) {
		localStorage.removeItem(key)
	} else {
		localStorage.clear()
	}
}
