<template>
	<div>
		<el-card style="max-width: 600px; margin: 0 auto">
			<el-form label-position="top" label-width="auto" :model="evaluation_info">
				<el-form-item label="评教名称">
					<el-input v-model="info.title" readonly></el-input>
				</el-form-item>
				<el-form-item label="关联年级">
					<el-input v-model="info.grade_info" readonly></el-input>
				</el-form-item>
				<el-form-item label="评教学科">
					<el-input v-model="info.course_info" readonly></el-input>
				</el-form-item>
				<el-form-item label="班主任">
					<el-input v-model="info.class_leader_num" readonly>
						<template #append
							><el-button type="success" style="color: #165DFF" @click="showUserInfo(1)">详情</el-button></template
						>
					</el-input>
				</el-form-item>
				<el-form-item label="科任教师">
					<el-input v-model="info.teacher_num" readonly>
						<template #append><el-button style="color: #165DFF" @click="showUserInfo(2)">详情</el-button></template>
					</el-input>
				</el-form-item>
				<el-form-item label="学生">
					<el-input v-model="info.student_num" readonly>
						<template #append><el-button style="color: #165DFF" @click="showUserInfo(3)">详情</el-button></template>
					</el-input>
				</el-form-item>
			</el-form>
		</el-card>
		<el-drawer v-model="showUser" :title="title" direction="rtl" size="50%" :destroy-on-close="true">
			<scTable ref="userTable" row-key="id" :apiObj="apiObj" :params="params">
				<template v-if="params.type !== 3">
					<el-table-column label="教师姓名" prop="teacher_name"></el-table-column>
					<el-table-column label="教师编号" prop="serial_number"></el-table-column>
					<el-table-column label="手机号" prop="phone"></el-table-column>
					<el-table-column label="年级班级" prop="grade_name">
						<template #default="scope"> {{ scope.row.grade_name }}{{ scope.row.class_name }} </template>
					</el-table-column>
					<el-table-column label="任教学科" prop="course_name"></el-table-column>
				</template>
				<template v-else>
					<el-table-column label="学生姓名" prop="student_name"></el-table-column>
					<el-table-column label="学号" prop="serial_number"></el-table-column>
					<el-table-column label="年级班级" prop="grade_name">
						<template #default="scope"> {{ scope.row.grade_name }}{{ scope.row.class_name }} </template>
					</el-table-column>
					<el-table-column label="评教状态" prop="status">
						<template #default="scope">
							<el-tag v-if="scope.row.status === 0" type="info">未评教</el-tag>
							<el-tag v-if="scope.row.status === 1">评教中</el-tag>
							<el-tag v-if="scope.row.status === 2" type="success">已完成</el-tag>
						</template>
					</el-table-column>
				</template>
			</scTable>
		</el-drawer>
	</div>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
	data() {
		return {
			params: {
				tenant_id: 0,
				campus_id: 0,
				type: 0,
				evaluation_id: 0,
				pageSize: 20,
				page: 1
			},
			showUser: false,
			title: '',
			info: {},
			titleMap: {
				1: '班主任',
				2: '科任教师',
				3: '学生'
			},
			apiObj: null
		}
	},
	inject: ['evaluation_info'],
	created() {
	},
	mounted() {
		this.getEvaluationInfo()
		this.params.tenant_id = this.evaluation_info.tenant_id
		this.params.evaluation_id = this.evaluation_info.id
		this.params.campus_id = this.evaluation_info.campus_id
	},
	methods: {
		table_info(val, index) {
			console.log(val, index, 'table_info')
		},
		async getEvaluationInfo() {
			const res = await this.$API.eduEvaluation.detail.get({
				id: this.evaluation_info.id,
				tenant_id: this.evaluation_info.tenant_id,
				campus_id: this.evaluation_info.campus_id
			})
			if (res.code === 200) {
				this.info = res.data
			} else {
				this.showMessage(res.code, res.message)
			}
		},
		showUserInfo(type) {
			this.apiObj = this.$API.eduEvaluation.user
			this.title = this.titleMap[type]
			this.showUser = true
			this.params.type = type
		},
		showMessage(code, message) {
			if (code === 200) {
				ElMessage({ type: 'success', message: message })
			} else {
				ElMessage({ type: 'error', message: message })
			}
		}
	}
}
</script>

<style lang="scss" scoped></style>
