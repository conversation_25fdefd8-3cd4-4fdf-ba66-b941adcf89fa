<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item label="">
						<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<cusSelectSemester
							v-model="params.semester_id"
							:params="params"
							:show-default-value="true"
							:width="'214px'"
							clearable
							style="margin-right: 15px"
						/>
					</el-form-item>
					<!--					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>-->
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel"></div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<!-- <el-table-column type="selection" width="50"></el-table-column> -->
				<el-table-column label="宿舍" prop="room_name" width="300" fixed="left">
					<template #default="scope"> {{ scope.row.room.name }} </template>
				</el-table-column>
				<el-table-column label="关联人员" prop="dormitory_user_names" width="200"></el-table-column>
				<el-table-column label="评价名称" prop="evaluate_name"></el-table-column>
				<!--
				<el-table-column label="类型" prop="type_name" width="100"></el-table-column>
-->
				<el-table-column label="性质" prop="nature" width="150">
					<template #default="scope">
						<el-tag v-if="scope.row.nature === 1" type="success">正面</el-tag>
						<el-tag v-if="scope.row.nature === -1" type="danger">负面</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作分数" prop="score" width="100"></el-table-column>
				<el-table-column label="操作人" prop="created_user_name" width="180"></el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="180"></el-table-column>
			</scTable>
		</el-main>
	</el-container>

	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import saveDialog from './save'

import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, tenantInfo, semesterInfo } = cusTom.getBaseQuery()
import { ElMessage } from 'element-plus'
import CusSelectSemester from '@/components/custom/cusSelectSemester.vue'
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		name: null,
		academic_id: null,
		semester_id: null
	}
}
export default {
	name: 'dept',
	components: {
		CusSelectSemester,
		saveDialog
	},
	data() {
		return {
			dialog: {
				save: false,
				inherit: false,
				saveOne: false
			},
			apiObj: this.$API.buildingRooms.evaluate.record,
			selection: [],

			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			semesterInfo,
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},

					{
						label: null,
						name: 'academic_id',
						value: null,
						component: 'select',

						options: {
							noClearable: true,
							placeholder: '请选择学年',
							items: []
						}
					},
					{
						label: null,
						name: 'semester_id',
						value: null,
						component: 'select',
						options: {
							noClearable: true,
							placeholder: '请选择学期',
							items: []
						}
					}
				]
			}
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.academic_id = null
				this.searchConfig.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
					.filter((v) => v.parent_id == 0 && v.campus_id == val)
					.map((v) => {
						return {
							label: v.name,
							value: v.value
						}
					})

				this.params.academic_id = this.searchConfig.formItems.find(
					(v) => v.name === 'academic_id'
				).options.items[0]?.value
			},
			immediate: true
		},
		'params.academic_id': {
			handler(val) {
				this.searchConfig.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
					.filter(
						(v) => v.parent_id != 0 && v.parent_id == this.params.academic_id && v.campus_id == this.params.campus_id
					)
					.map((v) => {
						return {
							label: v.name,
							value: v.value
						}
					})
				this.params.semester_id = this.searchConfig.formItems.find(
					(v) => v.name === 'semester_id'
				).options.items[0]?.value
			},
			immediate: true
		}
	},
	computed: {},
	async created() {},
	methods: {
		inherit() {
			this.dialog.inherit = true
			this.$nextTick(() => {
				this.$refs.inheritDialog.open('add')
			})
		},

		//添加
		add(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', row)
			})
		},
		addOne(row) {
			this.dialog.saveOne = true
			this.$nextTick(() => {
				this.$refs.saveOneDialog.open('edit').setData(row)
			})
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.buildingRooms.AccommodationArrangements.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},

		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped>
.roomItem {
	& + & {
		margin-top: 5px;
	}
}
</style>
