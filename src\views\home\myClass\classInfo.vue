<template>
	<el-container>
		<el-container>
			<el-header>
				<el-tabs v-model="activeName" @tab-change="handleClick">
					<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
				</el-tabs>
			</el-header>
			<el-main>
				<component :is="currComponent.component" :data="activeName == 'teachers' ? classTeacherList : ''"> </component>
			</el-main>
		</el-container>
		<el-aside width="250px" style="margin-left: 10px; border: none">
			<el-main style="padding: 0; margin-bottom: 10px">
				<el-card style="max-width: 250px; border: none" class="card" shadow="never">
					<template #header>
						<div class="card-header">
							<span>班级信息</span>
						</div>
					</template>
					<div class="card-body">
						<el-row v-for="item in infoList" class="class-row row">
							<el-col :span="8">
								<div class="title">{{ item.label }}</div>
							</el-col>
							<el-col :span="16">
								<div class="content">{{ item.value }}</div>
							</el-col>
						</el-row>
					</div>
				</el-card>
			</el-main>
			<el-main style="padding: 0">
				<el-card style="max-width: 250px; border: none" shadow="never">
					<template #header>
						<div class="card-header">
							<span>班主任信息</span>
						</div>
					</template>
					<div class="card-body">
						<el-row v-for="item in classTeacher" class="row">
							<el-col :span="8">
								<div class="content">
									<cusHead
										loading="lazy"
										:lazy="true"
										fit="contain"
										style="width: 40px; height: 40px"
										:src="item.teacher_head"
										:preview-src-list="[item.teacher_head]"
										preview-teleported
									>
									</cusHead>
								</div>
							</el-col>
							<el-col :span="16">
								<div class="title">{{ item.teacher_name }}</div>
							</el-col>
						</el-row>
					</div>
				</el-card>
			</el-main>
		</el-aside>
	</el-container>
</template>

<script>
import students from './students'
import teachers from './teachers'
import schedule from './schedule.vue'
import leaveRecord from './leaveRecord'
import classMien from './classMien'
import cusHead from '@/components/custom/cusStaffHead.vue'
import { UserFilled } from '@element-plus/icons-vue'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, studentVacateTypeMap } = cusTom.getBaseQuery()
export default {
	components: {
		students,
		teachers,
		schedule,
		leaveRecord,
		cusHead,
		classMien
	},
	data() {
		return {
			UserFilled: UserFilled,
			campusId,
			tenantId,
			classInfo: {},
			infoList: [],
			classTeacher: [],
			teacherInfoList: [],
			classTeacherList: [],
			activeName: 'schedule',
			currComponent: {
				name: 'schedule',
				component: schedule
			},
			tabs: [
				{
					name: 'schedule',
					label: '班级课表',
					component: schedule
				},
				{
					name: 'teachers',
					label: '老师列表',
					component: teachers
				},
				{
					name: 'students',
					label: '学生列表',
					component: students
				},
				{
					name: 'leaveRecord',
					label: '学生请假记录',
					component: leaveRecord
				},
				{
					name: 'classMien',
					label: '班级风采',
					component: classMien
				}
			]
		}
	},
	created() {
		this.getClassInfo()
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
		},
		getClassInfo() {
			this.$API.class.info
				.get({
					class_id: this.$route.query.id,
					tenant_id: tenantId,
					campus_id: campusId
				})
				.then((res) => {
					this.classInfo = res.data.class_info
					this.infoList = [
						{
							label: '学年',
							value: this.classInfo?.academic_name
						},
						{
							label: '学期',
							value: this.classInfo?.semester_name
						},
						{
							label: '年级',
							value: this.classInfo?.grade_name
						},
						{
							label: '班级',
							value: this.classInfo?.class_name
						},
						{
							label: '教室',
							value: this.classInfo?.room.name
						},
						{
							label: '学生',
							value: this.classInfo?.student_num + ' 人'
						},
						{
							label: '备注',
							value: this.classInfo?.remark
						}
					]
					this.classTeacher = res.data.class_teacher_list.filter((item) => item.is_head == 1)
					this.classTeacherList = res.data.class_teacher_list
					console.log(this.classTeacher, 'res')
				})
		}
	}
}
</script>

<style lang="scss" scoped>
:deep(.el-card__header) {
	padding: 10px 20px;
	border-bottom: 1px solid var(--el-border-color-light);
}

.card-header {
	font-size: 16px;
	font-weight: 600;
}

.card-body {
	.row {
		margin-bottom: 10px;
		line-height: 2.5;

		.title {
			font-size: 14px;
			font-weight: bold;
		}

		.content {
			font-size: 14px;
		}
	}

	.class-row {
		line-height: 2;
	}
}
</style>
