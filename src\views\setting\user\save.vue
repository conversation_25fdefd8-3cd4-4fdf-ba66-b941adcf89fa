<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<el-form
			ref="dialogForm"
			:model="form"
			:rules="rules"
			:disabled="mode === 'show'"
			label-width="100px"
			label-position="left"
		>
			<!--			<el-form-item label="头像" prop="avatar">
							<sc-upload v-model="form.avatar" :height="100" :width="100" title="上传头像"></sc-upload>
						</el-form-item>-->
			<el-form-item label="登录账号" prop="user_name">
				<el-input v-model="form.user_name" placeholder="用于登录系统" readonly disabled clearable></el-input>
			</el-form-item>
			<el-form-item label="昵称" prop="nickname">
				<el-input v-model="form.nickname" placeholder="请输入昵称" clearable></el-input>
			</el-form-item>
			<!--			<el-form-item label="邮箱" prop="email">-->
			<!--				<el-input v-model="form.email" placeholder="请输入邮箱地址" clearable></el-input>-->
			<!--			</el-form-item>-->
			<template v-if="mode === 'add'">
				<el-form-item label="登录密码" prop="password">
					<el-input v-model="form.password" type="password" clearable show-password></el-input>
				</el-form-item>
				<el-form-item label="确认密码" prop="password2">
					<el-input v-model="form.password2" type="password" clearable show-password></el-input>
				</el-form-item>
			</template>
			<!--<el-form-item label="所属部门" prop="dept">
        <el-cascader
          v-model="form.dept"
          :options="depts"
          :props="deptsProps"
          clearable
          style="width: 100%"
        ></el-cascader>
      </el-form-item>-->
			<el-form-item label="所属角色" prop="roles">
				<el-select
					v-model="form.roles"
					multiple
					filterable
					:max-collapse-tags="2"
					collapse-tags
					collapse-tags-tooltip
					style="width: 100%"
				>
					<el-option v-for="item in allRoles" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="校区权限" prop="campusIds">
				<el-select
					v-model="form.campusIds"
					multiple
					filterable
					:max-collapse-tags="2"
					collapse-tags
					collapse-tags-tooltip
					style="width: 100%"
				>
					<el-option label="全部校区" :value="0" />
					<el-option v-for="item in allCampus" :key="item.id" :label="item.campus_name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="状态" prop="status">
				<el-switch v-model="form.status" :active-value="1" :inactive-value="0"></el-switch>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增用户',
				edit: '编辑用户',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: {
				mode: '',
				id: 0,
				user_name: '',
				avatar: '',
				email: '',
				password: '',
				password2: '',
				nickname: '',
				status: 1,
				roles: [],
				campusIds: []
			},
			//验证规则
			rules: {
				avatar: [{ required: true, message: '请上传头像' }],
				user_name: [{ required: true, message: '请输入登录账号' }],
				nickname: [{ required: true, message: '请输入昵称' }],
				password: [
					{ required: true, message: '请输入登录密码' },
					{
						validator: (rule, value, callback) => {
							if (this.form.password2 !== '') {
								this.$refs.dialogForm.validateField('password2')
							}
							callback()
						}
					}
				],
				password2: [
					{ required: true, message: '请再次输入密码' },
					{
						validator: (rule, value, callback) => {
							if (value !== this.form.password) {
								callback(new Error('两次输入密码不一致!'))
							} else {
								callback()
							}
						}
					}
				],
				roles: [{ required: true, message: '请选择所属角色', trigger: 'change' }]
			},
			//所需数据选项
			allRoles: [],
			allCampus: [],
			depts: [],
			params: {
				tenant_id: null
			},
			deptsProps: {
				value: 'id',
				checkStrictly: true
			}
		}
	},
	mounted() {
		this.params.tenant_id = this.$TOOL.data.get('USER_INFO').tenant_id
		this.getAllRoles()
		this.getAllCampus()
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			return this
		},
		//加载树数据
		async getAllRoles() {
			var res = await this.$API.role.all.get()
			console.log(res)
			this.allRoles = res.data
		},
		async getAllCampus() {
			var res = await this.$API.CampusManagement.all.get(this.params)
			this.allCampus = res.data
		},
		async getDept() {
			var res = await this.$API.system.dept.list.get()
			this.depts = res.data
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					this.form.mode = this.mode
					var res = await this.$API.user.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				} else {
					return false
				}
			})
		},
		//表单注入数据
		setData(data) {
			var roles = []
			var campusIds = []
			if (data.roleList !== null) {
				data.roleList.map((item) => {
					roles.push(item.id)
				})
			}
			if (data.campusList !== null) {
				data.campusList.map((item) => {
					campusIds.push(item.id)
				})
			}
			if (data.campus_id === '0') {
				campusIds.push(0)
			}
			console.log(data, campusIds)
			this.form.id = data.id
			this.form.user_name = data.user_name
			this.form.avatar = data.avatar
			this.form.nickname = data.nickname
			this.form.email = data.email
			this.form.roles = roles
			this.form.status = data.status
			this.form.campusIds = campusIds
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			//Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
