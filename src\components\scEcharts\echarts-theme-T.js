const T = {
	color: [
		'#5B8FF9',
		'#5AD8A6',
		'#5D7092',
		'#F6BD16',
		'#6F5EF9',
		'#6DC8EC',
		'#945FB9',
		'#FF9845',
		'#1E9493',
		'#FF99C3',
		'#AABA01',
		'#BC7CFC',
		'#237CBC',
		'#2DE379',
		'#CE8032',
		'#FF7AF4',
		'#545FD3',
		'#AFE410',
		'#D8C608',
		'#FFA1E0'
	],
	grid: {
		left: '3%',
		right: '3%',
		bottom: '10',
		top: '40',
		containLabel: true
	},
	legend: {
		textStyle: {
			color: '#999'
		},
		inactiveColor: 'rgba(128,128,128,0.4)'
	},
	categoryAxis: {
		axisLine: {
			show: true,
			lineStyle: {
				color: 'rgba(128,128,128,0.2)',
				width: 1
			}
		},
		axisTick: {
			show: false,
			lineStyle: {
				color: '#333'
			}
		},
		axisLabel: {
			color: '#999'
		},
		splitLine: {
			show: false,
			lineStyle: {
				color: ['#eee']
			}
		},
		splitArea: {
			show: false,
			areaStyle: {
				color: ['rgba(255,255,255,0.01)', 'rgba(0,0,0,0.01)']
			}
		}
	},
	valueAxis: {
		axisLine: {
			show: false,
			lineStyle: {
				color: '#999'
			}
		},
		splitLine: {
			show: true,
			lineStyle: {
				color: 'rgba(128,128,128,0.2)'
			}
		}
	}
}

export default T
