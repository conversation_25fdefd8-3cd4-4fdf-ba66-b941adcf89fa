{"globals": {"CodeType": true, "Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "DirectiveBinding": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "InjectionKey": true, "MaybeRef": true, "MaybeRefOrGetter": true, "PropType": true, "Ref": true, "Slot": true, "Slots": true, "VNode": true, "WritableComputedRef": true, "acceptHMRUpdate": true, "advanceComponents": true, "audioSuffix": true, "basicComponents": true, "checkAmr": true, "checkExcel": true, "checkFileIsAllFile": true, "checkFileIsCom": true, "checkFileIsImage": true, "checkFileIsOffice": true, "checkFileIsRadio": true, "checkFileIsVideo": true, "checkImage": true, "checkOffice": true, "checkPPT": true, "checkPdf": true, "checkRadio": true, "checkVideo": true, "checkWord": true, "clearOssConfig": true, "clearStorage": true, "color": true, "computed": true, "createApp": true, "createPinia": true, "cusTom": true, "customRef": true, "db": true, "defineAsyncComponent": true, "defineComponent": true, "defineStore": true, "effectScope": true, "errorHandler": true, "excelSuffix": true, "getActivePinia": true, "getCurrentInstance": true, "getCurrentScope": true, "getDateBefore": true, "getDateWeekArray": true, "getDaysLater": true, "getFileLimitMB": true, "getMediaLimitInfo": true, "getMonthRange": true, "getNDaysAfterRange": true, "getNDaysRange": true, "getNewFileKey": true, "getOssConfig": true, "getOssConfigProcess": true, "getStorage": true, "getStorageValue": true, "h": true, "imageSuffix": true, "inject": true, "isBeforeToday": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "judementSameArr": true, "layoutComponents": true, "loadCSS": true, "loadImageUrl": true, "loadImageUrl2": true, "loadJS": true, "mapActions": true, "mapGetters": true, "mapState": true, "mapStores": true, "mapWritableState": true, "markRaw": true, "nextTick": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "permission": true, "permissionAll": true, "pptSuffix": true, "print": true, "provide": true, "putObject": true, "putObject2": true, "putObjectBig": true, "reactive": true, "readonly": true, "ref": true, "request": true, "resolveComponent": true, "rolePermission": true, "setActivePinia": true, "setMapStoreSuffix": true, "setOssConfig": true, "setStorage": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "storeToRefs": true, "systemComponents": true, "template": true, "toRaw": true, "toRef": true, "toRefs": true, "toValue": true, "tool": true, "triggerRef": true, "unref": true, "useAttrs": true, "useCssModule": true, "useCssVars": true, "useI18n": true, "useId": true, "useLink": true, "useModel": true, "useRoute": true, "useRouter": true, "useSlots": true, "useTabs": true, "useTemplateRef": true, "verifyCars": true, "verifyPhone": true, "videoSuffix": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true, "webPreview": true, "widgetForm": true, "wordSuffix": true, "zipSuffix": true}}