<template>
	<el-dialog
		v-model="visible"
		:title="titleMap[mode][form.building_type - 1]"
		:width="500"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<el-form ref="dialogForm" :model="form" :disabled="mode == 'show'">
			<el-form-item v-if="mode == 'add'" label="新增">
				<el-button type="primary" :disabled="form.building_name.length >= 9" @click="addFloor">新增</el-button>
			</el-form-item>
			<el-form-item
				v-for="(item, index) in form.building_name"
				:key="item.key"
				label="名称"
				:rules="rules"
				:prop="`building_name.${index}.value`"
			>
				<div class="flex_bc">
					<el-input v-model="item.value" placeholder="请输入名称" style="width: 240px" clearable> </el-input
					><el-icon v-if="mode == 'add'" style="font-size: 24px; margin-left: 10px" @click="removeFloor(index)"
						><el-icon-remove
					/></el-icon>
				</div>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
const defaultData = () => {
	return {
		tenant_id: '',
		campus_id: '',
		building_type: '',
		parent_id: '',
		building_name: [{ value: '', key: Date.now() }]
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',

			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			titleMap: {
				add: ['新增建筑', '新增建筑楼层'],
				edit: ['编辑建筑', '编辑建筑楼层']
			},
			//验证规则
			rules: {
				required: true,
				message: '请输入名称',
				trigger: 'blur'
			}
			//所需数据选项
		}
	},
	mounted() {},
	methods: {
		//添加楼层
		addFloor() {
			this.form.building_name.push({ value: '', key: Date.now() })
		},
		//删除楼层
		removeFloor(index) {
			this.form.building_name.splice(this.form.building_name.indexOf(index), 1)
		},
		//显示
		open(mode = 'add', type = 1, parent_id) {
			this.mode = mode
			this.visible = true
			this.form.campus_id = this.params.campus_id
			this.form.tenant_id = this.params.tenant_id
			this.form.building_type = type
			this.form.parent_id = parent_id

			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					if (this.mode == 'add') {
						this.form.building_name = this.form.building_name.map((v) => v.value)

						var res = await this.$API.fieldRoom.create.post(this.form)
						this.isSaveing = false
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode)
							this.visible = false
							this.$message.success('操作成功')
						} else {
							this.$alert(res.message, '提示', { type: 'error' })
						}
					} else {
						this.form.building_name = this.form.building_name.map((v) => v.value)[0]
						var res = await this.$API.fieldRoom.save.post(this.form)
						this.isSaveing = false
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode)
							this.visible = false
							this.$message.success('操作成功')
						} else {
							this.$alert(res.message, '提示', { type: 'error' })
						}
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			if (data.name) {
				this.form.building_name = [{ value: data.name, key: Date.now() }]
			}
		}
	}
}
</script>

<style></style>
