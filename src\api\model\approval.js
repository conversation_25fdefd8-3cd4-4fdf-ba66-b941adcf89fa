import config from '@/config'
import http from '@/utils/request'

export default {
	flow:{
		all: {
			url: `${config.API_URL}/sysapi/approval/all`,
			name: '获取审批流程',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		list: {
			url: `${config.API_URL}/sysapi/approval/all_apply`,
			name: '获取所有审批申请列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		detail: {
			url: `${config.API_URL}/sysapi/approval/one`,
			name: '获取单个申请详情',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		apply: {
			url: `${config.API_URL}/sysapi/approval/apply`,
			name: '发起审批申请',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		myApply:{
			url: `${config.API_URL}/sysapi/approval/my`,
			name: '获取我的审批申请',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		cancel: {
			url: `${config.API_URL}/sysapi/approval/cancel`,
			name: '取消审批申请',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		myApproval: {
			url: `${config.API_URL}/sysapi/approval/mine`,
			name: '获取我的审批',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		copyMyApproval: {
			url: `${config.API_URL}/sysapi/approval/send_me`,
			name: '获取抄送我的审批',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		action:{
			url: `${config.API_URL}/sysapi/approval/action`,
			name: '审批操作',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		}
	},
	deploy:{
		list: {
			url: `${config.API_URL}/sysapi/approval_conf/list`,
			name: '获取流程列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		detail: {
			url: `${config.API_URL}/sysapi/approval_conf/one`,
			name: '获取流程信息',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		set:{
			url: `${config.API_URL}/sysapi/approval_conf/set`,
			name: '流程设置',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		save:{
			url: `${config.API_URL}/sysapi/approval_conf/save`,
			name: '保存/修改流程',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		delete:{
			url: `${config.API_URL}/sysapi/approval_conf/del`,
			name: '删除流程',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		status:{
			url: `${config.API_URL}/sysapi/approval_conf/status`,
			name: '启用/禁用流程',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		}
	}
	
}
