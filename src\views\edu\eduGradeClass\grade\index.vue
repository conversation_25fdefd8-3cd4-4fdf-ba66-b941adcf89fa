<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList.length > 1" label="">
						<el-select v-model="params.campus_id" placeholder="校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="" prop="semester_id">
						<cusSelectSemester
							v-model="params.semester_id"
							:params="params"
							:show-default-value="true"
							:width="'214px'"
							clearable
							style="margin-right: 15px"
							@semesterChange="semesterChange"
						/>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="年级名称" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-plus" @click="add">新增年级</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
				<!-- <el-table-column label="校区" prop="grade_name" width="150"></el-table-column> -->
				<el-table-column label="名称" prop="grade_name" width="260"></el-table-column>
				<!-- <el-table-column label="学段" prop="listorder" width="150"></el-table-column> -->
				<!-- <el-table-column label="班级数量" prop="listorder" width="150"></el-table-column>
				<el-table-column label="年级长" prop="listorder" width="150"></el-table-column>
				<el-table-column label="下级行政" prop="listorder" width="150"></el-table-column>
				<el-table-column label="学科信息" prop="listorder" width="150"></el-table-column> -->
				<el-table-column label="年级长" prop="leader_info" show-overflow-tooltip>
					<template #default="{ row }">
						<span v-for="(item, index) in row.leader_info" :key="item.id">{{
							item.name + (index < row.leader_info.length - 1 ? ' | ' : '')
						}}</span>
					</template>
				</el-table-column>
				<el-table-column label="年级课程" prop="course_info" show-overflow-tooltip>
					<template #default="{ row }">
						<span v-for="(item, index) in row.course_info" :key="item.id">{{
							item.name + (index < row.course_info.length - 1 ? ' | ' : '')
						}}</span>
					</template>
				</el-table-column>
				<el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="170">
					<template #default="scope">
						<el-button-group>
							<!--
							<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>
-->
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>

	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import saveDialog from './save'
import cusTom from '@/utils/cusTom'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'
const { campusId, tenantId, campusInfo, tenantInfo, semesterInfo, academicYearInfo } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		name: null,
		semester_id: null
	}
}

export default {
	name: '',
	components: {
		cusSelectSemester,
		saveDialog
	},
	data() {
		return {
			dialog: {
				save: false
			},
			apiObj: this.$API.eduGradeClass.grade.list,
			selection: [],
			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			semesterArr: [],
			//所需数据选项
			groupsProps: {
				value: 'id',
				label: 'semester_name',
				emitPath: false
			},
			semesterInfo,
			academicYearInfo
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.semester_id = null
			}
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	async created() {},
	methods: {
		//添加
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
			// this.getDept()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.eduGradeClass.grade.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				// this.upsearch()
				this.$refs.table.refresh()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			this.$API.eduGradeClass.grade.status.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		semesterChange(val) {
			this.params.semester_id = val
			this.$nextTick(() => {
				this.upsearch()
			})
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.$refs.table.refresh()
			}
		}
	}
}
</script>

<style lang="scss" scoped></style>
