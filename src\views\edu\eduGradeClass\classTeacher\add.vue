<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<!--		<el-form-item label="班級" label-width="88px">
			<el-cascader
				v-model="form.class_id"
				:options="classList"
				:props="{ multiple: false, label: 'name', value: 'id', children: 'class_list' }"
				clearable
			></el-cascader>
		</el-form-item>-->
		<cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode">
			<template #ahead v-if="mode !== 'edit'">
				<el-form-item label="学期" prop="semester_id" label-width="95px">
					<cusCascader v-model="form.semester_id" placeholder="请选择学期" :options="getSemester">
					</cusCascader>
				</el-form-item>
			</template>
		</cusForm>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { semesterInfo } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		id: null,
		staff_id: [],
		week_num: 4,
		is_head: -1,
		position: null,
		remark: null,
		mode: null,
		course_id: '',
		class_id: []
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => { }
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			grade_id: null,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				class_name: [{ required: true, message: '请输入名称' }],
				semester_id: [{ required: true, message: '请选择学期' }],
				grade_id: [{ required: true, message: '请选择年级' }]
			},
			//所需数据选项
			groups: [],
			groupsProps: {
				value: 'id',
				label: 'semester_name',
				emitPath: false
			},
			gradeOptions: [],
			semesterInfo,
			grade: [],
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: '班级',
						name: 'class_id',
						value: null,
						component: 'cascader',
						options: {
							prop: {
								emitPath: false,
								value: 'id',
								label: 'name',
								children: 'class_list'
							},
							all: true,
							items: []
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }],
						hideHandle: '$.mode == "edit" || $.mode == "show"'
					},

					{
						label: '教师',
						name: 'staff_id',
						value: null,
						component: 'cusSelectTeacher',
						rules: [{ required: true, message: '请输入', trigger: 'blur' }],
						hideHandle: '$.mode == "edit" || $.mode == "show"'
					},
					{
						label: '授课学科',
						name: 'course_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请先选择年级班级',
							items: []
						},
						hideHandle: '$.mode == "edit" || $.mode == "show"'
					},
					{
						label: '周课时数',
						name: 'week_num',
						value: null,
						component: 'number',
						options: {
							placeholder: '请输入',
							controlsPosition: '1'
						}
					},
					{
						label: '班主任',
						name: 'is_head',
						value: -1,
						component: 'switch',
						options: {
							activeValue: 1,
							inactiveValue: -1
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '职位',
						name: 'position',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入'
						}
					},
					{
						label: '备注',
						name: 'remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入备注'
						}
					}
				]
			}
		}
	},
	mounted() { },
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	watch: {
		/*form: {
			deep: true,
			handler(val) {
				if (val.class_id) {
					this.getCourse()
				}
			}
		}*/
		'form.semester_id': {
			deep: true,
			handler(val) {
				this.getClass(val)
			}
		},
		'form.class_id': {
			deep: true,
			handler(val) {
				if (val) {
					this.getCourse()
				}
			}
		}
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.form.mode = mode
			this.visible = true
			return this
		},
		async getClass(val) {
			const res = await this.$API.eduGradeClass.class.all.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				semester_id: val
			})
			this.formConfig.formItems[0].options.items = res.data
			// console.log(res.data.map((v) => v.grade_id))
			/*		this.formConfig.formItems[0].options.items = res.data.map((v) => {
				return {
					label: v.grade_name + '-' + v.class_name,
					value: v.id
				}
			})*/
		},
		async getCourse() {
			if (this.form.mode !== 'add') {
				return
			}
			console.log(this.form)
			if (this.form.class_id) {
				// grade_id = this.classList.find((v) => v.id === this.form.class_id).id
				console.log(this.form.class_id)
			}
			const res = await this.$API.eduCourseSet.course.class_course.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				id: this.form.class_id
			})
			console.log(res)
			this.$nextTick(() => {
				this.formConfig.formItems[2].options.items = res.data.map((v) => {
					return {
						label: v.course_name,
						value: v.course_id
					}
				})
			})
		},
		//表单提交方法
		submit() {
			this.$refs.formref.validate(async (valid) => {
				console.log(this.form)
				if (valid) {
					this.isSaveing = true
					let f
					let res
					if (this.mode === 'add') {
						//this.form.class_id = this.form.class_id[1]
						f = {
							...this.form,
							staff_id: this.form.staff_id[0].id,
							tenant_id: this.params.tenant_id,
							campus_id: this.params.campus_id
						}
						res = await this.$API.eduGradeClass.classTeacher.add.post(f)
					}
					if (this.mode === 'edit') {
						f = {
							...this.form,
							tenant_id: this.params.tenant_id,
							campus_id: this.params.campus_id
						}
						res = await this.$API.eduGradeClass.classTeacher.save.post(f)
					}
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			// this.form.id = data.id
			// this.form.label = data.label
			// this.form.status = data.status
			// this.form.sort = data.sort
			// this.form.parentId = data.parentId
			// this.form.remark = data.remark

			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
			console.log(this.form)
			/*if (data.staff_id !== null) {
				this.form.staff_id = [
					{
						id: data.staff_id,
						label: data.teacher_name
					}
				]
			}*/

			// console.log(this.form)
		}
	}
}
</script>

<style scoped>
.addteacher {
	display: grid;
	grid-template-columns: 130px 130px 1fr;
	gap: 10px;
	margin-bottom: 10px;
	max-height: 300px;
	overflow: auto;
}
</style>
