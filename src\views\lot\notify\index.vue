<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main>
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script>
import message from './message'

export default {
	name: 'layoutTCB',
	components: {
		message
	},
	data() {
		return {
			activeName: 'message',
			tabs: [
				{
					label: '通知消息',
					name: 'message',
					component: message
				}
			],
			currComponent: {}
		}
	},
	created() {
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
		}
	}
}
</script>

<style></style>
