@media screen {
	html {
		background: #f4f4f4;
		min-height: 100%;
	}
}
body {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
		'Helvetica Neue', sans-serif;
}
@media screen {
	body {
		background-color: #fff;
		box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
		box-sizing: border-box;
		margin: 1rem auto 0;
		max-width: 820px;
		min-height: calc(100vh - 1rem);
		padding: 4rem 6rem 6rem 6rem;
	}
}
table {
	border-collapse: collapse;
}
/* Apply a default padding if legacy cellpadding attribute is missing */
table:not([cellpadding]) th,
table:not([cellpadding]) td {
	padding: 0.4rem;
}
/* Set default table styles if a table has a positive border attribute
   and no inline css */
table[border]:not([border='0']):not([style*='border-width']) th,
table[border]:not([border='0']):not([style*='border-width']) td {
	border-width: 1px;
}
/* Set default table styles if a table has a positive border attribute
   and no inline css */
table[border]:not([border='0']):not([style*='border-style']) th,
table[border]:not([border='0']):not([style*='border-style']) td {
	border-style: solid;
}
/* Set default table styles if a table has a positive border attribute
   and no inline css */
table[border]:not([border='0']):not([style*='border-color']) th,
table[border]:not([border='0']):not([style*='border-color']) td {
	border-color: #ccc;
}
figure figcaption {
	color: #999;
	margin-top: 0.25rem;
	text-align: center;
}
hr {
	border-color: #ccc;
	border-style: solid;
	border-width: 1px 0 0 0;
}
.mce-content-body:not([dir='rtl']) blockquote {
	border-left: 2px solid #ccc;
	margin-left: 1.5rem;
	padding-left: 1rem;
}
.mce-content-body[dir='rtl'] blockquote {
	border-right: 2px solid #ccc;
	margin-right: 1.5rem;
	padding-right: 1rem;
}
