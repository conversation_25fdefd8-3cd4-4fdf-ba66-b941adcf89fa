<template>
	<el-drawer v-model="visible" :title="currentName" size="800" destroy-on-close @close="close">
		<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>

			<component :is="currComponent.component" :roomId="currentId"></component>
		
	</el-drawer>
</template>

<script setup>
import lot from './lot.vue'
import eduDevice from './eduDevice.vue'
import { ref } from 'vue'
const emit = defineEmits(['closed'])

const activeName = ref('eduDevice')
const currentId = ref('')
const currentName = ref('')
const visible = ref(false)
const currComponent = ref({
	name: 'eduDevice',
	component: eduDevice
})
const tabs = [
	 {
		name: 'eduDevice',
		label: '电教设备',
		component: eduDevice
	}, 
	{
		name: 'lot',
		label: '物联设备',
		component: lot
	}
]
const handleClick = (name) => {
	currComponent.value = tabs.find((item) => item.name === name)
}
const show = (name,id) => {
    currentId.value = id
    currentName.value = name
    visible.value = true
}
const close = () => {
    //clearInterval(getInfoTimer.value)
    visible.value = false
    emit('closed')
}


defineExpose({
    show,
    close
})
</script>
<style lang="scss" scoped></style>
