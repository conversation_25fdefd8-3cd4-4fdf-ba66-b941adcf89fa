import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/sysapi/ash/list`,
		name: '获取列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/sysapi/ash/save`,
		name: '创建和保存',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
	del: {
		url: `${config.API_URL}/sysapi/ash/del`,
		name: '删除',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	}
}
