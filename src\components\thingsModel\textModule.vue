<template>
	<el-form-item label="数据长度">
		<el-input
			v-model="modelValue.len"
			style="width: 100%"
			controls-position="right"
			type="number"
			:controls="false"
			placeholder="整数，单位：字符"
			clearable
		>
			<template #append>
				<span>字符</span>
			</template>
		</el-input>
	</el-form-item>
</template>
<script>
export default {
	name: 'textModule',
	props: {
		modelValue: {
			type: Object,
			default: () => {
				return {
					len: null
				}
			}
		}
	},
	data() {
		return {
			unitConfig: []
		}
	},
	emits: ['update:modelValue'], // 明确声明该组件会触发 update:modelValue 事件
	watch: {
		modelValue: {
			handler(val) {
				this.$emit('update:modelValue', val)
			},
			deep: true
		}
	},
	created() {},
	methods: {}
}
</script>

<style scoped lang="scss">
.enum_header {
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.enum_item {
	margin: 5px 0;
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.text-center {
	text-align: center;
}
</style>
