<template>
	<el-container>
		<el-header>
			<div class="left-panel" style="width: 90%">
				<div class="left-panel-search">
					<el-form-item label="">
						<el-select
							v-if="CampusManagementList.length > 1"
							v-model="params.campus_id"
							placeholder="校区"
							filterable
							style="margin-right: 15px"
						>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select
							v-model="params.product_id"
							style="width: 150px"
							placeholder="请选择产品品类"
							clearable
							filterable
						>
							<el-option
								v-for="(item, index) in productList"
								:key="index"
								:label="item.product_name"
								:value="item.id"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.status" style="width: 150px" placeholder="请选择设备状态" clearable>
							<el-option
								v-for="(item, index) in enumConfig.deviceStatusMap"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<cusSelectField v-model="params.room" :multiple="false" placeholder="请选择设备位置"></cusSelectField>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="请输入设备名称/设备ID搜索" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<div>
					<el-segmented v-model="showType" :options="showTypeOptions" style="margin: 5px 15px 5px 0">
						<template #default="scope">
							<el-tooltip class="box-item" effect="dark" :content="scope.item.tips" placement="bottom">
								<div class="items-center">
									<el-icon size="20">
										<component :is="scope.item.icon" />
									</el-icon>
								</div>
							</el-tooltip>
						</template>
					</el-segmented>
				</div>
				<el-button type="primary" icon="el-icon-CirclePlus" @click="add">添加设备</el-button>
			</div>
		</el-header>
		<el-main>
			<div ref="scTableMain" v-loading="loading" class="scTable" style="height: 100%">
				<div class="scTable-table" style="height: calc(100% - 40px)">
					<el-table v-if="showType === 'list'" ref="table" row-key="id" :data="listData" size="small" height="100%">
						<el-table-column label="设备ID" fixed="left" prop="device_key" width="180"></el-table-column>
						<el-table-column label="设备名称" prop="device_name" min-width="180"></el-table-column>
						<el-table-column label="所属产品" prop="product_info" min-width="180">
							<template #default="scope">
								{{ scope.row.product_info?.product_name }}
							</template>
						</el-table-column>
						<el-table-column label="节点类型" prop="node_type" width="100">
							<template #default="scope">
								{{ enumConfig.nodeTypeMap.find((item) => item.value === scope.row.product_info?.node_type)?.name }}
							</template>
						</el-table-column>
						<el-table-column label="设备位置" prop="room_info" min-width="200">
							<template #default="scope">
								<span v-if="scope.row.room_info">{{ scope.row.room_info.name }}</span>
								<span v-else>-</span>
							</template>
						</el-table-column>
						<el-table-column label="状态" prop="status" width="100">
							<template #default="scope">
								<sc-status-indicator :type="deviceStatusType[scope.row.status]"> </sc-status-indicator>
								{{ enumConfig.deviceStatusMap.find((item) => item.value === scope.row.status)?.name }}
							</template>
						</el-table-column>
						<el-table-column label="创建时间" prop="created_at" width="150"></el-table-column>
						<el-table-column label="最近在线时间" prop="last_online_time" width="150"></el-table-column>
						<el-table-column label="操作" fixed="right" align="left" width="200">
							<template #default="scope">
								<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)"
									>详情</el-button
								>
								<el-divider direction="vertical" />
								<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)"
									>编辑
								</el-button>
								<el-divider direction="vertical" />
								<el-button text type="danger" size="small" @click="table_del(scope.row, scope.$index)">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
					<div v-if="showType === 'card'" style="height: 100%">
						<el-scrollbar class="no-horizontal-scrollbar" style="height: 100%">
							<div v-if="listData && listData.length > 0" style="padding-right: 10px">
								<el-row :gutter="20">
									<el-col v-for="(item, index) in listData" :key="index" :xl="8" :lg="8" :md="8" :sm="12" :xs="24">
										<el-card class="card-item" shadow="hover">
											<div class="card-info">
												<div class="card-info-img">
													<div>
														<el-image
															style="width: 60px; height: 60px; border-radius: 6px"
															fit="cover"
															:src="item.device_img"
															:preview-src-list="[item.device_img]"
														>
															<template #error>
																<div class="image-slot">
																	<el-icon>
																		<sc-icon-psyResource />
																	</el-icon>
																</div>
															</template>
														</el-image>
													</div>
													<div>
														<div class="device_name" @click="table_show(item, index)">{{ item.device_name }}</div>
														<div class="device_tag">
															<el-tag size="small" :type="deviceStatusType[item.status]">
																{{ enumConfig.deviceStatusMap.find((i) => i.value === item.status)?.name }}</el-tag
															>
															<el-tag size="small" type="primary" effect="plain">
																{{
																	enumConfig.nodeTypeMap.find((i) => i.value === item.product_info?.node_type)?.name
																}}</el-tag
															>
														</div>
													</div>
												</div>
												<el-row>
													<el-col :span="12">
														<el-form-item label="设备编号：">{{ item.device_key }}</el-form-item>
													</el-col>
													<el-col :span="12">
														<el-form-item label="所属产品：">{{ item.product_info?.product_name }}</el-form-item>
													</el-col>
													<el-col :span="12">
														<el-form-item label="创建时间：">{{ item.created_at }}</el-form-item>
													</el-col>
													<el-col :span="12">
														<el-form-item label="最近在线时间：">{{ item.last_online_time }}</el-form-item>
													</el-col>
													<el-col :span="24">
														<el-form-item label="设备位置："
															><span v-if="item.room_info">{{ item.room_info.name }}</span>
															<span v-else>-</span></el-form-item
														>
													</el-col>
												</el-row>
											</div>
											<template #footer>
												<div>
													<el-button text type="primary" size="small" @click="table_show(item, index)">详情</el-button>
													<el-divider direction="vertical" />
													<el-button text type="primary" size="small" @click="table_edit(item, index)">编辑 </el-button>
													<el-divider direction="vertical" />
													<el-button text type="danger" size="small" @click="table_del(item, index)">删除</el-button>
												</div>
											</template>
										</el-card>
									</el-col>
								</el-row>
							</div>
							<el-empty v-else description="暂无设备"></el-empty>
						</el-scrollbar>
					</div>
				</div>
				<div class="scTable-page">
					<div class="pagination">
						<el-pagination
							v-if="total > 0"
							v-model:current-page="params.page"
							:page-sizes="[12, 24, 36, 72, 96]"
							:page-size="params.pageSize"
							size="small"
							background
							layout="total,sizes, prev, pager, next,jumper"
							:total="total"
							@size-change="handleSizeChange"
							@current-change="handleCurrentChange"
						/>
					</div>
				</div>
			</div>
		</el-main>
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import saveDialog from './save'
import { ElMessageBox } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()
import useTabs from '@/utils/useTabs'

const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		campus_id: campusId,
		product_id: null,
		room: null,
		room_id: null,
		page: 1,
		pageSize: 12,
		status: null
	}
}

export default {
	name: 'deviceList',
	data() {
		return {
			showType: 'card',
			showTypeOptions: [
				{
					label: '',
					value: 'card',
					icon: 'el-icon-grid',
					tips: '点击切换卡片视图'
				},
				{
					label: '',
					value: 'list',
					icon: 'el-icon-operation',
					tips: '点击切换列表视图'
				}
			],
			CampusManagementList: [],
			groupFilterText: '',
			loading: false,
			total: 0,
			enumConfig: [],
			productList: [],
			listData: [],
			deviceStatusType: {
				1: 'info',
				2: 'success',
				3: 'info',
				4: 'warning',
				5: 'danger'
			},
			params: defaultParams(),
			search: {
				name: null
			},
			dialog: {
				save: false
			}
		}
	},
	components: {
		saveDialog
	},
	watch: {
		'params.room': {
			handler(val) {
				if (val && val.length > 0) {
					this.params.room_id = val[0].id?.toString()
				} else {
					this.params.room_id = null
				}
			},
			deep: true
		}
	},
	created() {
		this.CampusManagementList = campusInfo
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
		this.getProductList()
		var product_id = this.$route.query.product_id
		if (product_id) {
			this.params.product_id = parseInt(product_id)
			useTabs.setTitle('设备管理：' + this.params.product_id)
		}
		this.getList()
	},
	computed: {},
	methods: {
		handleSizeChange(page_size) {
			this.params.pageSize = page_size
			this.getList()
		},
		handleCurrentChange(page) {
			this.params.page = page
			this.getList()
		},
		async getList() {
			this.loading = true
			const res = await this.$LotApi.device.list.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.listData = res.data.rows
				this.total = res.data.total
			}
		},
		getProductList() {
			this.$LotApi.product.all
				.get({
					tenant_id: tenantId,
					status: 1
				})
				.then((res) => {
					if (res.code === 200) {
						this.productList = res.data
					}
				})
		},
		//搜索
		upsearch() {
			this.params.page = 1
			this.getList()
		},
		refresh() {
			this.params = defaultParams()
			this.params.room = null
			this.upsearch()
		},
		//新增职位
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open().setData(null, this.params.campus_id)
			})
		},
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.getList()
			}
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row, this.params.campus_id)
			})
		},
		//查看
		table_show(row) {
			/*this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})*/
			this.$router.push({
				path: '/lot/device/detail',
				query: {
					id: row.id,
					campus_id: row.campus_id
				}
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: row.tenant_id, campus_id: row.campus_id }
			ElMessageBox.confirm('谨慎操作！确保当前设备已拆除，是否确认删除当前设备！', '谨慎操作', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.device.del.post(reqData)
				if (res.code === 200) {
					this.$message.success('删除成功')
					this.getList()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.el-header > .left-panel {
	flex: 4;
}
.scTable-table {
	height: calc(100% - 40px);
}

.scTable-page {
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding: 0 15px;
	border-top: 1px solid var(--el-border-color-light);
	.pagination {
	}
}

.scTable-do {
	white-space: nowrap;
}

.scTable:deep(.el-table__footer) .cell {
	font-weight: bold;
}

.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
	height: 8px;
	border-radius: 8px;
}

.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
	width: 8px;
	border-radius: 8px;
}
:deep(.card-item) {
	.el-card__footer {
		padding: 10px;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}
}
:deep(.el-scrollbar__wrap) {
	overflow-x: hidden !important;
}
:deep(.el-card__body) {
	padding: 10px;
	.el-form-item--default {
		margin-bottom: 0px;
		height: 30px;
		line-height: 30px;
	}
	.el-form-item__label {
		font-size: 12px;
		padding-right: 5px;
		height: 30px;
		line-height: 30px;
	}
	.el-form-item__content {
		font-size: 12px;
		height: 30px;
		line-height: 30px;
	}
}
.image-slot {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	background: var(--el-color-primary-light-9);
	color: var(--el-color-primary-light-2);
	font-size: 24px;
}
.card-info-img {
	display: flex;
	.el-image {
		margin-right: 10px;
	}

	.device_name {
		cursor: pointer;
		font-size: 15px;
		font-weight: bold;
		line-height: 25px;
		margin-bottom: 5px;
	}
}
.el-col .el-card {
	margin-bottom: 20px;
}
</style>
