<template>
	<el-dialog
		v-model="visible"
		:title="titleMap[mode] + getName"
		:width="500"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<el-form ref="dialogForm" :model="form" :disabled="mode == 'show'">
			<el-form-item label="搬离日期" prop="leave_time">
				<el-date-picker
					v-model="form.leave_time"
					type="date"
					:placeholder="'请选择日期'"
					format="YYYY-MM-DD"
					value-format="YYYY-MM-DD"
				/>
			</el-form-item>

			<el-form-item label="搬离原因" prop="leave_remark">
				<el-input v-model="form.leave_remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
const defaultData = () => {
	return {
		tenant_id: '',
		campus_id: '',
		id: null,
		leave_remark: null,
		leave_time: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',

			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			titleMap: {
				edit: '搬离人员-'
			},
			//验证规则
			rules: {
				required: true,
				message: '请输入名称',
				trigger: 'blur'
			}
			//所需数据选项
		}
	},
	mounted() {},
	computed: {
		getName() {
			if (this.form.user_type == 1) {
				return this.form.student_info ? this.form.student_info.student_name : ''
			} else if (this.form.user_type == 2) {
				return this.form.staff_info ? this.form.staff_info.name : ''
			} else {
				return ''
			}
		}
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.campus_id = this.params.campus_id
			this.form.tenant_id = this.params.tenant_id

			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.buildingRooms.personnel.leave.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
