<!--
 * @Descripttion: 表格选择器组件
 * @version: 1.3
 * @Author: sakuya
 * @Date: 2021年6月10日10:04:07
 * @LastEditors: Xujianchen
 * @LastEditTime: 2023-03-18 13:12:15
-->

<template>
	<!-- 暂存在此 -->
	<!-- <el-select ref="select" v-model="defaultValue" popper-class="pop-class" :size="size" :clearable="clearable"
		:multiple="true" :collapse-tags="collapseTags" :collapse-tags-tooltip="collapseTagsTooltip"
		:filterable="filterable" :placeholder="placeholder" :disabled="disabled" :filter-method="filterMethod"
		@remove-tag="removeTag" @visible-change="visibleChange" @clear="clear" @focus="showDialog" :style="{width: width}">
	</el-select> -->
	<el-input
		ref="select"
		:disabled="disabled"
		:readonly="readonly"
		:clearable="clearable"
		:style="{ width: width }"
		:placeholder="!defaultValue || defaultValue.length == 0 ? placeholder : ''"
		@focus="showDialog"
	>
		<template v-if="defaultValue && defaultValue.length > 0" #prefix>
			<template v-for="(item, index) in defaultValue.slice(0, 2)" :key="item.id">
				<el-tag effect="dark" closable type="info" @close="removeTag(item, index)">{{ item.label }}</el-tag>
			</template>
			<template v-if="defaultValue.length > 2">
				<el-popover width="500" placement="bottom" trigger="hover" popper-class="input-tag-popover">
					<template #reference>
						<el-tag effect="dark" type="info">+{{ defaultValue.length - 2 }}</el-tag>
					</template>
					<el-tag
						v-for="(item, index) in defaultValue.slice(2)"
						effect="dark"
						closable
						type="info"
						@close="removeTag(item, index + 2)"
						>{{ item.label }}</el-tag
					>
				</el-popover>
			</template>
		</template>
		<template #suffix>
			<el-icon class="el-input__icon"><el-icon-arrow-down /></el-icon>
		</template>
	</el-input>
	<el-dialog
		v-model="dialogVisible"
		title="选择场地"
		width="900"
		append-to-body
		:close-on-press-escape="false"
		class="select-dialog"
		@closed="closeDialog"
	>
		<el-header class="el-h">
			<el-select
				v-if="CampusManagementList.length > 1"
				v-model="params.campus_id"
				placeholder="校区"
				:teleported="false"
			>
				<el-option
					v-for="item in CampusManagementList"
					:key="item.code"
					:label="item.name"
					:value="item.value"
				></el-option>
			</el-select>
		</el-header>
		<el-tabs v-model="tabName" type="border-card" @tab-change="tabChange">
			<el-tab-pane label="场地" name="field">
				<el-container>
					<el-aside :style="{ width: treeWidth + 'px' }">
						<el-container>
							<el-main>
								<el-tree
									ref="group"
									class="menu"
									node-key="id"
									:data="groupsAdd"
									:highlight-current="true"
									:expand-on-click-node="false"
									:props="treeDefaultProps"
									:default-expanded-keys="[]"
									@node-click="groupClick"
								>
								</el-tree>
							</el-main>
						</el-container>
					</el-aside>
					<el-main class="el-m">
						<template v-if="multiple">
							<template v-if="tableData.length">
								<!-- <el-checkbox-group v-model="checkedRoom">
									<section v-for="(v, index) in tableData" :key="v.id" class="el-b">
										<el-checkbox-button :label="v.id" :value="v.id" @click="changeRoom($event, v)">
											<span>{{ v.room_name }}</span>
										</el-checkbox-button>
									</section>
								</el-checkbox-group> -->
								<ul class="room-list">
									<li
										v-for="(v, index) in tableData"
										:key="v.id"
										:class="checkedRoom.includes(v.id) ? 'room-select' : ''"
										@click="changeRoom($event, v)"
									>
										{{ v.room_name }}
									</li>
								</ul>
							</template>
							<template v-else>
								<el-empty description="暂无数据" :image-size="80"></el-empty>
							</template>
						</template>
						<template v-else>
							<template v-if="tableData.length">
								<section v-for="(v, index) in tableData" :key="v.id" class="el-b">
									<el-button :type="checkedRoom.includes(v.id) ? 'primary' : 'default'" @click="changeRoom($event, v)">
										<span>{{ v.room_name }}</span>
									</el-button>
								</section>
							</template>
							<template v-else>
								<el-empty description="暂无数据" :image-size="80"></el-empty>
							</template>
						</template>
					</el-main>
				</el-container>
			</el-tab-pane>
			<el-tab-pane label="已选" name="selected">
				<div class="selectBox">
					<div class="selectBox_one">
						<template v-if="multiple">
							<div
								v-for="(v, index) in statusData"
								:key="v.id"
								:class="{ is_active: index == activeIndexStatus }"
								class="st"
								@click="clickStatus(v, index)"
							>
								{{ v.label }}
							</div>
						</template>
						<template v-else>
							<template v-if="statusData.length">
								<section
									v-for="(v, index) in statusData"
									:key="v.id"
									:class="{ is_active: index == activeIndexStatus }"
									class="single-section"
									@click="clickStatus(v, index)"
								>
									{{ v.label }}
								</section>
							</template>
						</template>
					</div>
					<div class="selectBox_three">
						<template v-if="multiple">
							<template v-if="statusData[activeIndexStatus].children.length">
								<section v-for="(v, index) in statusData[activeIndexStatus].children" :key="v.id" class="selectItem">
									<el-button class="el-b" type="primary">
										<span>{{ v.room_name || v.label }}</span>
									</el-button>
								</section>
							</template>
							<template v-else>
								<el-empty description="暂无数据" :image-size="80"></el-empty>
							</template>
						</template>
						<template v-else>
							<template v-if="statusData.length">
								<section v-for="(v, index) in statusData[activeIndexStatus].children" :key="v.id">
									<el-button class="el-b" type="primary">
										<span>{{ v.room_name || v.label }}</span>
									</el-button>
								</section>
							</template>
							<template v-else>
								<el-empty description="暂无数据" :image-size="80"></el-empty>
							</template>
						</template>
					</div>
				</div>
			</el-tab-pane>
		</el-tabs>
		<div class="el-f">
			<span
				>已选择
				<span style="color: red">{{ this.statusData[0].children.length || 0 }}</span>
				个</span
			>
			<el-button type="primary" @click="confirm">确定</el-button>
		</div>
	</el-dialog>
</template>

<script>
import config from '@/config/tableSelect'
import api from '@/api'
import cusTom from '@/utils/cusTom'
export default {
	props: {
		modelValue: null,
		apiObj: {
			type: Object,
			default: () => {
				return api.fieldRoom.rooms.all
			}
		},

		placeholder: { type: String, default: '请选择' },
		size: { type: String, default: 'default' },
		clearable: { type: Boolean, default: false },
		multiple: { type: Boolean, default: false },
		filterable: { type: Boolean, default: false },
		collapseTags: { type: Boolean, default: true },
		collapseTagsTooltip: { type: Boolean, default: true },
		disabled: { type: Boolean, default: false },
		tableWidth: { type: Number, default: 600 },
		treeWidth: { type: Number, default: 240 },
		width: { type: String, default: '200px' },

		mode: { type: String, default: 'popover' },
		props: { type: Object, default: () => {} }
	},
	data() {
		return {
			loading: false,
			keyword: null,
			defaultValue: [],
			tableData: [],
			pageSize: config.pageSize,
			total: 0,
			currentPage: 1,
			defaultProps: {
				label: config.props.label,
				value: config.props.value,
				page: config.request.page,
				pageSize: config.request.pageSize,
				keyword: config.request.keyword
			},
			formData: {},
			params: {
				campus_id: '',
				tenant_id: ''
			},
			CampusManagementList: [],
			groupData: [],
			treeDefaultProps: {
				children: 'floor',
				label: 'name'
			},
			treeNodeId: null,
			timer: null,
			checkedRoom: [],
			rowClickData: [],
			dialogVisible: false,
			statusData: [
				{ id: 1, label: '未提交', children: [], data: [] },
				{ id: 2, label: '已提交', children: [], data: [] }
			],
			activeIndexStatus: 0,
			shuldBlur: false,
			searchSelect: 1,
			tabName: 'field'
		}
	},
	computed: {
		getWidth() {
			return this.tableWidth + this.treeWidth + 20 + 'px'
		},
		groupsAdd() {
			let arr = [...this.groupData]
			return arr
		}
	},
	watch: {
		modelValue: {
			handler(val) {
				if (val) {
					this.defaultValue = val
					this.statusData[1].children = val
					this.statusData[1].data = val.map((item) => item.id)
				}
			},
			immediate: true,
			deep: true
		},
		'params.campus_id': {
			handler() {
				// this.getData()
			},
			immediate: true
		},
		rowClickData: {
			handler(newval, oldval) {
				console.log(newval, oldval, 'rowClickData')
				if (!this.multiple) {
					this.statusData[0].children = [newval]
					this.statusData[0].data = this.statusData[0].children.map((item) => item.id)
				} else {
					this.statusData[0].children = Array.from(new Set(newval.map(JSON.stringify))).map(JSON.parse)
					this.statusData[0].data = this.statusData[0].children.map((item) => item.id)
				}
			},
			deep: true
		}
	},
	created() {
		const { campusId, tenantId, campusInfo } = cusTom.getBaseQuery()
		this.CampusManagementList = campusInfo
		this.params.campus_id = campusId
		this.params.tenant_id = tenantId
	},
	mounted() {
		this.defaultProps = Object.assign(this.defaultProps, this.props)
		this.defaultValue = this.modelValue
	},
	methods: {
		showDialog() {
			if (!this.shuldBlur) {
				this.dialogVisible = true
				this.getDept()
				this.getData()
			}
		},
		closeDialog() {
			this.blur()
		},
		tabChange(val) {
			console.log(val, 'tabChange')
			this.groupData = []
			switch (val) {
				case 'field':
					this.getDept()
					break
				case 'selected':
					break
			}
		},
		// 点击更改显示未提交/已提交的children
		clickStatus(val, index) {
			console.log(val, index, 'clickStatus')
			this.activeIndexStatus = index
		},
		confirm() {
			if (this.multiple) {
				const arr = Array.from(new Set(this.rowClickData.map(JSON.stringify))).map(JSON.parse)
				this.defaultValue = arr.map((item) => {
					return {
						label: item.building_name + '-' + item.floor_name + '-' + item.room_name,
						id: item.id
					}
				})
				this.$emit('update:modelValue', this.defaultValue)
				this.$emit('change', this.defaultValue)
			} else {
				if (this.rowClickData.id) {
					this.defaultValue = [
						{
							label:
								this.rowClickData.building_name +
								'-' +
								this.rowClickData.floor_name +
								'-' +
								this.rowClickData.room_name,
							id: this.rowClickData.id
						}
					]
				}
				this.$emit('update:modelValue', this.defaultValue)
				this.$emit('change', this.defaultValue)
			}
			this.dialogVisible = false
			this.shuldBlur = true
			this.$nextTick(() => {
				this.blur()
			})
		},
		//获取建筑楼层
		async getDept() {
			const res = await this.$API.fieldRoom.all.get(this.params)
			this.transData(res, 'name')
		},
		transData(res, field, type) {
			if (!res.data) res.data = []
			this.groupData = res.data.map((v) => {
				v.name = v[field]
				if (v.child && v.child.length > 0) {
					v.children = v.child
				}
				return v
			})
			if (type == 'tree') {
				this.groupData = cusTom.arrayToTree(this.groupData, 'id', 'parent_id')
			}
		},
		//树点击事件
		groupClick(data, node) {
			this.treeNodeId = data.id
			this.getData(node.level)
		},
		//表格显示隐藏回调
		visibleChange(visible) {
			if (visible) {
				this.currentPage = 1
				this.keyword = null
				this.formData = {}
				this.getData()
			} else {
			}
		},
		//获取表格数据
		async getData(level) {
			this.loading = true
			var reqData = {
				[this.defaultProps.page]: this.currentPage,
				[this.defaultProps.pageSize]: this.pageSize,
				[this.defaultProps.keyword]: this.keyword
			}
			var customParams = {
				building_id: level == 1 ? this.treeNodeId : null,
				floor_id: level == 2 ? this.treeNodeId : null
			}
			Object.assign(reqData, this.params, this.formData, customParams)
			var res = await this.apiObj.get(reqData)
			this.tableData = res.data || []
			this.loading = false
		},
		//插糟表单提交
		formSubmit() {
			this.currentPage = 1
			this.keyword = null
			this.getData()
		},
		//分页刷新表格
		reload() {
			this.getData()
		},
		changeRoom(e, v) {
			console.log(v, 'changeRoom')
			if (!this.multiple) {
				this.rowClickData = v
				this.checkedRoom = [v.id]
				return false
			}
			let has = this.rowClickData.some((item) => item.id == v.id)
			if (has) {
				this.rowClickData = this.rowClickData.filter((item) => item.id != v.id)
				this.checkedRoom = this.checkedRoom.filter((item) => item != v.id)
			} else {
				this.rowClickData.push(v)
				this.checkedRoom = this.rowClickData.map((item) => item.id)
			}
		},
		//tags删除后回调
		removeTag(tag, index) {
			if (this.disabled) {
				return false
			}
			if (this.multiple) {
				this.rowClickData.splice(index, 1)
				this.defaultValue.splice(index, 1)
				this.checkedRoom = this.checkedRoom.filter((item) => item != tag.id)
			} else {
				this.rowClickData = []
				this.defaultValue = []
			}
			this.$emit('update:modelValue', this.defaultValue)
			this.$emit('change', this.defaultValue)
		},
		//清空后的回调
		clear() {
			this.$emit('update:modelValue', this.defaultValue)
		},
		// 关键值查询表格数据行
		findRowByKey(value) {
			return this.tableData.find((item) => item[this.defaultProps.value] === value)
		},
		filterMethod(keyword) {
			if (!keyword) {
				this.keyword = null
				return false
			}
			this.keyword = keyword
			this.getData()
		},
		// 触发select隐藏
		blur() {
			if (this.shuldBlur) {
				this.shuldBlur = false
				return false
			}
			this.$refs.select.blur()
		},
		// 触发select显示
		focus() {
			this.$refs.select.focus()
		}
	}
}
</script>

<style lang="scss">
.select-dialog {
	.el-dialog__body {
		padding-top: 12px;
	}
}

.input-tag-popover {
	.el-tag {
		margin-bottom: 5px;
		margin-right: 3px;
	}

	.el-tag + .el-tag {
		margin-left: 0px;
	}
}
</style>

<style scoped lang="scss">
.el-h {
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	border: none;
	padding-left: 0;

	> .el-select {
		margin-right: 15px;
	}
}

.el-m {
	height: 300px;
	overflow: auto;

	.room-list {
		display: flex;
		flex-wrap: wrap;

		li {
			line-height: 1;
			height: 32px;
			width: 185px;
			padding: 8px 15px;
			border: 1px solid #dcdfe6;
			border-radius: 4px;
			text-align: center;
			box-sizing: border-box;
			white-space: nowrap;
			cursor: pointer;
			margin-bottom: 5px;
			margin-right: 8px;
			font-size: 14px;
			user-select: none;
			vertical-align: middle;

			&:hover {
				color: #2745b2;
				border-color: #bec7e7;
				background-color: #e9ecf7;
			}
		}

		.room-select {
			color: #ffffff;
			background-color: #2745b2;
			border-color: #2745b2;

			&:hover {
				color: #ffffff;
				background-color: #677cc9;
				border-color: #677cc9;
			}
		}
	}
}

.is_active {
	background-color: #e9ecf7;
	position: relative;

	&::after {
		content: '';
		width: 3px;
		height: 100%;
		position: absolute;
		right: 0;
		background-color: var(--el-color-primary-light-3);
	}
}

.selectBox {
	display: flex;
	min-height: 300px;
	border: 1px solid #eee;

	section {
		padding: 0 10px;
	}

	& > div {
		padding: 10px;
	}

	&_one {
		flex: 1;
		padding-right: 0 !important;
		border-right: 1px solid #eee;
	}

	&_three {
		flex: 3;
		max-height: 300px;
		overflow: auto;

		.selectItem {
			display: inline-block;
			width: 185px;

			.el-b {
				min-width: 100%;

				:deep(.el-checkbox-button__inner) {
					width: 100%;
				}
			}
		}

		section + section {
			margin-top: 5px;
		}
	}
}

.el-b {
	width: 30%;
	margin-bottom: 5px;
	margin-right: 8px;
	display: inline-block;

	:deep(.el-button--default) {
		width: 100%;
	}
}

.st {
	height: 32px;
	font-size: 16px;
	padding-left: 10px;
	display: flex;
	align-items: center;
}

.single-section {
	height: 32px;
	line-height: 32px;
}

.menu {
	height: 300px;
	overflow: auto;
}

.sc-table-select__table {
	padding: 0px;
}

.sc-table-select__page {
	padding-top: 12px;
}

.el-f {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 10px;
	height: 50px;
}

.selectTitle {
	padding: 10px;
	font-weight: bold;
	color: #333;
	font-size: 18px;
	border-bottom: 1px solid #eee;
}
</style>
