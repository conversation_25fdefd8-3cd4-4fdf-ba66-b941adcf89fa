import config from '@/config'
import http from '@/utils/request'

export default {
	course: {
		list: {
			url: `${config.API_URL}/eduapi/course/list`,
			name: '获取学科列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/eduapi/course/all`,
			name: '获取学科列表不分页',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		single_course: {
			url: `${config.API_URL}/eduapi/grade/getGradeCourse`,
			name: '获取单个年级的课程列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		class_course:{
			url: `${config.API_URL}/eduapi/class/getClassCourse`,
			name: '获取班级的课程列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/course/save`,
			name: '新增学科/修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},

		del: {
			url: `${config.API_URL}/eduapi/course/del`,
			name: '删除学科',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		status: {
			url: `${config.API_URL}/eduapi/course/changeStatus`,
			name: '修改学科状态',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	}
}
