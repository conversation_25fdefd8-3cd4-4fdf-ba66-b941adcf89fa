import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/manapi/campus/list`,
		name: '获取校区列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	all: {
		url: `${config.API_URL}/manapi/campus/all`,
		name: '获取校区列表不分页',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	listOne: {
		url: `${config.API_URL}/manapi/campus/getOne`,
		name: '获取单个校区',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/manapi/campus/save`,
		name: '新增校区/修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},

	del: {
		url: `${config.API_URL}/manapi/campus/del`,
		name: '删除校区',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	edit: {
		url: `${config.API_URL}/manapi/campus/changeStatus`,
		name: '修改校区状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	getTenant: {
		url: `${config.API_URL}/sysapi/tenant/getOne`,
		name: '获取学校信息',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	tenantMandate: {
		url: `${config.API_URL}/sysapi/tenant/tenantMandate`,
		name: '强制播放',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	syncStudent: {
		url: `${config.API_URL}/manapi/campus_org/syncStudent`,
		name: '同步学生数据',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	syncTeacher: {
		url: `${config.API_URL}/manapi/campus_org/syncTeacher`,
		name: '同步教师数据',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	}
}
