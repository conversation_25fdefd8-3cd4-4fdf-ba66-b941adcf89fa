<template>
	<div class="">
		<div v-if="showCourse" class="date-box">
			<div v-for="(item, index) in dateList" :key="index" class="date-item"
				:style="`flex:${item.course.length} 1 auto`">
				<div class="date-item-header">{{ item.date }}</div>
				<div class="date-item-course">
					<div v-for="(course, index) in item.course" :key="index" class="course-item"
						:class="{ active: currentId == course.id }" @click="selectCourse(course)">
						<p>{{ course.course_name }}</p>
						<p>{{ course.time }}</p>
					</div>
				</div>
			</div>
		</div>
		<div class="listContent" :style="cusHeight">
			<div class="studentList">
				<div style="margin-bottom: 10px; text-align: right">
					<el-input v-model="params.name" style="width: 200px; margin-right: 10px" clearable placeholder="输入姓名搜索"
						prefix-icon="el-icon-search" @change="upsearch" />
					<el-button type="primary" @click="arrange">一键安排</el-button>
				</div>
				<scTable ref="table" row-key="id" :apiObj="apiObj" :params="params" :hideDo="true">
					<el-table-column label="学号/考号" prop="serial_number"></el-table-column>
					<el-table-column label="姓名" prop="student_name"></el-table-column>
					<el-table-column label="原班级" prop="grade_info">
						<template #default="{ row }"> {{ row.grade_info?.name }}{{ row.class_info?.name || '' }} </template>
					</el-table-column>
					<el-table-column label="考场" prop="room_info">
						<template #default="{ row }">
							{{ row.room_info?.name }}
						</template>
					</el-table-column>
					<el-table-column label="座位号" prop="seat_number"></el-table-column>
					<el-table-column v-if="showCourse" label="科目" prop="course_info">
						<template #default="{ row }">
							{{ row.course_info?.name }}
						</template>
					</el-table-column>
					<el-table-column label="操作" width="200">
						<template #default="{ row }">
							<el-button text type="primary" size="small" @click="table_edit(row)">编辑</el-button>
						</template>
					</el-table-column>
				</scTable>
			</div>
			<div class="roomList">
				<div class="roomList-title">考场</div>
				<div v-if="roomList && roomList.length" class="roomList-content">
					<div v-for="item in roomList" :key="item.room_id" class="roomList-item">
						<div class="roomList-item-title">{{ item.room_info?.name }}</div>
						<div v-if="!showCourse || currentId">{{ item.real_student_num }} /{{ item.student_num }}</div>
						<div>
							<el-button text type="primary" size="small" @click="showInfo(item)">详情</el-button>
						</div>
					</div>
				</div>
				<div v-else class="roomList-content">
					<el-empty description="暂无考场数据" :image-size="100"></el-empty>
				</div>
			</div>
		</div>
	</div>

	<save ref="saveRef" :roomList="roomList" @saveSuccess="tableUpdate"></save>
	<roomContent ref="roomContentRef" :params="params" :showCourse="showCourse" :courseList="courseList"></roomContent>
</template>

<script setup>
import save from './save.vue'
import roomContent from './roomContent.vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()

const defaultParams = () => {
	return {
		campus_id: campusId,
		tenant_id: tenantId,
		examine_id: Number(query.id),
		name: null,
		course_id: null
	}
}
const params = ref(defaultParams())
const apiObj = ref(globalPropValue.examine.studentList)

const currentId = ref('')
const selectCourse = (course) => {
	if (currentId.value === course.id) {
		currentId.value = ''
		params.value.course_id = ''
	} else {
		currentId.value = course.id
		params.value.course_id = course.id
	}
	tableUpdate()
	getRoomList()
}

const table = ref()
const saveRef = ref()
const roomContentRef = ref()
// 一键安排
const arrange = () => {
	ElMessageBox.confirm('确定执行一键安排吗？若存在已安排的考生安排，执行后会覆盖之前的考生安排！', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	})
		.then(() => {
			globalPropValue.examine.arrange.post(params.value).then((res) => {
				if (res.code === 200) {
					ElMessage.success('一键安排成功')
					params.value = defaultParams()
					currentId.value = ''
					tableUpdate()
				}
			})
		})
		.catch(() => { })

}

const tableUpdate = () => {
	table.value.upData(params.value)
}

// 搜索
const upsearch = () => {
	tableUpdate()
}

// 修改
const table_edit = (row) => {
	saveRef.value.open(row)
}

// 详情
const showInfo = (item) => {
	roomContentRef.value.open(item)
}

// 获取时段
const dateList = ref([])
const courseList = ref([])
const getTime = () => {
	globalPropValue.examine.timeList.get(params.value).then((res) => {
		if (res.code === 200) {
			const groupedData = {}
			res.data.forEach((item) => {
				if (!groupedData[item.examine_date]) {
					groupedData[item.examine_date] = {
						date: item.examine_date,
						course: []
					}
				}
				groupedData[item.examine_date].course.push({
					id: item.course_id,
					course_name: item.course_name,
					time: item.begin_time + '-' + item.end_time
				})
			})
			dateList.value = Object.values(groupedData)
			courseList.value = dateList.value.flatMap((item) => item.course)
		}
	})
}

// 获取考场列表
const roomList = ref([])
const getRoomList = () => {
	globalPropValue.examine.roomList.get(params.value).then((res) => {
		if (res.code === 200) {
			roomList.value = res.data || []
		}
	})
}

const cusHeight = ref('height: calc(100vh - 250px);')
const showCourse = ref(false)
onMounted(() => {
	getRoomList()
	if (query.seat_type == 2) {
		getTime()
		cusHeight.value = 'height: calc(100vh - 385px);'
		showCourse.value = true
	}
})
</script>

<style lang="scss" scoped>
.date-box {
	display: flex;
	border: 1px solid var(--el-border-color-light);
	margin-bottom: 20px;

	.date-item {
		border-right: 1px solid var(--el-border-color-light);

		&:nth-child(even) {
			.date-item-header {
				background-color: var(--el-fill-color-light);
			}
		}

		&:nth-child(odd) {
			.date-item-header {
				background-color: var(--el-fill-color-lighter);
			}
		}

		&:last-child {
			border-right: none;
		}

		.date-item-header {
			height: 50px;
			line-height: 50px;
			text-align: center;
			font-size: 15px;
			border-bottom: 1px solid var(--el-border-color-light);
		}

		.date-item-course {
			display: flex;
			justify-content: space-around;
			padding: 0 10px;
			min-width: 120px;

			.course-item {
				padding: 0 10px;
				height: 50px;
				margin: 10px;
				font-size: 16px;
				border-radius: 5px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				background: var(--el-fill-color-light);
				cursor: pointer;

				p {
					&:last-child {
						font-size: 14px;
					}
				}
			}

			.active {
				background: var(--el-color-primary-light-8);
				color: var(--el-color-primary);
			}
		}
	}
}

.listContent {
	display: flex;
	justify-content: space-between;

	.studentList {
		flex: 1;
		padding-right: 10px;
	}

	.roomList {
		width: 25%;
		border-left: 1px solid #e5e5e5;

		&-title {
			font-size: 13px;
			padding: 8px 15px;
			margin-bottom: 10px;
		}

		&-content {
			height: 100%;
			overflow-y: auto;
			padding: 10px;
		}

		&-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px 15px;
			margin-bottom: 10px;
			background: #f6f8f9;
			border-radius: 5px;

			&:last-child {
				margin-bottom: 0;
			}

			&-title {
				width: 50%;
				// 超出显示省略号
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
}
</style>
