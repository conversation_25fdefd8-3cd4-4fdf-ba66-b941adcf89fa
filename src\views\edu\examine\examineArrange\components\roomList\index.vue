<template>
	<el-alert type="warning"  show-icon :closable="false">
		<template #default>
			<b>1. 为了保证考试的公平性，建议您在安排考场时，根据考生人数和监考人数进行合理分配。</b><br/>
			<b>2. 编辑信息后请点击右侧【保存】按钮保存编辑</b>
      </template>
	</el-alert>
	<div class="addBtn" style="margin-top: 10px;">
		<div class="addBtn-left">
			<p>共{{ totalRooms }}考场，可容纳{{ totalStudents }}名考生，监考员至少{{ totalTeachers }}人</p>
		</div>
		<div>
			<el-button type="primary" icon="el-icon-plus" @click="table_add">新增场地</el-button>
			<el-button type="danger" @click="saveRoom">保存</el-button>
		</div>
	</div>
	<scTable ref="table" row-key="id" :data="roomList" :hideDo="true" hidePagination>
		<el-table-column prop="room_info" label="场地名称">
			<template #default="{ row }">
				{{ row.room_info?.name }}
			</template>
		</el-table-column>
		<el-table-column prop="student_num" label="考生人数">
			<template #default="{ row }">
				<el-input-number v-model="row.student_num" :min="1" />
			</template>
		</el-table-column>
		<el-table-column prop="teacher_num" label="监考人数">
			<template #default="{ row }">
				<el-input-number v-model="row.teacher_num" :min="1" />
			</template>
		</el-table-column>
		<el-table-column prop="column" label="每列人数">
			<template #default="{ row }">
				<el-input-number v-model="row.column" :min="1" />
			</template>
		</el-table-column>
		<el-table-column label="操作">
			<template #default="{ row }">
				<!-- <el-button text type="primary" @click="table_edit(row)">编辑</el-button> -->
				<el-popconfirm title="确定删除吗？" @confirm="table_del(row)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
			</template>
		</el-table-column>
	</scTable>
	<save ref="saveRef" @addRoom="pushRoom" />
</template>
<script setup>
import save from './save.vue'

import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()

const saveRef = ref()
const table_add = () => {
	saveRef.value.open('add')
}
const pushRoom = (list) => {
	// 对比roomList，若存在教室则将list中人数信息代入roomList对应的教室中
	list.forEach((item) => {
		const room = roomList.value.find((room) => room.room_id == item.room_id)
		if (room) {
			room.student_num = item.student_num
			room.teacher_num = item.teacher_num
			room.column = item.column
		} else {
			roomList.value.push(item)
		}
	})
}

const saveRoom = () => {
	let saveParams = {
		examine_id: Number(query.id),
		tenant_id: tenantId,
		campus_id: campusId,
		list: roomList.value
	}
	globalPropValue.examine.roomSave.post(saveParams).then((res) => {
		if (res.code === 200) {
			ElMessage({ type: 'success', message: '保存成功' })
			getRoomList()
		}
	})
	console.log(roomList.value)
}
const table_edit = (row) => {
	saveRef.value.open('edit', row)
}
const table_del = (row) => {
	roomList.value = roomList.value.filter((item) => item.room_id != row.room_id)
}

const roomList = ref([])
const getRoomList = () => {
	globalPropValue.examine.roomList
		.get({
			tenant_id: tenantId,
			campus_id: campusId,
			examine_id: query.id
		})
		.then((res) => {
			if (res.code === 200) {
				roomList.value = res.data || []
			}
		})
}
onMounted(() => {
	getRoomList()
})
const totalRooms = computed(() => roomList.value.length)
const totalStudents = computed(() => roomList.value.reduce((sum, room) => sum + room.student_num, 0))
const totalTeachers = computed(() => roomList.value.reduce((sum, room) => sum + room.teacher_num, 0))
</script>

<style lang="scss" scoped>
.addBtn {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;

	&-left {
		font-size: 16px;
	}
}
</style>
