<template>
	<el-dialog v-model="visible" :title="type[model]" destroy-on-close width="30%">
		<el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
			<el-form-item label="附件名称" prop="name" label-width="100px">
				<el-input v-model="form.name" placeholder="请输入"></el-input>
			</el-form-item>
			<el-form-item label="附件地址" prop="url" label-width="100px">
				<scUploadFile v-model="form.url" accept="*" @suc="handlerSuc" fileTypeTag="eduMaterials"></scUploadFile>
			</el-form-item>
			<el-form-item label="附件描述" label-width="100px">
				<el-input v-model="form.description" placeholder="请输入"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit">保 存</el-button>
		</template>
	</el-dialog>
</template>
<script setup>
import { reactive, ref, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps({
	params: {
		type: Object,
		default: () => {
			return {}
		}
	}
})
const emit = defineEmits(['success'])
const defaultData = () => {
	return {
		id: null,
		tenant_id: 0,
		campus_id: 0,
		material_id: 0,
		name: '',
		description: '',
		file_id: null,
		url: '',
		obj: '',
		listorder: 0
	}
}
let formRef = ref(null)
const type = reactive({ add: '新增', edit: '编辑' })
let model = ref('add')
let visible = ref(false)
let isSaveing = ref(false)
const form = ref(defaultData())
const rules = reactive({
	name: [{ required: true, message: '请输入附件名称', trigger: 'blur' }],
	url: [{ required: true, message: '请上传附件', trigger: 'blur' }]
})
const handlerSuc = (res) => {
	form.value.url = res.url
	form.value.obj = res.key
}
const open = (mode = 'add') => {
	form.value = defaultData()
	model.value = mode
	form.value.tenant_id = Number(props.params.tenant_id)
	form.value.campus_id = Number(props.params.campus_id)
	form.value.material_id = Number(props.params.material_id)
	visible.value = true
}
// 保存按钮点击事件
const submit = async () => {
	await formRef.value.validate()
	isSaveing.value = true
	const res = await globalPropValue.eduMaterials.material.add_edit_attachment.post(form.value)
	isSaveing.value = false
	if (res.code === 200) {
		ElMessage({ type: 'success', message: `${model.value === 'add' ? '新增成功' : '编辑成功'}` })
		emit('success', form.value, model.value)
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
	visible.value = false
}
// 表单数据注入
const setData = (data) => {
	Object.assign(form.value, data)
}
defineExpose({
	open,
	setData
})
</script>
<style scoped></style>
