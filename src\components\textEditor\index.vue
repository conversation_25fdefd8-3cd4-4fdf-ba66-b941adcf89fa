<template>
	<div style="border: 1px solid #dcdfe6; z-index: 110; width: 100%">
		<Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
		<Editor
			v-model="valueHtml"
			:style="{ height: height }"
			:defaultConfig="editorConfig"
			:mode="mode"
			@onCreated="handleCreated"
		/>
	</div>
</template>
<script>
import '@wangeditor/editor/dist/css/style.css'
import { defineComponent, onBeforeUnmount, ref, shallowRef, onMounted, watch, getCurrentInstance } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import tool from '@/utils/tool'
import { putObjectBig } from '@/utils/ossLib'
import { ElMessage } from 'element-plus'

export default defineComponent({
	components: { Editor, Toolbar },
	props: {
		value: {
			type: String,
			default: ''
		},
		height: {
			type: String,
			default: '150px'
		},
		fileTypeTag: {
			type: String,
			default: 'editor'
		}
	},
	setup(props, { emit }) {
		const { proxy } = getCurrentInstance()
		const editorRef = shallowRef()
		let valueHtml = ref(props.value)
		const toolbarConfig = {
			excludeKeys: [
				'video',
				'uploadVideo',
				'insertVideo',
				'insertTable',
				'through'
			]
		}
		const editorConfig = { MENU_CONF: {} }
		editorConfig.MENU_CONF['uploadImage'] = {
			// server: proxy.$API.common.upload.url,
			fieldName: 'file',
			// 单个文件的最大体积限制，默认为 2M
			maxFileSize: 1 * 1024 * 1024, // 1M
			// 最多可上传几个文件，默认为 100
			maxNumberOfFiles: 10,
			// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
			allowedFileTypes: ['image/*'],
			// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
			meta: {
				name: 'xxx'
			},
			// 将 meta 拼接到 url 参数中，默认 false
			metaWithUrl: false,
			// 自定义增加 http  header
			headers: {
				Authorization: tool.cookie.get('USER_TOKEN')
			},
			// 跨域是否传递 cookie ，默认为 false
			withCredentials: true,
			// 超时时间，默认为 10 秒
			timeout: 5 * 1000, // 5 秒
			// customInsert(res, insertFn) {
			//   let url = res.data.url
			//   insertFn(url)
			// },
			async customUpload(file, insertFn) {
				var result = await putObjectBig(file, file.name, props.fileTypeTag)
				if (result) {
					insertFn(result.url)
				} else {
					ElMessage.error('上传失败')
				}
			}
		}
		// 模拟 ajax 异步获取内容
		onMounted(() => {
		})
		onBeforeUnmount(() => {
			const editor = editorRef.value
			if (editor == null) return
			editor.destroy()
		})

		const handleCreated = (editor) => {
			editorRef.value = editor
		}
		watch(props, (newValue, oldValue) => {
			valueHtml.value = newValue.value
		})
		watch(valueHtml, (newValue, oldValue) => {
			emit('update:value', newValue)
		})
		return {
			editorRef,
			valueHtml,
			mode: 'simple', // 或 'simple'
			toolbarConfig,
			editorConfig,
			handleCreated
		}
	}
})
</script>
