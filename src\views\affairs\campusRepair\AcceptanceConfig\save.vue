<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" destroy-on-close width="400" @closed="$emit('closed')">
		<el-form ref="formref" :model="form" :rules="rules" label-width="120px">
			<el-form-item label="选择人员" prop="user">
				<cusSelectTeacher v-model="form.user" :multiple="mode === 'add'" :disabled="mode === 'edit'"></cusSelectTeacher>
			</el-form-item>
			<el-form-item label="总务报修受理" prop="affairs">
				<el-switch v-model="form.affairs" :active-value="1" :inactive-value="-1" />
			</el-form-item>
			<el-form-item label="信息化报修受理" prop="information">
				<el-switch v-model="form.information" :active-value="1" :inactive-value="-1" />
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		user: null,
		user_id: null,
		affairs: -1,
		information: -1,
		id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			oneLevel: [],
			visibleone: false,
			isSaveingone: false,
			oneName: null,
			//验证规则
			rules: {
				user: [{ required: true, message: '请选择人员'}],
			},
		}
	},
	computed: {},
	mounted() {},
	created() {},
	watch: {
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			return this
		},
		//表单提交方法
		submit() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					this.form.user_ids = this.form.user.map((item) => item.id)
					var res = await this.$API.campusRepair.acceptance.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			this.form.user = []
			if (data.user !== null) {
				this.form.user = [
					{
						id: data.user.id,
						label: data.user.name
					}
				]
			}
		}
	}
}
</script>

<style></style>
