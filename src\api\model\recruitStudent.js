import config from '@/config'
import http from '@/utils/request'

export default {
	recruit: {
		list: {
			url: `${config.API_URL}/recapi/plan/list`,
			name: '获取招生计划列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all_list: {
			url: `${config.API_URL}/recapi/plan/all`,
			name: '获取所有招生计划',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add_edit: {
			url: `${config.API_URL}/recapi/plan/save`,
			name: '修改或新增招生计划',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/recapi/plan/del`,
			name: '删除',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		cadet_list: {
			url: `${config.API_URL}/recapi/student/list`,
			name: '获取学员列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add_edit_cadet: {
			url: `${config.API_URL}/recapi/student/save`,
			name: '修改或新增学员信息',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del_cadet: {
			url: `${config.API_URL}/recapi/student/del`,
			name: '删除学员',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		list_report: {
			url: `${config.API_URL}/recapi/student/report_record`,
			name: '学员报到记录',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		}
	}
}
