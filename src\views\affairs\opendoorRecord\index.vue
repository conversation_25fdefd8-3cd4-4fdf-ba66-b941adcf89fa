<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList.length > 1" label="校区">
						<el-select v-model="params.campus_id" placeholder="请选择校区" filterable>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="设备SN">
						<el-input v-model="params.device_sn" style="width: 150px" placeholder="请输入设备SN" clearable></el-input>
					</el-form-item>
					<el-form-item label="房间名称">
						<el-input v-model="params.room_name" style="width: 150px" placeholder="请输入房间名称" clearable></el-input>
					</el-form-item>
					<el-form-item label="时间">
						<cusDate
							v-model="params.date"
							type="daterange"
							style="width: 300px"
							placeholder="请输入房间名称"
							clearable
						/>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<el-table-column label="人员类型" prop="user" width="100">
					<template #default="scope">
						<div v-if="scope.row.user_type === 1">学生</div>
						<div v-if="scope.row.user_type === 2">教师</div>
					</template>
				</el-table-column>
				<el-table-column label="开门人员" prop="user" width="100">
					<template #default="scope">
						<div v-if="scope.row.user">{{ scope.row.user.name }}</div>
					</template>
				</el-table-column>
				<el-table-column label="开门位置" prop="position" min-width="200">
					<template #default="scope">
						<div v-if="scope.row.position">{{ scope.row.position.name }}</div>
					</template>
				</el-table-column>
				<el-table-column label="开门方式" prop="recognize_type" width="200">
					<template #default="scope">
						<div v-if="scope.row.recognize_type === 20">人脸识别</div>
						<div v-else-if="scope.row.recognize_type === 300">指纹识别</div>
						<div v-else-if="scope.row.recognize_type === 400">密码开门</div>
						<div v-else-if="scope.row.recognize_type === 401">
							<p>临时密码开门</p>
							<p style="font-size: 9px; color: var(--el-text-color-secondary)">密码ID：{{ scope.row.pwd_id }}</p>
						</div>
						<div v-else-if="scope.row.recognize_type === 500">磁卡开门</div>
						<div v-else-if="scope.row.recognize_type === 600">扫码开门</div>
						<div v-else-if="scope.row.recognize_type === 1000">蓝牙开门</div>
						<div v-else>远程开门</div>
					</template>
				</el-table-column>
				<el-table-column label="开门时间" prop="recognize_time" width="150"></el-table-column>
				<el-table-column label="设备ID" prop="device_id" width="200" align="center"></el-table-column>
				<el-table-column label="设备SN" prop="device_sn" width="200"></el-table-column>
				<el-table-column label="位置空间ID" prop="tree_id " width="200">
					<template #default="scope">
						<div v-if="scope.row.tree_id > 0">{{ scope.row.tree_id }}</div>
						<div v-else>-</div>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<el-dialog v-model="dialog.unbind" title="解绑并重置" width="500" center :before-close="closeUnbind">
		<p v-if="countdown > 0" class="countdown">
			<span>{{ countdown }}</span>
		</p>
		<p v-else style="font-size: 48px; text-align: center; color: #f53f3f">
			<el-icon><el-icon-warning /></el-icon>
		</p>

		<p style="text-align: center; font-size: 14px">解绑后，数据将被清除，无法使用。确认解绑并重置？</p>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="closeUnbind">取消</el-button>
				<el-button type="danger" :disabled="countdown > 0" @click="unbind">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import cusImage from '@/components/custom/cusImage.vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const { campusId, tenantId, campusInfo, tenantInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		device_sn: null,
		room_name: null,
		date: null,
		begin_date: null,
		end_date: null
	}
}
export default {
	name: '',
	components: {
		cusImage
	},
	data() {
		return {
			unbindData: null,
			timer: null,
			countdown: 5,
			dialog: {
				save: false,
				unbind: false
			},
			unbindDisable: true,
			apiObj: this.$API.device.opendoorRecord,
			selection: [],

			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null,
			disciplineOptions: []
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.$SET_campus_id(val)
			},
			immediate: true
		}
	},
	computed: {},
	async created() {
		clearInterval(this.timer)
		this.timer = null
	},
	unmounted() {
		clearInterval(this.timer)
		this.timer = null
	},
	methods: {
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		//搜索
		upsearch() {
			this.params.begin_date = this.params.date ? this.params.date[0] : null
			this.params.end_date = this.params.date ? this.params.date[1] : null
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null

			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}

			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped>
.countdown {
	text-align: center;
	padding-bottom: 10px;
	span {
		width: 45px;
		height: 45px;
		border: 3px solid var(--el-color-error);
		border-radius: 50%;
		display: inline-block;
		line-height: 40px;
		font-size: 18px;
		font-weight: bold;
		color: var(--el-color-error);
		text-align: center;
	}
}
</style>
