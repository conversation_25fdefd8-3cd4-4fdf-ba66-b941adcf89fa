<template>
  <el-form ref="form" :model="form" :label-width="formConfig.labelWidth"
    :label-position="position(formConfig.labelPosition)" :size="formConfig.size">
    <template v-for="element in formConfig.formItems" :key="element.key">
      <div v-if="element.type === 'divider'">
        <el-divider :direction="element.options.direction" :border-style="element.options.borderStyle"
          :content-position="element.options.contentPosition">
          {{ element.options.content }}
        </el-divider>
      </div>
      <div v-if="element.type === 'text'">
        <div v-html="element.options.htmlContent"></div>
      </div>

      <el-form-item v-if="element && element.type !== 'divider' && element.type !== 'text'" :key="element.key"
        :label="element.label" :prop="element.model" :rules="element.options.rules">
        <template v-if="element.type === 'input'">
          <el-input v-model="form[element.model]" :style="{ width: element.options.width }"
            :placeholder="element.options.placeholder" :minlength="parseInt(element.options.minlength)"
            :maxlength="parseInt(element.options.maxlength)" :clearable="element.options.clearable"
            :readonly="element.options.readonly" :disabled="disabled || element.options.disabled"
            :show-word-limit="element.options.showWordLimit">
            <template #prefix v-if="element.options.prefix">{{ element.options.prefix }}</template>
            <template #suffix v-if="element.options.suffix">{{ element.options.suffix }}</template>
            <template #prepend v-if="element.options.prepend">{{ element.options.prepend }}</template>
            <template #append v-if="element.options.append">{{ element.options.append }}</template>
          </el-input>
        </template>

        <template v-if="element.type === 'password'">
          <el-input v-model="form[element.model]" :style="{ width: element.options.width }"
            :placeholder="element.options.placeholder" :minlength="parseInt(element.options.minlength)"
            :maxlength="parseInt(element.options.maxlength)" :clearable="element.options.clearable"
            :disabled="disabled || element.options.disabled" :readonly="element.options.readonly"
            :show-password="element.options.showPassword">
            <template #prefix v-if="element.options.prefix">{{ element.options.prefix }}</template>
            <template #suffix v-if="element.options.suffix">{{ element.options.suffix }}</template>
            <template #prepend v-if="element.options.prepend">{{ element.options.prepend }}</template>
            <template #append v-if="element.options.append">{{ element.options.append }}</template>
          </el-input>
        </template>

        <template v-if="element.type === 'textarea'">
          <el-input type="textarea" v-model="form[element.model]" :rows="element.options.rows"
            :style="{ width: element.options.width }" :placeholder="element.options.placeholder"
            :minlength="parseInt(element.options.minlength)" :maxlength="parseInt(element.options.maxlength)"
            :show-word-limit="element.options.showWordLimit" :autosize="element.options.autosize"
            :clearable="element.options.clearable" :readonly="element.options.readonly"
            :disabled="disabled || element.options.disabled" />
        </template>

        <template v-if="element.type === 'number'">
          <el-input-number v-model="form[element.model]" :style="{ width: element.options.width }"
            :max="element.options.max" :min="element.options.min" :disabled="disabled || element.options.disabled" />
        </template>

        <template v-if="element.type === 'radio'">
          <el-radio-group v-model="form[element.model]" :style="{ width: element.options.width }"
            :disabled="disabled || element.options.disabled">
            <el-radio v-for="item of element.options.remote
              ? element.options.remoteOptions
              : element.options.options" :key="item.value" :value="item.value" :style="{
                display: element.options.inline ? 'inline-block' : 'block'
              }">{{ element.options.showLabel ? item.label : item.value }}</el-radio>
          </el-radio-group>
        </template>

        <template v-if="element.type === 'checkbox'">
          <el-checkbox-group v-model="form[element.model]" :style="{ width: element.options.width }"
            :disabled="disabled || element.options.disabled" :min="element.options.min" :max="element.options.max">
            <el-checkbox v-for="item of element.options.remote
              ? element.options.remoteOptions
              : element.options.options" :key="item.value" :value="item.value" :style="{
                display: element.options.inline ? 'inline-block' : 'block'
              }">
              {{
                element.options.showLabel ? item.label : item.value
              }}
            </el-checkbox>
          </el-checkbox-group>
        </template>

        <template v-if="element.type === 'time'">
          <el-time-picker v-model="form[element.model]" :placeholder="element.options.placeholder"
            :readonly="element.options.readonly" :editable="element.options.editable"
            :clearable="element.options.clearable" :format="element.options.format"
            :value-format="element.options.valueFormat" :disabled="disabled || element.options.disabled"
            :style="{ width: element.options.width }" />
        </template>

        <template v-if="element.type === 'timerange'">
          <el-time-picker v-model="form[element.model]" :placeholder="element.options.placeholder" is-range
            :range-separator="element.options.rangeSeparator" :start-placeholder="element.options.startPlaceholder"
            :end-placeholder="element.options.endPlaceholder" :readonly="element.options.readonly"
            :editable="element.options.editable" :clearable="element.options.clearable" :format="element.options.format"
            :value-format="element.options.valueFormat" :disabled="disabled || element.options.disabled"
            :style="{ width: element.options.width }" />
        </template>

        <template v-if="element.type === 'date'">
          <el-date-picker v-model="form[element.model]" :placeholder="element.options.placeholder"
            :readonly="element.options.readonly" :editable="element.options.editable"
            :clearable="element.options.clearable" :type="element.options.type" :format="element.options.format"
            :value-format="element.options.valueFormat" :disabled="disabled || element.options.disabled"
            :style="{ width: element.options.width }" />
        </template>

        <template v-if="element.type === 'daterange'">
          <el-date-picker v-model="form[element.model]" :placeholder="element.options.placeholder"
            :range-separator="element.options.rangeSeparator" :start-placeholder="element.options.startPlaceholder"
            :end-placeholder="element.options.endPlaceholder" :readonly="element.options.readonly"
            :editable="element.options.editable" :clearable="element.options.clearable" :type="element.options.type"
            :format="element.options.format" :value-format="element.options.valueFormat"
            :disabled="disabled || element.options.disabled" :style="{ width: element.options.width }" />
        </template>

        <template v-if="element.type === 'rate'">
          <el-rate v-model="form[element.model]" :max="element.options.max" :allowHalf="element.options.allowHalf"
            :disabled="disabled || element.options.disabled" />
        </template>

        <template v-if="element.type === 'select'">
          <el-select v-model="form[element.model]" :multiple="element.options.multiple"
            :collapseTags="element.options.collapseTags" :placeholder="element.options.placeholder"
            :clearable="element.options.clearable" :multipleLimit="element.options.multipleLimit"
            :filterable="element.options.filterable" :disabled="disabled || element.options.disabled"
            :style="{ width: element.options.width }">
            <el-option v-for="item of element.options.remote
              ? element.options.remoteOptions
              : element.options.options" :key="item.value" :value="item.value"
              :label="element.options.showLabel ? item.label : item.value" />
          </el-select>
        </template>

        <template v-if="element.type === 'switch'">
          <el-switch v-model="form[element.model]" :active-text="element.options.activeText"
            :inactive-text="element.options.inactiveText" :disabled="disabled || element.options.disabled" />
        </template>

        <template v-if="element.type === 'slider'">
          <el-slider v-model="form[element.model]" :min="element.options.min" :max="element.options.max"
            :step="element.options.step" :range="element.options.range" :disabled="disabled || element.options.disabled"
            :style="{ width: element.options.width }" />
        </template>

        <!-- <template v-if="element.type == 'text'">
          <span>{{ element.options.defaultValue }}</span>
        </template> -->

        <template v-if="element.type === 'img-upload'">
          <sc-upload-Multiple v-model="form[element.model]" :limit="element.options.limit"
            :file-list="element.options.defaultValue" :name="element.options.file" :disabled="element.options.disabled"
            :multiple="element.options.multiple"></sc-upload-Multiple>
        </template>

        <template v-if="element.type === 'file-upload'">
          <sc-upload-file v-model="form[element.model]" :limit="element.options.limit"
            :file-list="element.options.defaultValue" :name="element.options.file" :disabled="element.options.disabled"
            :multiple="element.options.multiple"></sc-upload-file>
        </template>
        <template v-if="element.type === 'divider'">
          <el-divider :direction="element.options.direction" :border-style="element.options.borderStyle"
            :content-position="element.options.contentPosition">
            {{ element.options.content }}
          </el-divider>
        </template>

        <template v-if="element.type === 'richtext-editor'">
          <CusEditor v-model="form[element.model]" :disable="disabled || element.options.disabled"
            :style="{ width: element.options.width }" :mode="element.options.mode" />
        </template>

        <template v-if="element.type === 'cascader'">
          <el-cascader v-model="form[element.model]" :options="element.options.remoteOptions"
            :placeholder="element.options.placeholder" :filterable="element.options.filterable"
            :clearable="element.options.clearable" :disabled="disabled || element.options.disabled"
            :style="{ width: element.options.width }" />
        </template>

        <!-- 系统字段组件 -->
        <!-- 学生 -->
        <template v-if="element.type === 'select-student'">
          <CusSelectStudent v-model="form[element.model]" :placeholder="element.options.placeholder"
            :multiple="element.options.multiple" :disabled="disabled || element.options.disabled"
            :clearable="element.options.clearable" :width="element.options.width" />
        </template>
        <!-- 教职工 -->
        <template v-if="element.type === 'select-teacher'">
          <CusSelectTeacher v-model="form[element.model]" :placeholder="element.options.placeholder"
            :multiple="element.options.multiple" :disabled="disabled || element.options.disabled"
            :clearable="element.options.clearable" :width="element.options.width" />
        </template>
        <!-- 资产 -->
        <template v-if="element.type === 'select-asset'">
          <CusSelectasset v-model="form[element.model]" :placeholder="element.options.placeholder"
            :multiple="element.options.multiple" :disabled="disabled || element.options.disabled"
            :clearable="element.options.clearable" :width="element.options.width" />
        </template>

        <template v-if="element.type === 'select-tree'">
          <CusSelectTree v-model="form[element.model]" :placeholder="element.options.placeholder"
            :multiple="element.options.multiple" :disabled="disabled || element.options.disabled"
            :clearable="element.options.clearable" :width="element.options.width" />
        </template>
        <!-- 场地 -->
        <template v-if="element.type === 'select-field'">
          <CusSelectField v-model="form[element.model]" :placeholder="element.options.placeholder"
            :multiple="element.options.multiple" :disabled="disabled || element.options.disabled"
            :clearable="element.options.clearable" :width="element.options.width" />
        </template>
        <!-- 宿舍 -->
        <template v-if="element.type === 'select-room'">
          <CusSelectRoom v-model="form[element.model]" :placeholder="element.options.placeholder"
            :multiple="element.options.multiple" :disabled="disabled || element.options.disabled"
            :clearable="element.options.clearable" :width="element.options.width" />
        </template>
        <!-- 消耗品 -->
        <template v-if="element.type === 'select-consumables'">
          <CusSelectConsumables v-model="form[element.model]" :placeholder="element.options.placeholder"
            :multiple="element.options.multiple" :disabled="disabled || element.options.disabled"
            :clearable="element.options.clearable" :style="{ width: element.options.width }" />
        </template>
        <!-- 部门 -->
        <template v-if="element.type === 'select-department'">
          <CusSelectDepartment v-model="form[element.model]" :multiple="element.options.multiple"
            :placeholder="element.options.placeholder" :clearable="element.options.clearable"
            :disabled="element.options.disabled" :width="element.options.width" />
        </template>
        <!-- 校区 -->
        <template v-if="element.type === 'select-campus'">
          <CusSelectInfo :type="element.type" v-model="form[element.model]" :multiple="element.options.multiple"
            :multipleLimit="element.options.multipleLimit" :placeholder="element.options.placeholder"
            :clearable="element.options.clearable" :disabled="element.options.disabled"
            :width="element.options.width" />
        </template>
        <!-- 学期 -->
        <template v-if="element.type === 'select-semester'">
          <CusSelectSemester v-model="form[element.model]" :multiple="element.options.multiple"
            :placeholder="element.options.placeholder" :clearable="element.options.clearable"
            :disabled="element.options.disabled" :width="element.options.width" />
        </template>
        <!-- 年级 -->
        <template v-if="element.type === 'select-grade'">
          <CusSelectInfo :type="element.type" v-model="form[element.model]" :multiple="element.options.multiple"
            :multipleLimit="element.options.multipleLimit" :placeholder="element.options.placeholder"
            :clearable="element.options.clearable" :disabled="element.options.disabled"
            :width="element.options.width" />
        </template>
        <!-- 班级 -->
        <template v-if="element.type === 'select-class'">
          <CusSelectClass v-model="form[element.model]" :multiple="element.options.multiple"
            :placeholder="element.options.placeholder" :clearable="element.options.clearable"
            :disabled="element.options.disabled" :width="element.options.width" />
        </template>
      </el-form-item>
    </template>
  </el-form>
</template>

<script>
import CusEditor from '@/components/custom/cusEditor.vue'
import CusSelectStudent from '@/components/custom/cusSelectStudent.vue'
import CusSelectTeacher from '@/components/custom/cusSelectTeacher.vue'
import CusSelectasset from '@/components/custom/cusSelectasset.vue'
import CusSelectTree from '@/components/custom/cusSelectTree.vue'
import CusSelectField from '@/components/custom/cusSelectField.vue'
import CusSelectRoom from '@/components/custom/cusSelectRoom.vue'
import CusSelectConsumables from '@/components/custom/cusSelectConsumables.vue'
import CusSelectDepartment from '@/components/custom/cusSelectDepartment.vue'
import CusSelectSemester from '@/components/custom/cusSelectSemester.vue'
import CusSelectClass from '@/components/custom/cusSelectClass.vue'
import CusSelectInfo from '@/components/custom/cusSelectInfo.vue'

export default {
  emits: ['update:modelValue'],
  components: {
    CusEditor,
    CusSelectStudent,
    CusSelectTeacher,
    CusSelectasset,
    CusSelectTree,
    CusSelectField,
    CusSelectRoom,
    CusSelectConsumables,
    CusSelectDepartment,
    CusSelectSemester,
    CusSelectClass,
    CusSelectInfo
  },
  props: {
    modelValue: {
      type: Object,
      default: () => { }
    },
    formConfig: {
      type: Object,
      default: () => { }
    },
    disabled: Boolean
  },
  data() {
    return {
      form: {}
    }
  },
  computed: {

  },
  watch: {
    modelValue: {
      handler(val) {
        this.form = val
      },
      deep: true,
      immediate: true
    },
    form: {
      handler(val) {
        this.$emit('update:modelValue', val)
      },
      deep: true
    }
  },
  methods: {
    position(value) {
      let direction
      switch (value) {
        case 1:
          direction = 'left'
          break
        case 2:
          direction = 'right'
          break
        case 3:
          direction = 'top'
          break
      }
      return direction
    },
    validate() {
      return new Promise((resolve, reject) => {
        if (!this.$refs.form) {
          reject(new Error('Form reference not found'))
          return
        }
        this.$refs.form.validate((valid, error) => {
          if (valid) {
            resolve(true)
          } else {
            reject(error)
          }
        })
      })
    },
    reset() {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    clearValidate(props) {
      if (this.$refs.form) {
        this.$refs.form.clearValidate(props)
      }
    }
  }
}
</script>
