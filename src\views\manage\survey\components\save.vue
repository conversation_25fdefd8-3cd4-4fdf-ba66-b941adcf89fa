<template>
    <el-dialog v-if="dialogFormVisible" v-model="dialogFormVisible" :title="titleMap[mode1]" width="500">
        <cusForm ref="formRef" v-model="form" :config="formConfig" :mode="mode1"></cusForm>
        <template #footer>
            <el-button @click="dialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="confirm">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance, nextTick, onMounted } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
const { campusId, tenantId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params'])
const emit = defineEmits(['success'])
let mode1 = ref('add')
const dialogFormVisible = ref(false)
const defaultData = () => {
    return {
        tenant_id: tenantId,
        campus_id: campusId,
        title: '',
        object: '',
        allow_repeat: 1,
        need_login: 1,
        begin_date: '',
        end_data: '',
    }
}
let form = ref(defaultData())
let formConfig = ref({
    labelPosition: 'right',
    size: 'medium',
    formItems: [
        {
            label: '问卷标题',
            name: 'title',
            value: null,
            component: 'input',
            options: {
                placeholder: '请输入问卷标题',
                items: []
            },
            rules: [{ required: true, message: '请输入问卷标题', trigger: 'blur' }, { min: 5, max: 20, message: '请输入5-20个字符', trigger: 'blur' }]
        },
        {
            label: '重复提交',
            name: 'allow_repeat',
            value: null,
            component: 'radio',
            options: {
                placeholder: '请选择是否允许重复提交',
                items: [
                    { label: '允许', value: 1 },
                    { label: '不允许', value: -1 },
                ]
            },
            rules: [{ required: true, message: '请选择是否允许重复提交', trigger: ['change'] }]
        },
        {
            label: '是否登录',
            name: 'need_login',
            value: null,
            component: 'radio',
            options: {
                placeholder: '请选择是否需要登录提交',
                items: [
                    { label: '是', value: 1 },
                    { label: '否', value: -1 },
                ]
            },
            rules: [{ required: true, message: '请选择是否需要登录提交', trigger: ['change'] }]
        },
        {
            label: '问卷对象',
            name: 'object',
            value: null,
            component: 'select',
            options: {
                placeholder: '请选择问卷对象',
                items: [
                    { label: '游客', value: 1 },
                    { label: '老师', value: 2 },
                    { label: '学生', value: 3 },
                    { label: '学生+老师', value: 4 },
                ]
            },
            rules: [{ required: true, message: '请选择问卷对象', trigger: 'blur' }]
        },
        {
            label: '开始时间',
            name: 'begin_date',
            value: null,
            component: 'cusDate',
            options: {
                type: 'date',
                placeholder: '请选择开始时间',
            },
        },
        {
            label: '结束时间',
            name: 'end_date',
            value: null,
            component: 'cusDate',
            options: {
                type: 'date',
                placeholder: '请选择结束时间',
            },
        },
    ]
})

let formRef = ref(null)
let titleMap = ref({ add: '新增问卷', edit: '编辑问卷' })
onMounted(async () => {

})
const open = async (mode = 'add') => {
    dialogFormVisible.value = true
    mode1.value = mode
    form.value.mode = mode
    if (mode === 'add') {
        form.value = defaultData()
        setTimeout(() => {
            formRef.value.resetFields()
        }, 0)
    }
    if (mode === 'edit') {
        nextTick(() => {
            formRef.value.resetFields()
        })
    }
}

// 监听是否登录的变化
watch(() => form.value.need_login, (newVal) => {
    const objectSelect = formConfig.value.formItems.find(item => item.name === 'object')
    if (objectSelect) {
        if (newVal === 1) { // 需要登录
            objectSelect.options.items = [
                { label: '老师', value: 2 },
                { label: '学生', value: 3 },
                { label: '学生+老师', value: 4 },
            ]
        } else { // 不需要登录
            objectSelect.options.items = [
                { label: '游客', value: 1 },
                { label: '老师', value: 2 },
                { label: '学生', value: 3 },
                { label: '学生+老师', value: 4 },
            ]
        }
        // 如果当前选择的是游客，但需要登录，则清空选择
        if (form.value.object === 1 && newVal === 1) {
            form.value.object = null
        }
    }
})

const confirm = async () => {
    await formRef.value.validate()
    let subForm = { ...form.value }
    const handleSuccess = (data) => {
        emit('success', { data, mode1: mode1.value });
        dialogFormVisible.value = false;
        ElMessage({ type: 'success', message: '操作成功' });
    };
    const handleError = (message) => {
        ElMessage({ type: 'error', message });
    };
    if (mode1.value === 'add') {
        delete subForm.id
        const res = await globalPropValue.survey.save.post(subForm)
        if (res.code === 200) {
            handleSuccess(res.data)
        } else {
            handleError(res.message);
        }
    } else if (mode1.value === 'edit') {
        const res = await globalPropValue.survey.save.post(subForm)
        if (res.code === 200) {
            handleSuccess(res.data)
        } else {
            handleError(res.message);
        }
    }
}

//表单注入数据
const setData = (data) => {
    Object.assign(form.value, data)
    // form.value = { ...data }
}

defineExpose({
    dialogFormVisible,
    open,
    setData
})
</script>

<style lang="scss" scoped></style>
