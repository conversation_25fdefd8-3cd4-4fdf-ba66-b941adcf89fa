<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-plus" @click="add">新增招生计划</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
				<el-table-column label="计划名称" prop="plan_name" width="150"></el-table-column>
				<el-table-column label="计划招生人数" prop="predict_num" width="150"></el-table-column>
				<el-table-column label="录入信息数量" prop="collected_num" width="150"></el-table-column>
				<el-table-column label="学员确认数量" prop="confirm_num" width="150"></el-table-column>
				<el-table-column label="学年学期" prop="academic_name" width="200" show-overflow-tooltip>
					<template #default="{ row }"> {{ row.academic_name }} - {{ row.semester_name }} </template>
				</el-table-column>
				<el-table-column label="开始时间" prop="start_time" width="180"></el-table-column>
				<el-table-column label="截至时间" prop="end_time" width="180"></el-table-column>
				<el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="210">
					<template #default="scope">
						<el-button-group>
							<el-button text type="primary" size="small" @click="table_edit(scope.row)">编辑</el-button>
							<el-popconfirm title="确定移除吗？" @confirm="table_del(scope.row)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<!-- 新增招生计划弹层组件 -->
	<saveDialog ref="dialog" :params="params" @success="handleSaveSuccess"></saveDialog>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick, watch } from 'vue'
import cusTom from '@/utils/cusTom'
import saveDialog from './save.vue'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null
	}
}
let apiObj = ref(globalPropValue.recruitStudent.recruit.list)
let params = ref(defaultParams())
let selectionArr = ref([])
let searchConfig = ref({
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: null,
			name: 'campus_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择校区',
				noClearable: true,
				items: campusInfo.map((v) => {
					return {
						label: v.name,
						value: v.value
					}
				})
			}
		},
		{
			label: null,
			name: 'name',
			value: null,
			component: 'input',
			options: {
				placeholder: '请输入计划名称',
				items: []
			}
		},
		{
			label: null,
			name: 'academic_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学年',
				items: []
			}
		},
		{
			label: null,
			name: 'semester_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学期',
				items: []
			}
		}
	]
})
const handleSaveSuccess = (data, mode) => {
	if (mode === 'add') {
		upsearch()
	} else {
		table.value.refresh()
	}
}
watch(
	() => params.value.campus_id,
	(val) => {
		params.value.academic_id = null
		searchConfig.value.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
			.filter((v) => v.parent_id === 0 && v.campus_id === val)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
watch(
	() => params.value.academic_id,
	() => {
		params.value.semester_id = null
		searchConfig.value.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
			.filter(
				(v) => v.parent_id !== 0 && v.parent_id === params.value.academic_id && v.campus_id === params.value.campus_id
			)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
let dialog = ref()
let table = ref()
// let CampusManagementList = ref(campusInfo)
// 搜索按钮回调
const upsearch = () => {
	table.value.upData(params.value)
}
// 重置按钮回调
const refresh = () => {
	params.value = defaultParams()
	upsearch()
}
// 新增招生计划回调
const add = () => {
	nextTick(() => {
		dialog.value.open()
	})
}
// 编辑操作
const table_edit = (row) => {
	dialog.value.dialogFormVisible = true
	nextTick(() => {
		dialog.value.open('edit')
		dialog.value.setData(row)
	})
}
// 删除操作
const table_del = async (row) => {
	const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.recruitStudent.recruit.del.post(reqData)
	if (res.code === 200) {
		upsearch()
		ElMessage({ type: 'success', message: '删除成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
// 表格选择回调
const selectionChange = (selection) => {
	selectionArr.value = selection
}
</script>

<style lang="scss" scoped></style>
