<template>
	<el-drawer
		v-model="visible"
		title="班牌背景图"
		size="50%"
		destroy-on-close
		:close-on-press-escape="false"
		@close="close"
	>
		<el-header>
			<div class="right-panel">
				<sc-upload-file
					v-model="image_url"
					accept="image/gif, image/jpeg, image/png"
					:limit="1"
					fileTypeTag="platoonHotspot"
					buttonText="更新背景图"
					tip="请选择图片上传，图片尺寸建议为1920*1080"
					@suc="save"
				></sc-upload-file>
			</div>
		</el-header>
		<div>
			<el-empty v-if="!hotspotInfo.image_url" description="班牌未配置自定义背景图"> </el-empty>
			<div v-else style="padding-top: 20px">
				<div style="text-align: right">
					<el-button type="danger" plain @click="sync">同步到所有班牌</el-button>
				</div>
				<div style="padding-top: 20px">
					<el-image :src="hotspotInfo.image_url" :preview-src-list="[hotspotInfo.image_url]" fit="contain"> </el-image>
				</div>
			</div>
		</div>
	</el-drawer>
</template>
<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'

import { ref, getCurrentInstance, defineExpose } from 'vue'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const visible = ref(false)
const hotspotInfo = ref({
	tenant_id: 0,
	campus_id: 0,
	image_url: '',
	id: '',
	device_sn: ''
})
const image_url = ref('')
const currentSn = ref('')

const sync = () => {
	//二次确认
	ElMessageBox.confirm('是否同步当前背景图到当前校区的所有班牌？', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消'
	}).then(() => {
		globalPropValue.platoonhotspot.syncBaseMap
			.post({
				tenant_id: hotspotInfo.value.tenant_id,
				campus_id: hotspotInfo.value.campus_id,
				image_url: hotspotInfo.value.image_url
			})
			.then((res) => {
				if (res.code === 200) {
					ElMessage.success('同步成功')
				} else {
					ElMessage.error('同步失败；' + res.message)
				}
			})
	})
}

const save = (res) => {
	hotspotInfo.value.image_url = res.url
	globalPropValue.platoonhotspot.saveBaseMap
		.post({
			device_sn: hotspotInfo.value.device_sn,
			tenant_id: hotspotInfo.value.tenant_id,
			campus_id: hotspotInfo.value.campus_id,
			image_url: res.url,
			id: hotspotInfo.value.id
		})
		.then((res) => {
			if (res.code === 200) {
				ElMessage.success('更新班牌背景图成功')
				image_url.value = ''
			} else {
				ElMessage.error('更新班牌背景图失败；' + res.message)
			}
		})
}
// 获取热点信息
const getInfo = (tenant_id, campus_id, sn) => {
	globalPropValue.platoonhotspot.getBaseMap
		.get({
			device_sn: sn,
			tenant_id: tenant_id,
			campus_id: campus_id
		})
		.then((res) => {
			if (res.code === 200) {
				hotspotInfo.value.image_url = res.data?.imageUrl
				hotspotInfo.value.id = res.data?.id
			}
		})
		.catch((err) => {
			console.log(`获取信息失败： ${err.message}`)
		})
}

const show = (row) => {
	visible.value = true
	hotspotInfo.value.tenant_id = row.tenant_id
	hotspotInfo.value.campus_id = row.campus_id
	hotspotInfo.value.device_sn = row.device_sn
	currentSn.value = getInfo(row.tenant_id, row.campus_id, row.device_sn)
}
const emit = defineEmits(['refresh'])
const close = () => {
	visible.value = false
	emit('refresh')
}
defineExpose({
	show,
	close
})
</script>
