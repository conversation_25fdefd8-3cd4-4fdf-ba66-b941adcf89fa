<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :name="item.name" :label="item.label" :key="item.name"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main>
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script>
import loginLog from './loginLog'
import opLog from './opLog'

export default {
	name: 'layoutTCB',
	components: {
		loginLog,
		opLog
	},
	data() {
		return {
			activeName: 'opLog',
			tabs: [
				{
					label: '操作日志',
					name: 'opLog',
					component: opLog
				},
				{
					label: '登录日志',
					name: 'loginLog',
					component: loginLog
				}
			],
			currComponent: {}
		}
	},
	created() {
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
		}
	}
}
</script>

<style></style>
