<template>
	<el-container>
		<el-tabs v-model="activeName" @tab-change="handleClick">
			<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
		</el-tabs>
		<component :is="currComponent.component"></component>
	</el-container>
</template>

<script>
import detailComponent from './detail.vue'
import commentComponent from './comment.vue'
import collectLikeComponent from './collectLike.vue'
import { shallowRef } from 'vue'
export default {
	name: 'layoutTCB',
	components: {
		collectLikeComponent,
		commentComponent,
		detailComponent
	},
	inject: ['userId'],
	provide() {
		return {
			resourcesData: this.modelValue,
			onlyShow: this.onlyShow
		}
	},
	props: {
		modelValue: {
			type: Object,
			default: () => {}
		},
		onlyShow: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			activeName: 'detail',
			tabs: [
				{
					label: '内容详情',
					name: 'detail',
					component: shallowRef(detailComponent)
				},
				{
					label: '点赞收藏记录',
					name: 'collectLike',
					component: shallowRef(collectLikeComponent)
				},
				{
					label: '评论数据',
					name: 'comment',
					component: shallowRef(commentComponent)
				}
			],
			currComponent: {},
			classData: {}
		}
	},
	created() {
		if (this.onlyShow === true) {
			this.tabs = [this.tabs[0], this.tabs[2]]
		}
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
			this.resetClassD()
		},
		resetClassD() {
			this.classData = {}
		}
	}
}
</script>

<style scoped>
.el-container {
	display: block;
}
</style>
