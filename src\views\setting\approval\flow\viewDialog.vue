<template>
	<el-dialog v-model="dialogViewVisible" title="查看" class="select-dialog" append-to-body width="800">
		<el-container v-if="applyData">
			<el-header class="view-title">
				<div>
					<el-avatar :size="45" :src="applyData.apply_info.apply_user_head">{{
						applyData.apply_info.apply_user_name[0]
					}}</el-avatar>
					<div>
						<p>
							{{ applyData.apply_info.apply_user_name + '的' + applyData.apply_info.approval_name + '审批' }}
						</p>
						<p>{{ applyData.apply_info.created_at }}</p>
					</div>
				</div>
				<div>
					<el-tag v-if="applyData.apply_info.status === 1" type="primary">审批中</el-tag>
					<el-tag v-if="applyData.apply_info.status === 2" type="success">已通过</el-tag>
					<el-tag v-if="applyData.apply_info.status === 3" type="danger">已驳回</el-tag>
					<el-tag v-if="applyData.apply_info.status === 4" type="warning">已撤销</el-tag>
				</div>
			</el-header>
			<el-container>
				<el-aside>
					<div v-if="applyData.apply_info.status === 1">
						<el-timeline v-if="applyData.apply_info?.node_infos" style="max-width: 600px; padding: 10px">
							<el-timeline-item
								v-for="(item, index) in applyData.apply_info.node_infos"
								:key="index"
								placement="top"
								:type="applyData.apply_info.current_step > index ? 'success' : 'info'"
								center
							>
								<el-card>
									<div class="node_title">
										<div>
											<h3>
												<span v-if="item.type === 0">发起申请</span>
												<span v-if="item.type === 1">审批</span>
												<span v-if="item.type === 2">抄送</span>
											</h3>
											<p v-if="item.type === 0">{{ applyData.apply_info.created_at }}</p>
											<p v-if="item.actionInfo">{{ item.actionInfo.created_at }}</p>
										</div>
										<div>
											<el-tag
												v-if="item.type === 1"
												:type="applyData.apply_info.current_step > index ? 'success' : 'info'"
											>
												<span>{{ applyData.apply_info.current_step > index ? '已审批' : '待审批' }}</span></el-tag
											>
											<el-tag
												v-if="item.type === 2"
												:type="applyData.apply_info.current_step > index ? 'success' : 'info'"
											>
												<span>{{ applyData.apply_info.current_step > index ? '已抄送' : '待抄送' }}</span></el-tag
											>
										</div>
									</div>
									<template
										v-if="item.actionInfo && item.actionInfo.length > 0 && applyData.apply_info.current_step > index"
									>
										<div class="node_user">
											<div v-for="(item2, index2) in item.actionInfo" :key="index2">
												<el-avatar :size="30" :src="item2.action_user_head">{{ item2.action_user_name[0] }}</el-avatar>
												<p>{{ item2.action_user_name }}</p>
											</div>
										</div>
									</template>
									<template v-else>
										<div v-if="item.setType === 1 || item.setType === 0" class="node_user">
											<div v-for="(item2, index2) in item.nodeUserList" :key="index2">
												<el-avatar :size="30" :src="item2.img">{{ item2.name[0] }}</el-avatar>
												<p>{{ item2.name }}</p>
											</div>
										</div>
										<div v-if="item.setType === 6" class="node_user">
											<div v-for="(item2, index2) in item.nodePositionList" :key="index2">
												<el-avatar :size="30">{{ item2.name[0] }}</el-avatar>
												<p>{{ item2.name }}</p>
											</div>
										</div>
									</template>
								</el-card>
							</el-timeline-item>
						</el-timeline>
					</div>
					<div v-else>
						<el-timeline v-if="applyData.approval_step" style="max-width: 600px; padding: 10px">
							<!--:timestamp="item.updated_at"-->
							<el-timeline-item placement="top" type="success" center>
								<el-card>
									<div class="node_title">
										<div>
											<h3>
												<span>发起申请</span>
											</h3>
											<p>{{ applyData.apply_info.created_at }}</p>
										</div>
									</div>
								</el-card>
							</el-timeline-item>
							<template v-for="(item, index) in applyData.approval_step">
								<el-timeline-item v-if="item.action_status === 2" :key="index" placement="top" type="success" center>
									<el-card>
										<div class="node_title">
											<div>
												<h3>
													<h3>
														<span v-if="item.action_type === 1">审批</span>
														<span v-if="item.action_type === 3">抄送</span>
													</h3>
												</h3>
												<p>{{ item.created_at }}</p>
											</div>
											<div>
												<el-tag v-if="item.action === 1" type="success"><span>通过</span></el-tag>
												<el-tag v-if="item.action === 2" type="warning"><span>驳回</span></el-tag>
												<el-tag v-if="item.action === 4" type="primary"><span>抄送</span></el-tag>
												<el-tag v-if="item.action === 3" type="danger"><span>撤销</span></el-tag>
											</div>
										</div>
										<div class="node_user">
											<div>
												<el-avatar :size="30" :src="item.action_user_head">{{ item.action_user_name[0] }}</el-avatar>
												<p>{{ item.action_user_name }}</p>
											</div>
										</div>
									</el-card>
								</el-timeline-item>
							</template>
						</el-timeline>
					</div>
				</el-aside>
				<el-main v-if="applyData.apply_info" style="min-height: 250px">
					<el-descriptions v-if="applyData.apply_info.apply_info" title="" column="1" direction="vertical">
						<el-descriptions-item
							v-for="item in JSON.parse(applyData.apply_info.apply_info)"
							label-class-name="info-label"
							:label="item.label"
						>
							<div v-if="isImage(item.value)">
								<el-image :src="item.value" />
							</div>
							<div v-else v-html="item.value"></div>
						</el-descriptions-item>
					</el-descriptions>
				</el-main>
			</el-container>
		</el-container>
	</el-dialog>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue'
import cusTom from '@/utils/cusTom'

const { campusId, tenantId } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const dialogViewVisible = ref(false)

const applyData = ref({})
const defaultParams = () => {
	return {
		id: null,
		tenant_id: tenantId,
		campus_id: campusId
	}
}

const params = ref(defaultParams())
const open = (id) => {
	params.value.id = id
	getApplyInfo()
}
const getApplyInfo = async () => {
	const { data } = await globalPropValue.approval.flow.detail.get(params.value)
	data.apply_info.node_infos = JSON.parse(data.apply_info.node_infos)
	data.apply_info.node_infos.map((item, index) => {
		let actionInfo = []
		data.approval_step?.forEach((i) => {
			if (i.step === index) {
				actionInfo.push(i)
			}
		})
		item.actionInfo = actionInfo
	})
	console.log(data.apply_info.node_infos)
	applyData.value = data
	dialogViewVisible.value = true
}
const isImage = (url) => {
	// 简单判断是否为图片链接
	return /\.(jpg|jpeg|png|gif|bmp)$/i.test(url)
}
defineExpose({ dialogViewVisible, open })
</script>

<style lang="scss" scoped>
.view-title {
	font-size: 15px;
	font-weight: bold;
	border-radius: 0 !important;

	> div {
		display: flex;
		align-items: center;

		div {
			margin-left: 10px;
		}
	}

	div > p:last-child {
		font-size: 12px;
		font-weight: bold;
	}
}

:deep(.el-card__body) {
	padding: 15px;
}

.node_title {
	display: flex;
	justify-content: space-between;
	line-height: 22px;

	p {
		font-size: 12px;
		color: var(--el-color-info);
	}
}

.node_user {
	display: flex;
	align-items: center;
	padding-top: 10px;

	div {
		font-size: 12px;
		color: var(--el-color-info);
		width: 45px;
		text-align: center;
		margin-right: 10px;
	}
}
</style>
<style>
.info-label {
	font-weight: bold !important;
}
</style>
