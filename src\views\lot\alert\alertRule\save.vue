<template>
	<el-drawer v-model="visible" :title="titleMap[mode]" :size="550" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-position="top">
			<el-form-item label="告警名称" prop="alert_name">
				<el-input v-model="form.alert_name" placeholder="请输入告警名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="告警级别" prop="alert_level">
				<el-select
					:key="index"
					v-model="form.alert_level"
					style="width: 100%; max-width: unset"
					placeholder="请选择告警级别"
					clearable
				>
					<el-option
						v-for="(item, index) in enumConfig.alertLevelMap"
						:key="index"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="启用状态" prop="status">
				<el-switch
					v-model="form.status"
					:active-value="1"
					:inactive-value="-1"
					active-text="开"
					inline-prompt
					style="--el-switch-on-color: #00b42a"
					inactive-text="关"
				></el-switch>
			</el-form-item>
			<el-form-item label="" prop="silence_time">
				<template #label
					>静默时间

					<el-tooltip>
						<template #content>
							若已配置静默时间，则告警规则触发告警后，在设定的时间范围内将不再重复触发告警。<br />
							超出设定时间告警仍未恢复，则会再次触发告警。
						</template>
						<el-icon size="16">
							<el-icon-QuestionFilled />
						</el-icon>
					</el-tooltip>
				</template>

				<el-select
					v-model="form.silence_time"
					style="width: 100%; max-width: unset"
					placeholder="请选择静默时间"
					clearable
				>
					<el-option
						v-for="(item, index) in silenceTimeMap"
						:key="index"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="通知方式" prop="notifyList">
				<el-checkbox-group v-model="form.notifyList">
					<el-checkbox label="通知中心" value="notify" disabled />
					<el-checkbox label="钉钉机器人" value="dingding" />
					<el-checkbox label="企业微信机器人" value="workweixin" />
					<!--					<el-checkbox label="飞书机器人" value="feishu" />-->
				</el-checkbox-group>
				<div style="padding-top: 15px; width: 100%">
					<el-card shadow="never" class="notify-card" header="通知中心">
						<el-form-item label="告警通知人" prop="alert_user">
							<cusSelectTeacher
								v-model="form.alert_user_list"
								placeholder="请选择告警通知人"
								width="100%"
								:multiple="true"
							></cusSelectTeacher>
						</el-form-item>
					</el-card>
					<el-card
						v-if="form.notifyList.indexOf('dingding') !== -1"
						shadow="never"
						header="钉钉机器人"
						class="notify-card"
					>
						<el-form-item
							label="通知地址"
							prop="notifyObj.dingding.webhook"
							:rules="{ required: true, message: '请填写webhook地址', trigger: 'blur' }"
						>
							<el-input v-model="form.notifyObj.dingding.webhook">
								<template #prepend>webhook</template>
							</el-input>
						</el-form-item>
						<el-form-item
							style="margin-top: 10px; width: 300px"
							label="生效时间"
							prop="notifyObj.dingding.effect_time"
							:rules="{ required: true, message: '请选择生效时间', trigger: 'blur' }"
						>
							<el-time-picker
								v-model="form.notifyObj.dingding.effect_time"
								is-range
								range-separator="至"
								format="HH:mm"
								value-format="HH:mm"
								start-placeholder="开始时间"
								end-placeholder="截止时间"
							/>
						</el-form-item>
					</el-card>
					<el-card
						v-if="form.notifyList.indexOf('workweixin') !== -1"
						shadow="never"
						header="企业微信机器人"
						class="notify-card"
					>
						<el-form-item
							label="通知地址"
							prop="notifyObj.workweixin.webhook"
							:rules="{ required: true, message: '请填写webhook地址', trigger: 'blur' }"
						>
							<el-input v-model="form.notifyObj.workweixin.webhook">
								<template #prepend>webhook</template>
							</el-input>
						</el-form-item>
						<el-form-item
							style="margin-top: 10px; width: 300px"
							label="生效时间"
							prop="notifyObj.workweixin.effect_time"
							:rules="{ required: true, message: '请选择生效时间', trigger: 'blur' }"
						>
							<el-time-picker
								v-model="form.notifyObj.workweixin.effect_time"
								is-range
								format="HH:mm"
								value-format="HH:mm"
								range-separator="至"
								start-placeholder="开始时间"
								end-placeholder="截止时间"
							/>
						</el-form-item>
					</el-card>

					<el-card
						v-if="form.notifyList.indexOf('feishu') !== -1"
						shadow="never"
						header="飞书机器人"
						class="notify-card"
					>
						<el-form-item
							label="通知地址"
							prop="notifyObj.feishu.webhook"
							:rules="{ required: true, message: '请填写webhook地址', trigger: 'blur' }"
						>
							<el-input v-model="form.notifyObj.feishu.webhook">
								<template #prepend>webhook</template>
							</el-input>
						</el-form-item>
						<el-form-item
							style="margin-top: 10px; width: 300px"
							label="生效时间"
							prop="notifyObj.feishu.effect_time"
							:rules="{ required: true, message: '请选择生效时间', trigger: 'blur' }"
						>
							<el-time-picker
								v-model="form.notifyObj.feishu.effect_time"
								is-range
								format="HH:mm"
								value-format="HH:mm"
								range-separator="至"
								start-placeholder="开始时间"
								end-placeholder="截止时间"
							/>
						</el-form-item>
					</el-card>
				</div>
			</el-form-item>
			<el-form-item label="告警描述" prop="alert_desc">
				<el-input v-model="form.alert_desc" rows="2" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import cusTom from '@/utils/cusTom'

const { tenantId, campusId } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		alert_name: '',
		alert_desc: '',
		alert_level: null,
		silence_time: 0,
		notify: '',
		notifyObj: {
			workweixin: {
				webhook: '',
				effect_time: []
			},
			dingding: {
				webhook: '',
				effect_time: []
			},
			feishu: {
				webhook: '',
				effect_time: []
			}
		},
		notifyList: ['notify'],
		alert_user: [],
		alert_user_list: null,
		alert_type: 1,
		status: 1
	}
}

export default {
	emits: ['success', 'closed'],
	props: {},

	data() {
		return {
			silenceTimeMap: [
				{
					value: 0,
					name: '不静默'
				},
				{
					value: 10,
					name: '10分钟'
				},
				{
					value: 20,
					name: '20分钟'
				},
				{
					value: 30,
					name: '30分钟'
				},
				{
					value: 60,
					name: '1小时'
				},
				{
					value: 180,
					name: '3小时'
				},
				{
					value: 360,
					name: '6小时'
				},
				{
					value: 720,
					name: '12小时'
				}
			],
			mode: 'add',
			titleMap: {
				add: '新增告警规则',
				edit: '编辑告警规则',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			enumConfig: [],
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				alert_name: [{ required: true, message: '请输入告警名称' }],
				alert_level: [{ required: true, message: '请选择告警级别' }],
				silence_time: [{ required: true, message: '请选择静默时间' }],
				status: [{ required: true, message: '请选择告警启用状态' }]
			}
		}
	},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	mounted() {},
	methods: {
		//显示
		open(mode = 'add', campus_id) {
			this.form.campus_id = campus_id
			this.mode = mode
			this.visible = true
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					//this.isSaveing = true
					let subForm
					if (this.mode === 'add') {
						subForm = { ...this.params, ...this.form }
					} else if (this.mode === 'edit') {
						subForm = { ...this.form }
					}
					console.log(this.form, subForm)
					let notifyAry = []

					this.form.notifyList.forEach((key) => {
						let value = this.form.notifyObj[key]
						if (value && value.webhook && value.effect_time.length > 0) {
							notifyAry.push({
								notify_type: key,
								webhook: value.webhook,
								start_effect_time: value.effect_time[0],
								end_effect_time: value.effect_time[1]
							})
						}
					})
					subForm.notify = JSON.stringify(notifyAry)

					let alertUser = []
					if (this.form.alert_user_list && this.form.alert_user_list.length > 0) {
						this.form.alert_user_list.forEach((item) => {
							alertUser.push(item.id)
						})
					} else {
						alertUser = []
					}
					subForm.alert_user = JSON.stringify(alertUser)
					var res = await this.$LotApi.alertRule.save.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			let notifyObj = {
				workweixin: {
					webhook: '',
					effect_time: []
				},
				dingding: {
					webhook: '',
					effect_time: []
				},
				feishu: {
					webhook: '',
					effect_time: []
				}
			}
			data.notify.forEach((item) => {
				this.form.notifyList.push(item.notify_type)
				notifyObj[item.notify_type] = {
					webhook: item.webhook,
					effect_time: [item.start_effect_time, item.end_effect_time]
				}
			})
			this.form.notifyObj = notifyObj
			let alert_user_list = []
			if (data && data.alert_user_list) {
				data.alert_user_list.forEach((item) => {
					alert_user_list.push({ label: item.name, id: item.id })
				})
			}
			this.form.alert_user_list = alert_user_list
		}
	}
}
</script>

<style scoped lang="scss">
:deep(.notify-card) {
	width: 100%;
	margin-bottom: 15px;

	.el-card__header {
		padding-top: 10px;
		font-size: 14px;
	}

	.el-card__body {
		padding-top: 5px;
	}
}
</style>
