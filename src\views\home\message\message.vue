<template>
	<el-container>
		<el-main>
			<scTable ref="table" :apiObj="apiObj" :params="params">
				<el-table-column label="标题" prop="title"></el-table-column>
				<el-table-column label="消息类型" prop="message_type">
					<template #default="{ row }">
						{{ formData(messageTypeMap, row.message_type) }}
					</template>
				</el-table-column>
				<el-table-column label="创建时间" prop="created_at"></el-table-column>
				<el-table-column label="状态" prop="read_status">
					<template #default="{ row }">
						<el-tag v-if="row.read_status == 1" type="success">已读</el-tag>
						<el-tag v-else type="danger">未读</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="150">
					<template #default="{ row }">
						<el-button type="text" @click="handleClick(row)">查看</el-button>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<el-drawer v-model="showMessage" :title="messageContent.title" direction="rtl">
			<div class="message-info">
				<div class="message-title">
					<p>发布时间： {{ messageContent.created_at }}</p>
				</div>
				<div class="message-content" v-html="messageContent.content"></div>
			</div>
		</el-drawer>
	</el-container>
</template>
<script>
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, messageTypeMap} = cusTom.getBaseQuery()
export default {
	data() {
		return {
			messageTypeMap,
			apiObj: this.$API.notice.message,
			params: {
				tenant_id: tenantId,
				campus_id: campusId
			},
			showMessage: false,
			messageContent: ''
		}
	},
	methods: {
		handleClick(row) {
			this.showMessage = true
			this.messageContent = row
			if (row.read_status == 1) return
			this.$API.notice.mesread
				.post({
					tenant_id: tenantId,
					campus_id: campusId,
					id: row.id
				})
				.then((res) => {
					if (res.code == 200) {
						this.$refs.table.getData(this.params)
					}
				})
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || '-'
		}
	}
}
</script>

<style lang="scss" scoped>
.message-info {
	padding: 0 10px;
}
.message-title {
	margin-bottom: 20px;
	h3 {
		font-size: 20px;
		font-weight: bold;
		margin-bottom: 10px;
	}
	p {
		font-size: 14px;
		color: #999;
	}
}
.message-content {
	font-size: 14px;
	line-height: 25px;
}
</style>
