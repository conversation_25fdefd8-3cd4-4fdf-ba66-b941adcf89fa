<template>
  <div class="drawer-body" >
    <div class="content-basic">
      <p>环境数据</p>
      <div v-if="environmentInfo" style="padding-top: 15px;">
        <el-row :gutter="20">
          <el-col :span="8"  v-for="(item,index) in environmentInfo" :key="index">
            <el-card shadow="hover">
              <template #header>
                  <span style="font-size: 16px; font-weight:bold;color: var(--el-color-primary);">{{ item.show_name }}</span>
              </template>
              <div class="demonstration">
                <p>{{ item.value_format }}</p>
              </div>
            </el-card>
          </el-col>				
        </el-row>							
      </div>
      <div v-else>
        <p style="padding: 15px;font-size: 14px; color: var(--el-color-warning);">暂无数据,请在 “教室物联关联设置” 中设置关联到教室的物联设备</p>
      </div>
    </div>
    <div class="content-basic">
      <p>空调控制</p>
      <div v-if="airConditionerInfo" style="padding-top: 15px;">
        <el-row :gutter="20">
          <el-col :span="12"  v-for="(item,index) in airConditionerInfo" :key="index">
            <el-card shadow="hover">
              <template #header>
                  <span style="font-size: 16px; font-weight:bold;color: var(--el-color-primary);">{{ index }}</span>
              </template>
              <el-form label-position="left" label-width="80">
                <el-form-item label="空调开关">
                  <el-switch
                    v-model="item.powerSwitch.value"
                    active-value="1"
                    inactive-value="0"
                    inline-prompt
                    :active-text="item.powerSwitch.tsl_info.type_spec.specs[1]"
                    :inactive-text="item.powerSwitch.tsl_info.type_spec.specs[0]"
                    @change="changeTsl(item.powerSwitch)"
                  ></el-switch>
                </el-form-item>
                <el-form-item label="空调模式" label-position="left">
                  <el-row :gutter="20">
                    <el-col
                      v-for="(item2, index2) in item.systemMode.tsl_info.type_spec.specs"
                      :key="index2"
                      :span="6"
                      class="el-col-item"
                      :class="{ selected: item.systemMode.value == item2.value }"
                      @click="item.systemMode.value = item2.value;changeTsl(item.systemMode)"
                    >
                      <p><cusSvgIcon :iconClass="'mode_' + item2.value" /></p>
                      <p>{{ item2.name }} </p>
                    </el-col>
                  </el-row>
                </el-form-item>
                <el-form-item label="空调风速" label-position="left">
                  <el-row :gutter="20">
                    <el-col
                      v-for="(item2, index2) in item.fanMode.tsl_info.type_spec.specs"
                      :key="index2"
                      :span="6"
                      class="el-col-item"
                      :class="{ selected: item.fanMode.value == item2.value }"
                      @click="item.fanMode.value = item2.value;changeTsl(item.fanMode)"
                    >
                      <p><cusSvgIcon :iconClass="'fan_' + item2.value" /></p>
                      <p>{{ item2.name }}</p>
                    </el-col>
                  </el-row>
                </el-form-item>
                <el-form-item label="设定温度" label-position="left">
                  <el-input-number
                    v-model="item.targetTemperature.value"
                    :step="1"
                    @change="changeTsl(item.targetTemperature)"
                    step-strictly
                  />
                </el-form-item>
			      </el-form>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <p style="padding: 15px;font-size: 14px; color: var(--el-color-warning);">暂无数据,请在 “教室物联关联设置” 中设置关联到教室的物联设备</p>
      </div>
    </div>
    <div class="content-basic">
      <p>灯光控制</p>
      <div v-if="lamplightInfo" style="padding-top: 15px;">
        <el-row :gutter="20">
          <el-col :span="8"  v-for="(item,index) in lamplightInfo" :key="index">
            <el-card shadow="hover">
              <template #header>
                  <span style="font-size: 16px; font-weight:bold;color: var(--el-color-primary);">{{ index }}</span>
              </template>
              <el-form label-position="left" label-width="80">
                <el-form-item label="灯光开关">
                  <el-switch
                    v-model="item.powerSwitch.value"
                    active-value="1"
                    inactive-value="0"
                    inline-prompt
                    :active-text="item.powerSwitch.tsl_info.type_spec.specs[1]"
                    :inactive-text="item.powerSwitch.tsl_info.type_spec.specs[0]"
                    @change="changeTsl(item.powerSwitch)"
                  ></el-switch>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <p style="padding: 15px;font-size: 14px; color: var(--el-color-warning);">暂无数据,请在 “教室物联关联设置” 中设置关联到教室的物联设备</p>
      </div>
    </div>
    <div class="content-basic">
      <p>窗帘控制</p>
      <div v-if="curtainInfo" style="padding-top: 15px;">
        <el-row :gutter="20">
          <el-col :span="12"  v-for="(item,index) in curtainInfo" :key="index">
            <el-card shadow="hover">
              <template #header>
                  <span style="font-size: 16px; font-weight:bold;color: var(--el-color-primary);">{{ index }}</span>
              </template>
              <el-form label-position="left" label-width="80">
                <el-form-item label="开启比例">
                  <el-slider v-model="item.positionPercentage.value" :step="10"  @change="changeTsl(item.positionPercentage)"/>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <p style="padding: 15px;font-size: 14px; color: var(--el-color-warning);">暂无数据,请在 “教室物联关联设置” 中设置关联到教室的物联设备</p>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, defineExpose, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$LotApi
const props = defineProps({
  roomId: {
    type: Number,
    default: 0
  }
})
const environmentInfo = ref(null)
const airConditionerInfo = ref(null)
const lamplightInfo = ref(null)
const curtainInfo = ref(null)

const getEnvironment = () => {
  globalPropValue.room.getEnvironment.get({
    room_id: props.roomId,
    tenant_id: tenantId,
    campus_id: campusId
  }).then(res => {
    environmentInfo.value = res.data
  })
}

const getAirConditioner = () => {
  globalPropValue.room.getAirConditioner.get({
    room_id: props.roomId,
    tenant_id: tenantId,
    campus_id: campusId
  }).then(res => {
    if(res.data.length > 0){
      let acc = {}
      res.data.forEach(item => {
        if (!acc[item.group_name]) {
          acc[item.group_name] = {};
        }
        if(item.tsl_info.type_spec.specs){
          item.tsl_info.type_spec.specs=JSON.parse(item.tsl_info.type_spec.specs)
        }
        acc[item.group_name][item.child_argument] = item;
      });
      airConditionerInfo.value = acc;
    }else{
      airConditionerInfo.value = null
    }
    console.log(airConditionerInfo.value)
  })
}
const changeArr = (arr) => {
  return arr.reduce((acc, item) => {
    const groupIndex = acc.findIndex(
      (group) => group[0].group_name === item.group_name
    );
    if (groupIndex !== -1) {
      acc[groupIndex].push(item);
    } else {
      acc.push([item]);
    }
    return acc;
  }, []);
};
const getLamplight = () => {
  globalPropValue.room.getLamplight.get({
    room_id: props.roomId,
    tenant_id: tenantId,
    campus_id: campusId
  }).then(res => {
    let acc = {}
      res.data.forEach(item => {
        if (!acc[item.group_name]) {
          acc[item.group_name] = {};
        }
        if(item.tsl_info.type_spec.specs){
          item.tsl_info.type_spec.specs=JSON.parse(item.tsl_info.type_spec.specs)
        }
        acc[item.group_name][item.child_argument] = item;
      });
    lamplightInfo.value = acc
    console.log(lamplightInfo.value)
  })
}

const getCurtain = () => {
  globalPropValue.room.getCurtain.get({
    room_id: props.roomId,
    tenant_id: tenantId,
    campus_id: campusId
  }).then(res => {
    let acc = {}
      res.data.forEach(item => {
        if (!acc[item.group_name]) {
          acc[item.group_name] = {};
        }
        if(item.tsl_info.type_spec.specs){
          item.tsl_info.type_spec.specs=JSON.parse(item.tsl_info.type_spec.specs)
        }
        acc[item.group_name][item.child_argument] = item;
      });
    curtainInfo.value = acc
    console.log(curtainInfo.value)
  })
}

const changeTsl=(item)=>{
  console.log(item)
  let property = []
				if (
					item &&
					item.value !== '' &&
					(item.tsl_info.access_mode === 2 || item.tsl_info.access_mode === 3)
				) {
					if (!item.value) {
						item.value = 0
					}
					property.push({
						tsl_id: item.tsl_id,
						value: item.value.toString()
					})
				}
			if (property.length === 0) {
				ElMessage.error('请选择要设置的属性和设置值！')
				return
			}
			let reqData = {
				device_id: item.device_id,
				tenant_id: item.tenant_id,
				campus_id: item.campus_id,
				property: property
			}
			globalPropValue.device.writeDevProperty.post(reqData).then((res) => {
				if (res.code === 200) {
					ElMessage.success('执行成功！')
				}
			})
}

onMounted(() => {
  getEnvironment()
  getAirConditioner()
  getLamplight()
  getCurtain()
})

</script>


<style lang="scss" scoped>
.content-basic {
  padding: 8px 0;
  line-height: 25px;

  >p {
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e5e5;
  }

  >div {
    span {
      margin-right: 15px;
      font-size: 13px;
    }
  }
}
.item-info{
		margin-bottom: 0px;
		height: 30px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		line-height: 30px;
	}
  .demonstration p:nth-child(1) {
    font-weight: bold;
    font-size: 18px;
  }
  
.el-col-item {
	text-align: center;
	line-height: 20px;
  min-width: 65px;
	cursor: pointer;
	.svg-icon {
		font-size: 36px;
	}
}

.selected {
	color: var(--el-color-primary);
}
</style>