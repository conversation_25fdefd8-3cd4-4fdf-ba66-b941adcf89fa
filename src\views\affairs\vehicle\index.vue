<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script setup>
import car from './car'
import approve from './approve'
import { ref } from 'vue'
const activeName = ref('car')
const currComponent = ref({
	name: 'car',
	component: car
})
const tabs = [
	{
		name: 'car',
		label: '车辆管理',
		component: car
	},
	{
		name: 'approve',
		label: '用车审批',
		component: approve
	}
]
const handleClick = (name) => {
	currComponent.value = tabs.find((item) => item.name === name)
}
</script>
<style lang="scss" scoped></style>
