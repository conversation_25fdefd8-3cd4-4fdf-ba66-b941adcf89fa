<template>
	<el-dialog v-if="dialogFormVisible" v-model="dialogFormVisible" :title="titleMap[mode1]" width="500">
		<cusForm ref="formref" v-model="form" :config="formConfig" :mode1="mode1"> </cusForm>
		<template #footer>
			<el-button @click="dialogFormVisible = false">取消</el-button>
			<el-button type="primary" @click="confirm">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance, nextTick } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
const { semesterInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params'])
const emit = defineEmits(['success'])
let mode1 = ref('add')
const dialogFormVisible = ref(false)
const defaultData = () => {
	return {
		id: null,
		plan_name: '',
		plan_code: '',
		predict_num: null,
		start_time: '',
		end_time: '',
		remark: '',
		semester_id: null,
		academic_id: null,
		campus_id: null,
		tenant_id: null,
		mode: null
	}
}
let form = ref(defaultData())
const validatePlanCode = (rule, value, callback) => {
	if (/[A-Za-z0-9]/g.test(value)) {
		callback()
	} else {
		callback('编号格式有误')
	}
}
let formConfig = ref({
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: '计划名称',
			name: 'plan_name',
			value: null,
			component: 'input',
			options: {
				placeholder: '请输入计划名称',
				items: []
			},
			rules: [{ required: true, message: '请输入计划名称', trigger: 'blur' }]
		},
		{
			label: '编号',
			name: 'plan_code',
			value: null,
			component: 'input',
			options: {
				placeholder: '请输入计划编号',
				items: []
			},
			rules: [
				{ required: true, message: '请输入计划编号', trigger: 'blur' },
				{ validator: validatePlanCode, trigger: 'blur' }
			]
		},
		{
			label: '计划人数',
			name: 'predict_num',
			value: null,
			component: 'number',
			options: {
				placeholder: '请输入计划人数',
				items: []
			},
			rules: [{ required: true, message: '请输入计划人数', trigger: 'blur' }]
		},
		{
			label: '学年',
			name: 'academic_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学年',
				items: []
			},
			rules: [{ required: true, message: '请输入', trigger: 'blur' }]
		},
		{
			label: '学期',
			name: 'semester_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学期',
				items: []
			},
			rules: [{ required: true, message: '请输入', trigger: 'blur' }]
		},
		{
			label: '开始时间',
			name: 'start_time',
			value: null,
			component: 'date',
			options: {
				items: [],
				type: 'datetime',
				valueFormat: 'YYYY-MM-DD hh:mm:ss',
				width: '216px'
			}
		},
		{
			label: '结束时间',
			name: 'end_time',
			value: null,
			component: 'date',
			options: {
				items: [],
				type: 'datetime',
				valueFormat: 'YYYY-MM-DD hh:mm:ss',
				width: '216px'
			}
		},
		{
			label: '备注',
			name: 'remark',
			value: null,
			component: 'textarea',
			options: {
				placeholder: '请输入...',
				items: []
			},
			rules: [{ required: true, message: '请输入备注', trigger: 'blur' }]
		}
	]
})
let formref = ref(null)
let titleMap = ref({ add: '新增', edit: '编辑' })
const open = async (mode = 'add') => {
	dialogFormVisible.value = true
	mode1.value = mode
	form.value.mode = mode
	if (mode === 'add') {
		form.value = defaultData()
		form.value.campus_id = props.params.campus_id
		form.value.tenant_id = props.params.tenant_id
		setTimeout(() => {
			formref.value.resetFields()
		}, 0)
	}
	if (mode === 'edit') {
		nextTick(() => {
			formref.value.resetFields()
		})
	}
}
const confirm = async () => {
	await formref.value.validate()
	let subForm
	if (mode1.value === 'add') {
		subForm = { ...form.value }
	} else if (mode1.value === 'edit') {
		subForm = { ...form.value }
	}
	const res = await globalPropValue.recruitStudent.recruit.add_edit.post(subForm)
	if (res.code === 200) {
		emit('success', form.value, mode1.value)
		dialogFormVisible.value = false
		ElMessage({ type: 'success', message: '操作成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}

watch(
	() => form.value.campus_id,
	(val) => {
		console.log(val)
		form.value.academic_id = null
		formConfig.value.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
			.filter((v) => v.parent_id === 0 && v.campus_id === val)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
watch(
	() => form.value.academic_id,
	(val) => {
		console.log(val)
		form.value.semester_id = null
		formConfig.value.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
			.filter(
				(v) => v.parent_id !== 0 && v.parent_id === form.value.academic_id && v.campus_id === form.value.campus_id
			)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
// )
//表单注入数据
const setData = (data) => {
	form.value = { ...data }
}

defineExpose({
	dialogFormVisible,
	open,
	setData
})
</script>

<style lang="scss" scoped></style>
