<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<el-radio-group v-model="recommendStatus" @change="recommendChange">
					<el-radio-button label="全部" :value="0" />
					<el-radio-button label="已推荐" :value="1" />
				</el-radio-group>
			</div>
			<div class="right-panel">
				<!-- <el-button type="primary" v-if="recommendStatus === 0" @click="recommendAll">推送到班级班牌热点</el-button> -->
				<el-button @click="add">新增班级风采</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :page-size="10" :apiObj="apiObj">
				<!-- <el-table-column type="selection" width="55"></el-table-column> -->
				<el-table-column label="图片" prop="url" width="130" align="center" fixed="left">
					<template #default="scope">
						<cusImage
							v-if="scope.row.is_video === -1"
							loading="lazy"
							:lazy="true"
							fit="cover"
							style="width: 100px; height: 100px"
							:src="scope.row.url"
							:preview-src-list="[scope.row.url]"
							preview-teleported
						/>
						<cusImage
							v-else
							loading="lazy"
							:lazy="true"
							fit="cover"
							style="width: 100px; height: 100px"
							src=""
							preview-teleported
						/>
					</template>
				</el-table-column>
				<el-table-column label="标题" prop="title">
					<template #default="scope">
						<div class="title">
							<el-tag v-if="scope.row.is_video === 1">视频</el-tag>
							<el-link
								v-if="scope.row.is_video === 1"
								:underline="false"
								type="primary"
								style="margin-left: 10px"
								@click="video_show(scope.row, scope.$index)"
								>{{ scope.row.title }}</el-link
							>
							<span v-else>{{ scope.row.title }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="排序" prop="listorder" width="100"></el-table-column>
				<el-table-column label="状态" prop="recommend" width="100">
					<template #default="scope">
						<!-- <el-tag v-if="scope.row.recommend === 1" type="success">已推荐</el-tag>
						<el-tag v-else type="info">未推荐</el-tag> -->
					<el-switch
							v-model="scope.row.recommend"
							inline-prompt
							active-color="#00B42A"
							inactive-color="#FF7D00"
							:active-value="1"
							:inactive-value="-1"
							active-text="已推荐"
							inactive-text="未推荐"
							@change="
								(val) => {
									recommend(val, scope.row)
								}
							"
						/> 
					</template>
				</el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="170">
					<template #default="scope">
						<el-button-group>
							<!-- <el-button
								v-if="scope.row.recommend === 1"
								text
								type="primary"
								size="small"
								@click="recommend(-1, scope.row)"
								>取消推荐</el-button
							>
							<el-button
								v-if="scope.row.recommend === -1"
								text
								type="primary"
								size="small"
								@click="recommend(1, scope.row)"
								>推荐</el-button
							> -->
							<el-button
								text
								:disabled="scope.row.recommend === 1"
								type="primary"
								size="small"
								@click="table_edit(scope.row, scope.$index)"
								>编辑</el-button
							>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<el-dialog
		v-model="videoDialogVisible"
		width="800px"
		:before-close="
			() => {
				videoDialogVisible = false
				$refs.videoPlayer.pause()
			}
		"
	>
		<template #title>
			<div class="title">{{ current.title }}</div>
		</template>
		<div class="video-box">
			<video
				ref="videoPlayer"
				controls
				style="width: 100%; height: 100%; background-color: #000"
				controlslist="nodownload"
			>
				<source :src="current.url" />
				<p>当前设备不支持 HTML5 视频。</p>
			</video>
		</div>
	</el-dialog>
	<save-dialog
		v-if="savedialogVisible"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="savedialogVisible = false"
	></save-dialog>
</template>
<script>
import cusTom from '@/utils/cusTom'
import cusImage from '@/components/custom/cusImage.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import saveDialog from './saveMien.vue'

const { campusId, tenantId } = cusTom.getBaseQuery()
export default {
	components: { saveDialog, cusImage },
	props: {
		data: {
			tpye: Array,
			default: () => []
		}
	},
	data() {
		return {
			campusId,
			tenantId,
			apiObj: this.$API.classMien.list,
			params: {},
			recommendStatus: 0,
			list: {},
			dialogTitle: '通过',
			dialogFormVisible: false,
			savedialogVisible: false,
			form: {
				audit_remark: null
			},
			videoDialogVisible: false,
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: '审批意见',
						name: 'audit_remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入审批意见',
							items: []
						},
						rules: [{ required: true, message: '请输入审批意见', trigger: 'blur' }]
					}
				]
			}
		}
	},
	created() {
		this.params = {
			class_id: parseInt(this.$route.query.id),
			tenant_id: tenantId,
			campus_id: campusId,
			recommend: this.recommendStatus
		}
	},
	methods: {
		//编辑
		table_edit(row) {
			this.savedialogVisible = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		add() {
			this.savedialogVisible = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add')
			})
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.$refs.table.upData(this.params)
			} else {
				this.$refs.table.refresh()
			}
		},
		video_show(item, index) {
			this.videoDialogVisible = true
			this.current = item
		},
		async table_del(row, index) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.classMien.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.$refs.table.refresh()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		recommendChange(val) {
			this.recommendStatus = val
			this.params.recommend = val
			this.$refs.table.upData(this.params)
		},
		recommend(val, item) {
			if (item.is_video === 1 && val === 1) {
				ElMessageBox.confirm(`推荐视频后，其他推荐将会取消?`, '提示', {
					confirmButtonText: '确认',
					cancelButtonText: '取消',
					type: 'warning'
				})
					.then(() => {
						this.recommendAction(val, item)
					})
					.catch(() => {
						item.recommend = -1
					})
			} else {
				this.recommendAction(val, item)
			}
		},
		recommendAction(val, item) {
			this.$API.classMien.save.post(item).then((res) => {
				if (res.code === 200) {
					ElMessage({ type: 'success', message: '操作成功！' })
					this.$refs.table.refresh()
				} else {
					ElMessage({ type: 'error', message: res.message })
				}
			})
		},
		confirm() {
			this.$refs.formref.validate((valid) => {
				if (valid) {
					let params = {
						id: this.form.id,
						tenant_id: tenantId,
						campus_id: campusId,
						audit_action: this.form.audit_action,
						audit_remark: this.form.audit_remark
					}
					this.$API.applyLeave.audit.post(params).then((res) => {
						if (res.code == 200) {
							ElMessage.success(res.message)
						} else {
							ElMessage.error(res.message)
						}
						this.$refs.table.upData(this.params)
					})
					this.dialogFormVisible = false
					this.$refs.formref.resetFields()
				}
			})
		}
	}
}
</script>
<style lang="scss" scoped>
.mien {
	display: flex;
	flex-wrap: wrap;

	li {
		width: 200px;
		height: 290px;
		margin: 0 20px 20px 0px;
		border: 1px solid var(--el-border-color-light);
		border-radius: 6px;
	}
}
</style>
