<template>
	<el-alert type="warning" :closable="false">
		<el-icon><el-icon-WarningFilled /></el-icon> 因查询性能要求，只能查询一个月内的数据</el-alert
	>
	<div class="left-panel" style="width: 100%; margin-top: 10px">
		<div class="left-panel-search">
			<el-form-item label="">
				<el-input v-model="params.function_code" placeholder="请输入功能码"></el-input>
			</el-form-item>
			<el-form-item label="">
				<el-date-picker
					v-model="params.time"
					type="datetimerange"
					:shortcuts="shortcuts"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					value-format="YYYY-MM-DD HH:mm:ss"
					:disabled-date="disabledDate"
				/>
			</el-form-item>
			<el-form-item label="">
				<el-button type="primary" icon="el-icon-search" @click="upsearch">查询</el-button>
				<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
			</el-form-item>
		</div>
	</div>
	<el-table
		ref="table"
		v-loading="loading"
		empty-text="当前所选日期时间内无数据"
		row-key="id"
		:data="logData"
		size="small"
	>
		<el-table-column label="时间" prop="report_time" width="180"></el-table-column>
		<el-table-column label="序列号" prop="seq" width="150"></el-table-column>
		<el-table-column label="功能码" prop="function_code" width="180"></el-table-column>
		<el-table-column label="topic" prop="topic" width="200"></el-table-column>
		<el-table-column label="数据载荷" prop="payload">
			<template v-slot="scope">
				<div class="payload">{{ scope.row.payload }}</div>
			</template>
		</el-table-column>
		<el-table-column label="操作" fixed="right" align="center" width="150">
			<template #default="scope">
				<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">原始数据</el-button>
			</template>
		</el-table-column>
	</el-table>
	<div class="page">
		<el-pagination
			v-if="total > 0"
			v-model:current-page="params.page"
			:page-sizes="[10, 20, 30, 50, 100]"
			:page-size="params.pageSize"
			size="small"
			background
			layout="total,sizes, prev, pager, next,jumper"
			:total="total"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		/>
	</div>
	<el-dialog v-model="showOriginalData" title="原始数据" width="500px">
		<codemirror
			ref="cmRef"
			v-model:value="originalData"
			:autofocus="true"
			:indent-with-tab="true"
			placeholder="请输入正确格式的物模型JSON"
			:options="cmOptions"
			height="400px"
			border
			width="100%"
		>
		</codemirror>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/theme/dracula.css'
import Codemirror from 'codemirror-editor-vue3'
import dayjs from 'dayjs'

export default {
	name: 'logList',
	props: {
		deviceInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			currentPage1: 1,
			showOriginalData: false,
			loading: false,
			originalData: null,
			total: 0,
			cmOptions: {
				mode: 'text/javascript',
				theme: 'dracula',
				readOnly: true,
				lineNumbers: true,
				smartIndent: true, //智能缩进
				indentUnit: 4, // 智能缩进单元长度为 4 个空格
				matchBrackets: true, //每当光标位于匹配的方括号旁边时，都会使其高亮显示
				autofocus: true,
				showCursorWhenSelecting: true,
				autoRefresh: true,
				tabSize: 2,
				lineWiseCopyCut: true,
				gutters: ['CodeMirror-lint-markers'],
				foldGutter: true,
				styleActiveLine: true,
				line: true
			},
			logData: [],
			tslList: [],
			enumConfig: {},
			params: {
				tenant_id: null,
				campus_id: null,
				device_id: null,
				function_code: null,
				time: [],
				begin_time: null,
				end_time: null,
				page: 1,
				pageSize: 10
			},
			shortcuts: [
				{
					text: '最近1小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 1)
						return [start, end]
					}
				},
				{
					text: '最近12小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 12)
						return [start, end]
					}
				},
				{
					text: '最近24小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 24)
						return [start, end]
					}
				}
			]
		}
	},
	components: { Codemirror },
	watch: {},
	created() {
		this.params.time = [dayjs().add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
		if (this.deviceInfo) {
			this.params.device_id = this.deviceInfo.id
			this.params.tenant_id = this.deviceInfo.tenant_id
			this.params.campus_id = this.deviceInfo.campus_id
			this.getList()
		}
	},
	computed: {},
	methods: {
		disabledDate(time) {
			const oneMonthLater = new Date()
			oneMonthLater.setMonth(oneMonthLater.getMonth() - 1) // 获取一个月前的日期

			// 返回true表示禁用, false表示不禁用
			// 禁用早于今天的日期和晚于一个月后的日期
			return time.getTime() > Date.now() || time.getTime() < oneMonthLater
		},
		table_show(row) {
			this.showOriginalData = true
			this.originalData = JSON.stringify(JSON.parse(row.original), null, 4)
		},
		handleSizeChange(page_size) {
			this.params.pageSize = page_size
			this.getList()
		},
		handleCurrentChange(page) {
			this.params.page = page
			this.getList()
		},
		//搜索
		upsearch() {
			this.params.page = 1
			this.getList()
		},
		async getList() {
			this.loading = true
			if (this.params.time) {
				this.params.begin_time = this.params.time[0]
				this.params.end_time = this.params.time[1]
			} else {
				this.params.begin_time = null
				this.params.end_time = null
			}
			const res = await this.$LotApi.device.getDeviceOriginalLog.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.logData = res.data.rows
				this.total = res.data.total
			}
		},
		refresh() {
			this.params.function_code = null
			this.params.page = 1
			this.params.time = [dayjs().add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
			this.getList()
		}
	}
}
</script>

<style lang="scss" scoped>
.left-panel {
	padding-bottom: 15px;
}

.page {
	padding: 20px;
	display: flex;
	justify-content: flex-end;
}
.payload {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
</style>
