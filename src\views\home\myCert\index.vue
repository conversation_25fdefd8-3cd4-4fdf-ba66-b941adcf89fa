<template>
	<el-container>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
				<el-table-column label="学年学期" prop="academic_name" width="180" show-overflow-tooltip>
					<template #default="{ row }"> {{ row.academic_name }} - {{ row.semester_name }}</template>
				</el-table-column>
				<el-table-column label="证书类型" prop="signed" width="100">
					<template #default="{ row }">
						{{ formData(certTypeMap, row.type) }}
					</template>
				</el-table-column>
				<el-table-column label="证书编号" prop="cert_number" width="150">
					<template #default="{ row }">
						{{ row.cert_number }}
					</template>
				</el-table-column>
				<el-table-column label="获奖名称" prop="award_name" max-width="200" min-width="100" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.award_name }}
					</template>
				</el-table-column>
				<el-table-column label="获奖名次" prop="award_ranking" width="150" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.award_ranking }}
					</template>
				</el-table-column>
				<el-table-column label="获奖信息" prop="award_desc" width="150" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.award_desc }}
					</template>
				</el-table-column>
				<el-table-column label="获奖日期" prop="award_date" width="150">
					<template #default="{ row }">
						{{ row.award_date }}
					</template>
				</el-table-column>
				<el-table-column label="创建人" prop="creator" width="100">
					<template #default="scope">
						<span v-if="scope.row.creator">{{ scope.row.creator.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="200"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="180">
					<template #default="scope">
						<el-button-group>
							<el-button
								text
								type="primary"
								:disabled="scope.row.status < 7 || scope.row.cert_file_url == ''"
								size="small"
								@click="show_cert(scope.row)"
								>查看证书
							</el-button>
							<el-button
								text
								type="primary"
								:disabled="scope.row.status < 7 || scope.row.cert_file_url == ''"
								size="small"
								@click="handleDownload(scope.row)"
								>下载证书
							</el-button>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
import { preview } from 'v-preview-image'

const { campusId, tenantId, campusInfo, semesterInfo, certTypeMap, certObjectMap, certStatusMap } =
	cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: null,
		name: null,
		type: null,
		object: null,
		audit: 1
	}
}
export default {
	name: '',
	components: {},
	data() {
		return {
			imageViewerVisible: false,
			imageViewerSrc: '',
			certTypeMap,
			certObjectMap,
			certStatusMap,
			semesterInfo,
			certStatusTagMap: {
				1: 'info',
				2: 'primary',
				3: 'success',
				4: 'danger',
				5: 'primary',
				6: 'warning',
				7: 'success',
				8: 'warning',
				9: 'success'
			},
			dialog: {
				save: false
			},
			apiObj: this.$API.cert.cert.my,
			selection: [],
			params: defaultParams(),
			CampusManagementList: campusInfo,
			treeData: null,
			disciplineOptions: []
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.semester_id = null
			},
			immediate: true
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	async created() {},
	methods: {
		handleDownload(row) {
			let url = row.cert_file_url
			let imgName =
				row.academic_name + row.semester_name + row.award_name + '_' + row.objecter?.name + '_' + row.cert_number
			let image = new Image()
			image.setAttribute('crossOrigin', 'anonymous') //设置图像的跨域属性
			image.src = url
			image.onload = () => {
				let canvas = document.createElement('canvas') //创建canvas
				canvas.width = image.width
				canvas.height = image.height
				let ctx = canvas.getContext('2d')
				ctx.drawImage(image, 0, 0, image.width, image.height)
				//图片转为文件流
				canvas.toBlob((blob) => {
					let url = URL.createObjectURL(blob)
					this.download(url, imgName)
					// 用完释放URL对象
					URL.revokeObjectURL(url)
				})
			}
		},
		//下载图片 创建a标签的方法
		download(href, name) {
			let eleLink = document.createElement('a')
			eleLink.download = name
			eleLink.href = href
			eleLink.click()
			eleLink.remove()
		},
		downloadPdf(blob, fileName, fileType) {
			let downloadElement = document.createElement('a')
			let href = blob
			downloadElement.href = href
			downloadElement.download = fileName + '.' + fileType //下载后文件名
			document.body.appendChild(downloadElement)
			downloadElement.click() //触发点击下载
			document.body.removeChild(downloadElement) //下载完成移除元素
		},
		semesterChange(val) {
			this.params.semester_id = val
			this.$refs.table.upData(this.params)
		},
		campusChange(val) {
			this.params.campus_id = val
			this.params.semester_id = null
			this.$refs.table.upData(this.params)
		},
		show_cert(row) {
			if (row.cert_file_url === '') {
				return
			}
			preview(row.cert_file_url)
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value === val)?.name || '-'
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else {
				this.$refs.table.refresh()
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.el-header > .left-panel {
	flex: 3;
}
</style>
