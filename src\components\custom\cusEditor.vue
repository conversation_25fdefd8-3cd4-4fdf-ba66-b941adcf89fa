<template>
	<div style="border: 1px solid #dcdfe6; z-index: 110; width: 100%">
		<Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
		<Editor
			v-model="valueHtml"
			:style="{ height: height }"
			:defaultConfig="editorConfig"
			:mode="mode"
			@onCreated="handleCreated"
			@onChange="handleChange"
		/>
	</div>
</template>
<script>
import '@wangeditor/editor/dist/css/style.css'
import { defineComponent, onBeforeUnmount, ref, shallowRef, onMounted, watch, getCurrentInstance } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import tool from '@/utils/tool'
import { putObjectBig } from '@/utils/ossLib'
import { ElMessage } from 'element-plus'

export default defineComponent({
	components: { Editor, Toolbar },
	props: {
		modelValue: null,
		readOnly: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: String,
			default: '请输入内容'
		},
		value: {
			type: String,
			default: ''
		},
		height: {
			type: String,
			default: '250px'
		},
		mode: {
			type: String,
			default: 'default' // 或者 'simple'精简模式
		},
		fileTypeTag: {
			type: String,
			default: 'editor'
		},
		excludeKeys: {
			type: Array,
			default: () => []
		},
		toolbarKeys: {
			type: Array,
			default: () => []
		}
	},
	setup(props, { emit }) {
		const { proxy } = getCurrentInstance()
		const editorRef = shallowRef()
		let valueHtml = ref(props.modelValue)
		const toolbarConfig = {
			excludeKeys: props.excludeKeys
		}
		if (props.toolbarKeys.length > 0) {
			toolbarConfig.toolbarKeys = props.toolbarKeys
		}
		console.log(toolbarConfig)
		const editorConfig = { MENU_CONF: {} }
		editorConfig.MENU_CONF['uploadImage'] = {
			// server: proxy.$API.common.upload.url,
			fieldName: 'file',
			// 单个文件的最大体积限制，默认为 2M
			maxFileSize: 20 * 1024 * 1024, // 1M
			// 最多可上传几个文件，默认为 100
			maxNumberOfFiles: 10,
			// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
			allowedFileTypes: ['image/*'],
			// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
			meta: {
				name: 'img'
			},
			// 将 meta 拼接到 url 参数中，默认 false
			metaWithUrl: false,
			// 自定义增加 http  header
			headers: {
				Authorization: tool.cookie.get('USER_TOKEN')
			},
			// 跨域是否传递 cookie ，默认为 false
			withCredentials: true,
			// 超时时间，默认为 10 秒
			timeout: 5 * 1000, // 5 秒
			// customInsert(res, insertFn) {
			//   let url = res.data.url
			//   insertFn(url)
			// },
			async customUpload(file, insertFn) {
				var result = await putObjectBig(file, file.name, props.fileTypeTag)
				if (result) {
					insertFn(result.url)
				} else {
					ElMessage.error('上传失败')
				}
			}
		}
		editorConfig.MENU_CONF['uploadVideo'] = {
			// form-data fieldName ，默认值 'wangeditor-uploaded-video'
			fieldName: 'your-custom-name',

			// 单个文件的最大体积限制，默认为 10M
			maxFileSize: 100 * 1024 * 1024, // 100M

			// 最多可上传几个文件，默认为 5
			maxNumberOfFiles: 3,

			// 选择文件时的类型限制，默认为 ['video/*'] 。如不想限制，则设置为 []
			allowedFileTypes: ['video/*'],

			// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
			meta: {
				name: 'video'
			},

			// 将 meta 拼接到 url 参数中，默认 false
			metaWithUrl: false,

			// 自定义增加 http  header
			headers: {
				Authorization: tool.cookie.get('USER_TOKEN')
			},

			// 跨域是否传递 cookie ，默认为 false
			withCredentials: true,

			// 超时时间，默认为 30 秒
			timeout: 15 * 1000, // 15 秒

			// 视频不支持 base64 格式插入
			async customUpload(file, insertFn) {
				var result = await putObjectBig(file, file.name,props.fileTypeTag)
				if (result) {
					insertFn(result.url)
				} else {
					ElMessage.error('上传失败')
				}
			}
		}
		editorConfig.MENU_CONF['fontSize'] = {
			fontSizeList: [
				'12px',
				'14px',
				'16px',
				'18px',
				'20px',
				'24px',
				'28px',
				'30px',
				'32px',
				'34px',
				'36px',
				'38px',
				'40px',
				'42px',
				'50px',
				'55px',
				'60px',
				'65px',
				'70px',
				'80px',
				'90px',
				'100px'
			]
		}
		// 编辑器是否支持滚动
		editorConfig.scroll = true
		// 是否只读
		editorConfig.readOnly = props.readOnly
		// 编辑器初始化时，默认的提示内容
		editorConfig.placeholder = props.placeholder
		// 模拟 ajax 异步获取内容
		onMounted(() => {
		})
		onBeforeUnmount(() => {
			const editor = editorRef.value
			if (editor == null) return
			editor.destroy()
		})

		const handleCreated = (editor) => {
			editorRef.value = editor
		}

		const clearContent = () => {
			const editor = editorRef.value
			if (editor == null) return
			editor.clear()
		}
		const handleChange = (editor) => {
			// console.log('editor', editor.getHtml())
			let htmlData = editor.getHtml()
			if (htmlData.indexOf('<table') > -1) {
				htmlData = tableBorderDel(htmlData.replace(/<table /g, '<table border="1" style="border-collapse: collapse;" '))
			}
			emit('update:modelValue', htmlData)
		}
		watch(props.modelValue, (newValue, oldValue) => {
			valueHtml.value = newValue.value
		})
		// 监听富文本编辑器内容变化
		// watch(valueHtml, (newValue, oldValue) => {
		// 	let data
		// 	if (newValue.indexOf('<table') > -1) {
		// 		data = tableBorderDel(newValue.replace(/<table /g, '<table border="1" style="border-collapse: collapse;" '))
		// 	}
		// 	emit('update:modelValue', data)
		// })
		// 处理表格内容超屏
		const tableBorderDel = (str) => {
			let tableReg = /<table.*?<\/table>/g // 匹配<table></table>对
			let styleReg = /width=".*?"/g // 匹配所有的width，设为auto
			let thReg = /<th.*?<\/th>/g // 匹配所有的th
			let trReg = /<tr.*?<\/tr>/ // 匹配tr
			let tdReg = /<td.*?<\/td>/g // 匹配所有td

			let tableList = str.match(tableReg)
			if (tableList != null) {
				tableList.map((tableItem) => {
					let newTable = tableItem.replace(styleReg, `width="auto"`)
					str = str.replace(tableItem, newTable)
				})

				// 计算出最大宽度（根据列数，平均分配）
				let firstTr = tableList[0].match(trReg)[0] // th
				if (firstTr.match(tdReg) == null) {
					let thNum = firstTr.match(thReg).length
					str = str.replace(/<th /g, `<th style="max-width:${100 / thNum}% !important;word-break:break-word; "`) // td 最大宽度、超出换行
				} else {
					let tdNum = firstTr.match(tdReg).length
					str = str.replace(/<td /g, `<td style="max-width:${100 / tdNum}% !important;word-break:break-word; "`) // td 最大宽度、超出换行
				}
				// style="width:auto" 改成 width="auto" 自适应
				str = str.replace(/style="width:\s*(\d+%|auto);"/g, (match, group1) => {
					return group1 === 'auto' ? 'width="auto"' : `width="${group1}"`
				})
			}
			return str
		}
		return {
			editorRef,
			valueHtml,
			toolbarConfig,
			editorConfig,
			handleCreated,
			handleChange,
			clearContent
		}
	}
})
</script>
<style scoped lang="scss"></style>
