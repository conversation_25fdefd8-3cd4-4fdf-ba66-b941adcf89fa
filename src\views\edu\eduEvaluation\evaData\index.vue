<template>
	<div>
		<el-input v-model="params.name" placeholder="请输入姓名搜索" style="width: 200px; margin-bottom: 10px" @input="nameSearch"></el-input>
		<el-select
			v-model="params.is_all"
			style="width: 200px; margin-bottom: 10px; margin-left: 10px"
			@change="typeSelect"
		>
			<el-option label="全部" :value="1"></el-option>
			<el-option label="未完成" :value="0"></el-option>
		</el-select>
		<el-table ref="userTable" :params="params" :data="list.rows">
			<template v-if="params.type !== 3">
				<el-table-column label="年级班级" prop="grade_name" width="100" fixed="left">
					<template #default="scope"> {{ scope.row.grade_name }}{{ scope.row.class_name }} </template>
				</el-table-column>
				<el-table-column label="学生姓名" prop="student_name" min-width="90" fixed="left"></el-table-column>
				<el-table-column v-for="(item, index) in list.head" :key="index" :label="item" prop="data" width="80">
					<template #default="scope">
						<el-icon v-if="scope.row.data[item] === 1" color="#67c23a" style="font-weight: bold;font-size: 14px"><el-icon-select /></el-icon>
						<el-icon v-else><el-icon-close /></el-icon>
					</template>
				</el-table-column>
			</template>
		</el-table>
		<div class="pagination">
			<el-pagination
				size="small"
				background
				layout="total, prev, pager, next,jumper"
				:total="list.total"
				:page-size="params.pageSize"
				class="mt-4"
				@current-change="pageChange"
			/>
		</div>
	</div>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
	data() {
		return {
			params: {
				tenant_id: 0,
				campus_id: 0,
				evaluation_id: 0,
				pageSize: 10,
				name: '',
				is_all: 1,
				page: 1
			},
			showUser: false,
			title: '',
			list: {},
			titleMap: {
				1: '班主任',
				2: '科任教师',
				3: '学生'
			},
			apiObj: null
		}
	},
	inject: ['evaluation_info'],
	created() {
		console.log(this.evaluation_info)
	},
	mounted() {
		console.log(this.evaluation_info)
		this.params.tenant_id = this.evaluation_info.tenant_id
		this.params.evaluation_id = this.evaluation_info.id
		this.params.campus_id = this.evaluation_info.campus_id
		this.getList()
	},
	methods: {
		typeSelect(val) {
			this.params.is_all = val
			this.getList()
		},
		nameSearch(val) {
			this.params.name = val
			this.getList()
		},
		table_info(val, index) {
			console.log(val, index, 'table_info')
		},
		async getList() {
			const res = await this.$API.eduEvaluation.data.get(this.params)
			if (res.code === 200) {
				this.list = res.data
			} else {
				this.showMessage(res.code, res.message)
			}
		},
		showMessage(code, message) {
			if (code === 200) {
				ElMessage({ type: 'success', message: message })
			} else {
				ElMessage({ type: 'error', message: message })
			}
		},
		pageChange(val) {
			this.params.page = val
			this.getList()
		}
	}
}
</script>

<style lang="scss" scoped>
.pagination {
	width: 100%;
	height: 50px;
	line-height: 50px;
	background-color: #fff;
	display: inline-flex;
	justify-content: flex-start;
	border-top: 1px solid var(--el-border-color-light);
	position: sticky;
	bottom: -10px;
}
</style>
