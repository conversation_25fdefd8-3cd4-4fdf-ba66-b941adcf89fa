<template>
    <el-dialog v-model="visible" title="签到情况" width="35%" destroy-on-close @close="close">
        <template v-if="signInfo">
            <div class="teacher-table" v-if="signInfo.teacher && signInfo.teacher.length">
                <div class="title">监考签到</div>
                <el-table :data="signInfo.teacher" border stripe style="width: 100%" max-height="200">
                    <el-table-column prop="user_info" label="监考人员姓名">
                        <template #default="{ row }">
                            {{ row.user_info.name }}（{{ row.examination_number }}）
                        </template>
                    </el-table-column>
                    <el-table-column prop="sign_or_not" label="是否签到" :filters="[
                        { text: '是', value: 2 },
                        { text: '否', value: 1 },
                    ]" :filter-method="filterHandler">
                        <template #default="{ row }">
                            <el-tag v-if="row.sign_or_not == 2" type="success">是</el-tag>
                            <el-tag v-else type="danger">否</el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="student-table" v-if="signInfo.student && signInfo.student.length">
                <div class="title">考生签到</div>
                <el-table :data="signInfo.student" border stripe style="width: 100%" max-height="200">
                    <el-table-column prop="user_info" label="学生姓名">
                        <template #default="{ row }">
                            {{ row.user_info.name }}（{{ row.examination_number }}）
                        </template>
                    </el-table-column>
                    <el-table-column prop="sign_or_not" label="是否签到" :filters="[
                        { text: '是', value: 2 },
                        { text: '否', value: 1 },
                    ]" :filter-method="filterHandler">
                        <template #default="{ row }">
                            <el-tag v-if="row.sign_or_not == 2" type="success">是</el-tag>
                            <el-tag v-else type="danger">否</el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </template>
        <template v-else>
            <el-empty description="未找到同步的考试计划"></el-empty>
        </template>
    </el-dialog>
</template>
<script setup>
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
// 获取签到信息
const signInfo = ref(null)
const getInfo = () => {
    globalPropValue.examine.getSignInfo.get(params.value).then(res => {
        if (res.code === 200) {
            signInfo.value = res.data
            console.log(res)
        }
    })
}
const filterHandler = (value, row, column) => {
    const property = column['property']
    return row[property] === value
}

const visible = ref(false)
const params = ref({})
const open = (val) => {
    params.value = val
    visible.value = true
    getInfo()
}
const close = () => {
    visible.value = false
    signInfo.value = null
}

defineExpose({
    open,
    close
})
</script>

<style lang="scss" scoped>
.teacher-table {
    margin-bottom: 20px;
}

.title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
}
</style>