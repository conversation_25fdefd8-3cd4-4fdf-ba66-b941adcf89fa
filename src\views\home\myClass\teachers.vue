<template>
    <scTable ref="table" row-key="id" stripe :data="data" hidePagination hideDo>
        <el-table-column prop="teacher_name" label="教师姓名" width="120" fixed="left"/>
        <el-table-column prop="serial_number" label="工号" width="120"/>
        <el-table-column prop="teacher_head" label="教师头像">
            <template #default="{row}">
                <cusHead loading="lazy" :lazy="true" fit="contain" style="width: 50px; height: 50px"
                    :src="row.teacher_head" :preview-src-list="[row.teacher_head]" preview-teleported>
                </cusHead>
            </template>
        </el-table-column>
        <el-table-column prop="is_head" label="是否班主任" width="100">
            <template #default="{ row }">
                {{ row.is_head == -1 ? '否' : '是' }}
            </template>
        </el-table-column>
        <el-table-column prop="course_name" label="学科" width="150">
        </el-table-column>
        <el-table-column prop="week_num" label="周课节数
">
        </el-table-column>
        <el-table-column prop="position" label="职务">
            <template #default="{ row }">
                {{ row.position || '-' }}
            </template>
        </el-table-column>
        <el-table-column prop="academic_name" label="学期" width="220" show-overflow-tooltip>
            <template #default="{ row }">
                {{ row.academic_name + '-' + row.semester_name }}
            </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" width="150" />
        <el-table-column prop="remark" label="备注" />
    </scTable>
</template>
<script>
import cusHead from '@/components/custom/cusStaffHead.vue'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, studentStatusMap } = cusTom.getBaseQuery()
export default {
    components: {
        cusHead
    },
    props: {
        data: {
            tpye: Array,
            default: () => []
        }
    },
    data() {
        return {
            studentStatusMap,
        }
    },
    mounted() {
        console.log(this.data)
    },
    methods: {
        //数据回显格式化
        formData(arr, val) {
            return arr.find((v) => v.value == val)?.name || '-'
        }
    }
}
</script>
<style lang="scss" scoped></style>
