import config from '@/config'
import http from '@/utils/request'

export default {
	all: {
		url: `${config.API_URL}/eduapi/student/all`,
		name: '获取学员列表不分页',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	list: {
		url: `${config.API_URL}/eduapi/student/list`,
		name: '获取学员列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getStudentForClassAdd: {
		url: `${config.API_URL}/eduapi/student/getStudentForClassAdd`,
		name: '获取班级添加学生列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	leave: {
		url: `${config.API_URL}/eduapi/student/leave`,
		name: '学生离校',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	bindCard: {
		url: `${config.API_URL}/eduapi/student/bindCard`,
		name: '绑卡',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	batchLeave: {
		url: `${config.API_URL}/eduapi/student/batchLeave`,
		name: '批量学生离校',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	save: {
		url: `${config.API_URL}/eduapi/student/save`,
		name: '学员修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	batchDel: {
		url: `${config.API_URL}/eduapi/student/batchDel`,
		name: '批量删除学员',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/eduapi/student/del`,
		name: '删除学员',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	attendance: {
		list: {
			url: `${config.API_URL}/eduapi/attendance/list`,
			name: '获取学员考勤列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		}
	},
	import: {
		url: `${config.API_URL}/eduapi/student/import`,
		name: '学生导入',
		post: async function (data = {}) {
			return await http.post(this.url, data, { timeout: 0 })
		}
	}
}
