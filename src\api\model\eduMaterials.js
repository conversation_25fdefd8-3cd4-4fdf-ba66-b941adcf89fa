import config from '@/config'
import http from '@/utils/request'
export default {
	material: {
		tree: {
			url: `${config.API_URL}/eduapi/material_type/all`,
			name: '获取所有类别(树形)',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		tree_add: {
			url: `${config.API_URL}/eduapi/material_type/creat`,
			name: '添加类别',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		tree_edit: {
			url: `${config.API_URL}/eduapi/material_type/edit`,
			name: '修改类别',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		tree_del: {
			url: `${config.API_URL}/eduapi/material_type/del`,
			name: '删除类别',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		list: {
			url: `${config.API_URL}/eduapi/material/list`,
			name: '获取教材列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		get_auth: {
			url: `${config.API_URL}/eduapi/material/getUploadToken`,
			name: '获取视频上传凭证',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		refresh_auth: {
			url: `${config.API_URL}/eduapi/material/refreshUploadToken`,
			name: '刷新视频上传凭证',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		materials_add: {
			url: `${config.API_URL}/eduapi/material/save`,
			name: '添加教材',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		materials_del: {
			url: `${config.API_URL}/eduapi/material/del`,
			name: '删除教材',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		get_weboffice_token: {
			url: `${config.API_URL}/eduapi/material/getWebofficeToken`,
			name: '获取教材文件在线预览凭证',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		get_video_play_atuh: {
			url: `${config.API_URL}/eduapi/material/getVideoPlayAtuh`,
			name: '获取视频播放凭证',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		get_video_slice_list: {
			url: `${config.API_URL}/eduapi/video_slice/list`,
			name: '获取视频分片列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add_edit_video_slice: {
			url: `${config.API_URL}/eduapi/video_slice/save`,
			name: '添加/修改视频分片',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del_video_slice: {
			url: `${config.API_URL}/eduapi/video_slice/del`,
			name: '删除视频分片',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		get_attachment_list: {
			url: `${config.API_URL}/eduapi/material_attachments/list`,
			name: '获取教材关联附件列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add_edit_attachment: {
			url: `${config.API_URL}/eduapi/material_attachments/save`,
			name: '添加/修改教材关联附件',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del_attachment: {
			url: `${config.API_URL}/eduapi/material_attachments/del`,
			name: '删除教材关联附件',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		get_comment_list: {
			url: `${config.API_URL}/eduapi/material_comment/list`,
			name: '获取教材评论列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add_edit_comment: {
			url: `${config.API_URL}/eduapi/material_comment/save`,
			name: '添加/修改教材评论',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del_comment: {
			url: `${config.API_URL}/eduapi/material_comment/del`,
			name: '删除教材评论',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		audit_comment: {
			url: `${config.API_URL}/eduapi/material_comment/audit`,
			name: '审核教材评论',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		get_collect_lick: {
			url: `${config.API_URL}/eduapi/material_collect_like/list`,
			name: '获取教材点赞收藏列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		getShareCode: {
			url: `${config.API_URL}/eduapi/material/getShareCode`,
			name: '获取分享链接',
			post: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, params)
			}
		}
	}
}
