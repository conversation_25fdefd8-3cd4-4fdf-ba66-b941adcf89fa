<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<el-select
					v-if="CampusManagementList.length > 1"
					v-model="params.campus_id"
					placeholder="校区"
					filterable
					style="margin-right: 15px"
					@change="campusChange"
				>
					<el-option
						v-for="item in CampusManagementList"
						:key="item.code"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
				<cusSelectSemester
					v-model="params.semester_id"
					:params="params"
					:show-default-value="true"
					:width="'214px'"
					clearable
					style="margin-right: 15px"
					@semesterChange="semesterChange"
				/>
			</div>
			<div class="right-panel">
				<el-button type="primary" @click="table_add">新增评教</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :apiObj="list.apiObj" :params="params">
				<el-table-column label="评教名称" prop="title" width="250" fixed="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="学年学期" prop="semester_name" width="180">
					<template #default="scope"> {{ scope.row.academic_name }} - {{ scope.row.semester_name }}</template>
				</el-table-column>
				<el-table-column label="班主任评教表单" prop="leader_form_obj" width="180" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.leader_form_obj">{{ scope.row.leader_form_obj.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="科任老师评教表单" prop="teacher_form_obj" width="180" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.teacher_form_obj">{{ scope.row.teacher_form_obj.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="关联年级" prop="grade_info" width="100"></el-table-column>
				<el-table-column label="评教学科" prop="" min-width="200" show-overflow-tooltip>
					<template #default="scope">
						<template v-for="(item, index) in scope.row.course_info" :key="index">
							<span
								>{{ item
								}}<span v-if="index !== scope.row.course_info.length - 1">&nbsp;&nbsp;|&nbsp;&nbsp; </span></span
							>
						</template>
					</template>
				</el-table-column>
				<el-table-column label="截止时间" prop="end_time" width="150"></el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="150"></el-table-column>
				<!--				<el-table-column label="是否隐藏学生姓名" prop="hide_student" align="center" width="150">
					<template #default="scope">
						<span v-if="scope.row.hide_student === 1">是</span>
						<span v-if="scope.row.hide_student === -1">否</span>
					</template>
				</el-table-column>-->
				<el-table-column label="状态" prop="status" width="100">
					<template #default="scope">
						<el-tag v-if="scope.row.status === 1">待发布</el-tag>
						<el-tag v-if="scope.row.status === 2" type="success">已发布</el-tag>
						<el-tag v-if="scope.row.status === 3" type="danger">已撤销</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="180">
					<template #default="scope">
						<el-button-group>
							<el-button
								v-if="scope.row.status === 1"
								text
								type="primary"
								size="small"
								@click="table_edit(scope.row, scope.$index)"
								>修改
							</el-button>
							<el-button
								v-if="scope.row.status === 1"
								text
								type="success"
								size="small"
								@click="table_publish(scope.row, scope.$index)"
								>发布
							</el-button>
							<el-button
								v-if="scope.row.status === 2"
								text
								type="primary"
								size="small"
								@click="showDetail(scope.row, scope.$index)"
								>评教详情
							</el-button>
							<el-button
								v-if="scope.row.status === 2"
								text
								type="info"
								size="small"
								@click="table_cancel(scope.row, scope.$index)"
								>撤销
							</el-button>
							<el-popconfirm
								v-if="scope.row.status === 1 || scope.row.status === 3"
								title="确定删除吗？"
								@confirm="table_del(scope.row, scope.$index)"
							>
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<save ref="saveDialog" @success="saveSuccess"></save>
		<show ref="showDetail"></show>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
import save from './save.vue'
import show from './show.vue'
import { ElMessage } from 'element-plus'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'

const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		pageSize: 20,
		page: 1,
		academic_id: null,
		semester_id: null
	}
}
export default {
	components: {
		cusSelectSemester,
		save,
		show
	},
	data() {
		return {
			params: defaultParams(),
			CampusManagementList: campusInfo,
			academicList: [],
			semesterList: [],
			semesterInfo,
			list: {
				apiObj: this.$API.eduEvaluation.list
			}
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.semester_id = null
			},
			immediate: true
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	methods: {
		campusChange(val) {
			this.params.campus_id = val
			this.$refs.table.upData(this.params)
		},
		table_add() {
			this.$refs.saveDialog.open('add', this.params)
		},
		table_edit(val) {
			this.$refs.saveDialog.open('edit', val)
		},
		// 新增/修改成功回调
		async saveSuccess() {
			// this.$refs.table.upData(this.params)
			this.$refs.table.refresh()
		},
		async table_publish(val) {
			this.$confirm(`确定发布吗？发布后学生可以填写评教！`, '提示', {
				type: 'success'
			})
				.then(async () => {
					const { code, message } = await this.$API.eduEvaluation.publish.post({
						id: val.id,
						tenant_id: tenantId,
						campus_id: campusId
					})
					this.showMessage(code, message)
				})
				.catch(() => {})
		},
		async table_cancel(val) {
			this.$confirm(`确定撤销发布吗？当前学生评教结果将不会被保留！`, '提示', {
				type: 'warning'
			})
				.then(async () => {
					const { code, message } = await this.$API.eduEvaluation.cancel.post({
						id: val.id,
						tenant_id: tenantId,
						campus_id: campusId
					})
					this.showMessage(code, message)
				})
				.catch(() => {})
		},
		async table_del(val) {
			const { code, message } = await this.$API.eduEvaluation.del.post({
				id: val.id,
				tenant_id: tenantId,
				campus_id: campusId
			})
			this.showMessage(code, message)
		},
		showDetail(val) {
			this.$refs.showDetail.setData(val)
		},
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		semesterChange(val) {
			this.params.semester_id = val
			this.$nextTick(() => {
				this.$refs.table.upData(this.params)
			})
		},
		showMessage(code, message) {
			if (code === 200) {
				ElMessage({ type: 'success', message: message })
				// this.$refs.table.upData(this.params)
				this.$refs.table.refresh()
			} else {
				ElMessage({ type: 'error', message: message })
			}
		}
	}
}
</script>

<style lang="scss" scoped></style>
