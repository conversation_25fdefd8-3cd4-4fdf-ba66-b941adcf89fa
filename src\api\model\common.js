import config from '@/config'
import http from '@/utils/request'

export default {
	campusOverview: {
		//url: `${config.MOCK_URL}/upload`,
		url: `${config.API_URL}/api/overview/campus`,
		name: '获取学校总览数据',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	eduOverview: {
		//url: `${config.MOCK_URL}/upload`,
		url: `${config.API_URL}/api/overview/edu`,
		name: '获取教务总览数据',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	affOverview: {
		//url: `${config.MOCK_URL}/upload`,
		url: `${config.API_URL}/api/overview/aff`,
		name: '获取总务总览数据',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	stuOverview: {
		//url: `${config.MOCK_URL}/upload`,
		url: `${config.API_URL}/api/overview/student`,
		name: '获取学生总览数据',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getCaptcha: {
		//url: `${config.MOCK_URL}/upload`,
		url: `${config.API_URL}/api/getCaptcha`,
		name: '获取验证码',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	sendVerifyCode: {
		//url: `${config.MOCK_URL}/upload`,
		url: `${config.API_URL}/api/sendVerifyCode`,
		name: '发送登录验证码',
		post: async function (data, config = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data, config)
		}
	},
	getAsh: {
		//url: `${config.MOCK_URL}/upload`,
		url: `${config.API_URL}/api/getAsh`,
		name: '置灰状态',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	upload: {
		//url: `${config.MOCK_URL}/upload`,
		url: `${config.API_URL}/api/common/upload`,
		name: '文件上传',
		post: async function (data, config = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data, config)
		}
	},
	uploadFile: {
		url: `${config.MOCK_URL}/uploadFile`,
		name: '附件上传',
		post: async function (data, config = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data, config)
		}
	},
	exportFile: {
		url: `${config.MOCK_URL}/fileExport`,
		name: '导出附件',
		get: async function (data, config = {}) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, data, config)
		}
	},
	importFile: {
		url: `${config.MOCK_URL}/fileImport`,
		name: '导入附件',
		post: async function (data, config = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data, config)
		}
	},
	file: {
		menu: {
			url: `${config.MOCK_URL}/file/menu`,
			name: '获取文件分类',
			get: async function () {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url)
			}
		},
		list: {
			url: `${config.MOCK_URL}/file/list`,
			name: '获取文件列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		}
	},
	getCommonData: {
		url: `${config.API_URL}/api/common/config`,
		name: '返回当前用户拥有的学校校区',
		get: async function (params) {
			return await http.get(this.url, params)
		}
	}
}
