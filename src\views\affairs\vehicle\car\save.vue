<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'" label-width="110">
			<el-form-item label="车辆名称" prop="car_name">
				<el-input v-model="form.car_name" placeholder="请输入名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="车牌号" prop="car_number">
				<el-input v-model="form.car_number" placeholder="请输入车牌号" clearable></el-input>
			</el-form-item>
			<el-form-item label="部门" prop="department_id">
				<cusCascader
					v-model="form.department_id"
					:options="treeData"
					:prop="{
						emitPath: false,
						value: 'id',
						label: 'department_name',
						checkStrictly: true
					}"
				></cusCascader>
			</el-form-item>
			<el-form-item label="车辆负责人" prop="car_principalObj">
				<cusSelectTeacher v-model="form.car_principalObj"></cusSelectTeacher>
			</el-form-item>
			<el-form-item label="车牌用途" prop="car_use">
				<el-input v-model="form.car_use" placeholder="请输入" clearable></el-input>
			</el-form-item>
			<el-form-item label="可乘人数" prop="car_seats">
				<el-input-number v-model="form.car_seats" placeholder="请输入" :min="0"></el-input-number>
			</el-form-item>

			<el-form-item label="车辆图片" prop="car_img">
				<scUpload v-model="form.car_img"  fileTypeTag="car" :disabled="mode == 'show'"></scUpload>
			</el-form-item>

			<el-form-item label="备注" prop="remark">
				<el-input v-model="form.remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'

const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		car_name: null,
		car_number: null,
		car_principal: null,
		car_principalObj: null,
		car_use: null,
		car_seats: null,
		car_img: null,
		remark: null,
		department_id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				car_name: [{ required: true, message: '请输入名称' }],
				car_number: [{ required: true, message: '请输入车牌号' }],
				//				car_principalObj: [{ required: true, message: '请输入负责人' }],
				car_use: [{ required: true, message: '请输入车牌用途' }],
				car_seats: [{ required: true, message: '请输入可乘人数' }],
				car_img: [{ required: true, message: '请输入车辆图片', trigger: 'blur' }],
				department_id: [{ required: true, message: '请选择所属部门' }]
			},

			course: [],
			discipline: [],
			treeData: [],
			position: []
		}
	},
	computed: {},
	mounted() {},
	created() {
		this.getDept()
	},
	methods: {
		async getDept() {
			const { data } = await this.$API.system.dept.all.get(this.params)
			this.treeData = cusTom.arrayToTree(data)
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					if (this.form.car_principalObj && this.form.car_principalObj.length > 0) {
						this.form.car_principal = this.form.car_principalObj[0].id
					} else if (this.form.car_principal) {
						this.form.car_principal = this.form.car_principal.id
					}
					var res = await this.$API.car.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			if (data.car_principal) {
				this.form.car_principalObj = [
					{
						id: data.car_principal.id,
						label: data.car_principal.name
					}
				]
			} else {
				this.form.car_principalObj = null
			}
			console.log(this.form)
		}
	}
}
</script>

<style></style>
