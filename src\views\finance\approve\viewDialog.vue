<template>
    <el-dialog title="查看" v-model="dialogViewVisible" class="select-dialog" append-to-body width="800">
        <el-container>
            <el-header class="view-title" v-if="applyData.apply_info">
                <div>
                    <el-avatar :size="45" :src="applyData.apply_info.apply_user_head" />
                    <div>
                        <p>
                            {{
                                applyData.apply_info.apply_user_name + '的' +
                                applyData.apply_info.approval_name + '审批' }}
                        </p>
                        <p>{{ applyData.apply_info.created_at }}</p>
                    </div>
                </div>
                <div>
                    <el-tag v-if="applyData.apply_info.status === 1" type="primary">审批中</el-tag>
                    <el-tag v-if="applyData.apply_info.status === 2" type="success">已通过</el-tag>
                    <el-tag v-if="applyData.apply_info.status === 3" type="danger">已驳回</el-tag>
                    <el-tag v-if="applyData.apply_info.status === 4" type="warning">已撤销</el-tag>
                </div>
            </el-header>
            <el-container>
                <el-aside>
                    <el-timeline style="max-width: 600px;padding: 10px;">
                        <el-timeline-item v-for="(item, index) in applyData.approval_step" :key="index"
                            :timestamp="item.updated_at" placement="top"
                            :type="item.action_status === 2 ? 'success' : 'info'" center>
                            <el-card>
                                <div style="display: flex;align-items: center;justify-content: space-between;">
                                    <h4 style="display: flex;align-items: center;"><el-avatar :size="30"
                                            :src="item.action_user_head" /><span style="padding-left: 10px;">{{
                                                item.action_user_name
                                            }}</span></h4>
                                    <el-tag :type="item.action_status === 2 ? 'success' : 'info'">{{ item.action_status
                                        ===
                                        2 ? '已审批' : '待审批' }}</el-tag>
                                </div>
                            </el-card>
                        </el-timeline-item>
                    </el-timeline>
                </el-aside>
                <el-main style="min-height: 250px;" v-if="applyData.apply_info">
                    <el-descriptions title="" column="1" direction="vertical" v-if="applyData.apply_info.apply_info">
                        <el-descriptions-item label-class-name="info-label"
                            v-for="item in JSON.parse(applyData.apply_info.apply_info)" :label="item.label">
                            <el-image :src="item.value" v-if="isImage(item.value)" />
                            <div v-html="item.value" v-else></div>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-main>
            </el-container>
        </el-container>
    </el-dialog>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const dialogViewVisible = ref(false)

const applyData = ref({})
const defaultParams = () => {
    return {
        id: null,
        tenant_id: tenantId,
        campus_id: campusId
    }
}

const params = ref(defaultParams())
const open = (id) => {
    params.value.id = id
    getApplyInfo()
    dialogViewVisible.value = true
}
const getApplyInfo = async () => {
    const { data } = await globalPropValue.approval.flow.detail.get(params.value)
    applyData.value = data
}
const isImage = (url) => {
    // 简单判断是否为图片链接
    return /\.(jpg|jpeg|png|gif|bmp)$/i.test(url);
}
defineExpose({ dialogViewVisible, open }) 
</script>

<style lang="scss" scoped>
.view-title {
    font-size: 15px;
    font-weight: bold;
    border-radius: 0 !important;

    >div {
        display: flex;
        align-items: center;

        div {
            margin-left: 10px;
        }
    }

    div>p:last-child {
        font-size: 12px;
        font-weight: bold;
    }
}
</style>
<style>
.info-label {
    font-weight: bold !important;
}
</style>