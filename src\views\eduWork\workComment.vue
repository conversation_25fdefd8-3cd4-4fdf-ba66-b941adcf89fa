<template>
	<el-drawer v-model="drawer" title="作业评论" size="50%">
		<div v-if="comments.list && comments.list.length">
			<childComment :comments="comments.list" @suc="getCommentList"></childComment>
		</div>
		<el-empty v-else description="暂无评论" />
		<template #footer>
			<scPagination
				v-if="comments.total"
				v-model:page="queryParam.obj.page"
				v-model:size="queryParam.obj.pageSize"
				:total="comments.total"
				@pagination="getCommentList"
			></scPagination>
		</template>
	</el-drawer>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, defineExpose } from 'vue'
import cusTom from '@/utils/cusTom'
import childComment from './childComment'
const { campusId, tenantId } = cusTom.getBaseQuery()
const { proxy } = getCurrentInstance()
let drawer = ref(false)
const queryParam = reactive({
	obj: {
		tenant_id: tenantId,
		campus_id: campusId,
		work_id: 0,
		page: 1,
		pageSize: 10
	}
})
const comments = reactive({
	list: [],
	total: 0
})
const getCommentList = async () => {
	let { data } = await proxy.$API.eduWork.workCommentList.get(queryParam.obj)
	comments.list = data.rows
	comments.total = data.total
	if (comments.list) {
		setReplyPopover(comments.list)
	}
}
const setReplyPopover = (data) => {
	data.forEach((item) => {
		item.replyPopover = false
		item.comment_desc2 = ''
		item.comment_score2 = 0
		if (item.child) {
			setReplyPopover(item.child)
		}
	})
}
const show = (data) => {
	drawer.value = true
	queryParam.obj.tenant_id = data.tenant_id
	queryParam.obj.campus_id = data.campus_id
	queryParam.obj.work_id = data.id
	getCommentList()
}
defineExpose({
	show
})
</script>

<style lang="scss">
ul,
li {
	list-style: none;
}
.workComment {
	color: #303133;
	padding: 8px 0;
	font-size: 12px;
	border-bottom: 1px solid var(--el-border-color-light);
	.publisher {
		display: flex;
		position: relative;
		.left {
			width: 40px;
			height: 40px;
			border: 1px solid #e8e8e8;
			border-radius: 50%;
			margin-right: 10px;
			overflow: hidden;
		}
		.right {
			p {
				&:last-child {
					display: flex;
					align-items: center;
					font-size: 12px;
					color: #909399;
				}
			}
		}
		.score {
			position: absolute;
			top: 8px;
			right: 5px;
		}
	}
	.content {
		position: relative;
		padding: 10px 50px 10px;
		white-space: pre-wrap;
		.score {
			position: absolute;
			right: 5px;
		}
	}
	.reply {
		margin: 0 0 0 50px;
		.replyContent {
			border-radius: 6px;
			padding: 5px 10px;
			background: #f7f8f9;
		}
	}
}
</style>
