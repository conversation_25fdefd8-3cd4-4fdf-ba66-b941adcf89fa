<template>
    <el-dialog v-model="visible" :title="title" width="400" destroy-on-close @close="close">
        <el-form ref="dialogForm" :model="formData" :rules="rules" label-width="auto">
            <el-form-item label="学号/考号" prop="serial_number">
                {{ formData.serial_number }}
            </el-form-item>
            <el-form-item label="姓名" prop="student_name">
                {{ formData.student_name }}
            </el-form-item>
            <el-form-item label="原班级" prop="teacher_num">
                {{ formData.grade_info.name }}{{ formData.class_info?.name || '' }}
            </el-form-item>
            <el-form-item label="考场" prop="room_id">
                <el-select v-model="formData.room_id" placeholder="请选择">
                    <el-option v-for="item in roomList" :key="item.room_id" :label="item.room_info.name"
                        :value="item.room_id">
                        <span style="float: left">{{ item.room_info.name }}</span>
                        <span style="float: right;color: var(--el-text-color-secondary);font-size: 13px;">
                            {{ item.real_student_num }} /{{ item.student_num }}
                        </span>
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="close">取 消</el-button>
                <el-button type="primary" @click="save">确 定</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()
const props = defineProps({
    roomList: {
        type: Array,
        default: []
    }
})
const emit = defineEmits(['saveSuccess'])

const rules = ref({
    room_id: [{ required: true, message: '请选择考场', trigger: 'change' }]
})

const dialogForm = ref()
const save = () => {
    dialogForm.value.validate((valid) => {
        if (valid) {
            globalPropValue.examine.changeStudentArrange.post({
                tenant_id: tenantId,
                campus_id: campusId,
                examine_id: Number(query.id),
                student_id: formData.value.student_id,
                room_id: formData.value.room_id,
                course_id: formData.value.course_id,
            }).then((res) => {
                console.log(res)
                if (res.code === 200) {
                    ElMessage.success('修改成功')
                    emit('saveSuccess')
                    close()
                }
            })
            console.log(formData.value)
            emit('saveSuccess')
            close()
        }
    })
}
const visible = ref(false)
const title = ref('编辑')
const formData = ref({})
const open = (data) => {
    formData.value = data
    visible.value = true
}
const close = () => {
    visible.value = false
}

defineExpose({
    open,
    close
})
</script>