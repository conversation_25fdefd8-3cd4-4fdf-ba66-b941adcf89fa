<template>
	<el-form-item label="数据对象（最多20项）" prop="modelValue">
		<div v-if="modelValue.length > 0" style="width: 100%">
			<el-table :data="modelValue" size="small">
				<el-table-column prop="name" label="参数名称" width="110"></el-table-column>
				<el-table-column prop="code" label="标识符" width="130"></el-table-column>
				<el-table-column prop="type" label="数据类型" width="100"></el-table-column>
				<el-table-column prop="type" label="操作">
					<template #default="scope">
						<el-button text type="primary" @click="editStruct(scope.row, scope.$index)">编辑</el-button>
						<el-button text type="danger" @click="delStruct(scope.row, scope.$index)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div>
			<el-button v-if="modelValue.length < 20" type="text" @click="addStruct"> + 添加参数</el-button>
		</div>
	</el-form-item>
	<struct-item-module v-if="showItem" ref="structItemModule" @submit="submit" @close="closeItem()"></struct-item-module>
</template>
<script>
import StructItemModule from '@/components/thingsModel/structItemModule.vue'

export default {
	name: 'structModule',
	components: { StructItemModule },
	props: {
		modelValue: {
			type: Array,
			default: () => {
				return []
			}
		}
	},
	data() {
		return {
			unitConfig: [],
			itemList: [],
			showItem: false,
			currentIndex: null
		}
	},
	emits: ['update:modelValue', 'changeSize'], // 明确声明该组件会触发 update:modelValue 事件
	watch: {
		modelValue: {
			handler(val) {
				this.$emit('update:modelValue', val)
			},
			deep: true
		}
	},
	created() {
		this.itemList = this.modelValue
	},
	methods: {
		addStruct() {
			this.showItem = true
			this.$emit('changeSize', 800)
			this.$nextTick(() => {
				this.$refs.structItemModule.show(null, 'add')
			})
		},
		closeItem() {
			this.showItem = false
			this.$emit('changeSize', 550)
		},
		submit(data, mode) {
			if (mode === 'add') {
				this.itemList.push(data)
			} else {
				this.itemList[this.currentIndex] = data
			}

			this.showItem = false
			this.$emit('update:modelValue', this.itemList)
			this.$emit('changeSize', 550)
		},
		editStruct(data, index) {
			this.currentIndex = index
			this.showItem = true
			this.$emit('changeSize', 800)
			this.$nextTick(() => {
				this.$refs.structItemModule.show(data, 'edit')
			})
		},
		delStruct(data, index) {
			this.itemList.splice(index, 1)
		}
	}
}
</script>

<style scoped lang="scss">
.enum_header {
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.enum_item {
	margin: 5px 0;
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.text-center {
	text-align: center;
}
</style>
