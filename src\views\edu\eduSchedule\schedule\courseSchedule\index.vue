<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<el-select
					v-if="CampusManagementList.length > 1"
					v-model="params.campus_id"
					placeholder="校区"
					filterable
					style="margin-right: 15px"
					@change="campusChange"
				>
					<el-option
						v-for="item in CampusManagementList"
						:key="item.code"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
				<!--				<cusSelectSemester
					v-model="params.semester_id"
					:params="params"
					:width="'214px'"
					clearable
					placeholder="请选择学期"
					style="margin-right: 15px"
					@semesterChange="semesterChange"
				/>-->
				<cusSelectSemester
					v-model="params.semester_id"
					:params="params"
					:show-default-value="true"
					:width="'214px'"
					clearable
					style="margin-right: 15px"
					@semesterChange="semesterChange"
				/>
				<el-select
					v-model="params.grade_id"
					clearable
					placeholder="请选择年级"
					style="margin-right: 15px"
					@change="infoChange"
				>
					<el-option v-for="item in gradeList" :key="item.id" :label="item.grade_name" :value="item.id"></el-option>
				</el-select>
				<el-input
					v-model="params.name"
					placeholder="请输入排课名称"
					clearable
					style="width: 214px"
					@change="nameChange"
				></el-input>
			</div>
			<div class="right-panel">
				<el-button :loading="syncLoading" plain @click="syncCourseAction()" type="warning">同步课表到班牌</el-button>
				<el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增排课</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :apiObj="apiObj" :params="params">
				<el-table-column prop="schedule_name" label="排课名称"></el-table-column>
				<el-table-column prop="rule_name" label="关联规则名称"></el-table-column>
				<el-table-column prop="created_at" label="创建时间"></el-table-column>
				<el-table-column prop="grade_name" label="关联年级"></el-table-column>
				<el-table-column prop="status" label="状态">
					<template #default="{ row }">
						<el-tag v-if="row.status === 1" type="success">启用</el-tag>
						<el-tag v-if="row.status === -1" type="danger">停用</el-tag>
						<el-switch
							v-model="row.status"
							style="margin-left: 10px"
							:active-value="1"
							:inactive-value="-1"
							@change="statusChange(row)"
						></el-switch>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="200">
					<template #default="scope">
						<el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
						<el-button link type="primary" size="small" @click="handleSet(scope.row)">配置</el-button>
						<el-button link type="danger" size="small" @click="handleDel(scope.row)">删除</el-button>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<scheduleSave ref="scheduleSaveDialog" :params="params" @success="scheduleSaveSuccess"></scheduleSave>
	</el-container>
</template>
<script setup>
import { getCurrentInstance, nextTick, ref } from 'vue'
import { useRouter } from 'vue-router'
import cusTom from '@/utils/cusTom'
import { ElMessage, ElMessageBox } from 'element-plus'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'
import scheduleSave from './scheduleSave.vue'
import { getNDaysAfterRange } from '@/utils/dayjs'

const { campusInfo, tenantId, campusId } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: null,
		grade_id: null,
		name: null
	}
}
const router = useRouter()
const CampusManagementList = ref(campusInfo)
const params = ref(defaultParams())
const apiObj = ref(globalPropValue.eduSchedule.schedule.list)
const table = ref(null)
const scheduleSaveDialog = ref(null)
const syncLoading = ref(false)
const syncCourseDialog = ref(null)
const campusChange = (val) => {
	params.value.campus_id = val
	table.value.upData(params.value)
}
const semesterChange = (val) => {
	params.value.semester_id = val
	nextTick(() => {
		getGradeData()
		table.value.upData(params.value)
	})
}
const infoChange = (val) => {
	params.value.grade_id = val
	table.value.upData(params.value)
}
const nameChange = (val) => {
	params.value.name = val
	table.value.upData(params.value)
}
const handleAdd = () => {
	scheduleSaveDialog.value.open('add')
}
const handleEdit = (row) => {
	scheduleSaveDialog.value.open('edit').setData(row)
}
const statusChange = (val) => {
	console.log(val, 'statusChange')
	globalPropValue.eduSchedule.schedule.edit.post(val).then((res) => {
		if (res.code == 200) {
			ElMessage.success('操作成功')
			table.value.upData(params.value)
		}
	})
}
const handleSet = (row) => {
	router.push({
		name: 'scheduleSet',
		query: {
			id: row.id
		}
	})
	// ElMessage.info('功能开发中~')
	console.log(row, 'peizhi')
}
const scheduleSaveSuccess = (data, mode) => {
	if (mode === 'add') {
		table.value.upData(params.value)
	} else {
		table.value.refresh()
	}
}
const handleDel = (row) => {
	ElMessageBox.confirm('确定删除该排课吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		globalPropValue.eduSchedule.schedule.del
			.post({ id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id })
			.then((res) => {
				if (res.code === 200) {
					ElMessage.success('删除成功')
					// table.value.upData(params.value)
					table.value.refresh()
				} else {
					ElMessage.error(res.message)
				}
			})
	})
}

const syncCourseAction = (row) => {
	let date = getNDaysAfterRange(0, 7)
	ElMessageBox.confirm(
		'确定同步 ' + date[0] + ' 至 ' + date[1] + ' 内课表到班牌吗？班牌已有课表将会被覆盖！',
		'提示',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}
	).then(() => {
		syncLoading.value = true
		globalPropValue.eduSchedule.schedule.sync
			.post({ semester_id: params.value.semester_id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id })
			.then((res) => {
				syncLoading.value = false
				if (res.code === 200) {
					ElMessage.success('同步成功')
				} else {
					ElMessage.error(res.message)
				}
			})
	})
}
const gradeList = ref([])
const getGradeData = () => {
	globalPropValue.eduGradeClass.grade.all.get(params.value).then((res) => {
		gradeList.value = res.data
	})
}
</script>

<style lang="scss" scoped></style>
