<template>
	<el-form ref="loginForm" :model="form" :rules="rules" label-width="0" size="large" @keyup.enter="login">
		<el-form-item v-if="type === 2" prop="tenant_id">
			<el-input v-model="form.tenant_code" prefix-icon="el-icon-school" clearable placeholder="请输入学校代号" />
		</el-form-item>
		<el-form-item prop="username">
			<el-input
				v-model="form.username"
				prefix-icon="el-icon-iphone"
				clearable
				:placeholder="$t('login.mobilePlaceholder')"
			>
				<template #prepend>+86</template>
			</el-input>
		</el-form-item>
		<el-form-item prop="isPassing" style="user-select: none">
			<drag-verify
				ref="dragVerify"
				v-model:isPassing="isPassing"
				text="请按住滑块拖动"
				successText="验证通过"
				handlerIcon="el-icon-DArrowRight"
				successIcon="el-icon-CircleCheck"
			>
			</drag-verify>
		</el-form-item>
		<el-form-item prop="code" style="margin-bottom: 35px">
			<div class="login-msg-yzm">
				<el-input
					v-model="form.code"
					prefix-icon="el-icon-unlock"
					clearable
					:placeholder="$t('login.smsPlaceholder')"
				></el-input>
				<el-button :disabled="disabled" @click="getYzm"
					>{{ this.$t('login.smsGet') }}<span v-if="disabled"> ({{ time }})</span></el-button
				>
			</div>
		</el-form-item>
		<el-form-item>
			<el-button type="primary" style="width: 100%" :loading="islogin" round @click="login">{{
				$t('login.signIn')
			}}</el-button>
		</el-form-item>
		<div v-if="$CONFIG.MY_OPEN_REGISTER" class="login-reg">
			{{ $t('login.noAccount') }} <router-link to="/user_register">{{ $t('login.createAccount') }}</router-link>
		</div>
	</el-form>
</template>

<script>
import { useCommonStore } from '@/stores/common.js'
import DragVerify from '@/components/dragVerify/index.vue'

const commonStore = useCommonStore()
export default {
	components: { DragVerify },
	props: {
		type: {
			type: Number,
			default: () => {
				return 1
			}
		}
	},
	data() {
		return {
			isPassing: false,
			form: {
				username: '',
				code: '',
				type: this.type,
				tenant_code: null
			},
			rules: {
				username: [{ required: true, message: this.$t('login.mobileError') }],
				code: [{ required: true, message: this.$t('login.smsError') }]
			},
			disabled: false,
			time: 0,
			islogin: false
		}
	},
	mounted() {},
	methods: {
		async getYzm() {
			if (!this.isPassing) {
				this.$message.warning('请拖动滑块验证')
				return false
			}
			var validate = await this.$refs.loginForm.validateField('username').catch(() => {})
			if (!validate) {
				return false
			}
			var res = await this.$API.common.sendVerifyCode.post({
				phone: this.form.username
			})
			if (res.code === 200) {
				this.$message.success(this.$t('login.smsSent'))
				this.disabled = true
				this.time = 60
				var t = setInterval(() => {
					this.time -= 1
					if (this.time < 1) {
						clearInterval(t)
						this.disabled = false
						this.time = 0
					}
				}, 1000)
			} else {
				this.$message.warning(res.message)
				return false
			}
		},
		async login() {
			var validate = await this.$refs.loginForm.validate().catch(() => {})
			if (!validate) {
				return false
			}

			this.islogin = true
			var data = {
				username: this.form.username,
				code: this.form.code
			}
			if (this.type === 2) {
				data.type = 2
				data.tenant_code = this.form.tenant_code
			}
			//获取token
			var user = await this.$API.auth.loginForCode.post(data)
			console.log(user)
			if (user.code === 200) {
				let currentDomain = window.location.hostname
				console.log(currentDomain)
				//this.$TOOL.data.set('token', user.data.token, this.form.autologin ? 24 * 60 * 60 : 12 * 60 * 60)
				this.$TOOL.cookie.set('USER_TOKEN', user.data.token, {
					expires: this.form.autologin ? 24 * 60 * 60 : 12 * 60 * 60,
					domain: document.domain.split('.').slice(-2).join('.'),
					path: '/'
				})
				this.$TOOL.data.set('USER_INFO', user.data.userInfo)
				commonStore.saveCommonData() //保存公共数据 如校区列表信息
			} else if (user.code === 400101) {
				this.islogin = false
				this.$message.warning(user.message)
				return false
			} else {
				this.islogin = false
				this.$message.warning(user.message)
				return false
			}
			//获取菜单
			var menu = null
			/*if (this.form.user === 'admin') {
				menu = await this.$API.system.menu.myMenus.get()
			} else {
				menu = await this.$API.demo.menu.get()
			}*/
			menu = await this.$API.system.menu.myMenus.get()
			if (menu.code === 200) {
				if (menu.data.menu === null || menu.data.menu.length === 0) {
					this.islogin = false
					this.$alert('当前用户无任何权限，请联系系统管理员', '无权限访问', {
						type: 'error',
						center: true
					})
					return false
				}
				this.$TOOL.data.set('MENU', menu.data.menu)
				this.$TOOL.data.set('PERMISSIONS', menu.data.permissions)
				if (menu.data.dashboardGrid) {
					this.$TOOL.data.set('DASHBOARDGRID', menu.data.dashboardGrid)
				}
				if (menu.data.dashboardCopmsList && menu.data.dashboardLayout) {
					this.$TOOL.data.set('GRID', {
						copmsList: menu.data.dashboardCopmsList,
						layout: menu.data.dashboardLayout
					})
				}
				if (menu.data.mods !== null) {
					this.$TOOL.data.set('my-mods', menu.data.mods)
				}
			} else {
				this.islogin = false
				this.$message.warning(menu.message)
				return false
			}

			this.$router.replace({
				path: '/'
			})
			this.$message.success('Login Success 登录成功')
			this.islogin = false
		}
	}
}
</script>

<style></style>
