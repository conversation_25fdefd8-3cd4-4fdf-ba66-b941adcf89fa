<template>
	<el-drawer v-model="showDrawer" :title="title" direction="rtl" size="70%" :destroy-on-close="true" append-to-body>
		<el-tabs v-model="activeName" @tab-change="handleClick">
			<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
		</el-tabs>
		<el-main>
			<component :is="currComponent.component"></component>
		</el-main>
	</el-drawer>

</template>

<script>
import evaData from './evaData'
import evaAnalyse from './evaAnalyse'
import evaSituation from './evaSituation'
import { shallowRef } from 'vue'
export default {
	name: 'layoutTCB',
	components: {
		evaData,
		evaAnalyse,
		evaSituation
	},
	data() {
		return {
			showDrawer:false,
			title: null,
			activeName: 'evaSituation',
			tabs: [
				{
					label: '情况总览',
					name: 'evaSituation',
					component: shallowRef(evaSituation)
				},
				{
					label: '评教数据',
					name: 'evaData',
					component: shallowRef(evaData)
				},
				{
					label: '评教分析',
					name: 'evaAnalyse',
					component: shallowRef(evaAnalyse)
				}
			],
			currComponent: {},
			evaluation_info: {}
		}
	},
	created() {
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
			this.resetClassD()
		},
		resetClassD() {
			this.classData = {}
		},
		setData(val) {
			this.title = val.title
			this.showDrawer = true
			Object.assign(this.evaluation_info, val)
		}
	},
	provide() {
		return {
			evaluation_info: this.evaluation_info
		}
	}
}
</script>

<style></style>
