<template>
	<div v-if="deviceInfo.product_info?.panel_type > 0" style="padding: 0 10px">
		<Model1
			v-if="deviceInfo.product_info?.panel_type === 1"
			:propertiesList="propertiesList"
			:deviceInfo="deviceInfo"
		/>
		<Model2
			v-if="deviceInfo.product_info?.panel_type === 2"
			:propertiesList="propertiesList"
			:deviceInfo="deviceInfo"
		/>
	</div>
	<div v-else>
		<el-empty description="未配置设备控制面板" />
	</div>
</template>
<script>
import Model1 from './model_1.vue'
import Model2 from './model_2.vue'
import { ElMessage } from 'element-plus'

export default {
	name: 'debugIndex',
	props: {
		deviceInfo: {
			type: Object,
			default: () => {}
		}
	},
	components: { Model1, Model2 },
	data() {
		return {
			propertiesList: [],
			loading: false,
			refresh: false,
			timer: null,
			historyShow: false,
			chartShow: false
		}
	},
	created() {},
	unmounted() {},
	methods: {}
}
</script>

<style scoped lang="scss">
h3 {
	margin-bottom: 20px;
}

.actions {
	padding: 15px 0px;
	border-bottom: 1px solid var(--el-border-color-light);
	border-radius: 6px;
}

.countdown {
	text-align: center;
	padding-bottom: 10px;

	span {
		width: 45px;
		height: 45px;
		border: 3px solid var(--el-color-error);
		border-radius: 50%;
		display: inline-block;
		line-height: 40px;
		font-size: 18px;
		font-weight: bold;
		color: var(--el-color-error);
		text-align: center;
	}
}

.action_content {
	font-size: 14px;
	width: 80%;
	line-height: 24px;
	margin: 0 auto;
	font-weight: bold;
}

.debug-main {
	width: 100%;
	display: flex;
	border: 1px solid var(--el-border-color-light);

	.debug-left {
		width: 30%;
		height: 500px;
		border-right: 1px solid var(--el-border-color-light);
	}

	.debug-left-content {
		height: 450px;
		overflow-x: hidden;
		overflow-y: auto;
		border-bottom: 1px solid var(--el-border-color-light);
	}

	.debug-left-action {
		display: flex;
		justify-content: flex-end;
		padding: 10px;
	}

	.debug-right {
		flex: 1;
		height: 500px;
		width: 70%;

		&-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px;
			border-bottom: 1px solid var(--el-border-color-light);

			h3 {
				margin-bottom: 0;
			}
		}

		&-content {
			height: 447px;
			padding: 15px;
			overflow-x: hidden;
			overflow-y: auto;

			> div {
				margin-bottom: 10px;
			}
		}
	}
}

h2 {
	margin-bottom: 10px;
}

:deep(.el-scrollbar__wrap) {
	overflow-x: hidden !important;
}

.tslItem {
	width: 100%;
	margin-bottom: 10px;
	padding-bottom: 15px;
	border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.demo-tabs) {
	.el-tabs__item {
		font-size: 12px;
	}
}
.logItem {
	padding: 15px;
	margin-bottom: 10px;
	background-color: var(--el-color-primary-light-9);
	border: 1px solid var(--el-color-primary-light-4);
	font-size: 12px;
	border-radius: 6px;
}
</style>
