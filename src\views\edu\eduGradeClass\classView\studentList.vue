<template>
	<el-drawer
		v-model="visible"
		:title="title"
		size="70%"
		:close-on-press-escape="false"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<div>
			<el-button type="primary" icon="el-icon-CirclePlusFilled" style="margin-bottom: 10px" @click="selectStudent"
				>新增班级学生 </el-button
			><el-text v-if="studentList && studentList.length > 0" class="mx-1" size="large" type="danger">
				共有班级学生 {{ studentList.length }} 名</el-text
			>
			<el-table
				v-loading="loading"
				:data="studentList"
				element-loading-text="数据加载中"
				max-height="calc(100vh - 200px)"
			>
				<el-table-column prop="student_name" label="学生姓名" width="120" />
				<el-table-column prop="serial_number" label="学号" width="150" />
				<el-table-column prop="user_head" label="学生头像" width="100">
					<template #default="scope">
						<cusStudentHead
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.user_head"
							:preview-src-list="[scope.row.user_head]"
							preview-teleported
						>
						</cusStudentHead>
					</template>
				</el-table-column>
				<el-table-column prop="sex" label="性别" width="80">
					<template #default="{ row }">
						<span v-if="row.sex === 1">男</span>
						<span v-if="row.sex === 2">女</span>
						<span v-if="row.sex === 3">保密</span>
					</template>
				</el-table-column>
				<el-table-column prop="phone" label="手机号" />
				<el-table-column prop="status" label="状态">
					<template #default="{ row }">
						{{ formData(studentStatusMap, row.student_status) }}
					</template>
				</el-table-column>
				<el-table-column prop="position" label="职务">
					<template #default="{ row }">
						{{ row.position || '-' }}
					</template>
				</el-table-column>
				<el-table-column prop="remark" label="备注" />
				<el-table-column label="操作" fixed="right" align="center" width="210">
					<template #default="scope">
						<el-button plain type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
						<el-popconfirm title="确定把该学生移出班级吗？" @confirm="table_del(scope.row, scope.$index)">
							<template #reference>
								<el-button type="danger" size="small">移出</el-button>
							</template>
						</el-popconfirm>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<template #footer>
			<el-button @click="visible = false">关 闭</el-button>
		</template>
	</el-drawer>
	<el-dialog v-model="dialog" title="编辑" :width="500" destroy-on-close align-center>
		<cusForm ref="formref" v-model="stuForm" :config="formConfig"></cusForm>
		<template #footer>
			<el-button @click="dialog = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>

	<el-drawer
		v-model="studentSelectDialog"
		title="选择学生"
		size="50%"
		destroy-on-close
		align-center
		class="studentSelect"
	>
		<div style="margin-bottom: 10px">
			<el-date-picker
				v-model="studentSelectParams.entrance_year"
				style="width: 130px; margin-right: 10px"
				type="year"
				placeholder="入学年份"
				value-format="YYYY"
				@change="getAddStudentList"
			/>
			<el-input
				v-model="studentSelectParams.name"
				style="width: 200px"
				placeholder="姓名或学号"
				@input="getAddStudentList"
			/>
			<el-radio-group
				v-model="studentSelectParams.hasClass"
				style="margin-left: 10px; vertical-align: middle"
				@change="getAddStudentList"
			>
				<el-radio-button label="0">全部</el-radio-button>
				<el-radio-button label="1">当前学期未安排班级</el-radio-button>
			</el-radio-group>
		</div>
		<el-table
			ref="selectRef"
			v-loading="studentLoading"
			row-key="id"
			:data="list.rows"
			element-loading-text="数据加载中"
			@selection-change="selectionChange"
		>
			<el-table-column type="selection" width="55" />
			<el-table-column prop="student_name" label="学生姓名" width="100" />
			<el-table-column prop="serial_number" label="学号" width="120" />
			<el-table-column prop="entrance_year" label="入学年份" width="90" />
			<el-table-column prop="sex" label="性别" width="80">
				<template #default="{ row }">
					<span v-if="row.sex === 1">男</span>
					<span v-if="row.sex === 2">女</span>
					<span v-if="row.sex === 3">保密</span>
				</template>
			</el-table-column>
			<el-table-column prop="semester_name" label="当前学期">
				<template #default="{ row }"> {{ row.academic_name }} - {{ row.semester_name }} </template>
			</el-table-column>
			<el-table-column prop="serial_number" label="当前班级">
				<template #default="{ row }"> {{ row.grade_name }} - {{ row.class_name }} </template>
			</el-table-column>
		</el-table>
		<el-pagination
			v-model:current-page="studentSelectParams.page"
			v-model:page-size="studentSelectParams.pageSize"
			style="margin-top: 15px"
			small
			layout="total,prev, pager, next"
			:total="list.total"
			background
			@current-change="getAddStudentList"
		/>
		<div style="padding-top: 15px">
			<template v-for="(item, index) in obj" :key="index">
				<el-tag
					v-for="(child, i) in item"
					:key="i"
					closable
					style="margin-bottom: 10px"
					@close="delSelect(child, index)"
				>
					{{ child.student_name }}
				</el-tag>
			</template>
		</div>
		<template #footer>
			<el-button @click="studentSelectDialog = false">取 消</el-button>
			<el-button type="primary" :loading="stuSaveing" @click="saveStudent()">确 定</el-button>
		</template>
	</el-drawer>
</template>

<script>
import cusStudentHead from '@/components/custom/cusStudentHead.vue'
import cusTom from '@/utils/cusTom'
import { cloneDeep } from 'lodash'

const { studentStatusMap } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		tenant_id: null,
		campus_id: null,
		class_id: null
	}
}
export default {
	components: { cusStudentHead },
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			studentStatusMap,
			studentSelectParams: {
				entrance_year: '',
				name: '',
				tenant_id: null,
				campus_id: null,
				class_id: null,
				page: 1,
				pageSize: 10,
				hasClass: 0
			},
			list: {},
			mode: 'add',
			title: '班级学生管理',
			loading: false,
			studentLoading: false,
			stuSaveing: false,
			addStudentDialog: false,
			studentSelectDialog: false,
			studentList: [],
			selectIds: {},
			selectedList: [],
			selectStudentList: [
				{
					student_id: 0,
					student_name: '12313',
					sex: 1,
					serial_number: '12313',
					position: null,
					remark: null
				}
			],
			visible: false,
			dialog: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			stuForm: {
				id: null,
				tenant_id: null,
				campus_id: null,
				student_id: null,
				class_id: null,
				position: null,
				remark: null,
				student_name: null,
				mode: null
			},
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				labelWidth: '80px',
				formItems: [
					{
						label: '学生',
						name: 'student_name',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入',
							disabled: true
						}
					},
					{
						label: '职位',
						name: 'position',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入'
						}
					},
					{
						label: '备注',
						name: 'remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入备注'
						}
					}
				]
			},

			obj: {}
		}
	},
	mounted() {},
	computed: {},
	methods: {
		delSelect(item, page) {
			let arr = cloneDeep(this.obj[page])
			let selected = arr.filter((i) => i.id !== item.id)
			if (this.studentSelectParams.page == page) {
				this.list.rows.map((v) => {
					let status = selected.find((findItem) => {
						return findItem.id === v.id
					})
					if (status) {
						this.$refs.selectRef.toggleRowSelection(v, true)
					} else {
						this.$refs.selectRef.toggleRowSelection(v, false)
					}
				})
			}
			this.obj[page] = selected
		},

		selectionChange() {
			let tableSelect = this.$refs.selectRef.getSelectionRows()
			if (this.studentSelectParams.page) {
				this.selectIds[this.studentSelectParams.page] = tableSelect.map((item) => {
					return item.id
				})
				this.obj[this.studentSelectParams.page] = tableSelect
			}
			console.log(this.obj)
		},
		selectStudent() {
			this.studentSelectParams.tenant_id = this.form.tenant_id
			this.studentSelectParams.campus_id = this.form.campus_id
			this.studentSelectParams.class_id = this.form.class_id
			this.studentSelectDialog = true
			this.selectIds = {}
			this.obj = {}
			this.studentSelectParams.page = 1
			this.getAddStudentList()
		},
		async saveStudent() {
			const ids = Object.values(this.obj)
				.flat()
				.map((item) => item.id)
			if (!ids.length) {
				this.$alert('请选择学生', '提示', { type: 'error' })
				return
			}
			this.stuSaveing = true
			var res = await this.$API.eduGradeClass.classStu.add.post({
				tenant_id: this.form.tenant_id,
				campus_id: this.form.campus_id,
				class_id: this.form.class_id,
				student_ids: ids
			})
			this.stuSaveing = false
			this.studentSelectDialog = false
			if (res.code === 200) {
				this.getStudentList()
				this.$message.success('操作成功')
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		submit() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.eduGradeClass.classStu.save.post(this.stuForm)
					this.isSaveing = false
					this.dialog = false
					if (res.code === 200) {
						this.getStudentList()
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//显示
		open(data) {
			this.form.tenant_id = data.tenant_id
			this.form.campus_id = data.campus_id
			this.form.class_id = data.id
			this.getStudentList()
			this.title = this.title + ' - ' + data.grade_name + data.class_name
			this.visible = true
			return this
		},
		getAddStudentList() {
			this.studentLoading = true
			let selected = cloneDeep(this.obj[this.studentSelectParams.page])
			this.$API.eduStudent.getStudentForClassAdd.get(this.studentSelectParams).then((res) => {
				this.studentLoading = false
				if (res.code === 200) {
					this.list = res.data
					this.$nextTick(() => {
						console.log(selected)
						if (selected) {
							selected.map((v) => {
								this.$refs.selectRef.toggleRowSelection(
									this.list.rows.find((findItem) => {
										return findItem.id === v.id
									}),
									true
								)
							})
						}
					})

					/*	this.list.rows.map((v) => {
						console.log(selected.includes(v.id))
						this.$refs.selectRef.toggleRowSelection(v, selected.includes(v.id))
					})*/
				} else {
					this.list.rows = []
					this.list.total = 0
				}
			})
		},
		getStudentList() {
			this.loading = true
			this.$API.class.student
				.get({
					class_id: this.form.class_id,
					tenant_id: this.form.tenant_id,
					campus_id: this.form.campus_id
				})
				.then((res) => {
					this.loading = false
					if (res.code === 200) {
						this.studentList = res.data
					} else {
						this.studentList = []
					}
				})
		},
		//删除
		async table_del(row) {
			var reqData = {
				id: row.id,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				class_id: row.class_id
			}
			var res = await this.$API.eduGradeClass.classStu.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('移除学生成功')
				this.getStudentList()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//编辑
		table_edit(row) {
			this.stuForm.tenant_id = row.tenant_id
			this.stuForm.campus_id = row.campus_id
			this.stuForm.class_id = row.class_id
			this.stuForm.id = row.id
			this.stuForm.student_name = row.student_name
			this.stuForm.position = row.position
			this.stuForm.remark = row.remark
			this.stuForm.student_id = row.user_id
			this.dialog = true
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || '-'
		}
	}
}
</script>

<style lang="scss">
.studentSelect .el-dialog__body {
	padding: 15px !important;
}
</style>
