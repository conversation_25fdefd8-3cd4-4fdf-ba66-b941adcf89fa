<template>
	<el-drawer
		v-model="visible"
		title="班牌Tab配置"
		size="700px"
		destroy-on-close
		:close-on-press-escape="false"
		@close="close"
	>
		<div>
			<div style="position: relative; width: 660px; height: 371px">
				<el-image :src="deviceInfo.basemap" style="width: 660px; height: 371px" fit="cover"> </el-image>
				<div style="position: absolute; top: 0; left: 0">
					<el-image
						src="https://educdn.xjzredu.cn/scms/system/front/pc/classCard/noMeng.png"
						style="width: 660px; height: 371px"
						fit="contain"
					>
					</el-image>
				</div>
				<div style="width: 660px; height: 40px; position: absolute; bottom: 5px; display: flex; padding: 0 60px">
					<div
						v-for="item in deviceTabInfo"
						style="flex: 1; line-height: 40px; font-size: 14px; text-align: center; color: #fff; font-weight: bold"
					>
						<div style="display: flex; align-items: center">
							<img v-if="item.tabIcon" :src="item.tabIcon" style="width: 20px; height: 20px; margin-right: 5px" />
							<img
								v-else
								:src="'https://educdn.xjzredu.cn/scms/system/front/pc/classCard/' + item.tabKey + '.png'"
								style="width: 20px; height: 20px; margin-right: 5px"
							/>
							<span>{{ item.tabName }}</span>
						</div>
					</div>
				</div>
			</div>
			<el-alert show-icon title="拖拽排序" :closable="false" style="margin-top: 10px"> </el-alert>
			<draggable v-model="deviceTabInfo" animation="200" item-key="tabKey" group="people">
				<template #item="{ element }">
					<div
						style="
							margin: 5px 0;
							padding: 5px 10px;
							border: 1px dashed var(--el-color-primary);
							border-radius: 5px;
							cursor: move;
						"
					>
						<div style="display: flex; justify-content: space-between; align-items: center">
							<div>
								<el-tag v-if="element.tabType == 0" size="small"> 原生</el-tag>
								<el-tag v-if="element.tabType == 1" size="small"> 自定义</el-tag>
								<b style="margin-left: 10px; font-size: 14px">{{ element.tabName }}</b>
								<span v-if="element.tabUrl" style="margin-left: 15px"
									>链接: <el-link :href="element.tabUrl" target="_blank">{{ element.tabUrl }}</el-link>
								</span>
							</div>
							<div>
								<el-button type="primary" text @click="edit(element)">编辑</el-button>
								<el-button type="danger" text @click="removeTab(element.tabKey)">删除</el-button>
							</div>
						</div>
					</div>
				</template>
			</draggable>
			<el-button type="primary" style="margin-top: 10px" plain @click="creat()">新增Tab</el-button>
		</div>
		<template #footer>
			<div style="flex: auto">
				<el-button @click="close">关闭</el-button>
				<el-button type="primary" @click="save">保存</el-button>
			</div>
		</template>
	</el-drawer>
	<el-dialog v-model="showDialog" width="600" :title="dialogActionType === 1 ? '新增' : '编辑'">
		<el-form ref="formRef" :model="deviceTabItem" :rules="tabRules">
			<el-form-item label="类型" prop="tabType">
				<el-select v-model="deviceTabItem.tabType" :disabled="dialogActionType === 2">
					<el-option label="原生" :value="0"></el-option>
					<el-option label="自定义" :value="1"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item v-if="deviceTabItem.tabType === 1" label="标识符" prop="tabKey">
				<el-input
					v-model="deviceTabItem.tabKey"
					placeholder="请输入tabKey，唯一标识，只允许使用英文字母，且不能为空"
				></el-input>
			</el-form-item>
			<el-form-item v-if="deviceTabItem.tabType === 0" label="内置Tab" prop="tabKey">
				<el-select v-model="deviceTabItem.tabKey" :disabled="dialogActionType === 2" @change="changeTabKey">
					<el-option
						v-for="(item, index) in preset[deviceInfo.device_mode]"
						:key="index"
						:label="item.tabName"
						:value="item.tabKey"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="显示名称" prop="tabName">
				<el-input v-model="deviceTabItem.tabName" placeholder="请输入Tab显示名称"></el-input>
			</el-form-item>
			<el-form-item v-if="deviceTabItem.tabType === 1" label="链接" prop="tabUrl">
				<el-input v-model="deviceTabItem.tabUrl" placeholder="请输入Tab链接"></el-input>
			</el-form-item>
			<el-form-item v-if="deviceTabItem.tabType === 1" label="图标" prop="tabIcon">
				<div>
					<sc-upload
						v-model="deviceTabItem.tabIcon"
						:width="120"
						:height="120"
						icon="el-icon-pictureFilled"
						title="图标"
						:cropper="false"
						fileTypeTag="platoonHotspot"
					></sc-upload>
				</div>
				<div style="margin-left: 10px">建议请上传 32*32或64*64的白色透明底图标</div>
			</el-form-item>
			<el-form-item label="">
				<el-button type="primary" @click="showDialog = false">取消</el-button>
				<el-button type="primary" @click="dialogAction">保存</el-button>
			</el-form-item>
		</el-form>
	</el-dialog>
</template>
<script setup>
import draggable from 'vuedraggable'

import { ElMessage, ElMessageBox } from 'element-plus'

import { ref, getCurrentInstance, defineExpose } from 'vue'
import { cloneDeep } from 'lodash'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const formRef = ref()

const visible = ref(false)
const showDialog = ref(false)
const dialogActionType = ref(1)
const deviceInfo = ref({
	tenant_id: 0,
	campus_id: 0,
	device_mode: 1,
	basemap: 1,
	device_sn: ''
})
const deviceTabInfo = ref([])
const deviceTabItem = ref({
	tabKey: '',
	tabName: '',
	tabIcon: '',
	tabType: 0,
	is_all: 0,
	tabUrl: ''
})
const tabRules = ref({
	tabKey: [{ required: true, message: '请输入标识符', trigger: 'blur' }],
	tabName: [{ required: true, message: '请输入显示名称', trigger: 'blur' }],
	tabIcon: [{ required: true, message: '请上传图标', trigger: 'blur' }],
	tabUrl: [{ required: true, message: '请输入链接', trigger: 'blur' }],
	tabType: [{ required: true, message: '请选择类型', trigger: 'blur' }]
})
const currentSn = ref('')
const preset = ref({
	1: [
		{
			tabKey: 'class-home',
			tabName: '主页'
		},
		{
			tabKey: 'class-schedule',
			tabName: '课表'
		},
		{
			tabKey: 'class-notice',
			tabName: '公告'
		},
		{
			tabKey: 'class-hot',
			tabName: '热点'
		},
		{
			tabKey: 'class-live',
			tabName: '督导巡课'
		}
	],
	2: [
		{
			tabKey: 'examination-home',
			tabName: '主页'
		},
		{
			tabKey: 'examination-plan',
			tabName: '考试计划'
		}
	]
})

const changeTabKey = (val) => {
	deviceTabItem.value.tabName = preset.value[deviceInfo.value.device_mode].find((item) => item.tabKey === val).tabName
}

const dialogAction = () => {
	formRef.value.validate((valid) => {
		if (valid) {
			if (dialogActionType.value === 1) {
				//新增
				let index = deviceTabInfo.value.findIndex((item) => item.tabKey === deviceTabItem.value.tabKey)
				if (index > -1) {
					ElMessage.error('菜单标识符已存在')
					return
				}
				deviceTabInfo.value.push(deviceTabItem.value)
				showDialog.value = false
			} else {
				deviceTabInfo.value.splice(
					deviceTabInfo.value.findIndex((item) => item.tabKey === deviceTabItem.value.tabKey),
					1,
					deviceTabItem.value
				)
				showDialog.value = false
			}
		}
	})
}

const creat = () => {
	deviceTabItem.value = {
		tabKey: '',
		tabName: '',
		tabIcon: '',
		tabType: 0,
		tabUrl: ''
	}
	showDialog.value = true
	dialogActionType.value = 1
}
const edit = (item) => {
	deviceTabItem.value = cloneDeep(item)
	showDialog.value = true
	dialogActionType.value = 2
}
const removeTab = (tabKey) => {
	ElMessageBox.confirm('确定要删除吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		let index = deviceTabInfo.value.findIndex((item) => item.tabKey === tabKey)
		if (index > -1) {
			deviceTabInfo.value.splice(index, 1)
		}
	})
}

const save = () => {
	globalPropValue.platoonhotspot.saveDeviceTab
		.post({
			deviceSn: deviceInfo.value.device_sn,
			tenantId: deviceInfo.value.tenant_id,
			campusId: deviceInfo.value.campus_id,
			deviceMode: deviceInfo.value.device_mode,
			list: deviceTabInfo.value
		})
		.then((res) => {
			if (res.code === 200) {
				ElMessage.success('更新成功')
				//close()
			} else {
				ElMessage.error('更新失败；' + res.message)
			}
		})
}
// 获取热点信息
const getInfo = (tenant_id, campus_id, sn, device_mode) => {
	globalPropValue.platoonhotspot.getDeviceTab
		.get({
			deviceSn: sn,
			tenantId: tenant_id,
			campusId: campus_id,
			deviceMode: device_mode
		})
		.then((res) => {
			if (res.code === 200) {
				deviceTabInfo.value = res.data
			}
		})
		.catch((err) => {
			console.log(`获取配置失败： ${err.message}`)
		})
}

const show = (row) => {
	visible.value = true
	deviceInfo.value.tenant_id = row.tenant_id
	deviceInfo.value.campus_id = row.campus_id
	deviceInfo.value.device_sn = row.device_sn
	deviceInfo.value.device_mode = row.device_mode
	deviceInfo.value.basemap = row.basemap
		? row.basemap
		: 'https://educdn.xjzredu.cn/scms/system/front/pc/classCard/bg1.png'
	currentSn.value = getInfo(row.tenant_id, row.campus_id, row.device_sn, row.device_mode)
}
const emit = defineEmits(['refresh'])
const close = () => {
	visible.value = false
	emit('refresh')
}
defineExpose({
	show,
	close
})
</script>
