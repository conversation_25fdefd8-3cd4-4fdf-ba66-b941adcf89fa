<template>
	<el-container v-if="objData">
		<el-header>
			<el-page-header :content="`订单号：${objData.order_sn}`" @back="goBack"></el-page-header>
		</el-header>
		<el-main>
			<div class="detail-item">
				<el-descriptions title=" ">
					<el-descriptions-item label="学年名称：" label-class-name="label" class-name="content">{{
						objData.academic_name
					}}</el-descriptions-item>
					<el-descriptions-item label="学期名称：" label-class-name="label" class-name="content">{{
						objData.semester_name
					}}</el-descriptions-item>
					<el-descriptions-item label="经办人:" label-class-name="label" class-name="content">{{
						objData.order_manager_name
					}}</el-descriptions-item>
					<el-descriptions-item label="创建时间：" label-class-name="label" class-name="content">{{
						objData.created_at
					}}</el-descriptions-item>
					<!-- <el-descriptions-item label="经办日期:" label-class-name="label" class-name="content">{{
                        objData.created_at }}</el-descriptions-item> -->

					<el-descriptions-item label="缴费单名称：" label-class-name="label" class-name="content">{{
						objData.order_name
					}}</el-descriptions-item>
					<el-descriptions-item label="缴费单号：" label-class-name="label" class-name="content">{{
						objData.order_sn
					}}</el-descriptions-item>
					<el-descriptions-item label="总单数:" label-class-name="label" class-name="content">{{
						objData.total_num
					}}</el-descriptions-item>
					<el-descriptions-item label="已缴数量:" label-class-name="label" class-name="content">{{
						objData.paid_num
					}}</el-descriptions-item>
					<el-descriptions-item label="订单总金额:" label-class-name="label" class-name="content"
						>￥{{ objData.amount }}</el-descriptions-item
					>
					<el-descriptions-item label="应收金额：" label-class-name="label" class-name="content"
						>￥{{ objData.total_amount }}
					</el-descriptions-item>
					<el-descriptions-item label="订单预计总收款:" label-class-name="label" class-name="content"
						>￥{{ objData.total_amount }}</el-descriptions-item
					>
					<el-descriptions-item label="订单已收款:" label-class-name="label" class-name="content"
						>￥{{ objData.total_received }}</el-descriptions-item
					>
					<el-descriptions-item label="订单状态:" label-class-name="label" class-name="content">
						<el-tag v-if="objData.status === 1" type="danger">待发布</el-tag>
						<el-tag v-if="objData.status === 2" type="success">已发布</el-tag>
						<el-tag v-if="objData.status === -1" type="info">作废</el-tag>
					</el-descriptions-item>
					<el-descriptions-item label="创建时间：" label-class-name="label" class-name="content"
						>{{ objData.created_at }}
					</el-descriptions-item>
					<el-descriptions-item label="经办人：" label-class-name="label" class-name="content"
						>{{ objData.order_manager_name }}
					</el-descriptions-item>
					<el-descriptions-item label="备注:" label-class-name="label" class-name="content">{{
						objData.remark
					}}</el-descriptions-item>
				</el-descriptions>
			</div>
			<el-row :gutter="20">
				<el-col :span="6"></el-col>
				<el-col :span="6"><div class="grid-content ep-bg-purple" /></el-col>
			</el-row>

			<div class="detail-item">
				<div class="item-title">关联缴费项信息</div>
				<scTable hidePagination hideDo show-summary :data="objData.order_item_list" border>
					<el-table-column label="缴费项类型" prop="type_name"></el-table-column>
					<el-table-column label="缴费项名称" prop="item_name"></el-table-column>
					<el-table-column label="数量" prop="num"></el-table-column>
					<el-table-column label="金额" prop="amount"></el-table-column>
					<el-table-column label="备注" prop="remark"></el-table-column>
				</scTable>
			</div>

			<div class="detail-item">
				<div style="display: flex; justify-content: space-between; margin-bottom: 10px">
					<div class="item-title">学员缴费信息</div>
					<div>
						<el-input
							v-model="params.order_sn"
							placeholder="请输入订单号"
							style="width: 250px; margin-right: 10px"
							@input="studentTable.upData()"
						/>
						<el-input
							v-model="params.name"
							placeholder="请输入学生姓名"
							style="width: 150px; margin-right: 10px"
							@input="studentTable.upData()"
						/>
						<el-button type="primary" @click="add_student">新增缴费学员</el-button>
					</div>
				</div>
				<scTable ref="studentTable" hideDo :apiObj="tableData" :params="params" border>
					<el-table-column label="学生姓名" prop="student_name" width="120"></el-table-column>
					<el-table-column label="学生头像" prop="user_head" width="100" align="center">
						<template #default="scope">
							<cus-student-head
								loading="lazy"
								:lazy="true"
								fit="contain"
								style="width: 50px; height: 50px"
								:src="scope.row.user_head"
								:preview-src-list="[scope.row.user_head]"
								preview-teleported
							>
							</cus-student-head>
						</template>
					</el-table-column>
					<el-table-column label="学生学号" prop="serial_number" width="120"></el-table-column>
					<el-table-column label="学生班级" width="150" show-overflow-tooltip>
						<template #default="{ row }"> {{ row.grade_name }}{{ row.class_name }} </template>
					</el-table-column>
					<el-table-column label="金额" prop="pay_amount" width="150"></el-table-column>
					<el-table-column label="状态" prop="status" width="120">
						<template #default="{ row }">
							<el-tag :type="['danger', '-', 'primary', 'info', 'warning', 'success'][objData.status + 1]">{{
								statusData(pay_status, row.status)
							}}</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="学生订单号" prop="order_sn" width="230"></el-table-column>
					<el-table-column label="支付方式" prop="payment_type" width="150">
						<template #default="{ row }"> {{ formData(type, row.payment_type) }} </template>
					</el-table-column>
					<el-table-column
						label="支付方订单号"
						prop="payment_order_sn"
						width="200"
						show-overflow-tooltip
					></el-table-column>

					<el-table-column label="支付成功时间" prop="sucess_time" width="180"></el-table-column>
					<el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
					<el-table-column label="操作" fixed="right" align="center" width="180">
						<template #default="scope">
							<el-button
								v-if="[1, 2, 3, -1].indexOf(scope.row.status) !== -1"
								text
								type="primary"
								size="small"
								@click="scanPay(scope.row)"
								>扫码支付</el-button
							>
							<el-button v-if="scope.row.status === 3" plain type="primary" size="small" @click="payQuery(scope.row)"
								>支付查询</el-button
							>
							<el-popconfirm v-if="scope.row.status === 4" title="确定退款吗？" @confirm="refundPay(scope.row)">
								<template #reference>
									<el-button plain type="warning" size="small">退款</el-button>
								</template>
							</el-popconfirm>

							<el-button
								v-if="[5, 6, -2].indexOf(scope.row.status) !== -1"
								type="warning"
								size="small"
								@click="refundQuery(scope.row)"
								>退款查询</el-button
							>
							<el-popconfirm
								v-if="scope.row.status === 1 || scope.row.status === 2"
								title="确定删除吗？删除后该学生无法支付这笔订单"
								@confirm="student_del(scope.row, scope.$index)"
							>
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>
				</scTable>
			</div>
		</el-main>
		<saveDialog ref="dialog" :params="params" @success="saveSuccess"></saveDialog>
		<el-dialog v-model="qrcode.show" title="扫码支付" width="550">
			<sc-qr-code :text="qrcode.url" :size="500" colorDark="#00B42A" colorLight="#fff"></sc-qr-code>
		</el-dialog>
	</el-container>
</template>
<script setup>
import { computed, watchEffect, onMounted, getCurrentInstance, ref, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import saveDialog from './save.vue'
import cusTom from '@/utils/cusTom'
import cusStudentHead from '@/components/custom/cusStudentHead.vue'
import { ElMessage } from 'element-plus'
const route = useRoute()

// 获取当前组件实例
const instance = getCurrentInstance()
// 使用解构赋值简化属性访问
const { id: orderIdRaw, tenantId: tenantIdRaw, campusId: campusIdRaw } = route.query
const { paymentTypeMap } = cusTom.getBaseQuery()

// 安全地处理查询参数
const orderId = computed(() => orderIdRaw || '').value.trim()
const tenantId = computed(() => tenantIdRaw || '').value.trim()
const campusId = computed(() => campusIdRaw || '').value.trim()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const getOrderId = () => orderId
let objData = ref()
const defaultParams = () => {
	return {
		order_id: getOrderId(),
		tenant_id: tenantId,
		campus_id: campusId,
		order_sn: '',
		name: ''
	}
}
let qrcode = ref({
	show: false,
	url: ''
})
let params = ref(defaultParams())
let tableData = ref(globalPropValue.finance.recruit.orderDetailList)
let studentTable = ref()
let type = ref(paymentTypeMap)
let pay_status = ref([
	{
		name: '待发布',
		value: 1
	},
	{
		name: '待支付',
		value: 2
	},
	{
		name: '支付中',
		value: 3
	},
	{
		name: '支付完成',
		value: 4
	},
	{
		name: '支付失败',
		value: -1
	}
])
let dialog = ref()
// 获取数据的函数
const fetchData = async (orderId = getOrderId()) => {
	// 根据查询参数获取数据的逻辑
	const { data } = await globalPropValue.finance.recruit.orderDetail.get({
		id: orderId,
		tenant_id: tenantId,
		campus_id: campusId
	})
	objData.value = data
}
const add_student = () => {
	nextTick(() => {
		dialog.value.open('add')
	})
}
const saveSuccess = () => {
	studentTable.value.upData()
	fetchData()
}
// 组件加载时初始化数据
onMounted(() => {
	fetchData()
})
const student_del = async (row) => {
	const reqData = { id: row.id, tenant_id: Number(tenantId), campus_id: Number(campusId) }
	const res = await globalPropValue.finance.recruit.orderDetailDel.post(reqData)
	if (res.code === 200) {
		saveSuccess()
		ElMessage({ type: 'success', message: '删除成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}

const scanPay = async (row) => {
	const reqData = { order_sn: row.order_sn, tenant_id: Number(tenantId), campus_id: Number(campusId) }
	const res = await globalPropValue.finance.recruit.orderQrpay.post(reqData)
	if (res.code === 200) {
		qrcode.value.show = true
		qrcode.value.url = res.data.qr_url
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
const refundPay = async (row) => {
	const reqData = { order_sn: row.order_sn, tenant_id: Number(tenantId), campus_id: Number(campusId) }
	const res = await globalPropValue.finance.recruit.refund.post(reqData)
	if (res.code === 200) {
		ElMessage({ type: 'success', message: '退款提交成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
const refundQuery = async (row) => {
	const reqData = { order_sn: row.order_sn, tenant_id: Number(tenantId), campus_id: Number(campusId) }
	const res = await globalPropValue.finance.recruit.refundQuery.post(reqData)
	if (res.code === 200) {
		let orderData = res.data
		if (orderData.status === 6) {
			ElMessage({ type: 'success', message: '退款成功' })
		} else if (orderData.status === -2) {
			ElMessage({ type: 'error', message: '退款失败' })
		} else {
			ElMessage({ type: 'warning', message: '退款中' })
		}
		console.log(res)
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
const payQuery = async (row) => {
	const reqData = { order_sn: row.order_sn, tenant_id: Number(tenantId), campus_id: Number(campusId) }
	const res = await globalPropValue.finance.recruit.orderQuery.post(reqData)
	if (res.code === 200) {
		let orderData = res.data
		if (orderData.status === 4) {
			ElMessage({ type: 'success', message: '支付完成 ' })
		} else if (orderData.status === -1) {
			ElMessage({ type: 'error', message: '支付失败' })
		} else {
			ElMessage({ type: 'warning', message: '支付中' })
		}
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
const goBack = () => {
	window.history.back()
}
//数据回显格式化
const formData = (arr, val) => {
	return arr.find((v) => v.value == val)?.name || '-'
}
const statusData = (arr, val) => {
	return arr.find((v) => v.value == val)?.name || '-'
}
</script>
<style lang="scss" scoped>
.detail-item {
	margin-bottom: 20px;
	padding: 10px;
	background-color: var(--el-bg-color-overlay);

	.item-title {
		font-size: 16px;
		font-weight: bold;
		margin-bottom: 10px;
	}

	&:nth-child(1):deep(.el-descriptions) {
		border: 1px solid #ebeef5;
	}

	:deep(.label) {
		background-color: var(--el-bg-color-overlay);
		padding-left: 11px;
	}
}
</style>
