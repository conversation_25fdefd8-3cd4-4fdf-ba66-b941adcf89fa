<template>
  <!-- 使用 v-if 判断元素是否可见 -->
  <div v-if="element.type === 'divider' && isElementVisible">
    <el-divider :direction="element.options.direction" :border-style="element.options.borderStyle"
      :content-position="element.options.contentPosition">
      {{ element.options.content }}
    </el-divider>
  </div>
  <div style="margin-bottom: 18px;" v-if="element.type === 'text' && isElementVisible">
    <div v-html="element.options.defaultValue"></div>
  </div>
  <div style="text-align: center;margin-bottom: 18px;" v-if="element.type === 'insert-img' && isElementVisible">
    <el-image :src="element.options.defaultValue" fit="cover">
      <template #error>
        请上传插入图片
      </template>
    </el-image>
  </div>

  <el-form-item
    v-if="element && element.type !== 'divider' && element.type !== 'text' && element.type !== 'headImage' && element.type !== 'title' && element.type !== 'insert-img' && isElementVisible"
    :key="element.key" :label="element.label" :prop="element.model" :rules="element.options.rules">
    <template v-if="element.type === 'input'">
      <el-input v-model="data" :style="{ width: element.options.width }" :placeholder="element.options.placeholder"
        :minlength="parseInt(element.options.minlength)" :maxlength="parseInt(element.options.maxlength)"
        :clearable="element.options.clearable" :readonly="element.options.readonly"
        :disabled="disabled || element.options.disabled" :show-word-limit="element.options.showWordLimit">
        <template #prefix v-if="element.options.prefix">{{ element.options.prefix }}</template>
        <template #suffix v-if="element.options.suffix">{{ element.options.suffix }}</template>
        <template #prepend v-if="element.options.prepend">{{ element.options.prepend }}</template>
        <template #append v-if="element.options.append">{{ element.options.append }}</template>
      </el-input>
    </template>

    <template v-if="element.type === 'textarea'">
      <el-input type="textarea" v-model="data" :rows="element.options.rows" :style="{ width: element.options.width }"
        :placeholder="element.options.placeholder" :minlength="parseInt(element.options.minlength)"
        :maxlength="parseInt(element.options.maxlength)" :show-word-limit="element.options.showWordLimit"
        :autosize="element.options.autosize" :clearable="element.options.clearable" :readonly="element.options.readonly"
        :disabled="disabled || element.options.disabled" />
    </template>

    <template v-if="element.type === 'number'">
      <el-input-number v-model="data" :style="{ width: element.options.width }" :max="element.options.max"
        :min="element.options.min" :disabled="disabled || element.options.disabled" />
    </template>

    <template v-if="element.type === 'radio'">
      <el-radio-group v-model="data" :style="{ width: element.options.width, display: 'block' }"
        :disabled="disabled || element.options.disabled">
        <p class="radio-style" v-for="item of element.options.remote
          ? element.options.remoteOptions
          : element.options.options" :key="item.value">
          <el-radio :label="item.value" :style="{
            display: element.options.inline ? 'inline-block' : 'block'
          }">{{ element.options.showLabel ? item.label : item.value }}</el-radio>
        </p>
      </el-radio-group>
    </template>

    <template v-if="element.type === 'checkbox'">
      <el-checkbox-group v-model="data" :style="{ width: element.options.width }"
        :disabled="disabled || element.options.disabled" :min="element.options.min" :max="element.options.max">
        <p class="radio-style checkbox-style" v-for="item of element.options.remote
          ? element.options.remoteOptions
          : element.options.options" :key="item.value">
          <el-checkbox :value="item.value" :style="{
            display: element.options.inline ? 'inline-block' : 'block'
          }">
            {{
              element.options.showLabel ? item.label : item.value
            }}
          </el-checkbox>
        </p>
      </el-checkbox-group>
    </template>

    <template v-if="element.type === 'time'">
      <el-time-picker v-model="data" :placeholder="element.options.placeholder" :readonly="element.options.readonly"
        :editable="element.options.editable" :clearable="element.options.clearable" :format="element.options.format"
        :value-format="element.options.valueFormat" :disabled="disabled || element.options.disabled"
        :style="{ width: element.options.width }" />
    </template>

    <template v-if="element.type === 'date'">
      <el-date-picker v-model="data" :placeholder="element.options.placeholder" :readonly="element.options.readonly"
        :editable="element.options.editable" :clearable="element.options.clearable" :type="element.options.type"
        :format="element.options.valueFormat" :value-format="element.options.format"
        :disabled="disabled || element.options.disabled" :style="{ width: element.options.width }" />
    </template>

    <template v-if="element.type === 'rate'">
      <el-rate v-model="data" :max="element.options.max" :allowHalf="element.options.allowHalf"
        :disabled="disabled || element.options.disabled" />
    </template>

    <template v-if="element.type === 'select'">
      <el-select v-model="data" :multiple="element.options.multiple" :collapseTags="element.options.collapseTags"
        :placeholder="element.options.placeholder" :clearable="element.options.clearable"
        :multipleLimit="element.options.multipleLimit" :filterable="element.options.filterable"
        :disabled="disabled || element.options.disabled" :style="{ width: element.options.width }">
        <el-option v-for="item of element.options.remote
          ? element.options.remoteOptions
          : element.options.options" :key="item.value" :value="item.value"
          :label="element.options.showLabel ? item.label : item.value" />
      </el-select>
    </template>

    <template v-if="element.type === 'switch'">
      <el-switch v-model="data" :active-text="element.options.activeText" :active-value="element.options.activeText"
        :inactive-text="element.options.inactiveText" :inactive-value="element.options.inactiveText"
        :disabled="disabled || element.options.disabled" />
    </template>

    <!-- <template v-if="element.type == 'text'">
      <span>{{ element.options.defaultValue }}</span>
    </template> -->

    <template v-if="element.type === 'img-upload'">
      <sc-upload-Multiple v-model="data" :limit="element.options.limit" :file-list="element.options.defaultValue"
        :name="element.options.file" :disabled="element.options.disabled"
        :multiple="element.options.multiple"></sc-upload-Multiple>
    </template>

    <template v-if="element.type === 'file-upload'">
      <sc-upload-file v-model="data" :limit="element.options.limit" :file-list="element.options.defaultValue"
        :name="element.options.file" :disabled="element.options.disabled"
        :multiple="element.options.multiple"></sc-upload-file>
    </template>
    <template v-if="element.type === 'divider'">
      <el-divider :direction="element.options.direction" :border-style="element.options.borderStyle"
        :content-position="element.options.contentPosition">
        {{ element.options.content }}
      </el-divider>
    </template>
  </el-form-item>
</template>

<script setup>
import { computed, toRefs, watchEffect } from 'vue'
import { widgetForm } from '../components'

const props = defineProps({
  model: {
    type: Object,
    default: () => widgetForm
  },
  element: {
    type: Object,
    default: () => widgetForm
  },
  config: {
    type: Object,
    default: () => widgetForm
  },
  disabled: Boolean
});
const { model, element, disabled } = toRefs(props)

// 新增计算属性：判断元素是否可见
const isElementVisible = computed(() => {
  const showCode = element.value.options.showCode;
  if (!showCode) return true; // 如果没有显示条件，默认显示

  const { model: showModel, val: showVal } = showCode;
  const currentValue = model.value[showModel];

  // 如果当前值是数组（多选情况）
  if (Array.isArray(currentValue)) {
    // 如果目标值也是数组，检查是否有交集
    if (Array.isArray(showVal)) {
      return showVal.some(val => currentValue.includes(val));
    }
    // 如果目标值是单个值，检查是否包含
    return currentValue.includes(showVal);
  }

  // 如果目标值是数组，检查当前值是否在数组中
  if (Array.isArray(showVal)) {
    return showVal.includes(currentValue);
  }

  // 单选情况，直接比较值
  return currentValue === showVal;
});

const data = computed({
  get: () => model.value[element.value.model],
  set: val => {
    // 在需要时确保 model 是响应式的
    model.value[element.value.model] = val
  }
})

// 监听组件初始化，处理defaultValue
watchEffect(() => {
  if (element.value?.options?.defaultValue !== undefined && element.value?.options?.defaultValue !== null) {
    // 确保model对象存在
    if (!model.value) {
      model.value = {}
    }

    // 设置默认值
    model.value[element.value.model] = element.value.options.defaultValue
  }
})

const handleFilterOption = (input, option) => {
  option.label.toLowerCase().includes(input)
}

const handleUploadSuccess = (_res, _file, fileList) => {
  data.value = fileList
}

// 不需要显式的 return 语句

</script>
<style lang="scss" scoped>
::v-deep .el-form-item__label {
  font-size: 16px;
}

.radio-style {
  // line-height: 25px;
  width: 100%;
  box-sizing: border-box;
  padding: 1px 8px;
  font-size: 14px;
  color: #52555a;
  font-weight: 400;
  background: #fbfcfd;
  border: 1px solid #dcdfe4;
  display: block;

  &:nth-child(n+2) {
    border-top: none;
  }
}

.checkbox-style {
  line-height: 25px;
  padding: 5px 8px;
}
</style>
