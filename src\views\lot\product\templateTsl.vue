<template>
	<el-dialog v-model="visible" title="功能定义" width="700" destroy-on-close @closed="closed">
		<scTable
			ref="templateTable"
			paginationLayout="total, prev, pager, next"
			row-key="id"
			:apiObj="apiObj"
			:page-size="10"
			:params="params"
			:hideDo="true"
		>
			<el-table-column label="功能类型" prop="tsl_type">
				<template #default="scope">
					<span v-if="scope.row.tsl_type === 1">属性类型</span>
					<span v-if="scope.row.tsl_type === 2">事件类型</span>
					<span v-if="scope.row.tsl_type === 3">服务类型</span>
				</template>
			</el-table-column>
			<el-table-column label="功能名称" prop="name"></el-table-column>
			<el-table-column label="标识符" prop="code"></el-table-column>
			<el-table-column label="数据类型" prop="type">
				<template #default="scope">
					<span v-if="scope.row.tsl_type === 1">
						{{ scope.row.type_spec.type }}（{{ typeConfig[scope.row.type_spec.type] }}）
					</span>
					<span v-else>-</span>
				</template>
			</el-table-column>
		</scTable>
	</el-dialog>
</template>

<script>
import ScTable from '@/components/scTable/index.vue'

const defaultParams = () => {
	return {
		name: null,
		tsl_type: 0,
		template_id: 0
	}
}
export default {
	components: { ScTable },
	emits: ['closed'],
	props: {},

	data() {
		return {
			mode: 'add',
			typeConfig: {
				int: '整数型',
				bool: '布尔型',
				float: '浮点型',
				text: '字符串',
				enum: '枚举型',
				date: '时间型',
				struct: '结构体',
				array: '数组'
			},
			params: defaultParams(),
			apiObj: this.$LotApi.templateTsl.listPage,
			visible: false,
			isSaveing: false,
			//表单数据
			enumConfig: []
		}
	},
	mounted() {},
	created() {},
	methods: {
		closed() {
			this.visible = false
			this.$emit('closed')
		},
		//显示
		open(data) {
			this.params.template_id = data.id
			this.visible = true
			return this
		}
	}
}
</script>

<style></style>
