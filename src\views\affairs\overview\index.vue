<template>
	<el-container>
		<el-header v-if="CampusManagementList.length > 1">
			<div class="left-panel">
				<div class="left-panel-search">
					<el-select
						v-if="CampusManagementList.length > 1"
						v-model="params.campus_id"
						placeholder="校区"
						filterable
						style="margin-right: 15px"
						@change="campusChange"
					>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</div>
			</div>
		</el-header>
		<el-main style="background-color: unset; padding: unset">
			<div v-loading="loading" class="echartsOut">
				<el-row :gutter="20">
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.room_count" class="room">
								<template #title>场室 / 开放场室</template>
								<template #suffix> / {{ overviewData.open_room_count }}</template>
							</el-statistic>
						</el-card>
					</el-col>
					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.assets_count">
								<template #title>资产设备</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>

					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.consumable_count">
								<template #title>耗材</template>
								<template #suffix></template>
							</el-statistic>
						</el-card>
					</el-col>

					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.car_count">
								<template #title>车辆</template>
								<template #suffix>辆</template>
							</el-statistic>
						</el-card>
					</el-col>

					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.repair_count">
								<template #title>校园报修</template>
								<template #suffix>次</template>
							</el-statistic>
						</el-card>
					</el-col>

					<el-col :span="4">
						<el-card shadow="hover">
							<el-statistic :value="overviewData.room_booking_count">
								<template #title>场室预约</template>
								<template #suffix>次</template>
							</el-statistic>
						</el-card>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="roomType" width="100%" height="400px"></scEcharts>
						</el-card>
					</el-col>
					<el-col :span="12">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="repairType" width="100%" height="400px"></scEcharts>
						</el-card>
					</el-col>
					<el-col :span="12">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="assetsType" width="100%" height="400px"></scEcharts>
						</el-card>
					</el-col>
					<el-col :span="12">
						<el-card shadow="hover">
							<scEcharts class="scEcharts" :option="consumableType" width="100%" height="400px"></scEcharts>
						</el-card>
					</el-col>
				</el-row>
			</div>
		</el-main>
	</el-container>
</template>
<script>
import cusTom from '@/utils/cusTom'
import scEcharts from '@/components/scEcharts/index.vue'
import { ElMessage } from 'element-plus'

const { campusInfo, tenantId, campusId } = cusTom.getBaseQuery()

export default {
	name: 'index',
	components: {
		scEcharts
	},
	data() {
		return {
			params: {
				tenant_id: tenantId,
				campus_id: campusId,
				semester_id: null
			},
			loading: false,
			overviewData: {},
			CampusManagementList: [],
			roomType: {
				color: [
					'#6F5EF9',
					'#6DC8EC',
					'#945FB9',
					'#FF9845',
					'#1E9493',
					'#FF99C3',
					'#AABA01',
					'#BC7CFC',
					'#237CBC',
					'#2DE379',
					'#CE8032',
					'#FF7AF4',
					'#545FD3',
					'#AFE410',
					'#D8C608',
					'#FFA1E0'
				],
				title: {
					text: '场室类型分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			repairType: {
				color: [
					'#1E9493',
					'#6F5EF9',
					'#6DC8EC',
					'#FF9845',
					'#1E9493',
					'#FF99C3',
					'#BC7CFC',
					'#CE8032',
					'#FF7AF4',
					'#545FD3',
					'#AFE410',
					'#D8C608',
					'#FFA1E0'
				],
				title: {
					text: '校园报修',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			assetsType: {
				color: ['#237CBC'],
				title: {
					text: '资产设备分布',
					subtext: ''
				},
				grid: {
					top: '80px'
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: []
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						barWidth: 20,
						data: [],
						type: 'bar'
					}
				]
			},
			consumableType: {
				color: ['#6F5EF9'],
				title: {
					text: '耗材分布',
					subtext: ''
				},
				grid: {
					top: '80px'
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: []
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						barWidth: 20,
						data: [],
						type: 'bar'
					}
				]
			}
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	created() {
		//获取总览数据
		this.CampusManagementList = campusInfo
		this.getData()
	},
	methods: {
		campusChange(val) {
			this.params.campus_id = val
			this.getData()
		},
		async getData() {
			this.loading = true
			const res = await this.$API.common.affOverview.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.overviewData = res.data

				if (res.data.room_type !== null) {
					this.roomType.series[0].data = res.data.room_type.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.roomType.series[0].data = []
				}
				if (res.data.repair_type !== null) {
					this.repairType.series[0].data = res.data.repair_type.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.repairType.series[0].data = []
				}

				if (res.data.assets_type !== null) {
					this.assetsType.series[0].data = res.data.assets_type.map((v) => {
						return v.count
					})

					this.assetsType.xAxis.data = res.data.assets_type.map((v) => {
						return v.name
					})
				} else {
					this.assetsType.series[0].data = []
					this.assetsType.xAxis.data = []
				}

				if (res.data.consumable_type !== null) {
					this.consumableType.series[0].data = res.data.consumable_type.map((v) => {
						return v.count
					})

					this.consumableType.xAxis.data = res.data.consumable_type.map((v) => {
						return v.name
					})
				} else {
					this.consumableType.series[0].data = []
					this.consumableType.xAxis.data = []
				}
			} else {
				ElMessage({ type: 'error', message: res.message })
			}
		}
	}
}
</script>
<style scoped lang="scss">
.echartsOut {
	margin-top: 10px;
}

:deep(.el-statistic) {
	.el-statistic__number {
		font-size: 26px;
	}

	.el-statistic__head {
		font-size: 16px;
		font-weight: bold;
		line-height: 30px;
	}

	.el-statistic__content {
		text-align: center;
		line-height: 30px;
	}

	.el-statistic__suffix {
		color: var(--el-text-color-secondary);
		font-size: 14px;
	}
}

:deep(.room) {
	.el-statistic__suffix {
		font-size: 26px;
		color: var(--el-text-color-primary);
	}
}
</style>
