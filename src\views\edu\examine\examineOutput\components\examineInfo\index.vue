<template>
	<div class="examine-content">
		<div class="examine-box">
			<div class="add-btn">
				<p>考试信息</p>
				<el-button type="primary" @click="exportExcel">导出</el-button>
			</div>
			<div class="info-list">
				<div class="info-item">
					<p>考试说明</p>
					<div class="info-item-content">
						{{ examineData.remark }}
					</div>
				</div>
				<div class="info-item">
					<p>主考</p>
					<div class="info-item-content">
						<template v-for="(item, index) in staffData.chief" :key="item.id">
							<el-divider v-if="index !== 0" direction="vertical" />
							<span>{{ item.label }}</span>
						</template>
					</div>
				</div>
				<div class="info-item">
					<p>副主考</p>
					<div class="info-item-content">
						<template v-for="(item, index) in staffData.deputy" :key="item.id">
							<el-divider v-if="index !== 0" direction="vertical" />
							<span>{{ item.label }}</span>
						</template>
					</div>
				</div>
				<div class="info-item">
					<p>巡考</p>
					<div class="info-item-content">
						<template v-for="(item, index) in staffData.patrol" :key="item.id">
							<el-divider v-if="index !== 0" direction="vertical" />
							<span>{{ item.label }}</span>
						</template>
					</div>
				</div>
				<div class="info-item">
					<p>机动</p>
					<div class="info-item-content">
						<template v-for="(item, index) in staffData.flexible" :key="item.id">
							<el-divider v-if="index !== 0" direction="vertical" />
							<span>{{ item.label }}</span>
						</template>
					</div>
				</div>
				<div class="info-item">
					<p>监考</p>
					<div class="info-item-content">
						<template v-for="(item, index) in staffData.invigilate" :key="item.id">
							<el-divider v-if="index !== 0" direction="vertical" />
							<span>{{ item.label }}</span>
						</template>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import cusTom from '@/utils/cusTom'
import { excelExport } from 'pikaz-excel-js'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()

const props = defineProps({
	examineData: {
		type: Object,
		default: () => {}
	}
})

const typeMap = reactive({
	1: 'chief',
	2: 'deputy',
	3: 'patrol',
	4: 'flexible',
	5: 'invigilate'
})

const exportExcel = async () => {
	await excelExport({
		// 表格数据
		sheet: [
			{
				// 表格标题
				title: props.examineData.examine_name,
				// 表头
				// tHeader: ['人员类型', "人员列表"],
				// 数据键名
				keys: ['type', 'names'],
				// 表格数据
				table: [
					{ type: '主考', names: staffData.value.chief?.map((item) => item.label).join(',') },
					{ type: '副主考', names: staffData.value.deputy?.map((item) => item.label).join(',') },
					{ type: '巡考', names: staffData.value.patrol?.map((item) => item.label).join(',') },
					{ type: '机动', names: staffData.value.flexible?.map((item) => item.label).join(',') },
					{ type: '监考', names: staffData.value.invigilate?.map((item) => item.label).join(',') }
				],
				colWidth: [15, 60],
				globalStyle: {
					font: {
						sz: 14
					}
				},
				sheetName: props.examineData.examine_name,
				cellStyle: [
					{ cell: 'A1', font: { bold: true, sz: 18 } },
					{ cell: 'B2', alignment: { vertical: 'center', horizontal: 'left', wrapText: true } },
					{ cell: 'B3', alignment: { vertical: 'center', horizontal: 'left', wrapText: true } },
					{ cell: 'B4', alignment: { vertical: 'center', horizontal: 'left', wrapText: true } },
					{ cell: 'B5', alignment: { vertical: 'center', horizontal: 'left', wrapText: true } },
					{ cell: 'B6', alignment: { vertical: 'center', horizontal: 'left', wrapText: true } }
				]
			}
		],
		// 文件名称
		filename: props.examineData.examine_name
	})
}

const staffData = ref({})
const getStaffList = () => {
	globalPropValue.examine.staffList
		.get({
			tenant_id: tenantId,
			campus_id: campusId,
			examine_id: query.id
		})
		.then((res) => {
			if (res.code === 200) {
				// 使用 reduce 分组，并通过 typeMap 映射 key
				const groupedData = res.data.reduce((acc, item) => {
					const userType = item.user_type // 获取当前 user_type
					// 检查 user_type 是否存在于 typeMap 中
					if (typeMap.hasOwnProperty(userType)) {
						const key = typeMap[userType] // 映射为value称作为键
						const staffInfoValue = { label: item.staff_info.name, id: item.staff_info.id }
						acc[key] = [...(acc[key] || []), staffInfoValue] // 合并到对应的键数组中
					}
					return acc
				}, {})
				staffData.value = groupedData
			}
			console.log(staffData.value)
		})
}

onMounted(() => {
	getStaffList()
})
// 计算
const invigilateCount = computed(() => {
	return staffData.value.invigilate?.length || 0
})
</script>

<style lang="scss" scoped>
.examine-content {
	display: flex;
	justify-content: center;

	.examine-box {
		width: 50%;

		.add-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-bottom: 10px;
			border-bottom: 1px solid #e5e5e5;

			p {
				font-size: 14px;
			}
		}

		.info-list {
			padding: 10px 0;

			.info-item {
				margin-bottom: 20px;

				p {
					font-size: 14px;
					margin-bottom: 5px;
				}

				&-content {
					padding: 10px;
					background: #f6f8f9;
					color: var(--el-color-info);

					span {
						line-height: 2;
					}
				}
			}
		}
	}
}
</style>
