<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search" style="padding-right: 0px">
					<el-form-item v-if="CampusManagementList.length > 1" label="">
						<el-select v-model="params.obj.campus_id" placeholder="请选择校区" filterable style="width: 150px">
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="" style="margin-left: 15px">
						<cusSelectSemester
							v-model="params.obj.semester_id"
							:params="params.obj"
							:show-default-value="true"
							:width="'214px'"
							clearable
							style="margin-right: 15px"
						/>
						<!--
						<cusCascader v-model="params.obj.semester_id" placeholder="请选择学期" :options="getSemester"></cusCascader>
-->
					</el-form-item>
					<el-form-item>
						<el-cascader
							v-model="params.obj.class_id"
							:options="myClass.list"
							placeholder="请选择班级"
							:props="{ multiple: false, label: 'name', value: 'id', children: 'class_list', emitPath: false }"
							clearable
						></el-cascader>
					</el-form-item>

					<el-form-item label="" prop="course_id">
						<el-select v-model="params.obj.course_id" placeholder="选择学科" filterable clearable style="width: 120px">
							<el-option
								v-for="item in getCourse"
								:key="item.id"
								:label="item.course_name"
								:value="item.id"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.obj.name" placeholder="请输入作业名称" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
					<el-button type="success" icon="el-icon-plus" @click="add">新增作业</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" pageSize="10" row-key="id" :params="params.obj" :apiObj="apiObj">
				<el-table-column label="作业" prop="work_name" fixed="left" show-overflow-tooltip>
					<template #default="{ row }">
						<div style="line-height: 40px">
							<el-link
								v-if="row.work_type === 1"
								type="primary"
								:underline="false"
								style="font-size: 16px"
								@click="goWorkTopic(row)"
								>{{ row.work_name }}
							</el-link>
							<el-link v-else :underline="false" style="font-size: 16px">{{ row.work_name }} </el-link>
						</div>
						<div>
							<template v-if="row.course_name">
								<span>{{ row.course_name }}</span>
								<el-divider direction="vertical" />
							</template>
							<span>{{ row.academic_name }}</span>
							<el-divider direction="vertical" />
							<span>{{ row.semester_name }}</span>
						</div>
						<div>
							<template v-if="row.class_name">
								<span>{{ row.class_name }}</span>
							</template>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="类型" prop="work_type" width="150">
					<template #default="{ row }">
						<el-tag v-if="row.work_type === 1" type="primary">线上</el-tag>
						<el-tag v-else type="success">线下</el-tag>
					</template>
				</el-table-column>

				<el-table-column label="来源" prop="created_user_name" width="150"></el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="200"></el-table-column>
				<el-table-column label="状态" fixed="right" align="center" width="150">
					<template #default="{ row }">
						<el-tag v-if="row.status === 1" type="success">已发布</el-tag>
						<el-tag v-if="row.status === -1" type="info">未发布</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" fixed="right" align="left" width="350">
					<template #default="{ row }">
						<el-button-group>
							<el-button v-if="row.work_type === 1" text type="primary" size="small" @click="goWorkTopic(row)">
								<template v-if="row.status === -1">管理</template>
								<template v-if="row.status === 1">查看</template>
							</el-button>
							<el-button v-if="row.status === -1" text type="success" size="small" @click="publishFun(row)"
								>发布
							</el-button>
							<el-button v-if="row.status === 1" text type="primary" size="small" @click="correct(row)">批阅</el-button>
							<el-button
								v-if="row.allow_comments === 1 && row.status === 1"
								text
								type="primary"
								size="small"
								@click.stop="lookComment(row)"
								>查看评论
							</el-button>
							<el-button v-if="row.status === -1" text type="primary" size="small" @click="edit(row)">编辑</el-button>
							<el-popconfirm v-if="row.status === -1" title="确定删除吗？" @confirm="delFun(row)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
							<el-button v-if="row.status === 1" text type="warning" size="small" @click="cacelFun(row)"
								>撤销发布
							</el-button>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<addWork
			ref="addWorkRef"
			:campusId="campusId"
			:tenantId="tenantId"
			:semesterInfo="semesterInfo"
			:myGrade="myGrade.list"
			:myClass="myClass.list"
			:course="course.list"
			@suc="success"
		></addWork>
		<correctWork ref="correctWorkRef"></correctWork>
		<workComment ref="workCommentRef"></workComment>
	</el-container>
</template>

<script setup>
import { reactive, ref, toRef, computed, onMounted, getCurrentInstance, watch } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage, ElAlert, ElMessageBox } from 'element-plus'
import addWork from './addWork'
import correctWork from './correctWork'
import workComment from './workComment'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'

const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: '',
		academic_id: '',
		grade_id: '',
		class_id: [],
		discipline_id: '',
		course_id: '',
		name: ''
	}
}
const { proxy } = getCurrentInstance()
// 获取当前组件实例
const instance = getCurrentInstance()
// 获取路由实例
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
let apiObj = ref(globalPropValue.eduWork.list)

const addWorkRef = ref(null)
const correctWorkRef = ref(null)
const workCommentRef = ref(null)
/*let workData = reactive({
	list: [],
	total: 0
})*/
let params = reactive({
	obj: defaultParams()
})
/*let last_list_id = computed(() => {
	return params.obj.class_id[params.obj.class_id.length - 1]
})*/
let CampusManagementList = reactive(campusInfo)
let myGrade = reactive({
	list: []
})
let myClass = reactive({
	list: []
})
/*const disciplineOptions = reactive({
	list: []
})*/
const course = reactive({
	list: []
})
let getSemester = computed(() => {
	return cusTom.arrayToTree(
		semesterInfo.filter((v) => v.campus_id == params.obj.campus_id),
		'value',
		'parent_id'
	)
})
/*let getGrade = computed(() => {
	return myGrade.list.filter(
		(v) => v.parent_id != 0 && v.semester_id == params.obj.semester_id && v.campus_id == params.obj.campus_id
	)
})*/
let getClass = computed(() => {
	return myClass.list
})
let table = ref()
let getCourse = computed(() => {
	return course.list.filter((item) => {
		return item.campus_id == params.obj.campus_id
	})
})
watch(
	() => params.obj.campus_id,
	(val) => {
		params.obj.academic_id = null
		getClassData()
	}
)
watch(
	() => params.obj.academic_id,
	(val) => {
		params.obj.semester_id = null
		getClassData()
	}
)
watch(
	() => params.obj.semester_id,
	(val) => {
		//params.obj.grade_id = null
		getClassData()
	}
) /*
watch(
	() => params.obj.grade_id,
	(val) => {
		params.obj.class_id = null
	}
)*/
const getGradeData = async () => {
	var res = await globalPropValue.eduGradeClass.grade.all.get(params.obj)
	myGrade.list = res.data
}
const getClassData = async () => {
	var res = await globalPropValue.eduGradeClass.class.all.get(params.obj)
	myClass.list = res.data
}
/*const getDisciplineOptions = async () => {
	let { data } = await globalPropValue.eduDiscipline.discipline.all.get({ tenant_id: tenantId, campus_id: campusId })
	disciplineOptions.list = data
}*/
const getEduCourse = async () => {
	let { data } = await globalPropValue.eduCourseSet.course.all.get({ tenant_id: tenantId, campus_id: campusId })
	course.list = data
}
/*
const upsearch = () => {
	params.obj.page = 1
	params.obj.class_id = last_list_id.value
	getWorkList()
}
*/

const upsearch = () => {
	table.value.upData(params.obj)
}
const refresh = () => {
	params.obj = defaultParams()
	upsearch()
}
const success = () => {
	table.value.refresh()
}
const add = () => {
	addWorkRef.value.show('add')
}
const edit = async (item) => {
	addWorkRef.value.show('edit', item)
}

const publishFun = async (item) => {
	ElMessageBox.confirm('确定发布吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'success'
	}).then(async () => {
		var reqData = { id: item.id, tenant_id: params.obj.tenant_id, campus_id: params.obj.campus_id }
		var res = await globalPropValue.eduWork.publish.post(reqData)
		if (res.code === 200) {
			ElMessage.success('发布成功')
			upsearch()
		} else {
			ElAlert(res.message, '提示', { type: 'error' })
		}
	})
}

const cacelFun = async (item) => {
	ElMessageBox.confirm(
		'确定撤销发布吗？<p style="color: red"><strong>撤销后学生当前已提交的作业将作废！</strong></p>',
		'谨慎操作',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
			dangerouslyUseHTMLString: true
		}
	).then(async () => {
		var reqData = { id: item.id, tenant_id: params.obj.tenant_id, campus_id: params.obj.campus_id }
		var res = await globalPropValue.eduWork.cancel.post(reqData)
		if (res.code === 200) {
			ElMessage.success('撤销发布成功')
			upsearch()
		} else {
			ElAlert(res.message, '提示', { type: 'error' })
		}
	})
}

const delFun = async (item) => {
	var reqData = { id: item.id, tenant_id: params.obj.tenant_id, campus_id: params.obj.campus_id }
	var res = await globalPropValue.eduWork.del.post(reqData)
	if (res.code === 200) {
		ElMessage.success('删除成功')
		success()
	} else {
		ElAlert(res.message, '提示', { type: 'error' })
	}
}
const correct = (item) => {
	proxy.$router.replace(`/eduWork/workResult?id=${item.id}&name=${item.work_name}`)
}
const goWorkTopic = (item) => {
	proxy.$router.replace(`/eduWork/workTopic?id=${item.id}&name=${item.work_name}`)
}
const lookComment = (item) => {
	workCommentRef.value.show(item)
}
onMounted(() => {
	getGradeData()
	getClassData()
	//getDisciplineOptions()
	getEduCourse()
	//getWorkList()
})
</script>

<style lang="scss" scoped>
.workItem {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	padding: 15px;
	border-top: 1px solid var(--el-border-color);
	cursor: pointer;

	&:first-child {
		border-top: none;
	}

	&:hover {
		box-shadow: var(--el-box-shadow);

		.workItem-desc-top {
			font-weight: bold;
		}
	}

	.workItem-left {
		.workItem-desc {
			display: flex;
			flex-direction: column;

			.workItem-desc-top {
				display: flex;
				align-items: center;
				margin-bottom: 10px;
				color: #303133;
				font-size: 14px;

				.el-tag {
					margin-left: 5px;
				}
			}

			.workItem-desc-bottom {
				color: #909399;
			}
		}

		width: 60%;
	}

	.workItem-right {
		display: flex;
		align-items: center;
	}

	.workItem-mid {
		display: flex;
		align-items: center;
	}
}
</style>
