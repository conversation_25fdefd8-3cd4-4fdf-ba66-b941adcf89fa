<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-page-header v-if="breadcrumb.length > 1" title="" @back="breadcrumb_back">
						<template #content>
							<el-breadcrumb separator="/">
								<el-breadcrumb-item v-for="item in breadcrumb"
									><b>{{ item.name }}</b></el-breadcrumb-item
								>
							</el-breadcrumb>
						</template>
					</el-page-header>
					<el-breadcrumb v-else separator="/">
						<el-breadcrumb-item v-for="item in breadcrumb"
							><b>{{ item.name }}</b></el-breadcrumb-item
						>
					</el-breadcrumb>
				</div>
			</div>
			<div class="right-panel">
				<el-form-item style="margin-bottom: 0">
					<el-select v-model="params.suffix_type" clearable @change="upsearch">
						<el-option
							v-for="item in fileSuffixType"
							:key="item.value"
							:value="item.value"
							:label="item.name"
						></el-option>
					</el-select>
				</el-form-item>
			</div>
		</el-header>
		<el-main>
			<scTable
				ref="ShareMyTableRef"
				:apiObj="apiObj"
				:params="params"
				postion
				size="large"
				@select-all="onSelectAll"
				@select-change="onSelectChange"
			>
				<!--				<el-table-column v-if="status === 1" type="selection"></el-table-column>-->
				<el-table-column label="文件名" prop="file_name">
					<template #default="{ row }">
						<div style="display: flex" @click="tabel_read(row)">
							<SvgIcon :iconName="suffix_type[row.file_suffix_type]" /> &nbsp;
							<el-link
								v-if="webPreview(row.file_url)"
								type="primary"
								:underline="false"
								@click="table_webpreview(row)"
								>{{ row.file_name }}</el-link
							>
							<el-link
								v-else-if="checkVideo(row.file_url)"
								type="primary"
								:underline="false"
								@click="table_play(row)"
								>{{ row.file_name }}</el-link
							>
							<el-link
								v-else-if="imageSuffix(row.file_url)"
								type="primary"
								:underline="false"
								@click="table_image(row)"
							>
								{{ row.file_name }}
							</el-link>
							<el-link v-else-if="row.file_type === 2" :underline="false" @click="table_preview(row)">{{
								row.file_name
							}}</el-link>
							<span v-else style="font-size: 14px">{{ row.file_name }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column v-if="status === 1" label="文件大小" prop="size" width="150">
					<template #default="{ row }">
						<span v-if="row.file_type === 1">{{ row.size_format }}</span>
						<span v-if="row.file_type === 2"> </span>
					</template>
				</el-table-column>
				<el-table-column v-if="status === 1" label="分享者" prop="share_user" width="150"></el-table-column>
				<el-table-column v-if="status === 1" label="时间" prop="created_at" width="200"></el-table-column>
				<el-table-column v-if="status === 1" label="状态" width="150">
					<template #default="{ row }">
						<el-tag v-if="row.read_status === -1" type="danger">未读</el-tag>
						<el-tag v-else type="success">已读</el-tag>
					</template>
				</el-table-column>
				<el-table-column v-if="status === 1" label="操作" width="120">
					<template #default="{ row }">
						<el-button v-if="row.file_type === 1 && row.file_url !== ''" type="text" @click="table_download(row)"
							>下载</el-button
						>
					</template>
				</el-table-column>
			</scTable>
			<!--			<div v-show="status === 1" class="footer-check-btn">
				<div class="checkout">
					<div>已选 {{ currentCheckedLength }}/{{ checkedTotal }}</div>
				</div>
				<div class="btn">
					<el-button :disabled="currentCheckedLength === 0" size="small" type="success">下载 </el-button>
				</div>
			</div>-->
		</el-main>
	</el-container>
	<ShowDialog ref="showDialogRef" :params="params"></ShowDialog>
	<el-dialog v-model="visible" title="视频播放" destroy-on-close>
		<VideoPlay :source="source"></VideoPlay>
	</el-dialog>
	<el-image-viewer
		v-if="isShowImage"
		:url-list="imageUrl"
		:zIndex="1000"
		style="width: 100px; height: 100px"
		@close="onClose"
	></el-image-viewer>
</template>
<script setup>
import { checkVideo, imageSuffix, webPreview } from '@/utils/mediaLib'

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		pid: null,
		suffix_type: 0
	}
}
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
import cusTom from '@/utils/cusTom'
import { computed, ref, getCurrentInstance, reactive } from 'vue'
import { useRouter } from 'vue-router'
import ShowDialog from '../showDialog.vue'
import VideoPlay from '@/components/videoPlay/index.vue'
const { tenantId, campusId, fileSuffixTypeMap } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const params = ref(defaultParams())
let ShareMyTableRef = ref(null)
let currentCheckedLength = ref(0)
let currentRow = ref([])
let status = ref(1)
let checkedTotal = ref(0)
let source = ref('')
let visible = ref(false)
let isShowImage = ref(false)
let imageUrl = ref([])
const breadcrumb = ref([{ id: -1, root: 1, name: '分享给我' }])
const suffix_type = reactive({
	0: 'icon-wenjianjia',
	1: 'icon-word',
	2: 'icon-excel',
	3: 'icon-PPT',
	4: 'icon-PDF',
	5: 'icon-tupian1',
	6: 'icon-shipin1',
	7: 'icon-yinle',
	8: 'icon-yasuowenjian',
	99: 'icon-qita1'
})
const apiObj = ref(globalPropValue.fileManagement.file.file_share_me)

// 计算属性
const fileSuffixType = computed(() => {
	return [{ name: '全部', value: 0 }, ...fileSuffixTypeMap]
})
// 表格点击事件
const table_preview = (row) => {
	status.value = 1
	params.value.pid = row.file_id
	breadcrumb.value.push({ id: row.id, root: 0, name: row.file_name })
	ShareMyTableRef.value?.upData(params.value)
}
// 设置已读事件
const tabel_read = async (row) => {
	await globalPropValue.fileManagement.file.file_share_set_read.post({
		tenant_id: params.value.tenant_id,
		campus_id: params.value.campus_id,
		file_id: row.file_id,
		share_id: row.share_id
	})
}
// 表格下载事件
const table_download = (row) => {
	let tagA = document.createElement('a')
	tagA.href = row.file_url
	tagA.download = row.name || '文件下载'
	tagA.click()
	tagA.remove()
}
// 表格office预览
const router = useRouter()
const table_webpreview = (row) => {
	const routeUrl = router.resolve({
		path: '/filepreview',
		query: {
			tenant_id: params.value.tenant_id,
			campus_id: params.value.campus_id,
			file_id: row.file_id
		}
	})
	window.open(routeUrl.href, '_blank')
}
// 表格视频播放事件
const table_play = (row) => {
	visible.value = true
	source.value = row.file_url
}
// 表格图片预览事件
const table_image = (row) => {
	isShowImage.value = true
	imageUrl.value = [row.file_url]
}
// 点击x按钮触发事件
const onClose = () => {
	isShowImage.value = false
}

// 返回按钮点击事件
const breadcrumb_back = () => {
	status.value = 0
	breadcrumb.value.pop()
	const last = breadcrumb.value[breadcrumb.value.length - 1]
	if (last.root === 1) {
		params.value.type_id = last.id
		params.value.pid = null
	} else {
		params.value.pid = last.id
	}
	params.value = defaultParams()
	upsearch()
}
const upsearch = () => {
	ShareMyTableRef.value?.upData(params.value)
}
// 自定义事件
const onSelectChange = (val) => {
	currentCheckedLength.value = val.length
	currentRow.value = val
}
const onSelectAll = (val) => {
	currentCheckedLength.value = val.length
}
// 自定义方法
const tableChange = () => {
	params.value.pid = null
	upsearch()
}
defineExpose({
	tableChange
})
</script>

<style scoped lang="scss">
.footer-check-btn {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-top: -40px;
	overflow: hidden;
	.checkout {
		margin-right: 10px;
	}
}
</style>
