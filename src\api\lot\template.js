import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/lot/template/list`,
		name: '获取模板列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/lot/template/save`,
		name: '新增模板/修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/lot/template/del`,
		name: '删除模板',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	one: {
		url: `${config.API_URL}/lot/template/one`,
		name: '获取单个模板',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	status: {
		url: `${config.API_URL}/lot/template/changeStatus`,
		name: '修改模板状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	}
}
