<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<!--<el-button type="primary" icon="el-icon-plus" @click="add">添加用户</el-button>
				<el-button
          type="danger"
          plain
          icon="el-icon-delete"
          :disabled="selection.length == 0"
          @click="batch_del"
        ></el-button>-->
				<el-input
					v-model="search.name"
					placeholder="登录账号 / 昵称"
					clearable
					style="width: 200px; margin-right: 10px"
				></el-input>
				<el-button type="primary" icon="el-icon-search" @click="upsearch"></el-button>
				<el-button type="primary" :disabled="selection.length === 0" @click="setRoles">批量分配角色</el-button>
				<el-button type="danger" :disabled="selection.length === 0" @click="batchReset">批量密码重置</el-button>
			</div>
			<div class="right-panel">
				<div class="right-panel-search"></div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" :apiObj="apiObj" row-key="id" @selection-change="selectionChange">
				<el-table-column type="selection" width="50"></el-table-column>
				<!--
								<el-table-column label="ID" prop="id" width="50" align="center"></el-table-column>
				-->
				<el-table-column label="头像" width="80" align="center">
					<template #default="scope">
						<cusHead
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.avatar"
							:preview-src-list="[scope.row.avatar]"
							preview-teleported
						>
						</cusHead>
					</template>
				</el-table-column>
				<el-table-column label="登录账号" prop="user_name" width="150"></el-table-column>
				<el-table-column label="昵称" prop="nickname" width="120"></el-table-column>
				<el-table-column label="所属角色" prop="groupName">
					<template #default="scope">
						<el-tag v-for="item in scope.row.roleList" :key="item.id">{{ item.name }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="校区权限" prop="groupName">
					<template #default="scope">
						<template v-if="scope.row.campus_id === '0'">
							<el-tag>全部校区</el-tag>
						</template>
						<template v-else>
							<el-tag v-for="item in scope.row.campusList" :key="item.id" style="margin-bottom: 5px"
								>{{ item.campus_name }}
							</el-tag>
						</template>
					</template>
				</el-table-column>
				<el-table-column label="状态" prop="status" width="80">
					<template #default="scope">
						<el-tag v-if="scope.row.status === 1" type="success">开启</el-tag>
						<el-tag v-if="scope.row.status === 0" type="danger">禁止</el-tag>
					</template>
				</el-table-column>
				<!--				<el-table-column label="创建时间" prop="created_at" width="150"></el-table-column>
								<el-table-column label="更新时间" prop="updated_at" width="150"></el-table-column>-->
				<el-table-column label="操作" fixed="right" align="center" width="200">
					<template #default="scope">
						<el-button-group>
							<!--<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看 </el-button>-->
							<el-button text type="primary" size="small" @click="user_edit(scope.row, scope.$index)"> 编辑</el-button>
							<el-button text type="warning" size="small" @click="user_reset(scope.row, scope.$index)"
								>重置密码</el-button
							>

							<!--							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
															<template #reference>
																<el-button text type="danger" size="small">删除</el-button>
															</template>
														</el-popconfirm>-->
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save = false"></save-dialog>
	<role-dialog v-if="dialog.role" ref="roleDialog" @success="handleSuccess" @closed="dialog.role = false"></role-dialog>
</template>

<script>
import saveDialog from './save'
import roleDialog from './role'
import cusHead from '@/components/custom/cusStaffHead.vue'

export default {
	name: 'user',
	components: {
		cusHead,
		saveDialog,
		roleDialog
	},
	data() {
		return {
			dialog: {
				save: false,
				role: false
			},
			showGrouploading: false,
			groupFilterText: '',
			group: [],
			apiObj: this.$API.user.list,
			selection: [],
			search: {
				name: null
			}
		}
	},
	watch: {
		groupFilterText(val) {
			this.$refs.group.filter(val)
		}
	},
	methods: {
		setRoles() {
			this.dialog.role = true
			this.$nextTick(() => {
				this.$refs.roleDialog.open('role').setData(this.selection)
			})
		},
		batchReset() {
			var reqData = {
				userIds: this.selection.map((item) => item.id)
			}
			if (reqData.userIds.length === 0) {
				this.$alert('请选择用户', '提示', { type: 'error' })
				return
			}
			this.$confirm('确认重置选中用户的密码？', '提示', {
				type: 'warning',
				confirmButtonText: '确认',
				confirmButtonClass: 'el-button--danger'
			})
				.then(async () => {
					var res = await this.$API.user.passwordResetBatch.post(reqData)
					if (res.code === 200) {
						this.$message.success('重置成功，新密码用户手机号码后6位，请尽快通知用户修改')
						this.$refs.table.refresh()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
						return false
					}
				})
				.catch(() => {
					//取消退出
				})
		},
		//添加
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},
		//编辑
		user_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async user_reset(row) {
			this.$confirm('确认重置选中用户的密码？<br/>新密码用户手机号码后6位，请尽快通知用户修改!', '提示', {
				type: 'warning',
				confirmButtonText: '确认',
				dangerouslyUseHTMLString: true,
				confirmButtonClass: 'el-button--danger'
			})
				.then(async () => {
					var reqData = { id: row.id }
					var res = await this.$API.user.passwordReset.post(reqData)
					if (res.code === 200) {
						this.$message.success('重置成功！')
						this.$refs.table.refresh()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
						return false
					}
				})
				.catch(() => {
					//取消退出
				})
		},
		//删除
		async table_del(row, index) {
			var reqData = { id: row.id }
			var res = await this.$API.user.del.post(reqData)
			if (res.code === 200) {
				//这里选择刷新整个表格 OR 插入/编辑现有表格数据
				this.$refs.table.tableData.splice(index, 1)
				this.$message.success('删除成功')
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.selection.forEach((item) => {
						this.$refs.table.tableData.forEach((itemI, indexI) => {
							if (item.id === itemI.id) {
								this.$refs.table.tableData.splice(indexI, 1)
							}
						})
					})
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		},
		//本地更新数据
		handleSuccess(data, mode) {
			if (mode === 'add') {
				data.id = new Date().getTime()
				this.$refs.table.tableData.unshift(data)
				this.$refs.table.refresh()
			} else if (mode === 'edit') {
				this.$refs.table.tableData
					.filter((item) => item.id === data.id)
					.forEach((item) => {
						Object.assign(item, data)
					})
				this.$refs.table.refresh()
			} else {
				this.$refs.table.refresh()
			}
		}
	}
}
</script>

<style></style>
