<template>
	<el-card shadow="never" header="人脸识别">
		<el-alert
			title="照片要求"
			type="warning"
			:closable="false"
			description="要求图像分辨率：大于200*200像素，小于1920 * 1920像素，包含清晰的脸部图片，避免出现模糊、旋转、遮挡、剪裁等情况。以免识别失败！"
			show-icon
		/>
		<el-form ref="dialogForm" :model="userInfo" :rules="rules" label-width="120px" style="padding-top: 30px">

			<el-form-item label="照片" prop="user_head">
				<sc-upload
					v-model="userInfo.face_img"
					:width="200"
					:height="200"
					round
					icon="el-icon-avatar"
					title="照片"
					:cropper="true"
					:compress="1"
					fileTypeTag="face"
					:aspectRatio="1 / 1"
				></sc-upload>
			</el-form-item>
			<el-form-item style="padding-top: 20px">
				<el-button type="primary" :loading="isSaveing" @click="saveUserInfo">保存</el-button>
			</el-form-item>
		</el-form>
	</el-card>
</template>

<script>
import cusTom from '@/utils/cusTom'

const {
	sexMap,
	politicalMap,
	identityMap,
	workStatusMap,
	staffTypeMap,
	academicTypeMap,
	professionalTypeMap,
	compileTypeMap
} = cusTom.getBaseQuery()
export default {
	data() {
		return {
			fileurl6: '',
			form: {
				id: null,
				tenant_id: null,
				campus_id: null
			},
			isSaveing: false,
			userInfo: {
				name: null,
				sex: null,
				birthday: null,
				political: null,
				identity: null,
				work_status: null,
				staff_type: null,
				compile_type: null,
				academic_type: null,
				professional_type: null,
				listorder: 1,
				remark: null,
				discipline_id: null,
				department_id: null,
				position_id: null,
				campus_id: null,
				tenant_id: null,
				face_img:""
			},
			rules: {
				face_img: [{ required: true, message: '请上传照片' }],
			},
			sexMap,
			politicalMap,
			identityMap,
			workStatusMap,
			staffTypeMap,
			academicTypeMap,
			professionalTypeMap,
			compileTypeMap
		}
	},
	created() {
		let user = this.$TOOL.data.get('USER_INFO')
		this.form.tenant_id = user.tenant_id
		this.form.campus_id = user.campus_id
		this.form.id = user.userId
		//获取用户信息
		this.getUserInfo()
	},
	methods: {
		getUserInfo() {
			this.$API.personnel.staff.one.get(this.form).then((res) => {
				if (res.code === 200) {
					this.userInfo = res.data
					console.log(this.userInfo)
				} else {
					this.$message.warning(res.message)
				}
			})
		},
		saveUserInfo() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.personnel.staff.face.post(this.userInfo)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.$message.success('修改成功！')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		}
	}
}
</script>

<style></style>
