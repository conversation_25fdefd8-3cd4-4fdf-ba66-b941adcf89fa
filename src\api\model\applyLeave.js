import config from '@/config'
import http from '@/utils/request'

export default {
    list: {
        url: `${config.API_URL}/eduapi/vacate/list`,
        name: '获取放假申请记录',
        get: async function (params) {
            // eslint-disable-next-line no-return-await
            return await http.get(this.url, params)
        }
    },
    del: {
        url: `${config.API_URL}/eduapi/vacate/del`,
        name: '删除请假申请记录',
        post: async function (data) {
            // eslint-disable-next-line no-return-await
            return await http.post(this.url, data)
        }
    },
    audit: {
        url: `${config.API_URL}/eduapi/vacate/audit`,
        name: '通过/驳回请假申请',
        post: async function (data) {
            // eslint-disable-next-line no-return-await
            return await http.post(this.url, data)
        }
    }
}