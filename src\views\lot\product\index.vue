<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item label="">
						<el-select v-model="params.node_type" style="width: 150px" placeholder="请选择节点类型" clearable>
							<el-option
								v-for="(item, index) in enumConfig.nodeTypeMap"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.status" style="width: 150px" placeholder="请选择品类状态" clearable>
							<el-option label="已发布" :value="1"></el-option>
							<el-option label="未发布" :value="-1"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="请输入产品名称/产品ID搜索" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<div>
					<el-segmented v-model="showType" :options="showTypeOptions" style="margin: 5px 15px 5px 0">
						<template #default="scope">
							<el-tooltip class="box-item" effect="dark" :content="scope.item.tips" placement="bottom">
								<div class="items-center">
									<el-icon size="20">
										<component :is="scope.item.icon" />
									</el-icon>
								</div>
							</el-tooltip>
						</template>
					</el-segmented>
				</div>
				<el-button type="primary" icon="el-icon-CirclePlus" @click="add">创建产品</el-button>
			</div>
		</el-header>
		<el-main>
			<div ref="scTableMain" v-loading="loading" class="scTable" style="height: 100%">
				<div class="scTable-table" style="height: calc(100% - 40px)">
					<el-table v-if="showType === 'list'" ref="table" row-key="id" :data="listData" size="small" height="100%">
						<el-table-column label="产品名称" prop="product_name" min-width="200"></el-table-column>
						<el-table-column label="产品编号" prop="product_key" min-width="200"></el-table-column>
						<el-table-column label="节点类型" prop="node_type" width="150">
							<template #default="scope">
								{{ enumConfig.nodeTypeMap.find((item) => item.value === scope.row.node_type)?.name }}
							</template>
						</el-table-column>
						<el-table-column label="接入协议" prop="protocol" width="150">
							<template #default="scope">
								{{ enumConfig.protocolMap.find((item) => item.value === scope.row.protocol)?.name }}
							</template>
						</el-table-column>
						<el-table-column label="网络类型" prop="net_type" width="150">
							<template #default="scope">
								{{ enumConfig.netTypeMap.find((i) => i.value === scope.row.net_type)?.name }}
							</template>
						</el-table-column>
						<el-table-column label="状态" prop="status" width="150">
							<template #default="scope">
								<el-tag v-if="scope.row.status === 1" type="success">已发布</el-tag>
								<el-tag v-if="scope.row.status === -1" type="info">未发布</el-tag>
							</template>
						</el-table-column>
						<el-table-column label="创建时间" prop="created_at" width="180"></el-table-column>
						<el-table-column label="操作" fixed="right" align="center" width="250">
							<template #default="scope">
								<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)"
									>详情
								</el-button>
								<el-divider direction="vertical" />
								<el-button text type="primary" size="small" @click="show_device(scope.row, scope.$index)"
									>管理设备
								</el-button>
								<el-divider direction="vertical" />
								<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)"
									>编辑
								</el-button>
								<el-divider direction="vertical" />
								<el-button text type="danger" size="small" @click="table_del(scope.row, scope.$index)">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
					<div v-if="showType === 'card'" style="height: 100%">
						<el-scrollbar class="no-horizontal-scrollbar" style="height: 100%">
							<div v-if="listData && listData.length > 0" style="padding-right: 10px">
								<el-row :gutter="20">
									<el-col v-for="(item, index) in listData" :key="index" :xl="8" :lg="8" :md="8" :sm="12" :xs="24">
										<el-card class="card-item" shadow="hover">
											<div class="card-info">
												<div class="card-info-img">
													<div>
														<el-image
															style="width: 60px; height: 60px; border-radius: 6px"
															fit="cover"
															:src="item.product_img"
															:preview-src-list="[item.product_img]"
														>
															<template #error>
																<div class="image-slot">
																	<el-icon>
																		<sc-icon-psyResource />
																	</el-icon>
																</div>
															</template>
														</el-image>
													</div>
													<div>
														<div class="device_name" @click="table_show(item, index)">{{ item.product_name }}</div>
														<div class="device_tag">
															<el-tag size="small"
																>{{ enumConfig.protocolMap.find((i) => i.value === item.protocol)?.name }}
															</el-tag>

															<el-tag
																v-if="item.status === 1"
																class="cursor"
																size="small"
																type="success"
																@click="publish(item)"
																><el-icon><el-icon-SuccessFilled /></el-icon> 已发布</el-tag
															>

															<el-tag
																v-if="item.status === -1"
																class="cursor"
																size="small"
																type="info"
																@click="publish(item)"
																><el-icon><el-icon-WarningFilled /></el-icon> 未发布</el-tag
															>
														</div>
													</div>
												</div>
												<el-row>
													<el-col :span="12">
														<el-form-item label="产品编号：">{{ item.product_key }}</el-form-item>
													</el-col>
													<el-col :span="12">
														<el-form-item label="节点类型："
															>{{ enumConfig.nodeTypeMap.find((i) => i.value === item.node_type)?.name }}
														</el-form-item>
													</el-col>
													<el-col :span="12">
														<el-form-item label="网络类型："
															>{{ enumConfig.netTypeMap.find((i) => i.value === item.net_type)?.name }}
														</el-form-item>
													</el-col>
													<el-col :span="12">
														<el-form-item label="创建时间：">{{ item.created_at }}</el-form-item>
													</el-col>
												</el-row>
											</div>
											<template #footer>
												<div>
													<el-button text type="primary" size="small" @click="table_show(item, index)">详情</el-button>
													<el-divider direction="vertical" />
													<el-button text type="primary" size="small" @click="show_device(item, index)"
														>管理设备
													</el-button>
													<el-divider direction="vertical" />
													<el-button text type="primary" size="small" @click="table_edit(item, index)">编辑</el-button>
													<el-divider direction="vertical" />
													<el-button text type="danger" size="small" @click="table_del(item, index)">删除</el-button>
												</div>
											</template>
										</el-card>
									</el-col>
								</el-row>
							</div>
							<el-empty v-else description="暂无产品品类"></el-empty>
						</el-scrollbar>
					</div>
				</div>
				<div class="scTable-page">
					<div class="pagination">
						<el-pagination
							v-if="total > 0"
							v-model:current-page="params.page"
							:page-sizes="[12, 24, 36, 72, 96]"
							:page-size="params.pageSize"
							size="small"
							background
							layout="total,sizes, prev, pager, next,jumper"
							:total="total"
							@size-change="handleSizeChange"
							@current-change="handleCurrentChange"
						/>
					</div>
				</div>
			</div>
		</el-main>
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
</template>

<script>
import saveDialog from './save'
import { ElMessageBox } from 'element-plus'
import cusTom from '@/utils/cusTom'

const { tenantId } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		node_type: null,
		status: null,
		page: 1,
		pageSize: 12
	}
}

export default {
	name: 'productList',
	data() {
		return {
			groupFilterText: '',
			showType: 'card',
			showTypeOptions: [
				{
					label: '',
					value: 'card',
					icon: 'el-icon-grid',
					tips: '点击切换卡片视图'
				},
				{
					label: '',
					value: 'list',
					icon: 'el-icon-operation',
					tips: '点击切换列表视图'
				}
			],
			loading: false,
			total: 0,
			enumConfig: [],
			listData: [],

			params: defaultParams(),
			search: {
				name: null
			},
			dialog: {
				save: false
			}
		}
	},
	components: {
		saveDialog
	},
	watch: {},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
		this.getList()
	},
	computed: {},
	methods: {
		handleSizeChange(page_size) {
			this.params.pageSize = page_size
			this.getList()
		},
		handleCurrentChange(page) {
			this.params.page = page
			this.getList()
		},
		async getList() {
			this.loading = true
			const res = await this.$LotApi.product.list.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.listData = res.data.rows
				this.total = res.data.total
			}
		},
		//搜索
		upsearch() {
			this.params.page = 1
			this.getList()
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//新增职位
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.getList()
			}
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			/*this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})*/
			this.$router.push({
				path: '/lot/product/detail',
				query: {
					id: row.id
				}
			})
		},
		//查看设备
		show_device(row) {
			this.$router.push({
				path: '/lot/device',
				query: {
					product_id: row.id
				}
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: row.tenant_id }
			ElMessageBox.confirm('是否删除当前产品模板？删除后模板下所有物模型都会被删除！', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.product.del.post(reqData)
				if (res.code === 200) {
					this.$message.success('删除成功')
					this.getList()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},

		async publish(row) {
			let text, status
			if (row.status === 1) {
				text = '是否取消发布当前产品【' + row.product_name + '】？'
				status = -1
			} else {
				text = '是否确认发布当前产品【' + row.product_name + '】？'
				status = 1
			}

			var reqData = { id: row.id, status: status, tenant_id: row.tenant_id }
			ElMessageBox.confirm(text, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.product.changeStatus.post(reqData)
				if (res.code === 200) {
					this.$message.success('删除成功')
					this.getList()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.el-header > .left-panel {
	flex: 4;
}

.scTable-table {
	height: calc(100% - 40px);
}

.scTable-page {
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding: 0 15px;
	border-top: 1px solid var(--el-border-color-light);

	.pagination {
	}
}

.scTable-do {
	white-space: nowrap;
}

.scTable:deep(.el-table__footer) .cell {
	font-weight: bold;
}

.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
	height: 8px;
	border-radius: 8px;
}

.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
	width: 8px;
	border-radius: 8px;
}

:deep(.card-item) {
	.el-card__footer {
		padding: 10px;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}
}

:deep(.el-scrollbar__wrap) {
	overflow-x: hidden !important;
}

:deep(.el-card__body) {
	padding: 10px;

	.el-form-item--default {
		margin-bottom: 0px;
		height: 30px;
		line-height: 30px;
	}

	.el-form-item__label {
		font-size: 12px;
		padding-right: 5px;
		height: 30px;
		line-height: 30px;
	}

	.el-form-item__content {
		font-size: 12px;
		height: 30px;
		line-height: 30px;
	}
}

.image-slot {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	background: var(--el-color-primary-light-9);
	color: var(--el-color-primary-light-2);
	font-size: 24px;
}

.card-info-img {
	display: flex;

	.el-image {
		margin-right: 10px;
	}

	.device_name {
		cursor: pointer;
		font-size: 15px;
		font-weight: bold;
		line-height: 25px;
		margin-bottom: 5px;
	}
}

.el-col .el-card {
	margin-bottom: 20px;
}
</style>
