<template>
	<el-container>
		<el-header>
			<el-page-header
				:content="`${templateData?.template_name} （${templateData?.template_key}）`"
				@back="goBack"
			></el-page-header>
			<el-divider />
			<div class="pannel">
				<div class="left-panel">
					<div class="left-panel-search">
						<el-form-item>
							<el-select v-model="params.tsl_type" placeholder="请选择功能类型" clearable style="width: 150px">
								<el-option
									v-for="item in allTslType"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								></el-option>
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-input v-model="params.name" placeholder="请输入功能名称/标识符搜索" clearable></el-input>
						</el-form-item>
						<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
						<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
					</div>
				</div>
				<div class="right-panel">
					<el-button type="primary" plain icon="el-icon-Download" @click="showImport">导入</el-button>
					<el-button type="primary" plain icon="el-icon-Upload" @click="showExport">导出</el-button>

					<el-button type="primary" icon="el-icon-CirclePlus" @click="add">添加物模型</el-button>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" border :pageSize="10" :apiObj="apiObj" :params="params" :hideDo="false">
				<el-table-column label="功能类型" prop="tsl_type" width="100">
					<template #default="scope">
						<span v-if="scope.row.tsl_type === 1">属性类型</span>
						<span v-if="scope.row.tsl_type === 2">事件类型</span>
						<span v-if="scope.row.tsl_type === 3">服务类型</span>
					</template>
				</el-table-column>
				<el-table-column label="功能名称" prop="name" width="120"></el-table-column>
				<el-table-column label="标识符" prop="code" width="150"></el-table-column>
				<el-table-column label="数据类型" prop="type" width="150">
					<template #default="scope">
						<span v-if="scope.row.tsl_type === 1">
							{{ scope.row.type_spec.type }}（{{ typeConfig[scope.row.type_spec.type] }}）
						</span>
						<span v-else>-</span>
					</template>
				</el-table-column>
				<el-table-column label="读写类型" prop="access_mode" width="80">
					<template #default="scope">
						<div v-if="scope.row.tsl_type === 1">
							<span v-if="scope.row.access_mode === 1">只读</span>
							<span v-if="scope.row.access_mode === 2">读写</span>
							<span v-if="scope.row.access_mode === 3">只写</span>
						</div>
						<div v-else>-</div>
					</template>
				</el-table-column>
				<el-table-column label="数据定义" prop="specs" width="450">
					<template #default="scope">
						<span v-if="scope.row.tsl_type === 1">
							<showSpecs
								:specsData="JSON.parse(scope.row.type_spec.specs)"
								:type="scope.row.type_spec.type"
							></showSpecs>
						</span>
						<span v-else>-</span>
					</template>
				</el-table-column>
				<el-table-column label="描述" prop="description"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="250">
					<template #default="scope">
						<el-button text type="success" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>
						<el-divider direction="vertical" />
						<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑 </el-button>
						<el-divider direction="vertical" />
						<el-button text type="danger" size="small" @click="table_del(scope.row, scope.$index)">删除</el-button>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
	<TslImportExportDialog
		v-if="dialog.tslImportExportSave"
		ref="tslImportExportDialog"
		@success="handleImportSuccess"
		@closed="dialog.tslImportExportSave = false"
	></TslImportExportDialog>
</template>

<script>
import saveDialog from './save'
import ScTable from '@/components/scTable/index.vue'
import TslImportExportDialog from './tslImportExport'

import { ElMessageBox } from 'element-plus'
import ShowSpecs from '@/components/thingsModel/showSpecs.vue'
import tool from '@/utils/tool'

const defaultParams = () => {
	return {
		name: null,
		tsl_type: 0,
		template_id: 0
	}
}

export default {
	name: 'templateList',
	data() {
		return {
			template_id: null,
			typeConfig: {
				int: '整数型',
				bool: '布尔型',
				float: '浮点型',
				text: '字符串',
				enum: '枚举型',
				date: '时间型',
				struct: '结构体',
				array: '数组'
			},
			allTslType: [
				{
					value: 0,
					label: '功能类型(全部)'
				},
				{
					value: 1,
					label: '属性类型'
				},
				{
					value: 2,
					label: '服务类型'
				},
				{
					value: 3,
					label: '事件类型'
				}
			],
			groupFilterText: '',
			templateData: null,
			apiObj: this.$LotApi.templateTsl.listPage,
			params: defaultParams(),
			search: {
				name: null
			},
			dialog: {
				save: false,
				tslImportExportSave: false
			}
		}
	},
	components: {
		TslImportExportDialog,
		ShowSpecs,
		ScTable,
		saveDialog
	},
	watch: {},
	created() {
		this.template_id = this.$route.params.id
		this.params.template_id = this.template_id
		this.getTemplateInfo()
	},
	computed: {},
	methods: {
		handleImportSuccess(mode) {
			if (mode === 'import') {
				this.upsearch()
			}
		},
		showExport() {
			this.dialog.tslImportExportSave = true
			this.$nextTick(() => {
				this.$refs.tslImportExportDialog.open(this.template_id, 'export').setData(this.templateData)
			})
		},
		showImport() {
			this.dialog.tslImportExportSave = true
			this.$nextTick(() => {
				this.$refs.tslImportExportDialog.open(this.template_id, 'import').setData(this.templateData)
			})
		},
		getTemplateInfo() {
			this.$LotApi.template.one
				.get({
					id: this.template_id
				})
				.then((res) => {
					if (res.code === 200) {
						this.templateData = res.data
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
		},
		goBack() {
			this.$router.back()
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.params.template_id = this.template_id
			this.upsearch()
		},
		//新增职位
		add() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open(this.template_id)
			})
		},
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else if (mode === 'edit') {
				this.$refs.table.refresh()
			}
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			var info = tool.objCopy(row)
			if (info.tsl_type === 1 && typeof info.type_spec.specs === 'string') {
				info.type_spec.specs = JSON.parse(info.type_spec.specs)
			}
			this.$nextTick(() => {
				this.$refs.saveDialog.open(this.template_id, 'edit').setData(info)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			var info = tool.objCopy(row)
			if (info.tsl_type === 1 && typeof info.type_spec.specs === 'string') {
				info.type_spec.specs = JSON.parse(info.type_spec.specs)
			}
			this.$nextTick(() => {
				this.$refs.saveDialog.open(this.template_id, 'show').setData(info)
			})
		},
		//删除
		async table_del(row) {
			this.$c
			var reqData = { id: row.id, template_id: row.template_id }
			ElMessageBox.confirm('是否删除当前物模型？删除后相应功能会被删除！', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				var res = await this.$LotApi.templateTsl.del.post(reqData)
				if (res.code === 200) {
					this.$message.success('删除成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
.pannel {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.el-header {
	display: block;
	.el-divider--horizontal {
		margin: 10px 0;
	}
}
</style>
