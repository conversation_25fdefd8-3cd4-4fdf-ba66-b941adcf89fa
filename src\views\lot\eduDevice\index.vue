<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item label="">
						<el-select
							v-if="CampusManagementList.length > 1"
							v-model="params.campus_id"
							placeholder="校区"
							filterable
							style="margin-right: 15px"
						>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="params.fault" placeholder="请选择故障状态" style="width: 180px" clearable>
							<el-option label="故障" :value="1"></el-option>
							<el-option label="正常" :value="-1"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<cusSelectField v-model="params.room" :multiple="false" placeholder="请选择场室"> </cusSelectField>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="params.name" placeholder="请输入设备名称/设备ID搜索" clearable></el-input>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="warning" icon="el-icon-turn-off" @click="syncPower">批量操作开关机</el-button>
				<el-button type="primary" icon="el-icon-switch" @click="syncToDevice">同步设备</el-button>
				<el-button icon="el-icon-SetUp" @click="configFault">故障受理配置</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :apiObj="apiObj" :params="params" :hideDo="true">
				<el-table-column label="设备ID" prop="device_key" width="150"></el-table-column>
				<el-table-column label="设备名称" prop="device_name" width="150"></el-table-column>
				<el-table-column label="在线状态" prop="online">
					<template #default="{ row }">
						<el-tag v-if="row.online == 1" type="success">在线</el-tag>
						<el-tag v-else type="info">离线</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="设备IP" prop="ip"></el-table-column>
				<el-table-column label="设备状态" prop="power">
					<template #default="{ row }">
						<el-tag v-if="row.power == 'on'" type="success">开机</el-tag>
						<el-tag v-if="row.power == 'off'" type="warning">关机</el-tag>
						<el-tag v-if="row.power == 'disable'" type="info">禁用</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="设备故障" prop="device_type" width="400px">
					<template #default="{ row }">
						<el-tag v-if="row.control_fault == 1" size="small" type="danger">中控故障</el-tag>
						<el-tag v-if="row.pc_fault == 1" size="small" type="danger">电脑故障</el-tag>
						<el-tag v-if="row.display_fault == 1" size="small" type="danger">显示故障</el-tag>
						<el-tag v-if="row.audio_fault == 1" size="small" type="danger">音频故障</el-tag>
						<el-tag v-if="row.other_fault == 1" size="small" type="danger">其他故障</el-tag>
						<el-tag
							v-if="
								row.other_fault == -1 &&
								row.control_fault == -1 &&
								row.pc_fault == -1 &&
								row.display_fault == -1 &&
								row.audio_fault == -1
							"
							size="small"
							type="success"
							>正常</el-tag
						>
					</template>
				</el-table-column>
				<el-table-column label="关联场室" prop="room_id" width="200">
					<template #default="{ row }">
						{{ row.room_info?.name }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="220px">
					<template #default="{ row }">
						<el-button
							v-if="
								row.other_fault == 1 ||
								row.control_fault == 1 ||
								row.pc_fault == 1 ||
								row.display_fault == 1 ||
								row.audio_fault == 1
							"
							text
							type="warning"
							size="small"
							@click="show_fault(row)"
							>故障处理</el-button
						>
						<el-button text type="primary" size="small" @click="table_show(row)">详情</el-button>
						<el-button v-if="!row.room_info" text type="primary" size="small" @click="showDialog(row)"
							>关联场室</el-button
						>
						<el-button v-else text type="danger" size="small" @click="unBindRoom(row)">解绑场室</el-button>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<el-dialog v-model="bindRoomDialog" title="关联场室" width="500">
		<el-form :model="formData" label-width="auto">
			<el-form-item label="关联场室">
				<cusSelectField v-model="formData.room" :multiple="false" placeholder="请选择关联场室"> </cusSelectField>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="bindRoomDialog = false">取消</el-button>
				<el-button type="primary" @click="bindRoom"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>
	<save ref="saveRef" @refresh="upsearch"></save>

	<el-dialog v-model="faultDialog" title="故障处理" width="400" center>
		<el-form v-if="faultForm" :model="faultForm" label-width="120">
			<el-form-item label="中控故障">
				<el-radio-group v-model="faultForm.control_fault">
					<el-radio :label="-1">已处理</el-radio>
					<el-radio :label="1">未处理</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="电脑故障">
				<el-radio-group v-model="faultForm.pc_fault">
					<el-radio :label="-1">已处理</el-radio>
					<el-radio :label="1">未处理</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="显示故障">
				<el-radio-group v-model="faultForm.display_fault">
					<el-radio :label="-1">已处理</el-radio>
					<el-radio :label="1">未处理</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="音频故障">
				<el-radio-group v-model="faultForm.audio_fault">
					<el-radio :label="-1">已处理</el-radio>
					<el-radio :label="1">未处理</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="其他故障">
				<el-radio-group v-model="faultForm.other_fault">
					<el-radio :label="-1">已处理</el-radio>
					<el-radio :label="1">未处理</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item>
				<el-button @click="faultDialog = false">取消</el-button>
				<el-button type="primary" @click="onSubmit">确定</el-button>
			</el-form-item>
		</el-form>
	</el-dialog>
	<el-drawer v-model="configFaultDialog" title="受理配置" size="60%">
		<component :is="AcceptanceConfig"></component>
	</el-drawer>

	<el-dialog v-model="powerDialog" title="批量操作开关机" width="500">
		<el-form :model="formData" label-width="auto">
			<p style="margin-bottom: 15px">选择批量操作设备状态：</p>
			<el-form-item label="">
				<el-radio-group v-model="powerFormData.power">
					<el-radio value="off">开机</el-radio>
					<el-radio label="on">关机</el-radio>
					<el-radio label="disable">禁用</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="powerDialog = false">取消</el-button>
				<el-button type="primary" @click="powerAction">确定</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup>
import save from './save.vue'
import AcceptanceConfig from '@/views/affairs/campusRepair/AcceptanceConfig'

import { ref, getCurrentInstance, onMounted, reactive } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
import _ from 'lodash'
const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$LotApi

const defaultParams = () => {
	return {
		campus_id: campusId,
		tenant_id: tenantId,
		room_id: null,
		room: null,
		name: null
	}
}
const CampusManagementList = ref(campusInfo)
const apiObj = ref(globalPropValue.eduDevice.list)
const params = ref(defaultParams())
const table = ref(null)
const saveRef = ref(null)
const faultForm = ref(null)
const bindRoomDialog = ref(false)
const faultDialog = ref(false)
const powerDialog = ref(false)
const configFaultDialog = ref(false)
const powerFormData = ref({
	power: null
})
// 关联场室
const formData = ref({
	room: null
})
const currentRow = ref({})
const showDialog = (row) => {
	formData.value.room = null
	bindRoomDialog.value = true
	currentRow.value = row
}

const show_fault = (row) => {
	faultDialog.value = true
	faultForm.value = _.cloneDeep(row)
}
const bindRoom = () => {
	console.log(formData.value.room)
	if (!formData.value.room) {
		ElMessage({
			type: 'warning',
			message: '请选择场室'
		})
		return
	}
	ElMessageBox.confirm('确定关联场室？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		globalPropValue.eduDevice.bindRoom
			.post({
				tenant_id: tenantId,
				campus_id: campusId,
				id: currentRow.value.id,
				room_id: formData.value.room[0].id
			})
			.then((res) => {
				if (res.code === 200) {
					ElMessage({
						type: 'success',
						message: '操作成功'
					})
					bindRoomDialog.value = false
					table.value.refresh()
				}
			})
	})
	console.log(formData.value.room[0])
}
const powerAction = () => {
	if (!powerFormData.value.power) {
		ElMessage({
			type: 'warning',
			message: '请选择操作状态'
		})
		return
	}
	ElMessageBox.confirm('确定操作所有设备？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		globalPropValue.eduDevice.allPowerCtrl
			.post({
				tenant_id: tenantId,
				campus_id: campusId,
				power: powerFormData.value.power
			})
			.then((res) => {
				if (res.code === 200) {
					ElMessage({
						type: 'success',
						message: '操作成功'
					})
					powerDialog.value = false
					table.value.refresh()
				}
			})
	})
}

const configFault = () => {
	configFaultDialog.value = true
}

const onSubmit = () => {
	console.log(faultForm.value)
	globalPropValue.eduDevice.ReportRepair.post(faultForm.value).then((res) => {
		console.log(res)
		if (res.code === 200) {
			ElMessage({
				type: 'success',
				message: '操作成功'
			})
			faultDialog.value = false
			table.value.refresh()
		}
	})
}
const unBindRoom = (row) => {
	ElMessageBox.confirm('确定解除关联？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		globalPropValue.eduDevice.unBindRoom
			.post({
				tenant_id: tenantId,
				campus_id: campusId,
				id: row.id
			})
			.then((res) => {
				if (res.code === 200) {
					ElMessage({
						type: 'success',
						message: '操作成功'
					})
					table.value.refresh()
				}
			})
	})
}

const upsearch = () => {
	if (params.value.room) {
		params.value.room_id = params.value.room[0][id].id
	}
	table.value.upData(params.value)
}
const refresh = () => {
	params.value = defaultParams()
	params.value.room = null
	table.value.upData(params.value)
}
// 同步设备后刷新列表
const syncToDevice = () => {
	globalPropValue.eduDevice.syncDevice
		.post({
			tenant_id: tenantId,
			campus_id: campusId
		})
		.then((res) => {
			console.log(res)
			if (res.code === 200) {
				table.value.refresh()
			}
		})
}

const syncPower = () => {
	powerDialog.value = true
}

const table_show = (row) => {
	saveRef.value.show(row.id)
}
</script>
