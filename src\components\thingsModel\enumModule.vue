<template>
	<el-form-item label="枚举数据定义（最多50项）">
		<div class="enum_header">
			<el-row :gutter="10">
				<el-col :span="8"> 参数值</el-col>
				<el-col :span="14"> 参数描述</el-col>
				<el-col :span="2"></el-col>
			</el-row>
		</div>
		<div v-for="(item, index) in modelValue" :key="index" class="enum_item">
			<el-row :gutter="10">
				<el-col :span="8">
					<el-form-item>
						<el-input-number
							v-model="item.value"
							type="number"
							style="width: 100%"
							:precision="0"
							:controls="false"
							placeholder="请输入整数"
							controls-position="right"
							clearable
						></el-input-number>
					</el-form-item>
				</el-col>
				<el-col :span="14">
					<el-form-item>
						<el-input v-model="item.name" placeholder="请输入参数描述" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="2">
					<el-button text type="danger" @click="modelValue.length > 1 ? modelValue.splice(index, 1) : null">
						<el-icon>
							<el-icon-CircleClose />
						</el-icon>
					</el-button>
				</el-col>
			</el-row>
		</div>
		<div>
			<el-button
				v-if="modelValue.length < 50"
				type="text"
				@click="modelValue.length < 50 ? modelValue.push({ value: null, name: null }) : null"
			>
				+ 添加枚举项
			</el-button>
		</div>
	</el-form-item>
</template>
<script>
export default {
	name: 'enumModule',
	props: {
		modelValue: {
			type: Array,
			default: () => {
				return [{ value: null, name: null }]
			}
		}
	},
	data() {
		return {
			unitConfig: [],
			rules: {
				field_value: [
					{
						required: true,
						message: '请输入参数值',
						trigger: 'blur'
					}
				],
				field_name: [
					{
						required: true,
						message: '请输入参数描述',
						trigger: 'blur'
					}
				]
			}
		}
	},
	emits: ['update:modelValue'], // 明确声明该组件会触发 update:modelValue 事件
	watch: {
		modelValue: {
			handler(val) {
				this.$emit('update:modelValue', val)
			},
			deep: true
		}
	},
	created() {},
	methods: {}
}
</script>

<style scoped lang="scss">
.enum_header {
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.enum_item {
	margin: 8px 0;
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.text-center {
	text-align: center;
}
</style>
