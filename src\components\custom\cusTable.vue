<template>
	<el-container>
		<el-header v-if="searchForm || searchTool" class="cHeader">
			<div class="left-panel" v-if="searchForm">
				<slot name="searchForm"> </slot>
			</div>
			<div class="right-panel" v-if="searchTool">
				<slot name="searchTool"> </slot>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" v-bind="$attrs">
				<slot name="content"></slot>
			</scTable>
		</el-main>
	</el-container>
</template>

<script>
export default {
	props: {
		searchForm: { type: Boolean, default: true },
		searchTool: { type: Boolean, default: true }
	}
}
</script>

<style lang="scss" scoped>
.cHeader {
	display: flex;
	align-items: center;
	.left-panel {
		flex: 1;
	}
	.right-panel {
		flex-shrink: 0;
	}
}
</style>
