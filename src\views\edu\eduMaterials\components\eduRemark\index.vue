<template>
	<el-drawer v-model="showDrawer" title="评论管理" direction="rtl" destroy-on-close size="60%">
		<div class="header">
			<div class="search">
				<el-select
					v-model="params.audit"
					placeholder="请选择审核状态"
					style="width: 240px"
					clearable
					@change="auditChange"
				>
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="btn">
				<el-button type="primary" icon="el-icon-Plus" @click="add_attachment">新增评论</el-button>
			</div>
		</div>

		<scTable ref="table" row-key="id" stripe :apiObj="apiObj" :params="params" height="calc(100% - 52px)">
			<el-table-column label="头像" prop="comment_user_head" width="90">
				<template #default="{ row }">
					<cusStaffHead
						v-if="row.user_type == 2"
						loading="lazy"
						:lazy="true"
						fit="contain"
						style="width: 50px; height: 50px"
						:src="row.comment_user_head"
						:preview-src-list="[row.comment_user_head]"
						preview-teleported
					>
					</cusStaffHead>
					<cusStudentHead
						v-else
						loading="lazy"
						:lazy="true"
						fit="contain"
						style="width: 50px; height: 50px"
						:src="row.comment_user_head"
						:preview-src-list="[row.comment_user_head]"
						preview-teleported
					>
					</cusStudentHead>
				</template>
			</el-table-column>
			<el-table-column label="评论人" prop="comment_user_name" width="120"></el-table-column>
			<el-table-column label="评论内容" prop="comment_desc" show-overflow-tooltip></el-table-column>
			<el-table-column label="分值" prop="comment_score" width="150">
				<template #default="scope">
					<el-rate v-model="scope.row.comment_score" size="small" disabled />
				</template>
			</el-table-column>
			<el-table-column label="审核状态" prop="audited" width="100">
				<template #default="{ row }">
					<el-tag v-if="row.audited === 1" type="warning">待审核</el-tag>
					<el-tag v-if="row.audited === 2" type="success">已审核</el-tag>
					<el-tag v-if="row.audited === 3" type="danger">审核不通过</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="审核备注" prop="audit_remark" show-overflow-tooltip></el-table-column>
			<el-table-column label="操作" width="180">
				<template #default="{ row }">
					<el-button-group>
						<el-button type="primary" text size="small" @click="table_edit(row)">编辑</el-button>
						<el-button v-if="row.audited !== 2" type="primary" text size="small" @click="table_audited(row)"
							>审核</el-button
						>
					</el-button-group>
					<el-popconfirm title="确定删除吗？" @confirm="table_del(row)">
						<template #reference>
							<el-button text type="danger" size="small">删除</el-button>
						</template>
					</el-popconfirm>
				</template>
			</el-table-column>
		</scTable>
	</el-drawer>
	<SaveDialog ref="dialogRef" :params="params" @success="handlerSuccess"></SaveDialog>
	<AuditedDialog ref="auditedRef" :params="params" @success="success"></AuditedDialog>
</template>

<script setup>
import SaveDialog from './save.vue'
import AuditedDialog from './auditedSave.vue'
import cusStaffHead from '@/components/custom/cusStaffHead.vue'
import cusStudentHead from '@/components/custom/cusStudentHead.vue'
import { ref, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { nextTick } from 'vue'
const route = useRoute()
const router = useRouter()
const defaultParams = () => {
	return {
		tenant_id: '',
		campus_id: '',
		material_id: '',
		audit: null
	}
}
let dialogRef = ref(null)
let table = ref(null)
let auditedRef = ref(null)
let showDrawer = ref(false)
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const params = ref(defaultParams())
const apiObj = ref(globalPropValue.eduMaterials.material.get_comment_list)

const open = (data) => {
	params.value = {
		...defaultParams(),
		...data
	}
	nextTick(() => {
		table.value.upData(params.value)
	})
	// table.value.upData(params.value)
	showDrawer.value = true
}
// 返回按钮点击事件
const goBack = () => {
	router.go(-1)
}
const options = [
	{
		value: null,
		label: '全部'
	},
	{
		value: 1,
		label: '待审核'
	},
	{
		value: 2,
		label: '已审核'
	},
	{
		value: 3,
		label: '审核不通过'
	}
]
const auditChange = (val) => {
	params.value.audit = val
	table.value.upData(params.value)
}
// 新增评论点击事件
const add_attachment = () => {
	dialogRef.value.open()
}
// 编辑点击事件
const table_edit = (row) => {
	dialogRef.value.open('edit')
	dialogRef.value.setData(row)
}
// 审核点击事件
const table_audited = (row) => {
	auditedRef.value.open(row.id)
}
// 删除点击事件
const table_del = async (row) => {
	const res = await globalPropValue.eduMaterials.material.del_comment.post({
		id: row.id,
		tenant_id: Number(params.value.tenant_id),
		campus_id: Number(params.value.campus_id)
	})
	if (res.code === 200) {
		ElMessage({
			type: 'success',
			message: '删除成功'
		})
		table.value.upData(params.value)
	} else {
		ElMessage({
			type: 'error',
			message: res.message
		})
	}
}
// 自定义事件
const handlerSuccess = () => {
	table.value.upData(params.value)
}
const success = () => {
	table.value.upData(params.value)
}
defineExpose({
	open
})
</script>

<style lang="scss" scoped>
.header {
	margin: 10px 0;
	display: flex;
	justify-content: space-between;
}
</style>
