<template>
	<el-container>
		<el-aside width="250px">
			<el-container>
				<el-header v-if="CampusManagementList.length > 1">
					<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-header>
				<el-main>
					<ul class="menu">
						<li
							v-for="(item, index) in modeData"
							:key="index"
							:class="{ active: item.value == params.mode }"
							@click="groupClick(item.value, true)"
						>
							{{ item.name }}
						</li>
					</ul>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<div class="hearder-panel">
					<div class="search-panel">
						<el-input v-model="params.name" placeholder="请输入流程名称" clearable @change="searchChange" style="width: 200px"></el-input>
					</div>
					<div class="btn-panel">
						<el-button type="primary" @click="addApproval">添加审批流程</el-button>
					</div>
				</div>
			</el-header>
			<el-main class="outer-main">
				<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
					<el-table-column label="流程名称" prop="approval_name"></el-table-column>
					<el-table-column label="创建者" prop="created_user_name"></el-table-column>
					<!-- <el-table-column label="所属模块" prop="mode">
                    </el-table-column> -->
					<el-table-column label="范围" prop="scope">
						<template #default="scope">
							{{ scope.row.scope == 1 ? '教职工' : '学生' }}
						</template>
					</el-table-column>
					<el-table-column label="描述" prop="remark"> </el-table-column>
					<el-table-column label="关联表单" prop="form_name"></el-table-column>
					<el-table-column label="状态" prop="status" >
						<template #default="scope">
							<el-switch
								v-model="scope.row.status"
								:active-value="1"
								:inactive-value="2"
								@change="table_status(scope.row.id, scope.row.status)"
							/>
						</template>
					</el-table-column>
					<el-table-column label="操作" fixed="right" align="center" width="250">
						<template #default="scope">
							<el-button-group>
								<el-button
									v-if="scope.row.is_sys !== 1"
									text
									type="primary"
									size="small"
									@click="table_edit(scope.row, scope.$index)"
									>编辑</el-button
								>
								<el-button text type="success" size="small" @click="table_config(scope.row, scope.$index)"
									>流程配置</el-button
								>
								<el-button
									text
									type="success"
									size="small"
									:disabled="!scope.row.flow_img"
									@click="table_img(scope.row)"
									>查看</el-button
								>
							</el-button-group>
							<el-popconfirm
								v-if="scope.row.is_sys !== 1"
								title="确定删除吗？"
								@confirm="table_del(scope.row, scope.$index)"
							>
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
		<save ref="saveForm" @success="handleSuccess"></save>
		<workFlow ref="workFlow" @success="handleWorkflowSuccess"></workFlow>
		<el-image-viewer
			v-if="isShowImage"
			:url-list="imageUrl"
			:zIndex="1000"
			style="width: 100px; height: 100px"
			@close="onClose"
		></el-image-viewer>
	</el-container>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import cusTom from '@/utils/cusTom'
import table from '@/config/table'
import save from './save.vue'
import workFlow from './workFlow.vue'
const { campusId, tenantId, campusInfo, approvalModelMap } = cusTom.getBaseQuery()
export default {
	components: {
		save,
		workFlow
	},
	data() {
		return {
			CampusManagementList: campusInfo,
			params: {
				pageSize: 20,
				page: 1,
				tenant_id: tenantId,
				campus_id: campusId,
				name: null,
				mode: 1
			},
			apiObj: this.$API.approval.deploy.list,
			isShowImage: false,
			imageUrl: []
		}
	},
	computed: {
		modeData() {
			if (approvalModelMap) {
				let data = approvalModelMap.map((item) => {
					item.label = item.name
					return item
				})
				return data
			}
		}
	},
	methods: {
		resetTable() {
			this.$refs.table.upData(this.params)
		},
		groupClick(data) {
			this.params.mode = data
			this.$refs.table.upData(this.params)
		},
		searchChange() {
			this.$refs.table.upData(this.params)
		},
		async table_status(id, status) {
			const { code, message } = await this.$API.approval.deploy.status.post({
				id: id,
				status: status,
				campus_id: this.params.campus_id,
				tenant_id: this.params.tenant_id
			})
			this.showMessage(code, message)
		},
		addApproval() {
			this.$refs.saveForm.open()
			console.log('新增')
		},
		table_edit(row, index) {
			this.$refs.saveForm.open(row)
			console.log(row, index, '编辑')
		},
		table_config(row, index) {
			this.$refs.workFlow.open(row)
			console.log(row, index, '配置')
		},
		table_img(row) {
			this.imageUrl = [row.flow_img]
			this.isShowImage = true
		},
		onClose() {
			this.isShowImage = false
		},
		async handleSuccess(data) {
			let params = {
				id: data.id,
				tenant_id: data.tenant_id,
				campus_id: data.campus_id,
				approval_name: data.approval_name,
				approval_send: data.approval_send.map((item) => item.id).join(','),
				mode: data.mode,
				form_id: data.form_id,
				scope: data.scope,
				icon: data.icon,
				icon_color: data.icon_color,
				remark: data.remark
			}
			const { code, message } = await this.$API.approval.deploy.save.post(params)
			this.showMessage(code, message)
		},
		handleWorkflowSuccess() {
			this.resetTable()
		},
		async table_del(row, index) {
			const { code, message } = await this.$API.approval.deploy.delete.post({
				id: row.id,
				campus_id: this.params.campus_id,
				tenant_id: this.params.tenant_id
			})
			this.showMessage(code, message)
		},
		showMessage(code, message) {
			if (code === 200) {
				ElMessage({ type: 'success', message: message })
				this.resetTable()
			} else {
				ElMessage({ type: 'error', message: message })
			}
		}
	}
}
</script>
<style lang="scss" scoped>
.menu {
	li {
		height: 36px;
		line-height: 36px;
		text-align: center;
		cursor: pointer;
		color: #606266;
		font-size: 14px;

		&:hover {
			background-color: #f5f7fa;
		}

		&.active {
			background-color: #e9ecf7;
		}
	}
}

.hearder-panel {
	width: 100%;
	display: flex;
	justify-content: space-between;
}
</style>
