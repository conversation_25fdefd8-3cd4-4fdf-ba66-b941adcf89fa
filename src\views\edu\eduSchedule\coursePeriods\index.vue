<template>
	<el-container>
		<el-aside width="270px">
			<el-container>
				<el-header v-if="CampusManagementList.length > 1" >
					<el-select
						v-if="CampusManagementList.length > 1"
						v-model="params.campus_id"
						placeholder="校区"
						filterable
						style="width: 100%"
						@change="campusChange"
					>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-header>
				<el-main>
					<div class="add-btn">
						<el-button type="primary" icon="el-icon-plus" @click="addPeriods">新增时段</el-button>
					</div>
					<ul class="menu">
						<li
							v-for="item in coursePeriodList"
							:class="{ active: item.id === params.periods_id }"
							@click="groupClick(item)"
						>
							<span>{{ item.periods_name }} {{ item.is_default == 1 ? '[默认]' : '' }}</span>
							<div class="action">
								<el-button link icon="el-icon-edit" @click.stop="editPeriods(item)"></el-button>
								<el-button link icon="el-icon-delete" @click.stop="delPeriods(item)"></el-button>
							</div>
						</li>
					</ul>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header style="display: flex; justify-content: flex-end">
				<el-button type="primary" icon="el-icon-plus" @click="addCoursePeriods">新增课程时段</el-button>
			</el-header>
			<el-main>
				<scTable ref="table" row-key="id" :apiObj="apiObj" :params="params" hidePagination hideDo>
					<el-table-column prop="periods_name"  label="时段名称"></el-table-column>
					<el-table-column prop="item_name" sortable label="课程时段名称"></el-table-column>
					<el-table-column prop="begin_time" sortable label="开始时间"></el-table-column>
					<el-table-column prop="end_time" label="结束时间"></el-table-column>
					<el-table-column prop="type" label="时段属性">
						<template #default="scope">
							{{ scope.row.type === 1 ? '课程' : '非课程' }}
						</template>
					</el-table-column>
					<el-table-column label="操作">
						<template #default="scope">
							<el-button type="primary" link size="small" @click="editCoursePeriods(scope.row)">编辑</el-button>
							<el-button type="danger" link size="small" @click="delCoursePeriods(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
		<!-- 时段名称 -->
		<periodsSave ref="periodsSaveDialog" :params="params" @success="periodsSaveSuccess"></periodsSave>
		<!-- 课程时段名称 -->
		<coursePeriodsSave ref="coursePeriodsSaveDialog" :params="params" @success="coursePeriodsSaveSuccess">
		</coursePeriodsSave>
	</el-container>
</template>
<script setup>
import { ref, watch, getCurrentInstance, onMounted } from 'vue'
import periodsSave from './periodsSave.vue'
import coursePeriodsSave from './coursePeriodsSave.vue'
import cusTom from '@/utils/cusTom'
import { ElMessage, ElMessageBox } from 'element-plus'

const { campusInfo, tenantId, campusId } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const CampusManagementList = ref(campusInfo)
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		periods_id: null
	}
}
const params = ref(defaultParams())
const apiObj = ref(null)
const coursePeriodList = ref([])
const table = ref(null)
const periodsSaveDialog = ref(null)
const coursePeriodsSaveDialog = ref(null)

onMounted(() => {
	getPeriodList()
})
watch(
	() => params.value.campus_id,
	(val) => {
		if (val) {
			params.value.campus_id = val
			getPeriodList()
		}
	}
)
const campusChange = (val) => {
	params.value.campus_id = val
	table.value.upData(params.value)
}
const addPeriods = () => {
	console.log('addPeriods')
	periodsSaveDialog.value.open()
}
const periodsSaveSuccess = () => {
	getPeriodList()
}
const groupClick = (item) => {
	params.value.periods_id = item.id
	table.value.upData(params.value)
}
const editPeriods = (item) => {
	console.log(item)
	periodsSaveDialog.value.open('edit').setData(item)
}
const delPeriods = (item) => {
	console.log(item, 'itemdel')
	ElMessageBox.confirm('确定删除该课程时段吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	})
		.then(() => {
			let delData = {
				id: item.id,
				tenant_id: item.tenant_id,
				campus_id: item.campus_id
			}
			globalPropValue.eduSchedule.periodDel.post(delData).then((res) => {
				if (res.code === 200) {
					ElMessage.success('删除成功')
					getPeriodList()
				}
			})
		})
		.catch(() => {})
}
const addCoursePeriods = () => {
	console.log('addCoursePeriods')
	coursePeriodsSaveDialog.value.open()
}
const editCoursePeriods = (item) => {
	coursePeriodsSaveDialog.value.open('edit').setData(item)
	console.log(item)
}
const coursePeriodsSaveSuccess = () => {
	table.value.upData(params.value)
}
const delCoursePeriods = (item) => {
	ElMessageBox.confirm('确定删除该时段吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		let delData = {
			id: item.periods_id,
			tenant_id: item.tenant_id,
			campus_id: item.campus_id
		}
		globalPropValue.eduSchedule.coursePeriodDel.post(delData).then((res) => {
			if (res.code === 200) {
				ElMessage.success('删除成功')
				table.value.upData(params.value)
			} else {
				ElMessage.error(res.message)
			}
		})
	})
}
const getPeriodList = async () => {
	const res = await globalPropValue.eduSchedule.period.get(params.value)
	if (res.code === 200) {
		coursePeriodList.value = res.data
		params.value.periods_id = coursePeriodList.value.length ? coursePeriodList.value[0].id : null
		apiObj.value = globalPropValue.eduSchedule.coursePeriod
	}
	console.log(coursePeriodList.value)
}
</script>

<style lang="scss" scoped>
.add-btn {
	text-align: center;
	padding-top: 5px;
	padding-bottom: 15px;
	border-bottom: 1px solid var(--el-border-color-light);
}

.menu {
	margin-top: 10px;

	li {
		height: 36px;
		line-height: 36px;
		cursor: pointer;
		color: #606266;
		font-size: 14px;
		padding: 0 5px;
		display: flex;
		justify-content: space-between;

		.action {
			display: none;
		}

		&:hover {
			background-color: var(--el-menu-hover-bg-color);

			.action {
				display: block;
			}
		}

		&.active {
			background-color: var(--el-color-primary-light-8);

			.action {
				display: block;
			}
		}
	}
}
</style>
