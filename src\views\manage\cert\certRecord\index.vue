<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item v-if="CampusManagementList.length > 1" label="">
						<el-select
							v-model="params.campus_id"
							placeholder="请选择校区"
							filterable
							style="width: 180px"
							@change="campusChange"
						>
							<el-option
								v-for="item in CampusManagementList"
								:key="item.code"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="" prop="semester_id">
						<!--						<cusCascader
							v-model="params.semester_id"
							placeholder="请选择学期"
							:options="getSemester"
							@valChange="semesterChange"
						></cusCascader>-->

						<cusSelectSemester
							v-model="params.semester_id"
							:params="params"
							:show-default-value="true"
							:width="'214px'"
							clearable
							style="margin-right: 15px"
							@semesterChange="semesterChange"
						/>
					</el-form-item>
					<el-form-item label="">
						<el-select
							v-model="params.type"
							placeholder="请选择证书类型"
							filterable
							style="width: 180px"
							clearable
							@change="upsearch"
						>
							<el-option
								v-for="item in certTypeMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-select
							v-model="params.object"
							placeholder="请选择证书对象"
							filterable
							style="width: 180px"
							clearable
							@change="upsearch"
						>
							<el-option
								v-for="item in certObjectMap"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input
							v-model="params.name"
							placeholder="请输入证书编号或者获奖名称"
							clearable
							style="width: 240px"
							@input="upsearch"
						></el-input>
					</el-form-item>
				</div>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
				<el-table-column label="学年学期" prop="academic_name" width="180" show-overflow-tooltip>
					<template #default="{ row }"> {{ row.academic_name }} - {{ row.semester_name }} </template>
				</el-table-column>
				<el-table-column label="证书类型" prop="signed" width="100">
					<template #default="{ row }">
						{{ formData(certTypeMap, row.type) }}
					</template>
				</el-table-column>
				<el-table-column label="对象" prop="issue" width="100">
					<template #default="{ row }">
						{{ formData(certObjectMap, row.object) }}
					</template>
				</el-table-column>
				<el-table-column label="对象名称" prop="objecter" width="100">
					<template #default="{ row }">
						<div v-if="row.objecter">
							{{ row.objecter.name }}
						</div>
					</template>
				</el-table-column>
				<el-table-column label="证书编号" prop="cert_number" width="120">
					<template #default="{ row }">
						{{ row.cert_number }}
					</template>
				</el-table-column>
				<el-table-column label="获奖名称" prop="award_name" max-width="200" min-width="100" show-overflow-tooltip>
					<template #default="{ row }">
						{{ row.award_name }}
					</template>
				</el-table-column>
				<el-table-column label="所属证书组" prop="name" max-width="200" min-width="100" show-overflow-tooltip>
					<template #default="{ row }">
						<div v-if="row.cert_group">
							{{ row.cert_group.name }}
						</div>
					</template>
				</el-table-column>

				<el-table-column label="状态" prop="status" width="100">
					<template #default="scope">
						<el-tag :type="certStatusTagMap[scope.row.cert_status]">
							{{ formData(certStatusMap, scope.row.cert_status) }}</el-tag
						>
					</template>
				</el-table-column>
				<el-table-column label="创建人" prop="creator" width="100">
					<template #default="scope">
						<span v-if="scope.row.creator">{{ scope.row.creator.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="200"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="120">
					<template #default="scope">
						<el-button-group>
							<el-button
								text
								type="primary"
								:disabled="scope.row.status < 7 || scope.row.cert_file_url == ''"
								size="small"
								@click="show_cert(scope.row)"
								>查看证书</el-button
							>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
import { preview } from 'v-preview-image'
import CusSelectSemester from '@/components/custom/cusSelectSemester.vue'

const { campusId, tenantId, campusInfo, semesterInfo, certTypeMap, certObjectMap, certStatusMap } =
	cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		semester_id: null,
		name: null,
		type: null,
		object: null,
		audit: 1
	}
}
export default {
	name: '',
	components: { CusSelectSemester },
	data() {
		return {
			imageViewerVisible: false,
			imageViewerSrc: '',
			certTypeMap,
			certObjectMap,
			certStatusMap,
			semesterInfo,
			certStatusTagMap: {
				1: 'info',
				2: 'primary',
				3: 'success',
				4: 'danger',
				5: 'primary',
				6: 'warning',
				7: 'success',
				8: 'warning',
				9: 'success'
			},
			dialog: {
				save: false
			},
			apiObj: this.$API.cert.cert.list,
			selection: [],
			params: defaultParams(),
			CampusManagementList: campusInfo,
			treeData: null,
			disciplineOptions: []
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.semester_id = null
			},
			immediate: true
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	async created() {},
	methods: {
		semesterChange(val) {
			this.params.semester_id = val
			this.$nextTick(() => {
				this.$refs.table.upData(this.params)
			})
		},
		campusChange(val) {
			this.params.campus_id = val
			this.params.semester_id = null
			this.$refs.table.upData(this.params)
		},
		show_cert(row) {
			if (row.cert_file_url === '') {
				return
			}
			preview(row.cert_file_url)
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value === val)?.name || '-'
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else {
				this.$refs.table.refresh()
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.el-header > .left-panel {
	flex: 3;
}
</style>
