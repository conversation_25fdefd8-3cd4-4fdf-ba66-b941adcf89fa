<template>
	<div>
		<el-card v-if="propertiesList.length > 0" class="card-item" shadow="hover">
			<el-form label-position="left">
				<el-form-item label="电机模式">
					<el-switch
						v-model="this.channelPropertiesList.CurtainMode_1.value"
						active-value="1"
						inactive-value="0"
						active-color="#165DFF"
						style="--el-switch-on-color: #ff7d00; --el-switch-off-color: #165dff"
						inactive-color="#FF7D00"
						inline-prompt
						:active-text="this.channelPropertiesList.CurtainMode_1.properties_info.type_spec.specs[1]"
						:inactive-text="this.channelPropertiesList.CurtainMode_1.properties_info.type_spec.specs[0]"
					></el-switch>
				</el-form-item>
				<el-form-item label="窗帘打开比例" label-position="top">
					<el-slider v-model="this.channelPropertiesList.CurtainPositionPercentage_1.value" :step="10" show-stops />
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button type="primary" style="width: 100%" plain round @click="setDeviceAttr">设置</el-button>
			</template>
		</el-card>

		<el-card v-else class="card-item" shadow="never">
			<el-empty description="请先选择空调通道"></el-empty>
		</el-card>
	</div>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
	name: 'index',
	props: {
		deviceInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			channelMap: [],
			propertiesList: [],
			channelPropertiesList: {},
			channel: null
		}
	},
	created() {
		this.getPropertiesInfo()
	},
	methods: {
		// 属性设置
		setDeviceAttr() {
			/*if (this.writerTslChecked.length === 0) {
				ElMessage.error('请选择要设置的属性！')
				return
			}*/
			console.log(this.channelPropertiesList)
			let property = []
			Object.keys(this.channelPropertiesList).forEach((key) => {
				let item = this.channelPropertiesList[key]
				if (
					item &&
					item.value !== '' &&
					(item.properties_info.access_mode === 2 || item.properties_info.access_mode === 3)
				) {
					if (!item.value) {
						item.value = 0
					}
					property.push({
						tsl_id: item.properties_info.tsl_id,
						value: item.value.toString()
					})
				}
			})
			if (property.length === 0) {
				ElMessage.error('请选择要设置的属性和设置值！')
				return
			}
			let reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id,
				property: property
			}
			this.$LotApi.device.writeDevProperty.post(reqData).then((res) => {
				if (res.code === 200) {
					ElMessage.success('执行成功！')
				}
			})
		},
		getPropertiesInfo() {
			this.loading = true
			// 获取设备属性信息
			this.$LotApi.device.getDeviceTslWithLastValue
				.get({
					id: this.deviceInfo.id,
					tenant_id: this.deviceInfo.tenant_id,
					campus_id: this.deviceInfo.campus_id
				})
				.then((res) => {
					this.loading = false
					if (res.code === 200) {
						this.propertiesList = []

						res.data?.map((item) => {
							if (item.properties_info.type_spec.specs) {
								item.properties_info.type_spec.specs = JSON.parse(item.properties_info.type_spec.specs)
							}
							/*if (item.properties_info.type_spec.type === 'bool' && item.last_value && item.last_value.value) {
									item.last_value.value = item.properties_info.type_spec.specs[item.last_value.value * 1]
								}
								if (item.properties_info.type_spec.type === 'enum' && item.last_value && item.last_value.value) {
									item.last_value.value = item.properties_info.type_spec.specs.find(
										(specsItem) => specsItem.value === item.last_value.value
									)?.name
								}*/
							item.value = item.last_value?.value
							if (item.properties_info.access_mode === 2 || item.properties_info.access_mode === 3) {
								this.channelPropertiesList[item.properties_info.code] = item
								this.propertiesList.push(item)
							}
						})
						console.log(this.propertiesList)
					}
				})
		}
	}
}
</script>

<style scoped lang="scss">
:deep(.el-form-item) {
	border-bottom: 1px solid #eaeaea;
	padding: 10px 0;
}

:deep(.card-item) {
	width: 350px;
	margin-top: 15px;

	.el-card__footer {
		padding: 10px 20px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.el-image {
	color: #ccc;
}

.el-col-item {
	text-align: center;
	line-height: 20px;
	cursor: pointer;

	.svg-icon {
		font-size: 42px;
	}
}

.selected {
	color: var(--el-color-primary);
}
</style>
