import config from '@/config'
import http from '@/utils/request'

export default {
	staff: {
		list: {
			url: `${config.API_URL}/perapi/staff/list`,
			name: '获取教职工列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/perapi/staff/all`,
			name: '获取教职工列表不分页',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		one: {
			url: `${config.API_URL}/perapi/staff/one`,
			name: '获取单个信息',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/perapi/staff/save`,
			name: '新增教职工/修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		face: {
			url: `${config.API_URL}/perapi/staff/face`,
			name: '人脸识别照片更新',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		bindCard: {
			url: `${config.API_URL}/perapi/staff/bindCard`,
			name: '绑卡',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/perapi/staff/del`,
			name: '删除教职工',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		import: {
			url: `${config.API_URL}/perapi/staff/import`,
			name: '导入教职工',
			post: async function (data = {}) {
				return await http.post(this.url, data, { timeout: 0 })
			}
		},
		status: {
			url: `${config.API_URL}/perapi/staff/changeStatus`,
			name: '修改教职工状态',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	},
	attendance: {
		list: {
			url: `${config.API_URL}/perapi/attendance/list`,
			name: '获取教职工考勤',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/eduapi/class/all`,
			name: '获取教职工列表不分页',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/eduapi/class/save`,
			name: '新增教职工/修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},

		del: {
			url: `${config.API_URL}/eduapi/class/del`,
			name: '删除教职工',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		status: {
			url: `${config.API_URL}/eduapi/class/changeStatus`,
			name: '修改教职工状态',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	}
}
