<template>
	<el-drawer
		v-model="visible"
		size="75%"
		title="证书管理"
		width="500"
		:show-close="false"
		destroy-on-close
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		@closed="$emit('closed')"
	>
		<template #header="{ close, titleId, titleClass }">
			<h4 :id="titleId" :class="titleClass">{{ info.name }}</h4>
			<el-button type="danger" icon="el-icon-CircleCloseFilled" @click="close"> 关闭窗口</el-button>
		</template>
		<el-container>
			<el-header>
				<div class="left-panel">
					<template v-if="mode != 'audit'">
						<el-button type="success" plain :disabled="generateStatus !== true" @click="batchGenerate">
							生成证书
						</el-button>
						<el-button type="primary" plain :disabled="archiveStatus !== true" @click="batchArchive">
							证书归档
						</el-button>
					</template>
					<template v-else>
						<el-button type="primary" plain :disabled="auditStatus !== true" @click="batchAudit"> 审核</el-button>
					</template>
					<el-button type="danger" plain :disabled="delStatus !== true" @click="batch_del"> 删除</el-button>
				</div>
				<div class="right-panel">
					<el-input
						v-model="params.name"
						placeholder="请输入证书编号或者获奖名称"
						clearable
						style="width: 240px"
						@input="upsearch"
					></el-input>
					<template v-if="mode != 'audit'">
						<el-button type="success" icon="el-icon-CirclePlusFilled" @click="creatCert"> 新增证书</el-button>
						<el-button type="primary" icon="el-icon-Promotion" :disabled="status !== 1" @click="submitAudit">
							提交审核
						</el-button>
					</template>
					<template v-else>
						<el-button type="primary" plain @click="groupAudit"> 审核全部</el-button>
					</template>
				</div>
			</el-header>
			<el-main>
				<scTable ref="certTable" row-key="id" :params="params" :apiObj="apiObj" @selection-change="selectionChange">
					<el-table-column type="selection" width="45" fixed="left" />
					<el-table-column label="证书编号" fixed="left" prop="cert_number" width="150">
						<template #default="{ row }">
							{{ row.cert_number }}
						</template>
					</el-table-column>
					<el-table-column label="对象名称" prop="objecter" width="100">
						<template #default="{ row }">
							<div v-if="row.objecter">
								{{ row.objecter.name }}
							</div>
						</template>
					</el-table-column>
					<el-table-column label="获奖名称" prop="award_name" max-width="200" min-width="100" show-overflow-tooltip>
						<template #default="{ row }">
							{{ row.award_name }}
						</template>
					</el-table-column>
					<el-table-column label="获奖日期" prop="award_date" width="120">
						<template #default="{ row }">
							{{ row.award_date }}
						</template>
					</el-table-column>
					<el-table-column label="获奖名次" prop="award_ranking" width="150" show-overflow-tooltip>
						<template #default="{ row }">
							{{ row.award_ranking }}
						</template>
					</el-table-column>
					<el-table-column label="获奖信息" prop="award_desc" max-width="200" min-width="100" show-overflow-tooltip>
						<template #default="{ row }">
							{{ row.award_desc }}
						</template>
					</el-table-column>
					<el-table-column label="状态" prop="status" width="100">
						<template #default="scope">
							<el-tag :type="certStatusTagMap[scope.row.cert_status]">
								{{ formData(certStatusMap, scope.row.cert_status) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="操作" fixed="right" align="center" width="200">
						<template #default="scope">
							<el-link
								v-if="scope.row.cert_status === 4"
								:underline="false"
								type="primary"
								@click="showRejectRemark(scope.row, scope.$index)"
								>驳回原因
							</el-link>
							<el-link
								v-if="scope.row.cert_file_url !== ''"
								:underline="false"
								type="success"
								@click="show_cert(scope.row, scope.$index)"
								>查看证书
							</el-link>
							<el-link
								v-else-if="scope.row.cert_status > 2"
								:underline="false"
								type="success"
								@click="preview_cert(scope.row, scope.$index)"
								>预览证书
							</el-link>

							<template v-if="mode !== 'audit'">
								<el-link :underline="false" type="warning" @click="table_edit(scope.row, scope.$index)">编辑</el-link>
								<el-link :underline="false" type="danger" @click="table_del(scope.row, scope.$index)">删除</el-link>
							</template>
							<template v-else>
								<el-link
									v-if="scope.row.cert_status === 2 || scope.row.cert_status === 4"
									:underline="false"
									type="warning"
									@click="table_audit(scope.row, scope.$index)"
									>审核
								</el-link>
							</template>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-drawer>

	<save-dialog
		v-if="dialogSave"
		ref="saveDialog"
		:params="currentGroup"
		@success="handleSaveSuccess"
		@closed="dialogSave = false"
	></save-dialog>

	<audit-dialog
		v-if="dialogAudit"
		ref="auditDialog"
		@success="handleSaveSuccess"
		@closed="dialogAudit = false"
	></audit-dialog>
	<preview-component v-if="previewDialog" ref="previewDialog"></preview-component>
	<generate-dialog v-if="generateDialog" ref="generateDialog" @closed="generateClose"></generate-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import saveDialog from './certSave.vue'
import generateDialog from './generate.vue'
import auditDialog from '@/views/manage/cert/certAudit/audit.vue'
import previewComponent from '@/views/manage/cert/certTemplateConfig/preview.vue'
import { cloneDeep } from 'lodash'
import { preview } from 'v-preview-image'
const { certTypeMap, certObjectMap, certStatusMap } = cusTom.getBaseQuery()

export default {
	components: { previewComponent, saveDialog, auditDialog, generateDialog },
	emits: ['success', 'closed'],
	props: {
		info: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			params: {
				tenant_id: this.info.tenant_id,
				campus_id: this.info.campus_id,
				name: null,
				cert_group_id: this.info.id
			},
			status: 1,
			currentGroup: {},
			imageViewerVisible: false,
			previewDialog: false,
			generateDialog: false,
			generateStatus: false,
			archiveStatus: false,
			auditStatus: false,
			delStatus: false,
			imageViewerSrc: null,
			certTypeMap,
			certObjectMap,
			certStatusMap,
			selectionList: [],
			certStatusTagMap: {
				1: 'info',
				2: 'primary',
				3: 'success',
				4: 'danger',
				5: 'primary',
				6: 'warning',
				7: 'success',
				8: 'warning',
				9: 'success'
			},
			apiObj: this.$API.cert.cert.list,
			mode: 'add',
			templateMap: [],
			visible: false,
			isSaveing: false,
			dialogSave: false,
			dialogAudit: false,
			course: [],
			discipline: [],
			treeData: [],
			position: [],
			certTemplate: {},
			certTemplateConfig: {}
		}
	},
	mounted() {},
	computed: {},
	created() {},
	methods: {
		selectionChange(selection) {
			this.delStatus = false
			this.generateStatus = false
			this.archiveStatus = false
			this.auditStatus = false
			this.selectionList = selection
			if (selection.length > 0) {
				this.delStatus = true
			}
			let generate = 0
			let archive = 0
			let audit = 0
			selection.forEach(function (item, index) {
				if (item.cert_status === 5) {
					generate++
				} else if (item.cert_status === 8) {
					archive++
				} else if (item.cert_status === 2 || item.cert_status === 4) {
					audit++
				}
			})
			if (generate > 0) {
				this.generateStatus = true
			}
			if (archive > 0) {
				this.archiveStatus = true
			}
			if (audit > 0) {
				this.auditStatus = true
			}
		},
		creatCert() {
			this.dialogSave = true
			this.currentGroup = this.info
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add').setData({
					campus_id: this.info.campus_id,
					tenant_id: this.info.tenant_id,
					object: this.info.object,
					cert_group_id: this.info.id,
					semester_id: this.info.semester_id
				})
			})
		},
		getCertData() {
			let params = {
				campus_id: this.info.campus_id,
				tenant_id: this.info.tenant_id,
				id: this.info.template_id
			}
			this.$API.cert.certTemplate.one.get(params).then((res) => {
				this.certTemplate = res.data
				if (this.certTemplate.extend_conf) {
					this.certTemplateConfig = JSON.parse(this.certTemplate.extend_conf)
				}
			})
		},
		//编辑
		table_edit(row) {
			this.dialogSave = true
			this.currentGroup = this.info
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		generateClose() {
			this.generateDialog = false
			this.$refs.certTable.refresh()
		},
		showRejectRemark(row) {
			this.$msgbox({
				title: '驳回原因',
				message: row.reject_remark,
				confirmButtonText: '确定'
			})
		},
		show_cert(row) {
			if (row.cert_file_url === '') {
				return
			}
			preview(row.cert_file_url)
		},
		preview_cert(row) {
			//证书对象 1学生 2教师 3部门 4年级 5班级 6学科
			/*teacher_name: '',
				grade_name: '',
				class_name: '',
				student_name: '',
				department_name: '',
				course_name: '',*/
			switch (row.object) {
				case 1:
					row.student_name = row.objecter?.name
					break
				case 2:
					row.teacher_name = row.objecter?.name
					break
				case 3:
					row.department_name = row.objecter?.name
					break
				case 4:
					row.grade_name = row.objecter?.name
					break
				case 5:
					row.class_name = row.objecter?.name
					break
				case 6:
					row.course_name = row.objecter?.name
			}
			row.signed = this.certTemplate.seal?.signed
			this.certTemplateConfig.list.map((item) => {
				if (item.field === 'seal_img') {
					item.content = this.certTemplate.seal?.seal_img
				}
				return item
			})

			this.previewDialog = true
			this.$nextTick(() => {
				this.$refs.previewDialog.open(this.certTemplateConfig, this.certTemplate, 2, row)
			})
		},
		compiler(template, data) {
			let reg = /\{\{(.+?)\}\}/g
			template = template.replace(reg, function (_, g) {
				let key = g.trim()
				let value = data[key]
				return value
			})
			return template
		},
		//搜索
		upsearch() {
			this.$refs.certTable.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = {
				tenant_id: this.info.tenant_id,
				campus_id: this.info.campus_id,
				name: null,
				cert_group_id: this.info.id
			}
			this.upsearch()
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value === val)?.name || '-'
		},
		submitAudit() {
			var reqData = { id: this.info.id, tenant_id: this.info.tenant_id, campus_id: this.info.campus_id }
			this.$confirm(`确定将此证书组中状态为草稿的所有证书提交审核吗？`, '提示', {
				type: 'warning'
			})
				.then(async () => {
					var res = await this.$API.cert.certGroup.submitAudit.post(reqData)
					if (res.code === 200) {
						this.$message.success('操作成功')
						this.status = 2
						this.$refs.certTable.refresh()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
				.catch(() => {})
		},
		groupAudit() {
			this.dialogAudit = true
			this.$nextTick(() => {
				this.$refs.auditDialog.open('audit', 'certGroup').setData({
					tenant_id: this.info.tenant_id,
					campus_id: this.info.campus_id,
					id: this.info.id
				})
			})
		},
		batchAudit() {
			if (this.selectionList.length <= 0) {
				return
			}
			let certIds = []
			this.selectionList.forEach(function (item, index) {
				certIds.push(item.id)
			})
			var reqData = {
				id: certIds.join(','),
				tenant_id: this.info.tenant_id,
				campus_id: this.info.campus_id,
				cert_group_id: this.info.id
			}
			this.dialogAudit = true
			this.$nextTick(() => {
				this.$refs.auditDialog.open('audit', 'cert').setData(reqData)
			})
		},
		table_audit(row) {
			this.dialogAudit = true
			this.$nextTick(() => {
				this.$refs.auditDialog.open('audit', 'cert').setData({
					tenant_id: row.tenant_id,
					campus_id: row.campus_id,
					cert_group_id: this.info.id,
					id: row.id + ''
				})
			})
		},
		//删除
		async table_del(row) {
			var reqData = {
				cert_ids: row.id + '',
				tenant_id: row.tenant_id,
				campus_id: row.campus_id,
				cert_group_id: this.info.id
			}
			this.$confirm(`证书删除后无法恢复，确定要删除吗？`, '提示', {
				type: 'warning'
			})
				.then(async () => {
					var res = await this.$API.cert.cert.del.post(reqData)
					if (res.code === 200) {
						this.$message.success('删除成功')
						this.$refs.certTable.refresh()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
				.catch(() => {})
		},

		//删除
		async batch_del() {
			if (this.selectionList.length <= 0) {
				return
			}
			let certIds = []
			this.selectionList.forEach(function (item, index) {
				certIds.push(item.id)
			})
			var reqData = {
				cert_ids: certIds.join(','),
				tenant_id: this.info.tenant_id,
				campus_id: this.info.campus_id,
				cert_group_id: this.info.id
			}
			this.$confirm(`证书删除后无法恢复，确定要删除吗？`, '提示', {
				type: 'warning'
			})
				.then(async () => {
					var res = await this.$API.cert.cert.del.post(reqData)
					if (res.code === 200) {
						this.$message.success('删除成功')
						this.$refs.certTable.refresh()
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
				.catch(() => {})
		},
		batchGenerate() {
			let list = []
			this.selectionList.forEach(function (item, index) {
				if (item.cert_status === 5) {
					list.push(item)
				}
			})
			if (list.length <= 0) {
				return
			}
			this.generateDialog = true
			this.$nextTick(() => {
				this.$refs.generateDialog.open(this.certTemplateConfig, this.certTemplate, list)
			})
		},
		async batchArchive() {
			let certIds = []
			this.selectionList.forEach(function (item, index) {
				if (item.cert_status === 8) {
					certIds.push(item.id)
				}
			})
			if (certIds.length <= 0) {
				return
			}
			var reqData = {
				cert_ids: certIds.join(','),
				tenant_id: this.info.tenant_id,
				campus_id: this.info.campus_id,
				cert_group_id: this.info.id
			}
			var res = await this.$API.cert.cert.archive.post(reqData)
			if (res.code === 200) {
				this.$message.success('操作成功')
				this.$refs.certTable.refresh()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.status = this.info.status
			this.params = {
				tenant_id: this.info.tenant_id,
				campus_id: this.info.campus_id,
				name: null,
				cert_group_id: this.info.id
			}
			this.getCertData()
			return this
		},
		handleSaveSuccess(data, mode) {
			if (mode === 'add') {
				this.upsearch()
			} else {
				this.$refs.certTable.refresh()
			}
		}
	}
}
</script>

<style scoped lang="scss">
.el-select {
	width: 100%;
	max-width: unset;
}

.el-header > .right-panel .el-input {
	margin-right: 10px;
}

.el-header > .right-panel .el-button {
	margin: 0 5px;
}

.el-link {
	margin-right: 10px;
	font-size: 12px;
}
</style>
