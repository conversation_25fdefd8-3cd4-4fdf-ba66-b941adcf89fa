<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
					<el-option
						v-for="item in CampusManagementList"
						:key="item.code"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
				<el-date-picker
					v-model="params.date"
					type="daterange"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					size="default"
					value-format="YYYY-MM-DD"
					style="width: 500px; margin-left: 15px; flex-grow: unset"
					@change="dataChange"
				/>
			</div>
			<div class="right-panel"></div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" stripe :apiObj="list.apiObj" :params="params">
				<el-table-column label="预约房间" prop="room_name" width="200" show-overflow-tooltip>
					<template #default="scope">
						<div v-if="scope.row.room">{{ scope.row.room.name }}</div>
					</template>
				</el-table-column>
				<el-table-column label="预约主题" prop="title" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="预约时间" prop="begin_time" width="300" >
					<template #default="scope"> {{ scope.row.begin_time }} - {{ scope.row.end_time }}</template>
				</el-table-column>
				<el-table-column label="预约人" prop="booker_name"></el-table-column>
				<el-table-column label="参与老师" prop="teachers_list" width="200" show-overflow-tooltip>
					<template #default="scope">
						<span v-for="(item, index) in scope.row.teachers_list" :key="index" size="small" type="info">{{
								item.name + ' '
							}}</span>
					</template>
				</el-table-column>
				<el-table-column label="参与学生" prop="students_list" width="200" show-overflow-tooltip>
					<template #default="scope">
						<span v-for="(item, index) in scope.row.students_list" :key="index" size="small" type="info"
						>{{ item.name + ' ' }}
						</span>
					</template>
				</el-table-column>
				<el-table-column label="备注" prop="remark"  show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="180">
					<template #default="scope">
						<el-button-group>
							<!-- <el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)"
								>修改预约</el-button
							> -->
							<el-popconfirm title="确定取消预约吗？" width="180" @confirm="table_cancel(scope.row, scope.$index)">
								<template #reference>
									<el-button type="danger" size="small" text>取消预约</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<!-- {{ $refs.table }}--- -->
		<!-- {{ roomNameFilters }}  -->
		<save ref="saveDialog" @success="saveSuccess"></save>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
import save from './save.vue'

const { campusId, tenantId, campusInfo, roomTypeMap, roomCapacityMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		begin_date: null,
		end_date: null,
		date: []
	}
}

export default {
	name: 'record',
	components: {
		save
	},
	data() {
		return {
			list: {
				apiObj: this.$API.fieldReservation.all
			},
			params: defaultParams(),
			CampusManagementList: campusInfo,
			roomNameFilters: [],
			roomTypeMap,
			roomCapacityMap
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
			}
		}
	},
	computed: {},
	created() {
	},
	methods: {
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		dataChange(val) {
			console.log(val, 'dataChange')
			if (val) {
				this.params.begin_date = val[0]
				this.params.end_date = val[1]
			} else {
				this.params.begin_date = null
				this.params.end_date = null
			}
			this.$refs.table.upData(this.params)
		},
		//修改预约
		table_edit(row) {
			this.$refs.saveDialog.open(row)
		},
		// 修改成功
		async saveSuccess(formVal) {
			console.log(formVal, 'saveSuccess')
			let formData = {
				id: formVal.id,
				tenant_id: tenantId,
				campus_id: campusId,
				title: formVal.title,
				room_id: formVal.room_id[formVal.room_id.length - 1] || formVal.room_id,
				begin_time: formVal.date + ' ' + formVal.startTime,
				end_time: formVal.date + ' ' + formVal.endTime,
				teachers: formVal.teachers.map((item) => item.id).join(','),
				students: formVal.students.map((item) => item.id).join(','),
				remark: formVal.remark
			}
			const { code, message } = await this.$API.fieldReservation.edit.post(formData)
			if (code == 200) {
				ElMessage.success(message)
				this.$refs.table.refresh()
			} else {
				ElMessage.error(message)
			}
		},
		//取消预约
		async table_cancel(row) {
			let delData = {
				id: row.id,
				tenant_id: tenantId,
				campus_id: campusId
			}
			const { code, message } = await this.$API.fieldReservation.del.post(delData)
			if (code == 200) {
				ElMessage.success(message)
				this.$refs.table.refresh()
			} else {
				ElMessage.error(message)
			}
		}
	}
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;

	.el-button {
		width: 80%;
	}
}

.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;
	box-sizing: border-box;

	a {
		font-size: 18px;
	}
}
</style>
