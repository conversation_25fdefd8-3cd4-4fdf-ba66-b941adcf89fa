<template>
	<el-drawer v-model="showDrawer" title="切片管理" direction="rtl" destroy-on-close	 size="50%">
		<div class="video-play">
			<VideoPlay v-if="showVideo" ref="videoRef" width="100%"
				:vid="VideoPlayAtuh.videoInfo.video_vod_id" :playauth="VideoPlayAtuh.videoInfo.play_auth"
				:cover="VideoPlayAtuh.videoInfo.cover_url" :progressMarkers="progressMarkers"></VideoPlay>
		</div>
		<div class="btn">
			<el-button type="primary" icon="el-icon-Plus" @click="add_slice">新增视频切片</el-button>
		</div>
		<scTable ref="tableRef" :params="params" :apiObj="apiObj" hidePagination hideDo>
			<el-table-column prop="name" label="切片封面" width="80">
				<template #default="{ row }">
					<el-image v-if="row.cover_url" :src="row.cover_url" style="width: 50px; height: 50px;"></el-image>
				</template>
			</el-table-column>
			<el-table-column prop="name" label="切片名称">
				<template #default="{ row }">
					{{ row.name }}
				</template>
			</el-table-column>
			<el-table-column prop="description" label="切片描述" show-overflow-tooltip></el-table-column>
			<el-table-column prop="time_begin" label="视频开始时间" width="110">
				<template #default="{ row }">
					<el-link type="primary" @click="handlerTime(row.time_begin)"> {{ row.time_begin + 's'
						}}</el-link>
				</template>
			</el-table-column>
			<el-table-column prop="created_user_name" label="创建人" width="100"></el-table-column>
			<el-table-column label="操作" fixed="right" width="150">
				<template #default="{ row }">
					<el-button type="primary" text size="small" @click="table_edit(row)">编辑</el-button>
					<el-popconfirm title="确定删除吗？" @confirm="table_del(row)">
						<template #reference>
							<el-button text type="danger" size="small">删除</el-button>
						</template>
					</el-popconfirm>
				</template>
			</el-table-column>
		</scTable>
	</el-drawer>

	<SaveDialog ref="dialogRef" :params="params" @success="handlerSuccess"></SaveDialog>
</template>
<script setup>
import VideoPlay from '@/components/videoPlay/index.vue'
import SaveDialog from './save.vue'
import { ref, getCurrentInstance, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		tenant_id: '',
		campus_id: '',
		material_id: ''
	}
}
const showVideo = ref(false)
const showDrawer = ref(false)
const VideoPlayAtuh = reactive({
	videoInfo: {
		play_auth: '',
		cover_url: '',
		duration: 0,
		status: '',
		title: '',
		video_vod_id: '',
		obj: ''
	}
})
// 视频打点配置字段
const progressMarkers = ref([])

const videoData = ref({
	tenant_id: '',
	campus_id: '',
	material_id: '',
	password: ''
})
let videoRef = ref(null)
let dialogRef = ref(null)
const params = ref(defaultParams())
let tableRef = ref(null)
// onMounted(async () => {
// 	await getVideoPlayAuth()
// 	progressMarkers.value = tableRef.value.tableData.map((v) => {
// 		return {
// 			offset: v.time_begin,
// 			isCustomized: true,
// 			coverUrl: v.cover_url,
// 			title: v.name,
// 			describe: v.description
// 		}
// 	})
// })
// 获取视频切片列表_list
const apiObj = ref(globalPropValue.eduMaterials.material.get_video_slice_list)
// 获取视频播放凭证
const getVideoPlayAuth = async () => {
	const res = await globalPropValue.eduMaterials.material.get_video_play_atuh.post(videoData.value)
	if (res.code === 200) {
		Object.assign(VideoPlayAtuh.videoInfo, res.data)
		showVideo.value = true
	}
}

const open = (data) => {
	params.value = data
	videoData.value = {
		...videoData.value,
		...data
	}
	showDrawer.value = true
	// showVideo.value = true
	getVideoPlayAuth()
	nextTick(() => {
		progressMarkers.value = tableRef.value.tableData.map((v) => {
			return {
				offset: v.time_begin,
				isCustomized: true,
				coverUrl: v.cover_url,
				title: v.name,
				describe: v.description
			}
		})
	})
}
// 返回按钮点击事件
const goBack = () => {
	router.back()
}
// 添加视频切片点击事件
const add_slice = () => {
	dialogRef.value.open('add')
}
// 编辑点击事件
const table_edit = (row) => {
	dialogRef.value.open('edit', row)
	dialogRef.value.setData(row)
}
// 删除点击事件
const table_del = async (row) => {
	const res = await globalPropValue.eduMaterials.material.del_video_slice.post({
		id: row.id,
		tenant_id: Number(params.value.tenant_id),
		campus_id: Number(params.value.campus_id)
	})
	if (res.code === 200) {
		ElMessage({
			type: 'success',
			message: '删除成功'
		})
		tableRef.value.upData(params.value)
	} else {
		ElMessage({
			type: 'error',
			message: res.message
		})
	}
}
// 时间点击事件
const handlerTime = (time) => {
	videoRef.value.seek(time)
	videoRef.value.play()
}
// 自定义事件
const handlerSuccess = () => {
	tableRef.value.upData(params.value)
}

/*const timeupdate = (e) => {
	console.log(e._TimeUpdateStamp)
	if (e._TimeUpdateStamp >= 10){
		videoRef.value.seek(3)
		videoRef.value.play()
	}
}*/
defineExpose({ open })
</script>

<style scoped lang="scss">
.video-play{
	width: 100%;
	height: 320px;
}
.btn{
	margin: 10px 0;
}
</style>
