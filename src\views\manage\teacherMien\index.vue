<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<el-select
					v-if="CampusManagementList.length > 1"
					v-model="params.campus_id"
					placeholder="校区"
					filterable
					style="margin-right: 15px"
					@change="campusChange"
				>
					<el-option
						v-for="item in CampusManagementList"
						:key="item.code"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
				<el-input v-model="params.name" placeholder="请输入教师名称" style="width: 200px" @input="upsearch"></el-input>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :apiObj="list.apiObj" :params="params">
				<el-table-column label="头像" prop="user_head" width="100">
					<template #default="scope">
						<cusHead
							loading="lazy"
							:lazy="true"
							fit="contain"
							style="width: 50px; height: 50px"
							:src="scope.row.teacher_info.img"
							:preview-src-list="[scope.row.teacher_info.img]"
							preview-teleported
						>
						</cusHead>
					</template>
				</el-table-column>
				<el-table-column label="姓名" prop="name" width="120">
					<template #default="scope">
						<span v-if="scope.row.teacher_info">{{ scope.row.teacher_info.name }}</span>
					</template>
				</el-table-column>
				<el-table-column label="简介" prop="introduction" show-overflow-tooltip> </el-table-column>
				<el-table-column label="文章数" prop="article_num" width="100"> </el-table-column>
				<el-table-column label="视频数" prop="video_num" width="100"> </el-table-column>
				<el-table-column label="点赞数" prop="like_num" width="100"> </el-table-column>
				<el-table-column label="收藏数" prop="collect_num" width="100"> </el-table-column>
				<el-table-column label="状态" prop="recommend" width="120">
					<template #default="scope">
						<el-switch
							v-model="scope.row.recommend"
							inline-prompt
							style="--el-switch-on-color: #00B42A;"
							:active-value="1"
							:inactive-value="-1"
							active-text="已推荐"
							inactive-text="未推荐"
							@change="
								(val) => {
									recommend(val, scope.row)
								}
							"
						/>
					</template>
				</el-table-column>
				<el-table-column label="排序(* 数字越大排名越前)" prop="listorder" width="200">
					<template #default="scope">
						<el-input-number
							v-model="scope.row.listorder"
							min="0"
							placeholder="排序"
							style="width: 130px"
							@change="
								(val) => {
									order(val, scope.row)
								}
							"
						/>
					</template>
				</el-table-column>
				<el-table-column label="查看" width="100" align="center" fixed="right">
					<template #default="scope">
						<el-button text type="primary" size="small" @click="show(scope.row)"> 查看 </el-button>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
		<el-drawer
			v-model="showDetailDialog"
			title="查看"
			direction="rtl"
			size="100%"
			class="articleDetail"
			append-to-body
			destroy-on-close
			:close-on-press-escape="false"
		>
			<show :userId="userId"></show>
		</el-drawer>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
import cusHead from '@/components/custom/cusStaffHead.vue'
import show from '@/views/home/<USER>/index.vue'

const { campusId, tenantId, campusInfo } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		pageSize: 20,
		page: 1,
		name: ''
	}
}
export default {
	components: {
		cusHead,
		show
	},
	data() {
		return {
			params: defaultParams(),
			CampusManagementList: campusInfo,
			showDetailDialog: false,
			userId: 0,
			list: {
				apiObj: this.$API.famous.list
			}
		}
	},
	methods: {
		show(item) {
			this.userId = item.teacher_id
			this.$nextTick(() => {
				this.showDetailDialog = true
			})
		},
		campusChange(val) {
			this.params.campus_id = val
			this.$refs.table.upData(this.params)
		},
		recommend(val, item) {
			this.$API.famous.recommend
				.post({
					id: item.id,
					tenant_id: item.tenant_id,
					campus_id: item.campus_id
				})
				.then((res) => {
					if (res.code === 200) {
						ElMessage({ type: 'success', message: '操作成功！' })
					} else {
						ElMessage({ type: 'error', message: res.message })
					}
				})
		},
		order(val, item) {
			this.$API.famous.order
				.post({
					id: item.id,
					tenant_id: item.tenant_id,
					campus_id: item.campus_id,
					order: val
				})
				.then((res) => {
					if (res.code === 200) {
						ElMessage({ type: 'success', message: '排序成功！' })
					} else {
						ElMessage({ type: 'error', message: res.message })
					}
				})
		}
	}
}
</script>

<style lang="scss" scoped>
:deep(.el-table) {
	.el-popper {
		max-width: 500px;
		max-height: 500px;
	}
}
</style>
<style lang="scss">
:deep(.articleDetail) {
	.el-aside {
		border-right: unset;
		margin-right: 10px;
	}
	.el-drawer__body{
		background: var(--el-border-color-light);
	}
}
</style>
