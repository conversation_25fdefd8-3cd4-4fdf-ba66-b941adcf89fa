<template>
	<el-drawer v-model="visible" :title="titleMap[mode]" :size="drSize" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-width="100">
			<el-form-item label="产品名称" prop="product_name">
				<el-input v-model="form.product_name" placeholder="请输入产品名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="所属品类" prop="template">
				<el-radio-group v-model="form.template" :disabled="mode === 'edit'">
					<el-radio :value="1" label="标准品类">标准品类</el-radio>
					<el-radio :value="2" label="自定义品类">自定义品类</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.template === 1" label="选择品类" prop="template_id">
				<el-input v-show="false" v-model="form.template_id" placeholder="请选择标准品类" readonly></el-input>
				<el-input
					v-model="form.template_info.name"
					class="selectTemplate"
					placeholder="请选择标准品类"
					readonly
					:disabled="mode === 'edit'"
					@click="selectTemplate"
				></el-input>
			</el-form-item>
			<el-form-item label="节点类型" prop="node_type">
				<el-radio-group v-model="form.node_type" :disabled="mode == 'edit'">
					<el-radio-button v-for="(item, index) in enumConfig.nodeTypeMap" :key="index" :value="item.value"
						>{{ item.name }}
					</el-radio-button>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="接入协议" prop="protocol">
				<el-radio-group v-model="form.protocol">
					<el-radio-button v-for="(item, index) in enumConfig.protocolMap" :key="index" :value="item.value"
						>{{ item.name }}
					</el-radio-button>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="网络类型" prop="net_type">
				<el-select v-model="form.net_type" style="width: 100%; max-width: unset" placeholder="请选择网络类型" clearable>
					<el-option
						v-for="(item, index) in enumConfig.netTypeMap"
						:key="index"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="产品状态" prop="status">
				<el-switch v-model="form.status" :active-value="1" :inactive-value="-1"></el-switch>
			</el-form-item>
			<el-form-item label="控制面板类型" prop="panel_type">
				<el-select
					v-model="form.panel_type"
					style="width: 100%; max-width: unset"
					placeholder="请选择设备控制面板类型"
					clearable
				>
					<el-option
						v-for="(item, index) in enumConfig.panelTypeMap"
						:key="index"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="产品图片" prop="product_img">
				<scUpload v-model="form.product_img" fileTypeTag="lotProduct"></scUpload>
			</el-form-item>
			<el-form-item label="工厂/品牌" prop="factory">
				<el-input v-model="form.factory" clearable></el-input>
			</el-form-item>
			<el-form-item label="产品描述" prop="desc">
				<el-input v-model="form.desc" clearable rows="3" type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
	<select-template
		v-if="templateSelectVisible"
		ref="templateSelect"
		@success="submitTemplate"
		@closed="closedTemplateSelect"
		@changeSize="changeSize"
	></select-template>
</template>

<script>
import cusTom from '@/utils/cusTom'
import SelectTemplate from '@/views/lot/product/selectTemplate.vue'

const { tenantId } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		id: null,
		tenant_id: tenantId,
		product_name: null,
		template_id: null,
		template: 1,
		product_img: null,
		desc: null,
		status: -1,
		protocol: null,
		node_type: null,
		net_type: null,
		factory: null,
		template_info: {
			id: null,
			name: null
		}
	}
}

export default {
	components: { SelectTemplate },
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},

	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '创建产品',
				edit: '编辑产品',
				show: '查看'
			},
			visible: false,
			templateSelectVisible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			drSize: 550,
			enumConfig: [],
			//验证规则
			rules: {
				product_name: [{ required: true, message: '请输入产品名称' }],
				template_id: [{ required: true, message: '请选择标准品类' }],
				protocol: [{ required: true, message: '请选择接入协议' }],
				node_type: [{ required: true, message: '请选择节点类型' }],
				template: [{ required: true, message: '请选择类型' }],
				status: [{ required: true, message: '请选择状态' }],
				net_type: [{ required: true, message: '请选择网络类型' }]
			}
		}
	},
	mounted() {},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	methods: {
		changeSize() {
			this.drSize = 550
		},
		submitTemplate(info) {
			this.form.template_id = info.id
			this.form.template_info.name = info.template_name
			this.form.template_info.id = info.id
			this.drSize = 550
			this.templateSelectVisible = false
		},
		closedTemplateSelect() {
			this.drSize = 550
			this.templateSelectVisible = false
		},

		selectTemplate() {
			this.templateSelectVisible = true
			this.$nextTick(() => {
				this.drSize = 800
				this.$refs.templateSelect.open()
			})
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let subForm
					if (this.mode === 'add') {
						subForm = { ...this.params, ...this.form }
					} else if (this.mode === 'edit') {
						subForm = { ...this.form }
					}
					var res = await this.$LotApi.product.save.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			if (data.template_id > 0) {
				this.form.template = 1
			} else {
				this.form.template = 2
			}
		}
	}
}
</script>

<style scoped lang="scss">
.selectTemplate {
	cursor: pointer;
}
</style>
