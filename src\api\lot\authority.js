import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/lot/authority/list`,
		name: '获取列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	one: {
		url: `${config.API_URL}/lot/authority/one`,
		name: '获取单个信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/lot/authority/save`,
		name: '新增/修改',
		post: async function (data) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/lot/authority/del`,
		name: '删除',
		post: async function (data) {
			return await http.post(this.url, data)
		}
	}
}
