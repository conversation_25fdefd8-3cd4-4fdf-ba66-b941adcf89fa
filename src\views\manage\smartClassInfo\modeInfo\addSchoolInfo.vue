<template>
	<el-dialog v-model="visible" destroy-on-close title="添加学校信息" width="40%">
		<cusEditor ref="editor" v-model="school_profile" placeholder="请输入学校信息" mode="simple"></cusEditor>
		同步至所有班牌：
		<el-switch
			v-model="is_all"
			:active-value="1"
			:inactive-value="0"
			class="ml-2"
			style="--el-switch-on-color: #13ce66; --el-switch-off-color: #909399"
		></el-switch>
		<template #footer>
			<el-button @click="close">取消</el-button>
			<el-button type="primary" @click="add">保存</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import cusEditor from '@/components/custom/cusEditor.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const school_profile = ref('')
const is_all = ref(0)
const emit = defineEmits(['addSuccess'])
const add = () => {
	globalPropValue.platoonhotspot.save_open_mode
		.post({
			tenant_id: tenantId,
			campus_id: campusId,
			device_sn: current.value.sn,
			school_profile: school_profile.value
		})
		.then((res) => {
			ElMessage.success(res.message)
			close()
		})
}
const current = ref({})
const visible = ref(false)
const show = (val) => {
	current.value = val
	visible.value = true
}
const close = () => {
	visible.value = false
	emit('addSuccess')
}
defineExpose({
	show,
	close
})
</script>
