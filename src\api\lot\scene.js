import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/lot/scene/list`,
		name: '获取房间场景列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	listByRoom: {
		url: `${config.API_URL}/lot/scene/getListByRoom`,
		name: '获取单个房间的场景列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	logList: {
		url: `${config.API_URL}/lot/scene/getLog`,
		name: '获取场景日志列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	one: {
		url: `${config.API_URL}/lot/scene/one`,
		name: '获取单个场景信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	changeStatus: {
		url: `${config.API_URL}/lot/scene/changeStatus`,
		name: '修改场景状态',
		post: async function (data) {
			return await http.post(this.url, data)
		}
	},
	setAction: {
		url: `${config.API_URL}/lot/scene/setAction`,
		name: '设置场景动作',
		post: async function (data) {
			return await http.post(this.url, data)
		}
	},
	save: {
		url: `${config.API_URL}/lot/scene/save`,
		name: '新增/修改规则',
		post: async function (data) {
			return await http.post(this.url, data)
		}
	},
	run: {
		url: `${config.API_URL}/lot/scene/run`,
		name: '运行场景',
		post: async function (data) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/lot/scene/del`,
		name: '删除规则',
		post: async function (data) {
			return await http.post(this.url, data)
		}
	}
}
