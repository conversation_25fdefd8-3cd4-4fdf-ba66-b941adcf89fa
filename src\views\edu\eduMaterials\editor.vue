<template>
	<el-form>
		<el-form-item label="教材描述" labelWidth="130px">
			<textEditor v-model:value="topicForm" height="300px"></textEditor>
		</el-form-item>
	</el-form>
</template>
<script setup>
import { ref, defineProps, watch } from 'vue'
import textEditor from '@/components/textEditor'
const props = defineProps({
	description: {
		type: String,
		default: ''
	},
	label: {
		type: String,
		default: '教材描述'
	}
})
const emit = defineEmits(['update:description'])
const topicForm = ref(props.description)
watch(topicForm, (newValue) => {
	emit('update:description', newValue)
})
</script>

<style lang="scss" scoped></style>
