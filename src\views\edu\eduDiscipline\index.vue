<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-form-item label="课程体系">
						<el-input v-model="params.name" placeholder="课程体系名称" clearable></el-input>
					</el-form-item>

					<el-form-item label="校区" v-if="CampusManagementList.length > 1">
						<el-select v-model="params.campus_id" placeholder="校区" filterable>
							<el-option
								:label="item.name"
								:value="item.value"
								v-for="item in CampusManagementList"
								:key="item.code"
							></el-option>
						</el-select>
					</el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-plus" @click="add">新增课程体系</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" @selection-change="selectionChange" :apiObj="apiObj">
				<!-- <el-table-column type="selection" width="50"></el-table-column> -->
				<el-table-column label="课程体系" prop="discipline_name" width="250"></el-table-column>

				<el-table-column label="创建时间" prop="created_at" width="180"></el-table-column>
				<el-table-column label="备注" prop="remark" min-width="300"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="240">
					<template #default="scope">
						<el-button-group>
							<el-button text type="primary" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>
							<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>

	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
		:params="params"
	></save-dialog>
</template>

<script>
import saveDialog from './save'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, tenantInfo } = cusTom.getBaseQuery()

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		name: null
	}
}
export default {
	name: 'dept',
	components: {
		saveDialog
	},
	data() {
		return {
			dialog: {
				save: false
			},
			apiObj: this.$API.eduDiscipline.discipline.list,
			selection: [],

			params: defaultParams(),
			CampusManagementList: campusInfo,
			TenantManagementList: tenantInfo,
			treeData: null
		}
	},
	watch: {},
	computed: {},
	async created() {},
	methods: {
		//添加
		add(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', row)
			})
			// this.upsearch()
		},
		//编辑
		table_edit(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.eduDiscipline.discipline.del.post(reqData)
			if (res.code === 200) {
				this.$message.success('删除成功')
				this.upsearch()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			this.$API.eduDiscipline.discipline.status.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.upsearch()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？如果删除项中含有子集将会被一并删除`, '提示', {
				type: 'warning'
			})
				.then(() => {
					const loading = this.$loading()
					this.$refs.table.refresh()
					loading.close()
					this.$message.success('操作成功')
				})
				.catch(() => {})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.params)
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		//根据ID获取树结构
		filterTree(id) {
			var target = null
			function filter(tree) {
				tree.forEach((item) => {
					if (item.id === id) {
						target = item
					}
					if (item.children) {
						filter(item.children)
					}
				})
			}
			filter(this.$refs.table.tableData)
			return target
		},
		//本地更新数据
		handleSaveSuccess(data, mode) {
			this.upsearch()
		}
	}
}
</script>

<style lang="scss" scoped></style>
