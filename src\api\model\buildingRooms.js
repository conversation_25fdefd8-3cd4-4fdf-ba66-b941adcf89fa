import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/manapi/dormitory/building_list`,
		name: '获取宿舍楼列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	all: {
		url: `${config.API_URL}/manapi/dormitory/building_all`,
		name: '获取宿舍楼列表不分页',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	create: {
		url: `${config.API_URL}/manapi/dormitory/building_creat`,
		name: '新增宿舍楼',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	save: {
		url: `${config.API_URL}/manapi/dormitory/building_save`,
		name: '宿舍楼修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},

	del: {
		url: `${config.API_URL}/manapi/dormitory/building_del`,
		name: '删除宿舍楼',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	status: {
		url: `${config.API_URL}/manapi/dormitory/building_changeStatus`,
		name: '修改宿舍楼状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	tree: {
		url: `${config.API_URL}/manapi/dormitory/building_tree`,
		name: '获取宿舍楼树形结构',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	rooms: {
		list: {
			url: `${config.API_URL}/manapi/dormitory/room_list`,
			name: '获取宿舍楼房间列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/manapi/dormitory/room_all`,
			name: '获取宿舍楼房间所有的',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/manapi/dormitory/room_save`,
			name: '宿舍楼房间修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/manapi/dormitory/room_del`,
			name: '删除宿舍楼房间',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		status: {
			url: `${config.API_URL}//manapi/dormitory/room_changeStatus`,
			name: '修改宿舍楼房间状态',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		record: {
			url: `${config.API_URL}/manapi/dormitory/record`,
			name: '宿舍楼房间记录',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		}
	},
	view: {
		url: `${config.API_URL}/manapi/dormitory/building_view`,
		name: '获取宿舍',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	buildingTree: {
		url: `${config.API_URL}/manapi/dormitory/building_tree`,
		name: '获取宿舍楼栋树',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	personnel: {
		list: {
			url: `${config.API_URL}/manapi/dormitory/stay_list`,
			name: '获取宿舍人员列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},

		leave: {
			url: `${config.API_URL}/manapi/dormitory/stay_leave`,
			name: '新增宿舍人员',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		save: {
			url: `${config.API_URL}/manapi/dormitory/stay_save	`,
			name: '宿舍人员修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},

		del: {
			url: `${config.API_URL}/manapi/dormitory/stay_del`,
			name: '删除宿舍人员',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	},
	AccommodationArrangements: {
		list: {
			url: `${config.API_URL}/manapi/dormitory/manage_list`,
			name: '获取宿管列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/manapi/dormitory/manage_save`,
			name: '新增修改宿管',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/manapi/dormitory/manage_del`,
			name: '删除宿管',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	},
	scoreSetting: {
		list: {
			url: `${config.API_URL}/manapi/dormitory_score/list`,
			name: '分值设置列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await

				return await http.get(this.url, params)
			}
		},
		copy: {
			url: `${config.API_URL}/manapi/dormitory_score/copy`,
			name: '继承其他学期的分数设置',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		quick: {
			url: `${config.API_URL}/manapi/dormitory_score/quick`,
			name: '快速设置',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		update: {
			url: `${config.API_URL}/manapi/dormitory_score/update`,
			name: '修改分值设置',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	},
	evaluate: {
		list: {
			url: `${config.API_URL}/manapi/dormitory_evaluate/list`,
			name: '所有评价配置',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/manapi/dormitory_evaluate/all`,
			name: '所有评价配置',
			get: async function (params) {
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/manapi/dormitory_evaluate/save`,
			name: '新增修改评价配置',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/manapi/dormitory_evaluate/del`,
			name: '删除评价配置',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		record: {
			url: `${config.API_URL}/manapi/dormitory_evaluate/record`,
			name: '评价记录',
			get: async function (params) {
				return await http.get(this.url, params)
			}
		},
		action: {
			url: `${config.API_URL}/manapi/dormitory_evaluate/action`,
			name: '评价记录',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		}
	}
}
