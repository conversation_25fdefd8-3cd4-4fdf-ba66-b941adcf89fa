<template>
	<el-dialog width="600" v-model="visible" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-width="110">
			<el-form-item label="公章名称" prop="name">
				<el-input v-model="form.name" placeholder="请输入名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="部门" prop="department_id">
				<cusCascader
					v-model="form.department_id"
					:options="treeData"
					:prop="{
						emitPath: false,
						value: 'id',
						label: 'department_name',
						checkStrictly: true
					}"
				></cusCascader>
			</el-form-item>

			<el-form-item label="落款文字" prop="signed">
				<el-input v-model="form.signed" placeholder="请输入" clearable></el-input>
			</el-form-item>
			<el-form-item label="颁发单位" prop="issue">
				<el-input v-model="form.issue" placeholder="请输入" clearable></el-input>
			</el-form-item>

			<el-form-item label="公章图片" prop="seal_img">
				<scUpload v-model="form.seal_img" fileTypeTag="seal" :disabled="mode == 'show'"></scUpload>
				<span style="color: #666; padding-left: 10px">公章图片需为透明底公章，建议文件不小于100KB</span>
			</el-form-item>

			<el-form-item label="状态" prop="status">
				<el-switch
					v-model="form.status"
					inline-prompt
					style="--el-switch-on-color: #00B42A;"
					:active-value="1"
					:inactive-value="-1"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'

const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		name: null,
		signed: null,
		issue: null,
		seal_img: null,
		status: 1,
		department_id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				name: [{ required: true, message: '请输入公章名称' }],
				department_id: [{ required: true, message: '请选择所属部门' }],
				signed: [{ required: true, message: '请输入落款文字' }],
				issue: [{ required: true, message: '请输入颁发单位' }],
				seal_img: [{ required: true, message: '请上传公章照片' }],
			},

			course: [],
			discipline: [],
			treeData: [],
			position: []
		}
	},
	computed: {},
	mounted() {},
	created() {
		this.getDept()
	},
	methods: {
		async getDept() {
			const { data } = await this.$API.system.dept.all.get(this.params)
			this.treeData = cusTom.arrayToTree(data)
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.seal.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
