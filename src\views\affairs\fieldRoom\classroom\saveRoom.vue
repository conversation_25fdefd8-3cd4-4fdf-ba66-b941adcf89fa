<template>
	<el-drawer v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'">
			<el-form-item v-if="form.parent_id === 0" label="建筑" prop="building_id">
				<el-select
					v-model="form.building_id"
					placeholder="请选择"
					filterable
					clearable
					:disabled="mode == 'edit'"
					@change="changebuild"
				>
					<el-option v-for="item in buildingOptions" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item v-if="form.parent_id === 0" label="楼层" prop="floor_id">
				<el-select v-model="form.floor_id" placeholder="请选择" filterable clearable :disabled="mode == 'edit'">
					<el-option v-for="item in floorOptions" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="房间名称" prop="room_name">
				<el-input v-model="form.room_name" placeholder="请输入名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="类型" prop="room_type">
				<el-radio-group v-model="form.room_type">
					<el-radio v-for="item in roomTypeMap" :label="item.value">{{ item.name }}</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="容纳人数" prop="room_capacity">
				<el-radio-group v-model="form.room_capacity">
					<el-radio v-for="item in roomCapacityMap" :label="item.value">{{ item.name }}</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="开放预约" prop="is_open">
				<el-radio-group v-model="form.is_open">
					<el-radio :label="1">开放</el-radio>
					<el-radio :label="-1">不开放</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.is_open === 1" label="门禁类型" prop="lock_type">
				<el-radio-group v-model="form.lock_type">
					<el-radio :label="1">门锁模式</el-radio>
					<el-radio :label="2">会议室门禁</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.is_open === 1" label="预约类型" prop="open_type">
				<el-radio-group v-model="form.open_type">
					<el-radio :label="1">自由时段</el-radio>
					<el-radio :label="2">课程时段</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.is_open === 1 && form.open_type === 1" label="开放时段" prop="allow_time">
				<el-time-select
					v-model="form.allow_start"
					style="width: 150px; margin-right: 10px"
					:max-time="form.allow_end"
					class="mr-4"
					placeholder="开始时间"
					start="07:00"
					step="00:30"
					end="22:00"
				/>
				-
				<el-time-select
					v-model="form.allow_end"
					style="width: 150px; margin-left: 10px"
					:min-time="form.allow_start"
					placeholder="截止时间"
					start="07:00"
					step="00:30"
					end="22:00"
				/>
			</el-form-item>
			<el-form-item v-if="form.is_open === 1 && form.open_type === 2" label="课程时段" prop="is_open">
				<el-select v-model="form.periods_id" placeholder="请选择课程时段" filterable clearable>
					<el-option v-for="item in coursePeriodList" :key="item.id" :label="item.periods_name" :value="item.id">
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="房间标签">
				<el-tag
					v-for="tag in form.tags_aryCopy"
					:key="tag"
					closable
					:disable-transitions="false"
					style="margin-top: 5px"
					@close="handleCloseTag(tag)"
				>
					{{ tag }}
				</el-tag>
				<el-input
					v-if="inputVisible"
					ref="InputRef"
					v-model="inputValue"
					style="width: 100px; margin-left: 10px; margin-top: 5px"
					size="small"
					@keyup.enter="handleInputConfirm"
					@blur="handleInputConfirm"
				/>
				<el-button
					v-else
					class="button-new-tag"
					style="margin-left: 10px; margin-top: 5px"
					size="small"
					@click="showInput"
				>
					增加标签
				</el-button>
			</el-form-item>
			<el-form-item label="房间图片" prop="room_img">
				<scUpload v-model="form.room_img" fileTypeTag="room"></scUpload>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input
					v-model="form.remark"
					placeholder="请输入"
					clearable
					type="textarea"
					:autosize="{ minRows: 4, maxRows: 6 }"
				></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import cusTom from '@/utils/cusTom'

const { roomTypeMap, roomCapacityMap } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		tenant_id: '',
		campus_id: '',
		building_id: '',
		floor_id: '',
		room_name: '',
		listorder: 1,
		parent_id: 0,
		status: 1,
		room_capacity: null,
		room_img: '',
		room_type: null,
		periods_id: null,
		open_type: 1,
		lock_type: 1,
		is_open: -1,
		tags_ary: [],
		tags_aryCopy: [],
		tags: '',
		allow_start: '',
		allow_end: ''
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		groupData: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			mode: 'add',
			inputVisible: false,
			inputValue: '',
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			titleMap: {
				add: '新增教室',
				edit: '编辑教室'
			},
			//验证规则
			rules: {
				room_name: [{ required: true, message: '请输入房间名称' }],
				building_id: [{ required: true, message: '请选择建筑' }],
				floor_id: [{ required: true, message: '请选择楼层' }],
				room_type: [{ required: true, message: '请选择类型' }],
				room_capacity: [{ required: true, message: '请选择容纳人数' }]
			},
			metaData: [],
			buildingOptions: [],
			floorOptions: [],
			coursePeriodList: [],
			roomTypeMap,
			roomCapacityMap
		}
	},
	mounted() {},
	created() {
		this.getLou()
		this.getData()
	},
	computed: {
		buildingOptions() {
			return this.metaData.filter((item) => item.parent_id <= 0 && item.building_type === 1)
		},
		floorOptions() {
			let c = this.metaData.find((item) => item.id === this.form.building_id)
			if (c) return c.floor
			return []
		}
	},
	watch: {},
	methods: {
		getData() {
			this.$API.eduSchedule.period
				.get({
					tenant_id: this.params.tenant_id,
					campus_id: this.params.campus_id
				})
				.then((res) => {
					this.coursePeriodList = res.data
				})
		},
		handleCloseTag(tag) {
			this.form.tags_aryCopy.splice(this.form.tags_aryCopy.indexOf(tag), 1)
		},
		showInput() {
			this.inputVisible = true
			//this.$refs.InputRef.input.focus()
		},
		handleInputConfirm() {
			if (this.inputValue) {
				this.form.tags_aryCopy.push(this.inputValue)
			}
			this.inputVisible = false
			this.inputValue = ''
		},
		changebuild() {
			this.form.floor_id = ''
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.campus_id = this.params.campus_id
			this.form.tenant_id = this.params.tenant_id
			return this
		},

		//获取建筑
		async getLou() {
			this.metaData = cusTom.treeToArray(this.groupData)
			this.delRoom
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				console.log(valid)
				if (valid) {
					this.isSaveing = true
					if (this.form.tags_aryCopy) {
						this.form.tags = this.form.tags_aryCopy.join(',')
					} else {
						this.form.tags = ''
					}
					var res = await this.$API.fieldRoom.rooms.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},

		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			if (data.tags) {
				this.form.tags_aryCopy = data.tags.split(',')
			} else {
				this.form.tags_aryCopy = []
			}
			console.log(this.form)
		},
		setParent(data) {
			if (data) {
				this.form.parent_id = data.id
				this.form.building_id = data.building_id
				this.form.floor_id = data.floor_id
			}
		}
	}
}
</script>

<style></style>
