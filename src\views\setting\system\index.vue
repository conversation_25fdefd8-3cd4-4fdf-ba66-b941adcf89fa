<template>
	<el-main>
		<el-card shadow="never">
			<el-tabs tab-position="top">
				<el-tab-pane label="系统设置">
					<el-form ref="form" :model="tenantInfo" label-width="150px" style="margin-top: 20px">
						<el-form-item label="强制播放">
							<el-switch v-model="tenantInfo.mandate" :active-value="1" :inactive-value="-1" />
							<div class="el-form-item-msg" data-v-b33b3cf8="">
								开启强制播放后，用户点击任意视频均强制播放指定视频源
							</div>
						</el-form-item>
						<el-form-item v-if="tenantInfo.mandate === 1" label="强制播放视频">
							<scUploadFile
								v-model="tenantInfo.mandate_url"
								:limit="1"
								fileTypeTag="sys"
								:multiple="false"
								:maxSize="500"
								accept="video/*"
							></scUploadFile>
						</el-form-item>
						<el-form-item label="门锁临时密码有效期">
							<el-select v-model="tenantInfo.temporaryPwdExpired" placeholder="请选择" style="width: 240px">
								<el-option
									v-for="item in [
										{ label: '5分钟', value: 5 },
										{ label: '10分钟', value: 10 },
										{ label: '15分钟', value: 15 },
										{ label: '20分钟', value: 20 },
										{ label: '30分钟', value: 30 }
									]"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
							<div class="el-form-item-msg" data-v-b33b3cf8="">用于在生成门锁临时密码时，设置临时密码的有效期</div>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="submitForm">保存</el-button>
						</el-form-item>
					</el-form>
				</el-tab-pane>

				<el-tab-pane label="系统置灰配置">
					<el-header>
						<el-button type="primary" @click="table_add">添加</el-button>
					</el-header>
					<scTable ref="table" :apiObj="apiObj" :params="params" height="calc(100% - 59px)">
						<el-table-column prop="begin_time" label="置灰开始时间"></el-table-column>
						<el-table-column prop="end_time" label="置灰结束时间"></el-table-column>
						<el-table-column prop="reason" label="置灰备注"></el-table-column>
						<el-table-column prop="status" label="状态">
							<template #default="scope">
								<el-tag v-if="scope.row.status == 1" type="success">开启</el-tag>
								<el-tag v-else type="danger">关闭</el-tag>
							</template>
						</el-table-column>

						<el-table-column label="操作" width="200">
							<template #default="scope">
								<el-button-group>
									<el-button text type="primary" size="small" @click="table_edit(scope.row)">编辑</el-button>
								</el-button-group>
								<el-popconfirm title="确定删除该公告吗？" @confirm="table_del(scope.row)">
									<template #reference>
										<el-button text type="danger" size="small">删除</el-button>
									</template>
								</el-popconfirm>
							</template>
						</el-table-column>
					</scTable>
				</el-tab-pane>
			</el-tabs>
		</el-card>
	</el-main>
	<save ref="saveDrawer" @success="saveSuccess"></save>
</template>

<script>
import save from './save.vue'
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId } = cusTom.getBaseQuery()

export default {
	name: 'system',
	components: { save },
	data() {
		return {
			apiObj: this.$API.ash.list,
			tenant_id: 0,
			tenantInfo: {
				mandate: -1,
				mandate_url: '',
				temporaryPwdExpired: 10
			},
			params: {
				tenant_id: tenantId
			}
		}
	},
	created() {
		this.tenant_id = this.$TOOL.data.get('USER_INFO').tenant_id
		this.getTanent()
	},
	methods: {
		getTanent() {
			this.$API.CampusManagement.getTenant
				.get({
					id: this.tenant_id
				})
				.then((res) => {
					if (res.code === 200) {
						this.tenantInfo = res.data
					} else {
						ElMessage({ type: 'error', message: res.message })
					}
				})
		},
		table_add() {
			this.$refs.saveDrawer.open('add')
		},
		submitForm() {
			/*if (this.tenantInfo.mandate === -1) {
				this.tenantInfo.mandate_url = ''
			}*/
			this.$API.CampusManagement.tenantMandate
				.post({
					tenant_id: this.tenant_id,
					mandate: this.tenantInfo.mandate,
					mandate_url: this.tenantInfo.mandate_url,
					temporaryPwdExpired: this.tenantInfo.temporaryPwdExpired
				})
				.then((res) => {
					if (res.code === 200) {
						this.getTanent()
						ElMessage.success('成功')
					} else {
						ElMessage({ type: 'error', message: res.message })
					}
				})
		},
		saveSuccess(data) {
			let params = {
				...data
			}
			this.$API.ash.save.post(params).then((res) => {
				if (res.code === 200) {
					ElMessage.success('成功')
					this.$refs.table.getData(this.params)
				} else {
					ElMessage({ type: 'error', message: res.message })
				}
			})
			console.log(params, 'saveSuccess')
		},
		table_edit(row) {
			this.$refs.saveDrawer.setData(row)
			this.$refs.saveDrawer.open('edit')
		},
		table_del(row) {
			let params = {
				id: row.id
			}
			this.$API.ash.del.post(params).then((res) => {
				if (res.code === 200) {
					ElMessage({ type: 'success', message: '删除成功' })
					this.$refs.table.getData(this.params)
				} else {
					ElMessage({ type: 'error', message: res.message })
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.notice-info {
	padding: 0 10px;

	.notice-title {
		margin-bottom: 15px;
		padding-bottom: 10px;
		border-bottom: 1px solid #eee;

		h3 {
			font-size: 20px;
			font-weight: bold;
			margin-bottom: 10px;
		}

		p {
			font-size: 14px;
			color: #999;
			margin-bottom: 5px;
		}
	}

	.notice-content {
		font-size: 14px;
	}

	.notice-file {
		padding-top: 15px;
		margin-bottom: 5px;
		border-top: 1px solid #eee;

		> p {
			padding-left: 10px;
			margin-bottom: 5px;
		}
	}
}
</style>
