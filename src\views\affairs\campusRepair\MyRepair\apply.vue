<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<cusForm ref="dialogForm" v-model="form" :config="formConfig"> </cusForm>
		<template #footer>
			<el-button v-if="mode === 'edit'" type="warning" @click="del">删除</el-button>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { repairTypeMap } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		type_id: null,
		room_idObj: [],
		room_id: null,
		repair_desc: null,
		repair_img: null,
		config_idObj: null,
		config_id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		treeData: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				type_id: [{ required: true, message: '请选择报修类型', trigger: 'blur' }],
				room_idObj: [{ required: true, message: '请选择位置', trigger: 'blur' }],
				repair_desc: [{ required: true, message: '请输入报修描述', trigger: 'blur' }]
			},

			typeMap: repairTypeMap,
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					// {
					// 	label: '报修类型',
					// 	name: 'type_id',
					// 	value: null,
					// 	component: 'select',
					// 	options: {
					// 		placeholder: '请选择报修类型',
					// 		noClearable: false,
					// 		items: repairTypeMap.map((v) => {
					// 			return {
					// 				label: v.name,
					// 				value: v.value
					// 			}
					// 		})
					// 	},
					// 	rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					// },
					{
						label: '报修配置',
						name: 'config_idObj',
						value: null,
						component: 'cascader',
						options: {
							placeholder: '请选择报修配置',
							noClearable: false,
							all: true
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					},
					{
						label: '位置',
						name: 'room_idObj',
						value: null,
						component: 'cusSelectField',
						options: {},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }]
					},
					{
						label: '报修描述',
						name: 'repair_desc',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入'
						}
					},
					{
						label: '报修图片',
						name: 'repair_img',
						component: 'upload',
						options: {
							items: [
								{
									name: 'repair_img',
									label: '报修图片',
									type: 'repair'
								}
							]
						}
					}
				]
			}
		}
	},
	computed: {},
	mounted() {},
	created() {},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id

			this.formConfig.formItems.find((v) => v.name == 'config_idObj').options.items = this.treeData
			return this
		},
		del(){
			this.$confirm(`确定删除此报修工单吗`, '提示', {
				type: 'warning'
			})
				.then(async () => {
					let res = await this.$API.campusRepair.del.post(this.form)
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
				.catch(() => {})
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					this.form.room_id = this.form.room_idObj[0].id
					this.form.config_id = this.form.config_idObj[this.form.config_idObj.length - 1]
					this.form.type_id = this.form.config_idObj[0]
					var res
					if (this.mode === 'add') {
						res = await this.$API.campusRepair.apply.post(this.form)
					} else {
						res = await this.$API.campusRepair.edit_my.post(this.form)
					}
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			if (data.room) {
				this.form.room_idObj = [{ label: data.room.name, id: data.room.id }]
			}
			if (data.config_parent_id) {
				this.form.config_idObj = [data.type_id, data.config_parent_id, data.config_id]
			} else {
				this.form.config_idObj = [data.type_id, data.config_id]
			}
			console.log(this.form)
		}
	}
}
</script>

<style></style>
