<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" label-width="100px" :model="form" :rules="rules">
			<el-form-item label="规则名称" prop="rule_name">
				<el-input v-model="form.rule_name" placeholder="请输入规则名称" clearable></el-input>
			</el-form-item>
			<el-form-item v-if="mode != 'copy'" label="上课天数" prop="attend_day">
				<ul class="day-list">
					<li v-for="item in dayMaps" :class="{ active: form.attend_day.includes(item) }"
						@click="selectDay(item)">
						{{ item }}
					</li>
				</ul>
			</el-form-item>
			<el-form-item v-if="mode != 'copy'" label="课程时段" prop="periods_id">
				<el-select v-model="form.periods_id" placeholder="请选择课程时段" filterable clearable style="width: 100%">
					<el-option v-for="item in coursePeriodList" :key="item.id" :label="item.periods_name"
						:value="item.id">
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="学年学期" prop="academic_id">
				<cusCascader v-model="form.semester_id" :options="getSemester" @valChange="semesterChange" width="100%"></cusCascader>
			</el-form-item>
			<el-form-item label="关联年级" prop="grade_id">
				<el-select v-model="form.grade_id" clearable placeholder="请选择年级" style="width: 100%;">
					<el-option v-for="item in gradeList" :key="item.id" :label="item.grade_name"
						:value="item.id"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item v-if="mode == 'copy'" label="选择规则" prop="class_rule_id">
				<el-select v-model="form.class_rule_id" style="width: 100%" placeholder="请选择规则" clearable>
					<el-option v-for="item in classRuleList" :key="item.id" :label="item.rule_name"
						:value="item.id"></el-option>
				</el-select>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusSelectInfo from '@/components/custom/cusSelectInfo.vue'
import cusTom from '@/utils/cusTom'

const { semesterInfo } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		tenant_id: '',
		campus_id: '',
		academic_id: null,
		semester_id: null,
		grade_id: null,
		rule_name: '',
		attend_day: [],
		periods_id: '',
		class_rule_id: null
	}
}
export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {
			}
		}
	},
	components: {
		cusSelectInfo
	},
	data() {
		return {
			mode: 'add',
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			coursePeriodList: [],
			titleMap: {
				add: '新增课程规则',
				edit: '编辑课程规则',
				copy: '拷贝课程规则'
			},
			rules: [],
			dayMaps: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'],
			semesterList: [],
			classRuleList: [],
			semesterInfo,
			gradeList: []
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.form.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	watch: {
		'form.semester_id': {
			handler(val) {
				this.getGradeData(val)
			}
		}
	},
	methods: {
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form = {
				...defaultData(),
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			if (mode == 'copy') {
				this.getRulesList()
			} else {
				this.getData()
			}
			return this
		},
		selectDay(item) {
			console.log(this.form.attend_day)
			if (this.form.attend_day.includes(item)) {
				this.form.attend_day = this.form.attend_day.filter((v) => v !== item)
			} else {
				this.form.attend_day.push(item)
			}
		},
		semesterChange(val){
			console.log(val)
			this.form.grade_id = null
		},
		submit() {
			console.log(this.form)
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					this.isSaveing = true
					if (this.mode == 'add') {
						this.form.attend_day = this.form.attend_day.join(',')
						this.$API.eduSchedule.rules.add.post(this.form).then((res) => {
							this.isSaveing = false
							if (res.code == 200) {
								this.$emit('success', this.form, this.mode)
								this.visible = false
								this.$message.success('操作成功')
							} else {
								this.$alert(res.message, '提示', { type: 'error' })
							}
						})
					} else if (this.mode == 'edit') {
						if (Array.isArray(this.form.attend_day)) {
							this.form.attend_day = this.form.attend_day.join(',')
						}
						this.$API.eduSchedule.rules.edit.post(this.form).then((res) => {
							this.isSaveing = false
							if (res.code == 200) {
								this.$emit('success', this.form, this.mode)
								this.visible = false
								this.$message.success('操作成功')
							} else {
								this.$alert(res.message, '提示', { type: 'error' })
							}
						})
					} else {
						this.$API.eduSchedule.rules.copy.post(this.form).then((res) => {
							this.isSaveing = false
							if (res.code == 200) {
								this.$emit('success', this.form, this.mode)
								this.visible = false
								this.$message.success('操作成功')
							} else {
								this.$alert(res.message, '提示', { type: 'error' })
							}
						})
					}
				}
			})
		},
		getData() {
			this.$API.eduSchedule.period.get(this.params).then((res) => {
				this.coursePeriodList = res.data
			})
		},
		getGradeData() {
			let params = {
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				semester_id: this.form.semester_id
			}
			this.$API.eduGradeClass.grade.all.get(params).then(res => {
				this.gradeList = res.data
			})
		},
		getRulesList() {
			let params = {
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			this.$API.eduSchedule.rules.all.get(params).then((res) => {
				if (res.code == 200) {
					this.classRuleList = res.data
				}
			})
		},
		setData(data) {
			if (!Array.isArray(data.attend_day)) {
				data.attend_day = data.attend_day.split(',')
			}
			Object.assign(this.form, data)
		}
	}
}
</script>
<style lang="scss" scoped>
.day-list {
	display: flex;
	flex-wrap: wrap;

	li {
		width: 50px;
		height: 30px;
		line-height: 28px;
		font-size: 10px;
		text-align: center;
		border: 1px solid #ccc;
		border-radius: 5px;
		margin-right: 10px;
		// margin-bottom: 10px;
		cursor: pointer;

		&.active {
			background-color: var(--el-color-primary-light-8);
			border-color: var(--el-color-primary-light-8);
			color: var(--el-color-primary);
		}
	}
}
</style>
