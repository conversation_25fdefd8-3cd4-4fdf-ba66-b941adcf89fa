<template>
	<el-drawer v-model="visible" title="角色权限设置" size="50%" destroy-on-close @closed="$emit('closed')">
		<el-tabs tab-position="top">
			<el-tab-pane label="PC菜单权限">
				<div class="desc">
					权限类型：
					<strong><sc-status-indicator type="success"></sc-status-indicator> 菜单</strong>
					<strong><sc-status-indicator type="warning"></sc-status-indicator> 内嵌网页</strong>
					<strong><sc-status-indicator type="danger"></sc-status-indicator> 外链网页</strong>
					<strong><sc-status-indicator type="info"></sc-status-indicator> 按钮/接口</strong>
				</div>
				<div class="treeMain">
					<el-tree
						ref="menu"
						node-key="id"
						:data="menu.list"
						:props="menu.props"
						show-checkbox
						:default-expand-all="true"
						highlight-current
						:render-after-expand="true"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span class="label">
									<sc-status-indicator v-if="data.meta.type === 'menu'" type="success"></sc-status-indicator>
									<sc-status-indicator v-if="data.meta.type === 'iframe'" type="warning"></sc-status-indicator>
									<sc-status-indicator v-if="data.meta.type === 'link'" type="danger"></sc-status-indicator>
									<sc-status-indicator v-if="data.meta.type === 'button'" type="info"></sc-status-indicator>
									{{ node.label }}
								</span>
							</span>
						</template>
					</el-tree>
				</div>
			</el-tab-pane>
			<el-tab-pane label="手机端权限">
				<el-button type="primary" size="small" @click="showMark = !showMark"
					><span v-if="!showMark">显示</span><span v-if="showMark">关闭</span>菜单标记</el-button
				>
				<div class="treeMain" style="margin-top: 15px">
					<el-tree
						ref="phoneMenu"
						node-key="mark"
						:data="phonePageList"
						show-checkbox
						:default-expand-all="true"
						highlight-current
						:render-after-expand="true"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node">
								<span class="label">
									<el-image
										v-if="data.icon"
										:src="'https://educdn.xjzredu.cn/scms/system/front/h5-teacher' + data.icon"
										style="width: 15px; height: 15px"
									></el-image>
									{{ data.label }} <span v-if="showMark">( {{ data.mark }} )</span>
								</span>
							</span>
						</template>
					</el-tree>
				</div>
			</el-tab-pane>
		</el-tabs>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script>
import phoneMenu from '@/config/phoneMenu'

export default {
	emits: ['success', 'closed'],
	data() {
		return {
			roleId: 0,
			visible: false,
			showMark: false,
			isSaveing: false,
			phonePageList: phoneMenu.teacher,
			phoneMenuChecked: [],
			tenantPhoneMenu: [],
			menu: {
				list: [],
				checked: [],
				props: {
					label: (data) => {
						return data.meta.title
					},
					class: (data, node) => {
						let isPenultimate = true
						for (const key in data.children) {
							if (data.children[key]?.children?.length ?? 0 > 0) {
								isPenultimate = false
								break
							}
						}
						return data.children?.length > 0 && isPenultimate ? `is-penultimate level${node.level}` : ''
					}
				}
			},
			grid: {
				list: [],
				checked: [],
				props: {
					label: (data) => {
						return data.title
					},
					disabled: (data) => {
						return data.isFixed
					}
				}
			},
			data: {
				dataType: '5',
				list: [],
				checked: ['12', '2', '21', '22', '1'],
				props: {},
				rule: ''
			},
			dashboard: '0',
			dashboardOptions: [
				{
					value: '0',
					label: '数据统计',
					views: 'stats'
				},
				{
					value: '1',
					label: '工作台',
					views: 'work'
				}
			]
		}
	},
	mounted() {},
	methods: {
		open(roleId) {
			this.roleId = roleId
			this.getInit()
			this.visible = true
		},
		async submit() {
			this.isSaveing = true
			//选中的和半选的合并后传值接口
			var checkedKeys = this.$refs.menu.getCheckedKeys().concat(this.$refs.menu.getHalfCheckedKeys())
			var checkedPhoneMenuKeys = this.$refs.phoneMenu.getCheckedKeys().concat(this.$refs.phoneMenu.getHalfCheckedKeys())
			//var checkedKeys_dept = this.$refs.dept.getCheckedKeys().concat(this.$refs.dept.getHalfCheckedKeys())

			//var checkedKeys_dashboard = this.$refs.grid.getCheckedKeys().concat(this.$refs.grid.getHalfCheckedKeys())
			var res = await this.$API.role.permissions.post({
				roleId: this.roleId,
				menuChecked: checkedKeys,
				phoneMenuChecked: checkedPhoneMenuKeys
				//dashboardChecked: checkedKeys_dashboard
			})
			if (res.code === 200) {
				this.isSaveing = false
				this.visible = false
				this.$message.success('操作成功')
				this.$emit('success')
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		async getInit() {
			var res = await this.$API.role.permissions.get(this.roleId)
			//var res = await this.$API.system.menu.list.get()
			this.menu.list = res.data.menus
			//获取接口返回的之前选中的和半选的合并，处理过滤掉有叶子节点的key
			this.menu.checked = res.data.menuChecked
			this.phoneMenuChecked = res.data.phoneMenuChecked
			this.tenantPhoneMenu = res.data.tenantPhoneMenu
			this.phonePageList = JSON.parse(JSON.stringify(phoneMenu.teacher))

			this.phonePageList = this.filterMenuByTenant(this.phonePageList)

			this.$nextTick(() => {
				let filterKeys = this.menu.checked.filter((key) => this.$refs.menu.getNode(key).isLeaf)
				this.$refs.menu.setCheckedKeys(filterKeys, true)

				let filterPhoneKeys = this.phoneMenuChecked.filter((key) => this.$refs.phoneMenu.getNode(key).isLeaf)
				this.$refs.phoneMenu.setCheckedKeys(filterPhoneKeys, true)
			})
		},
		// 递归过滤菜单
		filterMenuByTenant(menuItems) {
			return menuItems.filter((item) => {
				if (item.children && item.children.length > 0) {
					item.children = this.filterMenuByTenant(item.children)
					// 如果过滤后没有子项，且当前项不在允许列表中，则过滤掉
					return item.children.length > 0 || this.tenantPhoneMenu.includes(item.mark)
				}
				return this.tenantPhoneMenu.includes(item.mark)
			})
		}
	}
}
</script>

<style scoped lang="scss">
.custom-tree-node {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	padding-right: 8px;
}
:deep(.el-dialog__body) {
	padding: 5px 10px;
}
:deep(.is-penultimate) {
	.el-tree-node__children {
		padding-left: 65px;
		white-space: pre-wrap;
		line-height: 100%;

		.el-tree-node {
			display: inline-block;
		}

		.el-tree-node__content {
			padding-left: 12px !important;
			padding-right: 12px;
			width: 180px;
			.el-tree-node__expand-icon.is-leaf {
				display: none;
			}
		}
	}
	&.level1 {
		.el-tree-node__children {
			padding-left: 36px;
		}
	}
	&.level2 {
		.el-tree-node__children {
			padding-left: 54x;
		}
	}
	&.level3 {
		.el-tree-node__children {
			padding-left: 72px;
		}
	}
	&.level4 {
		.el-tree-node__children {
			padding-left: 90px;
		}
	}
}
.treeMain {
	min-height: 300px;
	overflow: auto;
	border: 1px solid #dcdfe6;
	margin-bottom: 10px;
}

.desc {
	padding-bottom: 10px;
}
.desc strong {
	padding-right: 15px;
	font-weight: unset;
}
</style>
