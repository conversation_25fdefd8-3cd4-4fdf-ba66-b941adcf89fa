<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'">
			<el-form-item label="学期" prop="semester_id">
				<cusSelectSemester
					v-model="form.semester_id"
					:params="form"
					:show-default-value="false"
					:width="'214px'"
					clearable
					style="margin-right: 15px"
				/>
				<!--				<cusCascader v-model="form.semester_id" :options="getSemester"></cusCascader>-->
			</el-form-item>
			<el-form-item label="名称" prop="grade_name">
				<el-input v-model="form.grade_name" placeholder="请输入年级名称" clearable></el-input>
			</el-form-item>
			<!--			<el-form-item label="是否有效" prop="status">
				<el-switch v-model="form.status" :active-value="1" :inactive-value="-1"></el-switch>
			</el-form-item>-->
			<el-form-item label="年级学科">
				<el-select v-model="form.course_ids" multiple collapse-tags clearable>
					<el-option
						v-for="item in allCourseList"
						:key="item.id"
						:label="item.course_name"
						:value="item.id"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item style="margin-left: -90px">
				<cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode"></cusForm>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input v-model="form.remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'
const { semesterInfo } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		semester_id: -1,
		grade_name: '',
		status: 1,
		remark: '',
		campus_id: '',
		tenant_id: '',
		leader_ids: [],
		course_ids: []
		// listorder: 1
	}
}

export default {
	components: { cusSelectSemester },
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			// 所有课程列表
			allCourseList: [],
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: '年级长',
						name: 'leader_ids',
						value: null,
						multiple: true,
						collapseTags: true,
						component: 'cusSelectTeacher',
						rules: [{ required: true, message: '请输入', trigger: 'blur' }],
						hideHandle: '$.mode == "edit" || $.mode == "show"'
					}
				]
			},
			//验证规则
			rules: {
				grade_name: [{ required: true, message: '请输入名称' }],
				semester_id: [{ required: true, message: '请选择学期' }]
			},
			//所需数据选项
			groups: [],
			groupsProps: {
				value: 'id',
				label: 'semester_name',
				emitPath: false
			},
			semesterInfo
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.form.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	mounted() {},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			if (mode === 'add') {
				this.getCourse()
				this.form.campus_id = this.params.campus_id
				this.form.tenant_id = this.params.tenant_id
			}
			if (mode === 'edit') {
				this.getCourse()
			}
			this.visible = true
			return this
		},
		async getCourse() {
			const res = await this.$API.eduCourseSet.course.all.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			})
			if (res.code === 200) {
				this.allCourseList = res.data
			}
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					if (this.mode === 'edit') {
						this.form.leader_ids = this.form.leader_ids?.map((v) => v.id)
					}
					if (this.mode === 'add') {
						this.form.leader_ids = this.form.leader_ids.map((v) => v.id)
					}
					var res = await this.$API.eduGradeClass.grade.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			// this.form.id = data.id
			// this.form.label = data.label
			// this.form.status = data.status
			// this.form.sort = data.sort
			// this.form.parentId = data.parentId
			// this.form.remark = data.remark

			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			// this.allCourseList = data.course_info?.map((v) => {
			// 	return { course_name: v.name, id: v.id }
			// })
			this.form.course_ids = data.course_info?.map((v) => v.id)
			this.form.leader_ids = data.leader_info?.map((v) => {
				return {
					id: v.id,
					label: v.name
				}
			})
			Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
