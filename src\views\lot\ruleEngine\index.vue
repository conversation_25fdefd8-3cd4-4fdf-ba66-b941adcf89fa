<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main class="el-main-hascontainer">
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script setup>
import ruleLog from './ruleLog'
import engine from './engine'
import { ref } from 'vue'
const activeName = ref('ruleLog')
const currComponent = ref({
	name: 'ruleLog',
	component: ruleLog
})
const tabs = [
	{
		name: 'ruleLog',
		label: '规则记录',
		component: ruleLog
	},
	{
		name: 'engine',
		label: '规则管理',
		component: engine
	}
]
const handleClick = (name) => {
	currComponent.value = tabs.find((item) => item.name === name)
}
</script>
<style lang="scss" scoped></style>
