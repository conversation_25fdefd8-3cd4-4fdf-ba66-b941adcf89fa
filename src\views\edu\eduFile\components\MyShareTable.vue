<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-page-header v-if="breadcrumb.length > 1" title="" @back="breadcrumb_back">
						<template #content>
							<el-breadcrumb separator="/">
								<el-breadcrumb-item v-for="item in breadcrumb"
									><b>{{ item.name }}</b></el-breadcrumb-item
								>
							</el-breadcrumb>
						</template>
					</el-page-header>
					<el-breadcrumb v-else separator="/">
						<el-breadcrumb-item v-for="item in breadcrumb"
							><b>{{ item.name }}</b></el-breadcrumb-item
						>
					</el-breadcrumb>
				</div>
			</div>
			<div class="right-panel">
				<el-form-item style="margin-bottom: 0">
					<el-select v-model="params.suffix_type" clearable @change="upsearch">
						<el-option
							v-for="item in fileSuffixType"
							:key="item.value"
							:value="item.value"
							:label="item.name"
						></el-option>
					</el-select>
					<el-button style="margin-left: 10px" type="primary" :disabled="true">上传文件 </el-button>
					<el-button type="success" :disabled="true">新增文件夹</el-button>
				</el-form-item>
			</div>
		</el-header>
		<el-main>
			<scTable
				ref="MySharetableRef"
				:apiObj="apiObj"
				postion
				:params="params"
				@select-all="onSelectAll"
				@select-change="onSelectChange"
			>
				<el-table-column type="selection"></el-table-column>
				<el-table-column label="文件名称" prop="file_name">
					<template #default="{ row }">
						<div style="display: flex">
							<SvgIcon :iconName="suffix_type[row.file_suffix_type]" /> &nbsp;&nbsp;
							<el-link v-if="webPreview(row.file_url)" type="primary" :underline="false" @click="table_preview(row)">{{
								row.file_name
							}}</el-link>
							<el-link
								v-else-if="checkVideo(row.file_url)"
								type="primary"
								:underline="false"
								@click="table_play(row)"
								>{{ row.file_name }}</el-link
							>
							<el-link
								v-else-if="imageSuffix(row.file_url)"
								type="primary"
								:underline="false"
								@click="table_image(row)"
							>
								{{ row.file_name }}
							</el-link>
							<el-link v-else-if="row.file_type === 2" type="primary" :underline="false" @click="dir_preview(row)">{{
								row.file_name
							}}</el-link>
							<span v-else style="font-size: 14px">{{ row.file_name }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="大小" prop="file_size">
					<template #default="{ row }">
						{{ row.size_format }}
					</template>
				</el-table-column>
				<el-table-column label="分享给" prop="share_user" show-overflow-tooltip></el-table-column>
				<el-table-column label="时间" prop="created_at"></el-table-column>
				<el-table-column label="操作" width="230">
					<template #default="scope">
						<el-button-group>
							<el-button type="success" size="small" text @click="table_download(scope.row)">下载</el-button>
							<el-button type="danger" size="small" text @click="table_cancel_share(scope.row)">取消分享</el-button>
							<el-button type="success" size="small" text @click="table_show(scope.row)">查看</el-button>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
			<div class="footer-check-btn">
				<div class="checkout">
					<div>已选 {{ currentCheckedLength }}/{{ checkedTotal }}</div>
				</div>
			</div>
		</el-main>
	</el-container>
	<ShowDialog ref="showDialogRef" :params="params"></ShowDialog>
	<el-dialog v-model="visible" title="视频播放" destroy-on-close>
		<VideoPlay :source="source"></VideoPlay>
	</el-dialog>
	<el-image-viewer
		v-if="isShowImage"
		:url-list="imageUrl"
		:zIndex="1000"
		style="width: 100px; height: 100px"
		@close="onClose"
	></el-image-viewer>
</template>
<script setup>
import cusTom from '@/utils/cusTom'
import { computed, ref, getCurrentInstance, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import ShowDialog from '../showDialog.vue'
import SvgIcon from '@/components/SvgIcon/SvgIcon.vue'
import VideoPlay from '@/components/videoPlay/index.vue'

const { tenantId, campusId, fileSuffixTypeMap } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		pid: null,
		suffix_type: 0
	}
}
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const params = ref(defaultParams())
let MySharetableRef = ref(null)
let showDialogRef = ref(null)
let currentCheckedLength = ref(0)
let currentRow = ref([])
let source = ref('')
let visible = ref(false)
let isShowImage = ref(false)
let imageUrl = ref([])
let checkedTotal = ref(0)
import { webPreview, checkVideo, imageSuffix } from '@/utils/mediaLib'
const suffix_type = reactive({
	0: 'icon-wenjianjia',
	1: 'icon-word',
	2: 'icon-excel',
	3: 'icon-PPT',
	4: 'icon-PDF',
	5: 'icon-tupian1',
	6: 'icon-shipin1',
	7: 'icon-yinle',
	8: 'icon-yasuowenjian',
	99: 'icon-qita1'
})
const apiObj = ref(globalPropValue.fileManagement.file.file_my_share)

const fileSuffixType = computed(() => {
	return [{ name: '全部', value: 0 }, ...fileSuffixTypeMap]
})
const breadcrumb = ref([{ id: -1, root: 1, name: '我的分享' }])
const breadcrumb_back = () => {
	breadcrumb.value.pop()
	const last = breadcrumb.value[breadcrumb.value.length - 1]
	if (last.root === 1) {
		params.value.type_id = last.id
		params.value.pid = null
	} else {
		params.value.pid = last.id
	}
	upsearch()
}
const upsearch = () => {
	MySharetableRef.value?.upData(params.value)
}
// 取消分享事件
const table_cancel_share = (row) => {
	ElMessageBox.confirm('确定取消分享吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		globalPropValue.fileManagement.file.file_share_cancel.post({
			id: row.id,
			tenant_id: tenantId,
			campus_id: campusId
		})
		ElMessage({
			type: 'success',
			message: '操作成功'
		})
		upsearch()
	})
}
// 下载事件
const table_download = (data) => {
	console.log(data)
	let tagA = document.createElement('a')
	tagA.href = data.file_url
	tagA.download = data.file_name || '文件下载'
	tagA.click()
	tagA.remove()
}
// 查看事件
const table_show = (row) => {
	showDialogRef.value.open(row.id)
}
// 表格预览事件
const router = useRouter()
const table_preview = (row) => {
	const routeUrl = router.resolve({
		path: '/filepreview',
		query: {
			tenant_id: params.value.tenant_id,
			campus_id: params.value.campus_id,
			file_id: row.file_id
		}
	})
	window.open(routeUrl.href, '_blank')
}
// 表格视频播放事件
const table_play = (row) => {
	visible.value = true
	source.value = row.file_url
}
// 表格图片预览事件
const table_image = (row) => {
	isShowImage.value = true
	imageUrl.value = [row.file_url]
}
// 点击表格文件夹事件
const dir_preview = (row) => {
	params.value.pid = row.file_id
	breadcrumb.value.push({ id: row.id, root: 0, name: row.file_name })
	MySharetableRef.value.upData(params.value)
}
// 点击x按钮触发事件
const onClose = () => {
	isShowImage.value = false
}
const onSelectChange = (val) => {
	currentCheckedLength.value = val.length
	currentRow.value = val
}
const onSelectAll = (val) => {
	currentCheckedLength.value = val.length
}
const tableTotal = () => {
	setTimeout(() => {
		checkedTotal.value = MySharetableRef.value.total
	}, 100)
}
defineExpose({
	tableTotal
})
</script>
<style scoped lang="scss">
.footer-check-btn {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-top: -40px;
	overflow: hidden;

	.checkout {
		margin-right: 10px;
	}
}
</style>
