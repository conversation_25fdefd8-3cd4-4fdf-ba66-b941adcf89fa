import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/manapi/survey/list`,
		name: '获取问卷列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	one: {
		url: `${config.API_URL}/manapi/survey/one`,
		name: '获取问卷详情',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/manapi/survey/save`,
		name: '新增/保存问卷',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/manapi/survey/del`,
		name: '删除问卷',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	publish: {
		url: `${config.API_URL}/manapi/survey/publish`,
		name: '发布问卷',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	cancel: {
		url: `${config.API_URL}/manapi/survey/cancel`,
		name: '下架问卷',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	setConf: {
		url: `${config.API_URL}/manapi/survey/setConf`,
		name: '保存问卷配置',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	submitList: {
		url: `${config.API_URL}/manapi/survey/submitList`,
		name: '获取问卷提交列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	submitDetail: {
		url: `${config.API_URL}/manapi/survey/submitDetail`,
		name: '获取问卷提交详情',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	}
}
