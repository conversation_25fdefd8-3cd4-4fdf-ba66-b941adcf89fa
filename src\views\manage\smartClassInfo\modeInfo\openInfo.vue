<template>
    <el-drawer v-model="visible" title="开放日信息" size="60%" destroy-on-close :close-on-press-escape="false"
        @close="close">
        <el-row :gutter="10">
            <el-col :span="10">
                <div v-if="openInfo?.schoolProfile">
                    <div class="schoolInfo">
                        <el-button type="danger" @click="delOpenInfo">删除学校信息</el-button>
                    </div>
                    <div class="schoolProfile" v-html="openInfo.schoolProfile"></div>
                </div>
                <div v-else>
                    <el-empty description="暂无学校信息">
                        <el-button type="primary" @click="addOpenInfo">添加学校信息</el-button>
                    </el-empty>
                </div>
            </el-col>
            <el-col :span="1">
                <div class="line-box">
                    <div class="line"></div>
                </div>
            </el-col>
            <el-col :span="13" v-if="openInfo">
                <div class="schoolInfo">
                    <el-button type="primary" icon="el-icon-plus" @click="add">新增关联信息</el-button>
                </div>
                <div v-if="openInfo.isSchoolClassHotspots == 2">
                    <el-table row-key="id" :data="openInfo.platoonSchoolRelatedContentVos">
                        <el-table-column label="封面图" prop="coverUrl">
                            <template #default="{ row }">
                                <cusImage loading="lazy" :lazy="true" fit="contain" style="width: 50px; height: 50px"
                                    :src="row.coverUrl" :preview-src-list="[row.coverUrl]" preview-teleported>
                                </cusImage>
                            </template>
                        </el-table-column>
                        <el-table-column label="内容类型" prop="contentType">
                            <template #default="{ row }">
                                {{ row.contentType == 1 ? '学校宣传内容' : '班级成长照片' }}
                            </template>
                        </el-table-column>
                        <el-table-column label="展示类型" prop="displayType">
                            <template #default="{ row }">
                                {{ row.displayType == 1 ? '图片' : '视频' }}
                            </template>
                        </el-table-column>
                        <el-table-column label="资源地址" prop="hotspotUrl">
                            <template #default="{ row }">
                                <cusImage loading="lazy" :lazy="true" fit="contain" style="width: 50px; height: 50px"
                                    :src="row.hotspotUrl" :preview-src-list="[row.hotspotUrl]" preview-teleported
                                    v-if="row.displayType == 1">
                                </cusImage>
                                <div v-else>
                                    <el-button text type="primary" size="small"
                                        @click="videoPreview(row)">预览视频</el-button>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="100">
                            <template #default="{ row }">
                                <el-button text type="danger" size="small" @click="table_del(row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-col>
        </el-row>
    </el-drawer>
    <el-dialog v-model="videoVisible" destroy-on-close title="视频预览">
        <video height="500px" width="100%" autoplay controls>
            <source :src="currentVideo.hotspotUrl" type="video/mp4">
        </video>
    </el-dialog>
    <addOpen ref="addOpenRef" @addSuccess="getModeInfo" />
    <addSchoolInfo ref="addSchoolInfoRef" @addSuccess="getModeInfo" />
</template>

<script setup>
import addOpen from './addOpen.vue'
import addSchoolInfo from './addSchoolInfo.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const currentSn = ref('')
const openInfo = ref({})

// 添加学校信息
const addSchoolInfoRef = ref(null)
const addOpenInfo = () => {
    addSchoolInfoRef.value.show({ sn: currentSn.value })
}
// 删除学校信息
const delOpenInfo = () => {
    ElMessageBox.confirm('确定删除该学校信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        globalPropValue.platoonhotspot.del_open_mode.post({
            id: openInfo.value.idString,
            tenant_id: tenantId,
            campus_id: campusId
        }).then((res) => {
            if (res.code === 200) {
                ElMessage.success('删除成功')
                getModeInfo()
            }
        })
    })
}
// 视频预览
const videoVisible = ref(false)
const currentVideo = ref({})
const videoPreview = (row) => {
    currentVideo.value = row
    videoVisible.value = true
}

// 新增关联热点信息
const addOpenRef = ref()
const add = () => {
    addOpenRef.value.show({ sn: currentSn.value, idString: openInfo.value.idString })
}
// 删除关联热点信息
const table_del = (row) => {
    ElMessageBox.confirm('确定删除该信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        globalPropValue.platoonhotspot.del_related.post({
            id: row.idString,
            tenant_id: tenantId,
            campus_id: campusId
        }).then((res) => {
            if (res.code === 200) {
                ElMessage.success('删除成功')
                getModeInfo()
            }
        })
    })
}
const getModeInfo = () => {
    globalPropValue.platoonhotspot.get_open_mode.get({
        device_sn: currentSn.value,
        tenant_id: tenantId,
        campus_id: campusId
    }).then((res) => {
        if (res.code === 200) {
            // 处理 schoolProfile 中的 img 标签
            let schoolProfile = res.data.schoolProfile || '';

            // 使用正则表达式匹配没有 width 的 <img> 标签，并注入 width="100%"
            schoolProfile = schoolProfile.replace(/<img(?![^>]*\swidth\s*=)([^>]*)>/gi, '<img width="100%" $1>');

            // 赋值处理后的数据
            openInfo.value = {
                ...res.data,
                schoolProfile: schoolProfile
            };
        }
    }).catch((err) => {
        console.log(`获取开放日信息失败： ${err.message}`)
    })
}
const visible = ref(false)
const show = (sn) => {
    currentSn.value = sn
    getModeInfo()
    visible.value = true
}
const emit = defineEmits(['refresh'])
const close = () => {
    visible.value = false
    emit('refresh')
}
defineExpose({
    show,
    close
})
</script>

<style lang="scss" scoped>
.schoolInfo {
    text-align: right;
    margin-bottom: 15px;
    margin-top: 5px;
}

.schoolProfile {
    max-height: calc(100vh - 150px);
    overflow-y: auto;
    overflow-x: hidden;
    font-size: 14px;
}

.line-box {
    height: 100%;
    display: flex;
    justify-content: center;
}

.line {
    width: 1px;
    height: 100%;
    background: #ccc;
}
</style>