<template>
  <div class="left-cate">{{ title }}</div>
  <Draggable tag="ul" item-key="type" ghostClass="ghost" :group="{ name: 'people', pull: 'clone', put: false }"
    :sort="false" :list="list">
    <template #item="{ element }">
      <li v-if="fields.includes(element.type)" class="form-edit-left-label"
        :class="{ 'no-put': element.tpye === 'divider' }">
        <a>
          <cusSvgIcon :iconClass="element.type" />
          <span>{{ element.label }}</span>
        </a>
      </li>
    </template>
  </Draggable>
</template>

<script>
import { defineComponent } from 'vue'
import Draggable from 'vuedraggable'
export default defineComponent({
  name: 'ComponentGroup',
  components: {
    Draggable
  },
  props: {
    title: {
      type: String,
      required: true
    },
    fields: {
      type: Array,
      required: true
    },
    list: {
      required: true
    }
  }
})
</script>
<style lang="scss" scoped>
$primary-color: #2745B2;
$primary-background-color : #ecf5ff;

.left-cate {
  padding: 8px 12px;
  font-size: 13px;

}

ul {
  position: relative;
  overflow: hidden;
  padding: 0 10px 10px;
  margin: 0;
}

.form-edit-left-label {
  font-size: 12px;
  display: block;
  width: 48%;
  line-height: 26px;
  position: relative;
  float: left;
  left: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 1%;
  border: 1px solid #F4F6FC;

  &:hover {
    border: 1px dashed $primary-color;

    &>a {
      color: $primary-color;
    }
  }

  &>a {
    color: #333;
    display: block;
    cursor: move;
    background: #f4f6fc;
    border: 1px solid #f4f6fc;

    .svg-icon {
      margin-right: 6px;
      margin-left: 8px;
      font-size: 14px;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      display: inline-block;
      vertical-align: middle;
    }

  }
}
</style>