import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/display.css'
import base from './base'
import customui from './customui'
import i18n from './locales'
import router from './router'
import App from './App.vue'
import '@/assets/iconfont/index'
const app = createApp(App)

import pinia from './stores/pinia'
import 'virtual:svg-icons-register'

app.use(pinia)
app.use(router)
app.use(ElementPlus)
app.use(i18n)
app.use(base)
app.use(customui)
console.log(import.meta.env.MODE)
//挂载app
app.mount('#app')
const ElementComponents = app._context.components
// console.log(ElementComponents)
ElementComponents.ElForm.props.labelWidth.default = '90px'
ElementComponents.ElDrawer.props.size.default = '35%'
ElementComponents.ElDialog.props.closeOnClickModal = false // 禁止点击遮罩层关闭弹窗，防止误操作关闭弹窗
ElementComponents.ElDrawer.props.closeOnClickModal = false // 禁止点击遮罩层关闭弹窗，防止误操作关闭弹窗
