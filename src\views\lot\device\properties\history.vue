<template>
	<el-drawer v-model="visible" title="历史数据" size="700" destroy-on-close @closed="$emit('closed')">
		<div class="left-panel-search">
			<el-form-item label="">
				<el-date-picker
					v-model="params.time"
					style="width: 350px; margin-right: 10px"
					type="datetimerange"
					:shortcuts="shortcuts"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					value-format="YYYY-MM-DD HH:mm:ss"
					:disabled-date="disabledDate"
					@change="upsearch"
				/>
				<el-button icon="el-icon-refresh" @click="refresh">刷新</el-button>
			</el-form-item>
		</div>
		<el-tabs v-model="activeName" class="demo-tabs">
			<el-tab-pane
				v-if="propertiesInfo.type_spec.type === 'int' || propertiesInfo.type_spec.type === 'float'"
				label="图表"
				name="chart"
			></el-tab-pane>
			<el-tab-pane label="列表" name="list"></el-tab-pane>
		</el-tabs>
		<div v-show="activeName === 'chart'">
			<scEcharts class="scEcharts" :option="chartOption" width="100%" height="400px"></scEcharts>
		</div>
		<div v-show="activeName === 'list'">
			<el-table
				ref="table"
				v-loading="loading"
				row-key="id"
				empty-text="当前所选日期时间内无数据"
				:data="logData"
				size="small"
			>
				<el-table-column label="属性" prop="name" width="120">
					<template #default="scope">
						{{ propertiesInfo.name }}
					</template>
				</el-table-column>
				<el-table-column label="标识符" prop="code" width="150">
					<template #default="scope">
						{{ propertiesInfo.code }}
					</template>
				</el-table-column>
				<el-table-column label="上报时间" prop="report_time" width="200"></el-table-column>
				<el-table-column
					:label="'原始值 (' + propertiesInfo.type_spec.specs.unit_symbol + ')'"
					prop="value"
				></el-table-column>
			</el-table>
			<div class="page">
				<el-pagination
					v-if="total > 0"
					v-model:current-page="params.page"
					:page-sizes="[10, 20, 30, 50, 100]"
					:page-size="params.pageSize"
					size="small"
					background
					:pager-count="5"
					layout="total,sizes, prev, pager, next"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</div>
		</div>
	</el-drawer>
</template>

<script>
import cusTom from '@/utils/cusTom'
import dayjs from 'dayjs'
import scEcharts from '@/components/scEcharts/index.vue'

export default {
	name: 'logList',
	data() {
		return {
			currentPage1: 1,
			visible: false,
			loading: false,
			activeName: 'chart',
			total: 0,
			logData: [],
			enumConfig: {},
			propertiesInfo: {},
			params: {
				tenant_id: null,
				campus_id: null,
				device_id: null,
				tsl_id: null,
				time: [],
				begin_time: null,
				end_time: null,
				page: 1,
				pageSize: 10
			},
			chartOption: {
				color: ['#165DFF'],
				title: {
					text: '',
					subtext: ''
				},
				grid: {
					top: '60px',
					bottom: '50px'
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: [],
					boundaryGap: [0, '100%']
				},
				dataZoom: [
					{
						type: 'slider',
						start: 0,
						end: 100,
						height: 24
					}
				],
				yAxis: {
					type: 'value',
					boundaryGap: [0, '100%']
				},
				series: [
					{
						data: [],
						type: 'line',
						smooth: true,
						name: '设备属性',
						showSymbol: false,
						lineStyle: {
							color: '#165DFF' //线条颜色
						},
						areaStyle: {
							// 使用方法二的写法
							color: {
								type: 'linear',
								x: 0, //右
								y: 0, //下
								x2: 0, //左
								y2: 1, //上
								colorStops: [
									{
										offset: 0,
										color: '#4080FF' // 0% 处的颜色
									},
									{
										offset: 1,
										color: '#E8F3FF' // 100% 处的颜色
									}
								]
							}
						}
					}
				]
			},

			shortcuts: [
				{
					text: '最近1小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 1)
						return [start, end]
					}
				},
				{
					text: '最近3小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 3)
						return [start, end]
					}
				},
				{
					text: '最近6小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 6)
						return [start, end]
					}
				},
				{
					text: '最近12小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 12)
						return [start, end]
					}
				},
				{
					text: '最近24小时',
					value: () => {
						const end = new Date()
						const start = new Date()
						start.setHours(start.getHours() - 24)
						return [start, end]
					}
				}
			]
		}
	},
	components: { scEcharts },
	watch: {},
	created() {
		this.params.time = [dayjs().add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	computed: {},
	methods: {
		handleClick(item) {
			console.log(item)
		},
		show(data) {
			console.log(data)

			this.params.device_id = data.device_id
			this.params.tenant_id = data.tenant_id
			this.params.campus_id = data.campus_id
			this.params.tsl_id = data.propertiesInfo.tsl_id
			this.propertiesInfo = data.propertiesInfo
			if (data.propertiesInfo.type_spec.type === 'int' || data.propertiesInfo.type_spec.type === 'float') {
				this.activeName = 'chart'
			} else {
				this.activeName = 'list'
			}
			this.visible = true

			this.chartOption.title.text = this.propertiesInfo.name + '(' + this.propertiesInfo.code + ')'
			this.chartOption.series[0].name = this.propertiesInfo.name + '(' + this.propertiesInfo.code + ')'
			this.chartOption.title.subtext = this.propertiesInfo.type_spec.specs.unit_symbol
				? '单位：' + (this.propertiesInfo.type_spec.specs.unit_symbol + '')
				: ''

			this.getList()
			this.getChartData()
		},
		disabledDate(time) {
			const oneMonthLater = new Date()
			oneMonthLater.setMonth(oneMonthLater.getMonth() - 1) // 获取一个月前的日期
			// 返回true表示禁用, false表示不禁用
			// 禁用早于今天的日期和晚于一个月后的日期
			return time.getTime() > Date.now() || time.getTime() < oneMonthLater
		},
		handleSizeChange(page_size) {
			this.params.pageSize = page_size
			this.getList()
		},
		handleCurrentChange(page) {
			this.params.page = page
			this.getList()
		},
		//搜索
		upsearch() {
			this.params.page = 1
			this.getList()
			this.getChartData()
		},
		async getChartData() {
			this.loading = true
			if (this.params.time) {
				this.params.begin_time = this.params.time[0]
				this.params.end_time = this.params.time[1]
			} else {
				this.params.begin_time = null
				this.params.end_time = null
			}
			const res = await this.$LotApi.device.getDevicePropertiesChart.get(this.params)
			this.loading = false
			if (res.code === 200) {
				let categoryData = []
				let seriesData = []
				res.data.map((item, index) => {
					categoryData.push(item.report_time.substring(0, 16))
					seriesData.push(item.value)
				})
				categoryData.reverse()
				seriesData.reverse()
				this.chartOption.xAxis.data = categoryData
				this.chartOption.series[0].data = seriesData
				this.chartOption.series[0].name = this.propertiesInfo.name + '(' + this.propertiesInfo.code + ')'
			}
		},
		async getList() {
			this.loading = true
			if (this.params.time) {
				this.params.begin_time = this.params.time[0]
				this.params.end_time = this.params.time[1]
			} else {
				this.params.begin_time = null
				this.params.end_time = null
			}
			const res = await this.$LotApi.device.getDevicePropertiesLog.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.logData = res.data.rows
				this.total = res.data.total
			}
		},
		refresh() {
			this.params.page = 1
			this.params.time = [dayjs().add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
			this.getList()
			this.getChartData()
		}
	}
}
</script>

<style lang="scss" scoped>
.left-panel-search {
	padding-bottom: 15px;
	margin-top: 10px;
}

.page {
	padding: 20px;
	display: flex;
	justify-content: flex-end;
}

.payload {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
</style>
