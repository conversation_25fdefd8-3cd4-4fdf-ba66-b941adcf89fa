<template>
	<el-dialog
		v-model="visible"
		:title="titleMap[mode] + student"
		:width="800"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode">
			<template #ahead v-if="mode !== 'edit'">
				<el-form-item label="学期" prop="semester_id" label-width="95px">
					<cusCascader v-model="form.semester_id" placeholder="请选择学期" :options="getSemester">
					</cusCascader>
				</el-form-item>
			</template>
			<template #customItem>
				<div>
					<el-form-item label="添加学员">
						<el-button type="primary" @click="studentList.push({})">添加</el-button>
					</el-form-item>

					<el-form-item label="学员">
						<div v-for="item in studentList" class="addStu">
							<cusSelectStudent v-model="item.student_id"></cusSelectStudent>
							<el-input v-model="item.position" placeholder="请输入职位"></el-input>
							<div style="display: flex">
								<el-input v-model="item.remark" autosize type="textarea" placeholder="请输入备注"></el-input>
								<el-button
									type="danger"
									style="margin-left: 10px"
									@click="studentList.splice(studentList.indexOf(item), 1)"
									>删除</el-button
								>
							</div>
						</div>
					</el-form-item>
				</div>
			</template>
		</cusForm>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { semesterInfo } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		tenant_id: null,
		campus_id: null,
		student_info: [],
		mode: null,
		class_id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			studentList:[],
			form: defaultData(),
			//验证规则
			rules: {
				class_name: [{ required: true, message: '请输入名称' }],
				semester_id: [{ required: true, message: '请选择学期' }],
				grade_id: [{ required: true, message: '请选择年级' }]
			},
			//所需数据选项
			groups: [],
			groupsProps: {
				value: 'id',
				label: 'semester_name',
				emitPath: false
			},
			gradeOptions: [],
			semesterInfo,
			grade: [],
			formConfig: {
				labelPosition: 'right',
				size: 'medium',

				formItems: [
					{
						label: '班级',
						name: 'class_id',
						value: null,
						component: 'cascader',
						options: {
							prop: {
								emitPath: false,
								value: 'id',
								label: 'name',

								children: 'class_list'
							},
							items: []
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }],
						hideHandle: '$.mode == "edit" || $.mode == "show"'
					}
				]
			}
		}
	},
	mounted() {},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.form.campus_id),
				'value',
				'parent_id'
			)
		},
		student() {
			if (this.form.student_name) {
				return '-' + this.form.student_name
			} else {
				return ''
			}
		}
	},
	watch: {
		'form.semester_id': {
			deep: true,
			handler(val) {
				this.getClass(val)
			}
		}
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.form.mode = mode
			if (mode === 'add') {
				this.form.campus_id = this.params.campus_id
				this.form.tenant_id = this.params.tenant_id
				this.studentList = [
					{
						student_id: [],
						position: null,
						remark: null
					}
				]
			}
			this.visible = true
			return this
		},
		async getClass(val) {
			const res = await this.$API.eduGradeClass.class.all.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				semester_id: val
			})
			this.formConfig.formItems[0].options.items = res.data /*.map((v) => {
				return {
					value: v.id,
					label: v.name,
					children: v.class_list
				}
			})*/
		},
		//表单提交方法
		submit() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					this.form.student_info = this.studentList.map((v) => {
						return {
							student_id: v.student_id[0].id,
							position: v.position,
							remark: v.remark
						}
					})
					console.log(this.form.student_info)
					var res = await this.$API.eduGradeClass.classStu.add.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			// this.form.id = data.id
			// this.form.label = data.label
			// this.form.status = data.status
			// this.form.sort = data.sort
			// this.form.parentId = data.parentId
			// this.form.remark = data.remark

			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
			this.form.student_id = data.user_id
		}
	}
}
</script>

<style scoped>
.addStu {
	display: grid;
	grid-template-columns: 130px 130px 1fr;
	gap: 10px;
	margin-bottom: 10px;
	max-height: 300px;
	overflow: auto;
}
</style>
