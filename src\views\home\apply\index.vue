<template>
	<el-container>
		<el-aside width="250px">
			<el-container>
				<el-header v-if="CampusManagementList.length > 1">
					<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-header>
				<el-main>
					<ul class="menu">
						<li
							v-for="(item, index) in groups"
							:key="index"
							:class="{ active: item.id === menuId }"
							@click="groupClick(item.id, true)"
						>
							{{ item.name }}
						</li>
					</ul>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header v-if="menuId !== 1">
				<div class="hearder-panel">
					<div class="search-panel">
						<el-select
							v-if="menuId === 2"
							v-model="params.status"
							placeholder="请选择状态"
							style="width: 240px; margin-right: 15px"
							clearable
							@change="statusChange"
							@clear="statusClear"
						>
							<el-option v-for="item in myStatusData" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
						<el-select
							v-else-if="menuId !== 3"
							v-model="params.status"
							placeholder="请选择状态"
							style="width: 240px; margin-right: 15px"
							clearable
							@change="statusChange"
							@clear="statusClear"
						>
							<el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
						<el-select
							v-model="params.mode"
							placeholder="请选择审批类型"
							style="width: 240px"
							clearable
							@change="modeChange"
							@clear="modeClear"
						>
							<el-option v-for="item in modeData" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</div>
				</div>
				<div class="right-panel"></div>
			</el-header>
			<el-main class="outer-main">
				<div v-if="menuId === 1" class="apply-list">
					<div v-for="(item, index) in applyList" :key="item.mode">
						<h2>{{ item.mode_name }}</h2>
						<ul>
							<li v-for="items in item.approval" :key="items.id" class="apply-list-item">
								<p>{{ items.approval_name }}</p>
								<div class="apply-list-item-btn" @click="applyClick(items)">申请</div>
							</li>
						</ul>
					</div>
				</div>
				<scTable v-else ref="table" row-key="id" :params="params" :apiObj="apiObj">
					<el-table-column v-for="column in tableColumn" :key="column.prop" :prop="column.prop" :label="column.label">
						<template v-if="column.prop === 'action'" #default="scope">
							<el-text v-if="scope.row.action === 0">-</el-text>
							<el-text v-if="scope.row.action === 1">通过</el-text>
							<el-text v-if="scope.row.action === 2">驳回</el-text>
							<el-text v-if="scope.row.action === 3">撤销</el-text>
							<el-text v-if="scope.row.action === 4">抄送</el-text>
							<el-text v-if="scope.row.action === 5">评论</el-text>
						</template>
						<template v-if="column.prop === 'action_status'" #default="scope">
							<el-text v-if="scope.row.action_status === 1">待审批</el-text>
							<el-text v-if="scope.row.action_status === 2">已操作</el-text>
						</template>
						<template v-if="column.prop === 'status'" #default="scope">
							<el-tag v-if="scope.row.status === 1" type="primary">审批中</el-tag>
							<el-tag v-if="scope.row.status === 2" type="success">已通过</el-tag>
							<el-tag v-if="scope.row.status === 3" type="danger">已驳回</el-tag>
							<el-tag v-if="scope.row.status === 4" type="warning">已撤销</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="操作" fixed="right" align="center" width="180">
						<template #default="scope">
							<el-button-group>
								<el-button text type="primary" size="small" @click="table_info(scope.row, scope.$index)"
									>查看</el-button
								>
								<el-button
									v-if="scope.row.action_status === 1"
									text
									type="success"
									size="small"
									@click="table_pass(scope.row, scope.$index)"
									>通过</el-button
								>
								<el-button
									v-if="scope.row.action_status === 1"
									text
									type="danger"
									size="small"
									@click="table_reject(scope.row, scope.$index)"
									>驳回</el-button
								>
							</el-button-group>
							<el-popconfirm
								v-if="menuId === 2 && scope.row.status === 1"
								title="确定撤销吗？"
								@confirm="table_cancel(scope.row, scope.$index)"
							>
								<template #reference>
									<el-button text type="danger" size="small">撤销</el-button>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
		<!-- 审批/驳回 -->
		<actionDialog ref="actionForm" @success="handleActionSuccess"></actionDialog>
		<!-- 查看审批详情 -->
		<viewDialog ref="viewD"></viewDialog>
		<!-- 申请审批 -->
		<applyDialog ref="applyForm" @success="handleApplySuccess"></applyDialog>
	</el-container>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import cusTom from '@/utils/cusTom'
import actionDialog from './actionDialog.vue'
import viewDialog from './viewDialog.vue'
import applyDialog from './applyDialog.vue'
const { campusId, tenantId, campusInfo, approvalModelMap } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		status: null,
		mode: null
	}
}
export default {
	name: '',
	components: {
		actionDialog,
		viewDialog,
		applyDialog
	},
	data() {
		return {
			params: defaultParams(),
			apiObj: {},
			menuId: 1,
			applyList: [],
			statusMap: [],
			CampusManagementList: campusInfo,
			groups: [
				{ id: 1, name: '流程申请' },
				{ id: 2, name: '我的申请' },
				{ id: 3, name: '抄送给我' },
				{ id: 4, name: '我的审批' }
			],
			statusData: [
				{ value: 0, label: '全部' },
				{ value: 1, label: '待审批' },
				{ value: 2, label: '已审批' }
			],
			myStatusData: [
				{ value: 0, label: '全部' },
				{ value: 1, label: '审批中' },
				{ value: 2, label: '已通过' },
				{ value: 3, label: '已驳回' },
				{ value: 4, label: '已撤销' }
			],
			applyId: '',
			applyData: {}
		}
	},
	computed: {
		tableColumn() {
			if (this.menuId === 0) {
				return [
					{ label: '申请人', prop: 'apply_user_name' },
					{ label: '审批流程', prop: 'approval_name' },
					{ label: '所属类型', prop: 'mode_name' },
					{ label: '申请时间', prop: 'created_at' },
					{ label: '状态', prop: 'status' }
				]
			} else if (this.menuId === 2) {
				return [
					{ label: '审批流程', prop: 'approval_name' },
					{ label: '所属类型', prop: 'mode_name' },
					{ label: '申请时间', prop: 'created_at' },
					{ label: '审批状态', prop: 'status' }
				]
			} else if (this.menuId === 3) {
				return [
					{ label: '申请人', prop: 'apply_user_name' },
					{ label: '审批流程', prop: 'approval_name' },
					{ label: '所属类型', prop: 'mode_name' },
					{ label: '申请时间', prop: 'apply_time' }
				]
			} else if (this.menuId === 4) {
				return [
					{ label: '申请人', prop: 'apply_user_name' },
					{ label: '审批流程', prop: 'approval_name' },
					{ label: '所属类型', prop: 'mode_name' },
					{ label: '申请时间', prop: 'apply_time' },
					{ label: '操作类型', prop: 'action' },
					{ label: '操作状态', prop: 'action_status' }
				]
			}
		},
		modeData() {
			if (approvalModelMap) {
				let data = approvalModelMap.map((item) => {
					item.label = item.name
					return item
				})
				return data
			}
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.getAllApply()
			},
			immediate: true
		}
	},
	created() {
		console.log(this.$API.approval.flow.list)
	},
	mounted() {},
	methods: {
		statusChange(val) {
			this.params.status = val
			this.groupClick(this.menuId, false)
			console.log(this.params, 'statusChange')
		},
		statusClear() {
			this.params.status = null
			this.groupClick(this.menuId, false)
		},
		modeChange(val) {
			this.params.mode = val
			this.groupClick(this.menuId, false)
			console.log(this.params, 'modeChange')
		},
		modeClear() {
			this.params.mode = null
			this.groupClick(this.menuId, false)
		},
		groupClick(data, isMenu) {
			if (isMenu) {
				this.params = defaultParams()
			} else {
				this.$refs.table.upData(this.params)
			}
			this.menuId = data
			switch (data) {
				case 0:
					this.apiObj = this.$API.approval.flow.list
					break
				case 1:
					this.getAllApply()
					break
				case 2:
					this.apiObj = this.$API.approval.flow.myApply
					break
				case 3:
					this.apiObj = this.$API.approval.flow.copyMyApproval
					break
				case 4:
					this.apiObj = this.$API.approval.flow.myApproval
			}
		},
		// 查看
		table_info(row, index) {
			this.$refs.viewD.open(row.apply_id || row.id)
			console.log(row, index, '查看')
		},
		// 通过
		table_pass(row, index) {
			this.applyId = row.apply_id
			this.$refs.actionForm.open(1, row.apply_id, row.step)
			console.log(row, index, '通过')
		},
		// 驳回
		table_reject(row, index) {
			this.applyId = row.apply_id
			this.$refs.actionForm.open(2, row.apply_id, row.step)
			console.log(row, index, '驳回')
		},
		async handleActionSuccess(data) {
			let params = {
				...data,
				apply_id: this.applyId,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			}
			const { code, message } = await this.$API.approval.flow.action.post(params)
			this.showMessage(code, message)
			console.log(data, 'handleActionSuccess')
		},
		async handleApplySuccess(data) {
			let params = {
				apply_content: JSON.stringify({ ...data.form }),
				apply_info: JSON.stringify([...data.formInfo]),
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				...this.applyData
			}
			const { code, message } = await this.$API.approval.flow.apply.post(params)
			this.showMessage(code, message)
		},
		// 撤销
		async table_cancel(row, index) {
			const { code, message } = await this.$API.approval.flow.cancel.post({ ...this.params, id: row.id })
			this.showMessage(code, message)
		},
		showMessage(code, message) {
			if (code === 200) {
				ElMessage({ type: 'success', message: message })
				if (this.menuId !== 1) {
					this.groupClick(this.menuId, false)
				}
			} else {
				ElMessage({ type: 'error', message: message })
			}
		},
		// 发起审批
		applyClick(data) {
			this.applyData = {
				mode: data.mode,
				approval_id: data.id
			}
			this.$refs.applyForm.open(data)
			console.log(data, '发起审批')
		},
		async getAllApply() {
			const { data } = await this.$API.approval.flow.all.get({ ...this.params, scope: 1 })
			this.applyList = data
		}
	}
}
</script>

<style lang="scss" scoped>
.menu {
	li {
		height: 36px;
		line-height: 36px;
		text-align: center;
		cursor: pointer;
		color: #606266;
		font-size: 14px;
		margin-bottom: 10px;
		&:nth-child(3) {
			margin-bottom: 21px;
		}

		&:last-child {
			position: relative;

			&::before {
				content: '';
				display: block;
				width: 230px;
				height: 1px;
				background-color: var(--el-border-color-light);
				position: absolute;
				top: -10px;
				left: 0;
			}
		}

		&:hover {
			background-color: var(--el-menu-hover-bg-color);
		}

		&.active {
			background-color: var(--el-color-primary-light-8);
		}
	}
}

.outer-main {
	.apply-list {
		padding: 10px;

		h2 {
			padding-bottom: 15px;
		}

		ul {
			display: flex;
			flex-wrap: wrap;

			.apply-list-item {
				width: 20%;
				height: 150px;
				border: 1px solid var(--el-border-color-light);
				margin-right: 20px;
				margin-bottom: 20px;
				border-radius: 5px;
				text-align: center;

				&:hover {
					box-shadow: 0 0 9px 2px var(--el-border-color-light);
				}

				p {
					font-size: 20px;
					line-height: 100px;
				}

				.apply-list-item-btn {
					width: 100%;
					font-size: 14px;
					line-height: 45px;
					border-top: 1px solid var(--el-border-color-light);
					background-color: var(--el-bg-color-overlay);
					border-radius: 0 0 5px 5px;
					cursor: pointer;

					&:hover {
						color: var(--el-menu-active-color);
					}
				}
			}
		}
	}
}
</style>
