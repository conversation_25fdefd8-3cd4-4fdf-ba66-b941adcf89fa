<template>
	<div id="file"></div>
</template>

<script setup>
import { reactive, onMounted, getCurrentInstance } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const WebofficeToken = reactive({
	webInfo: {}
})
const props = defineProps({
	aliplayerSdkPath: {
		type: String,
		default: 'https://g.alicdn.com/IMM/office-js/1.1.19/aliyun-web-office-sdk.min.js'
	}
})
onMounted(async () => {
	await getToken()
	await insertScriptTag()
	setTimeout(async function () {
		await preview()
	}, 200)
})
const insertScriptTag = async () => {
	let playerScriptTag = document.getElementById('playerScriptTag')
	// 如果这个tag不存在，则生成相关代码tag以加载代码
	if (playerScriptTag === null) {
		playerScriptTag = document.createElement('script')
		playerScriptTag.type = 'text/javascript'
		playerScriptTag.src = props.aliplayerSdkPath
		playerScriptTag.id = 'playerScriptTag'
		const s = document.getElementsByTagName('head')[0]
		s.appendChild(playerScriptTag)
	}
}
// 获取文件预览凭证
const getToken = async () => {
	const res = await globalPropValue.fileManagement.file.weboffice_preview.post({
		tenant_id: Number(route.query.tenant_id),
		campus_id: Number(route.query.campus_id),
		file_id: Number(route.query.file_id)
	})
	console.log(res)
	if (res.code === 200) {
		WebofficeToken.webInfo = res.data
	}
}
const preview = async () => {
	let mount = document.querySelector('#affairFile')
	let instance = window.aliyun.config({
		mount: mount,
		url: WebofficeToken.webInfo.weboffice_url,
		mode: 'normal'
	})
	instance.setToken({ token: WebofficeToken.webInfo.access_token, timeout: 25 * 60 * 1000 })
	instance.on('fileOpen', function (data) {
		console.log(data)
	})
	instance.on('error', (err) => {
		console.log('发生错误：', err)
	})
}
</script>

<style scoped lang="scss">
#file {
	width: 100%;
	height: 100%;
}
</style>
