<template>
	<el-dialog v-model="visible" :width="500" destroy-on-close @closed="$emit('closed')">
		<template #header="{ close, titleId, titleClass }">
			<div class="my-header">
				<h4 :id="titleId" :class="titleClass">流转记录</h4>
			</div>
		</template>
		<div style="max-height: 600px; overflow: auto; padding: 10px; box-sizing: border-box">
			<el-timeline style="max-width: 600px">
				<el-timeline-item :color="'#0bbd87'" v-for="item in logList">
					<div class="logItem">
						<div class="logItem__left">
							<div>{{ formData(assetslogMap, item.log_type) }}</div>
							<div>{{ item.created_at }}</div>
						</div>
						<div class="logItem__right">操作人：{{ item.action_user_name }}</div>
					</div>
					<div class="logContent">
						<template v-if="item.log_type == 1">
							<p>位置：{{ item.room.name }}</p>
							<!-- <p>金额：{{  }}</p> -->
							<p>入库备注：{{ item.log_remark }}</p>
						</template>
						<template v-if="item.log_type == 2">
							<p>领用人：{{ item.log_user_name }}</p>
							<p>位置：{{ item.room.name }}</p>
							<p>领用备注：{{ item.log_remark }}</p>
						</template>
						<template v-if="item.log_type == 3">
							<p>报废人：{{ item.log_user_name }}</p>
							<p>位置：{{ item.room.name }}</p>
							<p>报废备注：{{ item.log_remark }}</p>
						</template>
						<template v-if="item.log_type == 4">
							<p>归还人：{{ item.log_user_name }}</p>
							<p>位置：{{ item.room.name }}</p>
							<p>归还备注：{{ item.log_remark }}</p>
						</template>
						<template v-if="item.log_type == 5">
							<p>返库人：{{ item.log_user_name }}</p>
							<p>位置：{{ item.room.name }}</p>
							<p>返库备注：{{ item.log_remark }}</p>
						</template>
					</div>
				</el-timeline-item>
			</el-timeline>
		</div>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { assetslogMap } = cusTom.getBaseQuery()
export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		groupData: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			mode: 'add',

			visible: false,
			isSaveing: false,
			logList: [],
			assets_id: null,
			assetslogMap
		}
	},
	mounted() {},
	created() {},
	computed: {},
	watch: {},
	methods: {
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},

		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			return this
		},
		async getLog() {
			var res = await this.$API.assets.rooms.log.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				assets_id: this.assets_id
			})
			this.logList = res.data
		},
		//表单注入数据
		setData(data) {
			this.assets_id = data.id
			this.getLog()
		}
	}
}
</script>

<style lang="scss" scoped>
.logItem {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
	&__left {
		& > div {
			&:nth-child(2) {
				font-size: 13px;
				color: #999;
			}
		}
	}
}
.logContent {
	padding: 10px;
	background-color: #f6f8fa;
	border-radius: 3px;
	line-height: 20px;
}
</style>
