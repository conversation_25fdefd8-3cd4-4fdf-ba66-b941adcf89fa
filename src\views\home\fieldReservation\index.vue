<template>
	<el-container>
		<el-aside width="250px">
			<el-container>
				<el-main>
					<ul class="menu">
						<li
							v-for="(item, index) in menuList"
							:key="index"
							:class="{ active: item.id === currentId }"
							@click="menuClick(item)"
						>
							{{ item.name }}
						</li>
					</ul>
				</el-main>
			</el-container>
		</el-aside>
		<el-container v-if="currentId === 2">
			<el-header>
				<div class="left-panel">
					<el-select
						v-if="CampusManagementList.length > 1"
						v-model="params.campus_id"
						placeholder="校区"
						filterable
						style="margin-right: 10px; width: 180px"
						@change="getOpenRoom"
					>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
					<el-date-picker
						v-model="params.date"
						type="date"
						placeholder="请选择日期"
						size="default"
						:disabled-date="dateOptions.disabledDate"
						value-format="YYYY-MM-DD"
						clearable
						style="width: 150px; margin-right: 10px; flex-grow: unset"
						@change="dateChange"
					/>
					<el-cascader
						:options="groupData"
						:props="{ label: 'name', value: 'id', children: 'floor' }"
						placeholder="请选择建筑楼层"
						clearable
						style="margin-right: 10px; width: 150px"
						@change="louChange"
					/>
					<el-select
						v-model="params.room_capacity"
						placeholder="请选择容纳人数"
						clearable
						style="margin-right: 10px; width: 150px"
						@change="roomCapacityChange"
					>
						<el-option
							v-for="item in roomCapacityMap"
							:key="item.value"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
					<el-select
						v-model="params.room_type"
						placeholder="请选择房间类型"
						clearable
						style="margin-right: 10px; width: 150px"
						@change="roomTypeChange"
					>
						<el-option v-for="item in roomTypeMap" :key="item.value" :label="item.name" :value="item.value"></el-option>
					</el-select>
					<el-input
						v-model="params.room_name"
						placeholder="请输入房间名称"
						clearable
						style="margin-right: 10px; width: 150px; flex-grow: unset"
						@change="roomNameChange"
					/>
					<el-button type="primary" icon="el-icon-search" @click="getOpenRoom">刷新</el-button>
				</div>
			</el-header>
			<el-main>
				<div class="room">
					<div v-if="openRoom && openRoom.length > 0" class="room-list">
						<el-card v-for="(item, index) in openRoom" :key="index" shadow="hover" style="margin-bottom: 15px">
							<div class="room-item">
								<div class="room-item-top">
									<div class="room-item-left">
										<cusImage
											loading="lazy"
											:lazy="true"
											fit="cover"
											style="width: 150px; height: 150px; margin-right: 15px; border-radius: 6px; cursor: pointer"
											:src="item.room_info.room_img"
											@click="room_info(item.room_info)"
										></cusImage>
										<div class="room-item-name">
											<h3 style="cursor: pointer" @click="room_info(item.room_info)">
												{{ item.room_info.building_name + item.room_info.floor_name + ' ' + item.room_info.room_name }}
											</h3>
											<p>容纳人数：{{ formData(roomCapacityMap, item.room_info.room_capacity) }}</p>
											<p v-if="item.room_info.open_type === 1">
												可预约时间：{{ item.room_info.allow_start + '~' + item.room_info.allow_end }}
											</p>
											<p v-if="item.room_info.open_type === 2">按课节时段预约： {{ item.room_info.periods?.name }}</p>
											<div>
												<el-tag v-for="i in item.room_info.tags_ary"> {{ i }}</el-tag>
											</div>
										</div>
									</div>
									<div class="room-item-right">
										<el-button round @click="room_info(item.room_info)">查看详情</el-button>
										<el-button type="primary" round @click="room_reservation(item.room_info)">立即预约</el-button>
									</div>
								</div>
								<div v-if="item.room_info.open_type === 1" class="room-item-bottom">
									<div class="legend">
										<div v-for="i in legendList" :key="i.id" class="legend-item"><span></span>{{ i.name }}</div>
									</div>
									<ul style="display: flex">
										<li
											v-for="items in timeFragment"
											:key="items.id"
											class="time-item"
											:class="{
												active: item.selectTimeIds && item.selectTimeIds.includes(items.id),
												noClick: item.room_info.notAllowIds.includes(items.id)
											}"
										>
											<span>{{ items.id % 2 == 0 ? items.start : '' }}</span>
											<span class="end-time">{{ items.id == timeFragment.length - 1 ? items.end : '' }}</span>
										</li>
									</ul>
								</div>
								<div v-if="item.room_info.open_type === 2" class="room-item-bottom">
									<div class="legend">
										<div v-for="i in legendList" :key="i.id" class="legend-item"><span></span>{{ i.name }}</div>
									</div>
									<div style="display: flex">
										<div
											v-for="(periodsItem, periodsIndex) in item.room_info.periods_list"
											:key="periodsIndex"
											class="periods_item"
											style="margin-bottom: 10px"
											:class="{
												active: item.selectTimeIds && item.selectTimeIds.includes(periodsItem.id),
												noClick: item.room_info.disabled_periods_list.includes(periodsItem.id)
											}"
										>
											<p>{{ periodsItem.item_name }}</p>
											<p>{{ periodsItem.begin_time }}-{{ periodsItem.end_time }}</p>
										</div>
									</div>
								</div>
							</div>
						</el-card>
					</div>
					<div v-else>
						<el-empty description="暂无数据"></el-empty>
					</div>
					<div v-if="openRoom && openRoom.length > 0" class="room-pages">
						<el-pagination
							layout="total,prev, pager, next"
							:total="total"
							background
							small
							:page-size="params.pageSize"
							:current-page="params.page"
							@current-change="handleCurrentChange"
							@prev-click="handlePrevClick"
							@next-click="handleNextClick"
						/>
					</div>
				</div>
			</el-main>
		</el-container>
		<myRecord v-else></myRecord>
		<save ref="saveDialog" @success="saveSuccess"></save>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
import myRecord from './myRecord.vue'
import save from './save.vue'
const { campusId, tenantId, campusInfo, roomTypeMap, roomCapacityMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		pageSize: 20,
		page: 1,
		date: null,
		room_name: '',
		building_id: null,
		floor_id: null,
		room_type: null,
		room_capacity: null
	}
}

export default {
	name: 'fieldReservation',
	components: {
		myRecord,
		save
	},
	data() {
		return {
			params: defaultParams(),
			CampusManagementList: campusInfo,
			dateOptions: {
				disabledDate(time) {
					return time.getTime() < Date.now() - 8.64e7 // 只能选择今天及今天之后的日期
				}
			},
			roomNameFilters: [],
			roomTypeMap,
			roomCapacityMap,
			currentId: 2,
			menuList: [
				{
					name: '我要预约',
					id: 2
				},
				{
					name: '我的预约',
					id: 1
				}
			],
			legendList: [
				{
					name: '已被预约时段',
					id: 1
				},
				{
					name: '可预约时段',
					id: 2
				},
				{
					name: '不可预约时段',
					id: 3
				}
			],
			groupData: [],
			total: 0,
			openRoom: [],
			selectRoomId: '',
			timeFragment: [],
			selectTimeIds: [],
			notAllowIds: [],
			roomIndex: null
		}
	},
	watch: {
		'params.campus_id': {
			handler(val) {}
		}
	},
	created() {
		this.getOpenRoom()
		this.getLou()
	},
	methods: {
		resetTime(data, start, end) {
			const startId = data.find((item) => item.end === start)?.id
			const endId = data.find((item) => item.start === end)?.id
			// 如果找到了目标 id，则获取所有 id 小于目标 id 的元素
			let startIds = data.filter((item) => item.id <= startId).map((item) => item.id)
			let endIds = data.filter((item) => item.id >= endId).map((item) => item.id)
			this.notAllowIds = startIds.concat(endIds)
			return this.notAllowIds
			console.log(this.notAllowIds, 'result')
		},
		// 获取当前日期
		getDate() {
			const today = new Date()
			const year = today.getFullYear()
			const month = String(today.getMonth() + 1).padStart(2, '0') // 月份从0开始，所以需要 +1
			const day = String(today.getDate()).padStart(2, '0')
			return `${year}-${month}-${day}`
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		menuClick(data) {
			this.currentId = data.id
		},
		louChange(val) {
			console.log(val, 'louChange')
			if (val) {
				this.params.building_id = val[0] || null
				this.params.floor_id = val[1] || null
			} else {
				this.params.building_id = null
				this.params.floor_id = null
			}
			this.getOpenRoom()
		},
		roomCapacityChange(val) {
			if (val) {
				this.params.room_capacity = val
			} else {
				this.params.room_capacity = null
			}
			this.getOpenRoom()
		},
		dateChange(val) {
			if (val) {
				this.params.date = val
			} else {
				this.params.date = null
			}
			this.getOpenRoom()
		},
		roomTypeChange(val) {
			if (val) {
				this.params.room_type = val
			} else {
				this.params.room_type = null
			}
			this.getOpenRoom()
		},
		roomNameChange(val) {
			if (val) {
				this.params.room_name = val
			} else {
				this.params.room_name = null
			}
			this.getOpenRoom()
		},
		room_info(data) {
			this.$router.push({
				name: 'roomInfo',
				query: {
					id: data.id
				}
			})
		},
		room_reservation(data) {
			console.log(data, 'room_info')
			this.selectRoomId = data.id
			this.$refs.saveDialog.open(data)
		},
		// 预约表单回调
		async saveSuccess(formVal) {
			console.log(formVal, 'saveSuccess')
			let formData = {
				id: formVal.id,
				tenant_id: tenantId,
				campus_id: campusId,
				title: formVal.title,
				room_id: this.selectRoomId,
				date: formVal.date,
				periods_item_id: formVal.periods_item_id,
				begin_time: formVal.date + ' ' + formVal.startTime,
				end_time: formVal.date + ' ' + formVal.endTime,
				teachers: formVal.teachers?.length ? formVal.teachers.map((item) => item.id).join(',') : '',
				students: formVal.students?.length ? formVal.students.map((item) => item.id).join(',') : '',
				remark: formVal.remark
			}
			const { code, message } = await this.$API.fieldReservation.add.post(formData)
			if (code === 200) {
				ElMessage.success(message)
			} else {
				ElMessage.error(message)
			}
		},
		handleCurrentChange(val) {
			this.params.page = val
			this.getOpenRoom()
		},
		handlePrevClick(val) {
			this.params.page = val
			this.getOpenRoom()
		},
		handleNextClick(val) {
			this.params.page = val
			this.getOpenRoom()
		},
		async getOpenRoom() {
			if (!this.params.date) this.params.date = this.getDate()
			const res = await this.$API.fieldReservation.list.get({ ...this.params })
			if (res.data.rows === null) {
				this.openRoom = []
				return
			}
			this.timeFragment = this.timeSlots()
			this.openRoom = res.data.rows.map((item) => {
				item.room_info.notAllowIds = this.resetTime(
					this.timeFragment,
					item.room_info.allow_start,
					item.room_info.allow_end
				)
				if (item.booking_list && item.booking_list.length > 0) {
					let idss = []
					if (item.room_info.open_type === 1) {
						item.booking_list.forEach((items) => {
							// 使用 this.sToe 函数获取指定时间段内的 ID，并添加到 idss 中
							idss = idss.concat(this.sToe(items.begin_time, items.end_time))
							item.selectTimeIds = idss
						})
					} else {
						item.booking_list.forEach((items) => {
							// 使用 this.sToe 函数获取指定时间段内的 ID，并添加到 idss 中
							idss = idss.concat(items.periods_item_id)
							item.selectTimeIds = idss
						})
					}
				}
				return item
			})
			this.total = res.data.total
			// this.resetTime(this.timeFragment)
			console.log(this.openRoom, 'getOpenRoom')
		},
		sToe(start, end) {
			let dateb = start.slice(0, -3).split(' ')
			let datee = end.slice(0, -3).split(' ')
			let ids = this.timeFragment.filter((item) => item.start == dateb[1] || item.end == datee[1])
			return this.completeTimeIds(ids.map((item) => item.id))
		},
		completeTimeIds(ids) {
			const minId = Math.min(...ids)
			const maxId = Math.max(...ids)
			// 创建补全后的数组
			const completeIds = []
			for (let i = minId; i <= maxId; i++) {
				completeIds.push(i)
			}
			return completeIds
		},
		//获取楼层
		async getLou() {
			const res = await this.$API.fieldRoom.all.get({ ...this.params, building_type: null })
			if (!res.data) res.data = []
			let data1 = res.data.map((v) => {
				return {
					...v
				}
			})
			this.groupData = data1
			console.log(this.groupData, 'getLou')
		},
		timeSlots() {
			const timeSlots = Array.from({ length: 48 }, (_, i) => {
				const hour = Math.floor(i / 2)
				const minute = (i % 2) * 30
				return {
					id: i,
					start: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
					end: `${(hour + (minute === 30 ? 1 : 0)).toString().padStart(2, '0')}:${minute === 30 ? '00' : '30'}`
				}
			})
			return timeSlots
		}
	}
}
</script>

<style scoped lang="scss">
.menu {
	li {
		height: 36px;
		line-height: 36px;
		text-align: center;
		cursor: pointer;
		color: #606266;
		font-size: 14px;

		&:first-child {
			margin-bottom: 21px;
		}

		&:last-child {
			position: relative;

			&::before {
				content: '';
				display: block;
				width: 230px;
				height: 1px;
				background-color: var(--el-border-color-light);
				position: absolute;
				top: -10px;
				left: 0;
			}
		}

		&:hover {
			background-color: var(--el-menu-hover-bg-color);
		}

		&.active {
			background-color: var(--el-color-primary-light-8);
		}
	}
}

.room {
	height: 100%;

	.room-list {
		height: calc(100% - 50px);
		overflow: auto;
	}

	.room-pages {
		height: 50px;
		width: 100%;
		line-height: 50px;
		padding: 0 15px;
	}
}

.room-item {
	.room-item-top {
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		margin-bottom: 15px;

		.room-item-left {
			display: flex;

			// align-items: flex-end;
			.room-item-name {
				display: flex;
				flex-direction: column;
				justify-content: space-around;
			}
		}

		.room-item-right {
		}
	}

	.room-item-bottom {
		margin-bottom: 20px;

		.legend {
			width: 100%;
			display: inline-flex;
			justify-content: center;
			margin-bottom: 15px;

			.legend-item {
				display: flex;
				align-items: center;
				margin-right: 12px;

				span {
					width: 15px;
					height: 15px;
					display: inline-block;
					margin-right: 8px;
					// background-color: #f1f1f1;
					border: 1px dashed #ddd;
				}

				&:first-child {
					span {
						background-color: var(--el-color-primary-light-9);
					}
				}
				&:last-child {
					span {
						background-color: var(--el-color-info-light-8);
					}
				}
			}
		}

		li {
			// background-color: #f1f1f1;
			// width: 25px;
			flex-grow: 1;
			height: 70px;
			margin-right: 2px;
			border: 1px dashed #ddd;
			position: relative;

			span {
				position: absolute;
				bottom: -16px;
				left: -16px;
			}

			.end-time {
				left: 10px;
			}
		}

		.noClick {
			pointer-events: none;
			background-color: var(--el-color-info-light-8);
		}

		.active {
			background-color: var(--el-color-primary-light-9);
		}
	}
}
.periods_item {
	width: 100%;
	padding: 5px 0px;
	line-height: 18px;
	text-align: center;
	border: 1px dashed #ddd;
	margin-right: 5px;
	border-radius: 4px;
}
</style>
