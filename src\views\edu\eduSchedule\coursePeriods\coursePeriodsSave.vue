<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" label-width="110px" :disabled="mode == 'show'" :rules="rules">
			<el-form-item label="课程时段名称" prop="item_name">
				<el-input v-model="form.item_name" placeholder="请输入课程名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="开始时间" prop="begin_time">
				<el-time-picker v-model="form.begin_time" format="HH:mm" value-format="HH:mm" placeholder="请选择开始时间" />
			</el-form-item>
			<el-form-item label="结束时间" prop="end_time">
				<el-time-picker v-model="form.end_time" format="HH:mm" value-format="HH:mm" placeholder="请选择结束时间" />
			</el-form-item>
			<el-form-item label="时段属性" prop="type">
				<el-radio-group v-model="form.type">
					<el-radio :label="1">课程</el-radio>
					<el-radio :label="-1">非课程</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
const defaultData = () => {
	return {
		tenant_id: '',
		campus_id: '',
		item_name: '',
		begin_time: '',
		end_time: '',
		type: 1
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',

			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			titleMap: {
				add: '新增时段',
				edit: '编辑时段'
			},
			//验证规则
			rules: {
				item_name: [
					{ required: true, message: '请输入时段名称', trigger: 'blur' },
					{ min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
				],
				begin_time: [
					{ required: true, message: '请选择开始时间', trigger: 'change' },
					{ validator: this.validateBeginTime, trigger: 'change' }
				],
				end_time: [
					{ required: true, message: '请选择结束时间', trigger: 'change' },
					{ validator: this.validateEndTime, trigger: 'change' }
				]
			}
			//所需数据选项
		}
	},
	methods: {
		validateBeginTime(rule, value, callback) {
			if (value === '') {
				callback(new Error('请选择开始时间'))
			} else if (this.form.end_time && value >= this.form.end_time) {
				callback(new Error('开始时间不能晚于或等于结束时间'))
			} else {
				callback()
			}
		},
		validateEndTime(rule, value, callback) {
			if (value === '') {
				callback(new Error('请选择结束时间'))
			} else if (this.form.begin_time && value <= this.form.begin_time) {
				callback(new Error('结束时间不能早于或等于开始时间'))
			} else {
				callback()
			}
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.campus_id = this.params.campus_id
			this.form.tenant_id = this.params.tenant_id
			this.form.periods_id = this.params.periods_id
			this.form.item_name = ''
			this.form.begin_time = ''
			this.form.end_time = ''
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					if (this.mode == 'add') {
						var res = await this.$API.eduSchedule.coursePeriodAdd.post(this.form)
						this.isSaveing = false
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode)
							this.visible = false
							this.$message.success('操作成功')
						} else {
							this.$alert(res.message, '提示', { type: 'error' })
						}
					} else {
						console.log(this.form, 'formData=====')
						var res = await this.$API.eduSchedule.coursePeriodEdit.post(this.form)
						this.isSaveing = false
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode)
							this.visible = false
							this.$message.success('操作成功')
						} else {
							this.$alert(res.message, '提示', { type: 'error' })
						}
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			// if (data.periods_name) {
			//     this.form.periods_name = [{ value: data.periods_name, key: Date.now() }]
			// }
			console.log(this.form)
		}
	}
}
</script>

<style></style>
