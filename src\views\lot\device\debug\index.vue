<template>
	<div v-if="deviceInfo" style="padding: 0 10px">
		<div>
			<!--			<h2>基础操作</h2>-->
			<div class="actions">
				<template v-if="deviceInfo.product_info.node_type === 1">
					<el-button size="small" type="primary" plain @click="getAppVer">获取网关版本</el-button>
					<el-button size="small" type="primary" plain @click="queryZBNwkInfo">查询ZigBee网络</el-button>
					<el-button size="small" type="warning" plain @click="action('reboot')">网关重启</el-button>
					<el-button
						v-if="deviceInfo.zgb_net_work !== 1"
						size="small"
						type="danger"
						plain
						@click="action('creatNetwork')"
						>创建ZigBee网络
					</el-button>
					<!--<el-button type="danger" plain @click="action('unbind')">解绑网关</el-button>
					<el-button type="danger" plain @click="action('reset')">网关重置（恢复出厂设置）</el-button>-->
				</template>
				<template v-if="deviceInfo.product_info.node_type === 2">
					<el-tooltip placement="top">
						<template #content> 若是发现⽹关判断⼦设备的在离线不准确，<br />可以使⽤这个校准⼦设备⼼跳周期</template>
						<el-button size="small" type="warning" plain @click="changeDevHeart">校准设备心跳周期</el-button>
					</el-tooltip>
					<!--		<el-button>设置心跳周期</el-button>
						<el-button type="danger" plain>从网关删除当前设备</el-button>-->
				</template>
			</div>
		</div>

		<div>
			<!--<h2>物模型操作</h2>-->
			<el-tabs v-model="activeName" class="demo-tabs">
				<el-tab-pane label="属性获取" name="readDevProperty"></el-tab-pane>
				<el-tab-pane label="属性设置" name="writeDevProperty"></el-tab-pane>
				<el-tab-pane label="服务调用" name="invokeDevCommand"></el-tab-pane>
				<el-tab-pane label="网络命令" name="getSubDevMAC"></el-tab-pane>
			</el-tabs>
			<div class="debug-main">
				<div v-if="activeName === 'readDevProperty'" class="debug-left">
					<div class="debug-left-content">
						<div v-if="readTslList.length > 0" style="padding: 20px">
							<el-checkbox
								v-model="readTslCheckAll"
								style="width: 100%; margin-bottom: 10px"
								:indeterminate="readTslIndeterminate"
								@change="handleCheckAllChange"
							>
								全选
							</el-checkbox>
							<el-checkbox-group v-model="readTslChecked" @change="handleCheckedChange">
								<el-checkbox
									v-for="(item, index) in readTslList"
									:key="index"
									style="width: 100%; margin-bottom: 15px"
									:label="item.name"
									:value="item.id"
									border
									size="large"
								>
									{{ item.name }} ({{ item.code }})
								</el-checkbox>
							</el-checkbox-group>
						</div>
						<el-empty v-else description="暂无属性"></el-empty>
					</div>
					<div v-if="readTslList.length > 0" class="debug-left-action">
						<el-button plain @click="resetChecked">重置</el-button>
						<el-button type="primary" plain @click="actionConfirm">执行</el-button>
					</div>
				</div>
				<div v-if="activeName === 'writeDevProperty'" class="debug-left">
					<div class="debug-left-content">
						<div v-if="writerTslList.length > 0" style="padding: 20px">
							<el-checkbox
								v-model="writerTslCheckAll"
								style="width: 100%; margin-bottom: 10px"
								:indeterminate="writerTslIndeterminate"
								@change="handleCheckAllChange"
							>
								全选
							</el-checkbox>
							<el-checkbox-group v-model="writerTslChecked" @change="handleCheckedChange">
								<div v-for="(item, index) in writerTslList" :key="index" class="tslItem">
									<el-checkbox style="width: 100%" :label="item.name" :value="item.id">
										{{ item.name }}<span style="font-size: 12px; color: var(--el-color-info)">{{ item.code }}</span>
									</el-checkbox>
									<el-input
										v-if="item.type_spec.type === 'text'"
										v-model="item.value"
										:maxlength="item.type_spec.specs.len"
										placeholder="请输入内容"
									/>
									<el-input-number
										v-if="item.type_spec.type === 'int'"
										v-model="item.value"
										:step="item.type_spec.specs.step"
										:max="item.type_spec.specs.max"
										:min="item.type_spec.specs.min"
										:step-strictly="true"
										:precision="0"
										:controls="false"
										style="width: 90%"
										type="number"
										:placeholder="item.type_spec.specs.min + ' - ' + item.type_spec.specs.max"
									>
										<template v-if="item.type_spec.specs.unit_name" #suffix>
											{{ item.type_spec.specs.unit_name }} / {{ item.type_spec.specs.unit_symbol }}
										</template>
									</el-input-number>
									<el-input-number
										v-if="item.type_spec.type === 'float'"
										v-model="item.value"
										:step="item.type_spec.specs.step"
										:max="item.type_spec.specs.max"
										:min="item.type_spec.specs.min"
										style="width: 90%"
										:step-strictly="true"
										type="number"
										:precision="2"
										:controls="false"
										:placeholder="item.type_spec.specs.min + ' - ' + item.type_spec.specs.max"
									>
										<template v-if="item.type_spec.specs.unit_name" #suffix>
											{{ item.type_spec.specs.unit_name }} / {{ item.type_spec.specs.unit_symbol }}
										</template>
									</el-input-number>
									<el-select
										v-if="item.type_spec.type === 'enum'"
										v-model="item.value"
										style="width: 90%; max-width: unset"
									>
										<el-option
											v-for="(item, index) in item.type_spec.specs"
											:key="index"
											:label="item.name"
											:value="item.value"
										>
											{{ item.name }}
										</el-option>
									</el-select>
									<el-radio-group v-if="item.type_spec.type === 'bool'" v-model="item.value">
										<el-radio-button v-for="(item, index) in item.type_spec.specs" :value="index"
											>{{ item }}
										</el-radio-button>
									</el-radio-group>
								</div>
							</el-checkbox-group>
						</div>
						<el-empty v-else description="暂无属性"></el-empty>
					</div>
					<div v-if="writerTslList.length > 0" class="debug-left-action">
						<el-button plain @click="resetChecked">重置</el-button>
						<el-button type="primary" plain @click="actionConfirm">执行</el-button>
					</div>
				</div>
				<div v-if="activeName === 'invokeDevCommand'" class="debug-left">
					<div class="debug-left-content">
						<div v-if="serviceTslList.length > 0" style="padding: 20px">
							<el-form-item label="选择服务" label-position="top">
								<el-select
									v-model="selectServiceId"
									clearable
									placeholder="请选择服务"
									filterable
									style="width: 100%; max-width: unset"
									@change="selectService"
								>
									<el-option v-for="(item, index) in serviceTslList" :key="index" :label="item.name" :value="item.id">
										{{ item.name }} ({{ item.code }})
									</el-option>
								</el-select>
							</el-form-item>
							<div v-if="selectServiceTsl && selectServiceTsl.input_params" style="margin-top: 15px">
								<div v-for="(item, index) in selectServiceTsl.input_params" :key="index" class="tslItem">
									<el-form-item label-position="top" :label="item.name + '(' + item.code + ')'">
										<el-input
											v-if="item.type_spec.type === 'text'"
											v-model="item.value"
											:maxlength="item.type_spec.specs.len"
											placeholder="请输入内容"
										/>
										<el-input
											v-if="item.type_spec.type === 'int'"
											v-model="item.value"
											:step="item.type_spec.specs.step"
											:max="item.type_spec.specs.max"
											:min="item.type_spec.specs.min"
											:step-strictly="true"
											:precision="0"
											:controls="false"
											type="number"
											:placeholder="item.type_spec.specs.min + ' - ' + item.type_spec.specs.max"
										>
											<template v-if="item.type_spec.specs.unit_name" #append>
												{{ item.type_spec.specs.unit_name }} / {{ item.type_spec.specs.unit_symbol }}
											</template>
										</el-input>
										<el-input
											v-if="item.type_spec.type === 'float'"
											v-model="item.value"
											:step="item.type_spec.specs.step"
											:max="item.type_spec.specs.max"
											:min="item.type_spec.specs.min"
											:step-strictly="true"
											type="number"
											:precision="2"
											:controls="false"
											:placeholder="item.type_spec.specs.min + ' - ' + item.type_spec.specs.max"
										>
											<template v-if="item.type_spec.specs.unit_name" #append>
												{{ item.type_spec.specs.unit_name }} / {{ item.type_spec.specs.unit_symbol }}
											</template>
										</el-input>
										<el-select
											v-if="item.type_spec.type === 'enum'"
											v-model="item.value"
											style="width: 90%; max-width: unset"
										>
											<el-option
												v-for="(item, index) in item.type_spec.specs"
												:key="index"
												:label="item.name"
												:value="item.value"
											>
												{{ item.name }}
											</el-option>
										</el-select>
										<el-radio-group v-if="item.type_spec.type === 'bool'" v-model="item.value">
											<el-radio-button v-for="(item, index) in item.type_spec.specs" :value="index"
												>{{ item }}
											</el-radio-button>
										</el-radio-group>
									</el-form-item>
								</div>
							</div>
						</div>
						<el-empty v-else description="暂无服务"></el-empty>
					</div>
					<div v-if="serviceTslList.length > 0" class="debug-left-action">
						<el-button plain @click="resetChecked">重置</el-button>
						<el-button type="primary" plain @click="actionConfirm">执行</el-button>
					</div>
				</div>
				<div v-if="activeName === 'getSubDevMAC'" class="debug-left">
					<div style="padding: 15px">
						<el-button type="primary" plain @click="permitAllJoin">允许所有子设备加入ZigBee网络</el-button>
					</div>
				</div>
				<div class="debug-right">
					<div class="debug-right-header">
						<h3>调用日志</h3>
						<el-button type="danger" size="small" plain @click="clearLog">清除日志</el-button>
					</div>
					<div class="debug-right-content">
						<div v-for="(item, index) in callLog">
							<div class="logItem">
								{{ item }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<el-dialog v-model="dialogAction" :title="actionTitle" width="500" center :before-close="closeAction">
		<p v-if="countdown > 0" class="countdown">
			<span>{{ countdown }}</span>
		</p>
		<p v-else style="font-size: 48px; text-align: center; color: #f53f3f">
			<el-icon>
				<el-icon-warning />
			</el-icon>
		</p>

		<div class="action_content" v-html="actionContent"></div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="closeAction">取消</el-button>
				<el-button type="danger" :disabled="countdown > 0" @click="actionConfirm">确认</el-button>
			</div>
		</template>
	</el-dialog>

	<el-dialog v-model="devHeartdialogAction" title="校准设备心跳周期" width="500">
		<div>
			<el-form-item label="请输入心跳周期(秒)" label-width="150">
				<el-input v-model="devHeartTime" type="number" placeholder="请输入心跳周期" />
			</el-form-item>
			<div
				style="
					color: var(--el-color-warning);
					font-size: 12px;
					line-height: 24px;
					padding-top: 10px;
					padding-left: 20px;
				"
			>
				<p>当设备超过这个周期时⻓后，将上报设备离线通知；</p>
				<p style="color: var(--el-color-error)">
					<b>使用情况：</b
					>若是发现⽹关判断⼦设备的在离线不准确，可能是⽹关内部判断⼦设备的⼼跳周期和实际设备的⼼跳周期不完全匹配，此时可以使⽤这个校准⼦设备⼼跳周期的参数来修改⽹关判断⼦设备在离线的⼼跳周期。
				</p>
			</div>
		</div>
		<div style="padding-top: 20px; text-align: right">
			<el-button type="" @click="devHeartdialogAction = false">取消</el-button>
			<el-button type="primary" @click="devHeartdialogActionConfirm">确认</el-button>
		</div>
	</el-dialog>
</template>
<script>
import dayjs from 'dayjs'
import { ElLoading, ElMessageBox, ElMessage } from 'element-plus'

export default {
	name: 'debugIndex',
	props: {
		deviceInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			activeName: 'readDevProperty',
			dialogAction: false,
			countdown: 5,
			timer: null,
			actionType: null,
			actionTitle: null,
			actionContent: null,
			writerTslChecked: [],
			readTslChecked: [],
			serviceTslChecked: [],
			writerTslCheckAll: false,
			readTslCheckAll: false,
			serviceTslCheckAll: false,
			writerTslIndeterminate: false,
			readTslIndeterminate: false,
			serviceTslIndeterminate: false,
			devHeartdialogAction: false,
			devHeartTime: 0,
			tslList: [],
			writerTslList: [],
			readTslList: [],
			serviceTslList: [],
			callLog: [],
			callTimer: null,
			num: 0,
			selectServiceTsl: null,
			selectServiceId: null,
			time: null,
			max_id: 0
		}
	},
	created() {
		clearInterval(this.timer)
		clearInterval(this.callTimer)
		this.timer = null
		this.callTimer = null
		this.getTslList()
	},
	unmounted() {
		clearInterval(this.timer)
		clearInterval(this.callTimer)
		this.timer = null
		this.callTimer = null
	},
	methods: {
		changeDevHeart() {
			this.devHeartTime = this.deviceInfo.heartbeat_cycle
			this.devHeartdialogAction = true
		},
		devHeartdialogActionConfirm() {
			//deviceHeart
			var reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id,
				heart_time: parseInt(this.devHeartTime)
			}
			this.$LotApi.device.deviceHeart.post(reqData).then((res) => {
				if (res.code === 200) {
					ElMessage.success('设置成功！')
					this.$emit('refresh')
					this.devHeartdialogAction = false
				}
			})
		},
		selectService(val) {
			this.selectServiceTsl = null
			this.selectServiceId = val
			this.serviceTslList.forEach((item) => {
				if (item.id === val) {
					this.selectServiceTsl = item
				}
			})
			if (this.selectServiceTsl) {
				this.selectServiceTsl.input_params.map((item) => {
					if (typeof item.type_spec.specs === 'string') {
						item.type_spec.specs = JSON.parse(item.type_spec.specs)
					}
				})
			}
		},
		handleCheckAllChange(val) {
			if (val) {
				switch (this.activeName) {
					case 'writeDevProperty':
						this.writerTslChecked = this.writerTslList.map((item) => item.id)
						break
					case 'readDevProperty':
						this.readTslChecked = this.readTslList.map((item) => item.id)
						break
					case 'invokeDevCommand':
						this.serviceTslChecked = this.serviceTslList.map((item) => item.id)
				}
				// this.checked = this.readTslList.map((item) => item.id)
			} else {
				this.writerTslChecked = []
				this.readTslChecked = []
				this.serviceTslChecked = []
			}
			this.readTslIndeterminate = false
			this.writerTslIndeterminate = false
			this.serviceTslIndeterminate = false
		},
		handleCheckedChange(value) {
			const checkedCount = value.length
			// this.checkAll = checkedCount === this.readTslList.length
			this.readTslCheckAll = checkedCount === this.readTslList.length
			this.writerTslCheckAll = checkedCount === this.writerTslList.length
			this.serviceTslCheckAll = checkedCount === this.serviceTslList.length
			// this.indeterminate = checkedCount > 0 && checkedCount < this.readTslList.length
			this.readTslIndeterminate = checkedCount > 0 && checkedCount < this.readTslList.length
			this.writerTslIndeterminate = checkedCount > 0 && checkedCount < this.writerTslList.length
			this.serviceTslIndeterminate = checkedCount > 0 && checkedCount < this.serviceTslList.length
		},
		renderTsl(list) {
			if (this.deviceInfo.channel !== '') {
				let channelMap = this.deviceInfo.channel.split(',')
				if (channelMap.length > 0) {
					let panelCode = []
					list.map((item) => {
						let codeAry = item.code.split('_')
						if (channelMap.includes(codeAry[1])) {
							panelCode.push(item)
						}
					})
					list = panelCode
				}
			}
			return list
		},
		getTslList() {
			var reqData = {
				product_id: this.deviceInfo.product_id,
				tenant_id: this.deviceInfo.tenant_id
			}
			this.$LotApi.productTsl.list.get(reqData).then((res) => {
				if (res.code === 200) {
					this.tslList = res.data
					let readTslList = []
					let serviceTslList = []
					let writerTslList = []
					res.data.forEach((item) => {
						if (item.type_spec.specs) {
							item.type_spec.specs = JSON.parse(item.type_spec.specs)
						}
						if (item.tsl_type === 1 && (item.access_mode === 1 || item.access_mode === 2)) {
							readTslList.push(item)
						}
						if (
							item.tsl_type === 1 &&
							item.type_spec.type !== 'array' &&
							item.type_spec.type !== 'struct' &&
							(item.access_mode === 2 || item.access_mode === 3)
						) {
							writerTslList.push(item)
						}
						if (item.tsl_type === 3 && item.type_spec.type !== 'array' && item.type_spec.type !== 'struct') {
							serviceTslList.push(item)
						}
					})

					if (this.deviceInfo.product_info?.panel_type === 1) {
						readTslList = this.renderTsl(readTslList)
						serviceTslList = this.renderTsl(serviceTslList)
						writerTslList = this.renderTsl(writerTslList)
					}

					this.readTslList = readTslList
					this.serviceTslList = serviceTslList
					this.writerTslList = writerTslList
					console.log(this.writerTslList)
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		closeAction() {
			clearInterval(this.timer)
			this.timer = null
			this.$nextTick(() => {
				this.dialogAction = false
			})
		},
		action(type) {
			//倒计时
			clearInterval(this.timer)
			this.timer = null
			this.countdown = 5
			switch (type) {
				case 'reboot':
					this.actionTitle = '网格重启'
					this.actionContent = '网关重启过程中，所连接的子设备会断时间暂时断开连接，<br>是否确认重启？'
					break
				case 'unbind':
					this.actionTitle = '解绑网关'
					this.actionContent = '网关解绑后，将断开连接，需要重新配置网络，<br>所连接的子设备也会断开，是否确认解绑？'
					break
				case 'reset':
					this.actionTitle = '网关重置'
					this.actionContent =
						'网关恢复出厂设置后，将断开连接，<br>需要重新配置Zigbee网络并注册网关，<br>所连接的子设备也会断开，需要重新入网注册<br>是否确认解绑？'
					break
				case 'creatNetwork':
					this.actionTitle = '创建ZigBee网络'
					if (this.deviceInfo.zgb_net_work === 1) {
						this.actionContent = '当前网关存在ZigBee网络，重新创建后,子设备将需要重新入网注册，<br>是否确认重新创建？'
					} else {
						this.countdown = 0
						this.actionContent = '当前网关不存在ZigBee网络，是否确认创建？'
					}
					break
				default:
					return
			}
			this.actionType = type
			this.dialogAction = true
			if (this.countdown > 0) {
				this.timer = setInterval(() => {
					this.countdown--
					if (this.countdown <= 0) {
						clearInterval(this.timer)
						this.timer = null
					}
				}, 1000)
			}
		},
		actionConfirm() {
			//操作
			if (this.deviceInfo.product_info.node_type == 1) {
				if (this.countdown > 0 || this.actionType === null) {
					return
				}
				clearInterval(this.timer)
				this.timer = null
				switch (this.actionType) {
					case 'reboot':
						this.reboot()
						break
					case 'unbind':
						this.unbind()
						break
					case 'creatNetwork':
						this.creatNetwork()
						break
					case 'reset':
						this.reset()
						break
				}
			} else {
				this.time = dayjs().format('YYYY-MM-DD HH:mm:ss')
				switch (this.activeName) {
					case 'writeDevProperty':
						this.setDeviceAttr()
						break
					case 'readDevProperty':
						this.getDeviceAttr()
						break
					case 'invokeDevCommand':
						this.callDeviceService()
				}
			}
		},
		// 重置
		resetChecked() {
			switch (this.activeName) {
				case 'writeDevProperty':
					this.writerTslChecked = []
					this.writerTslList.map((item) => {
						if (item.value) {
							item.value = ''
						}
						return item
					})
					this.writerTslIndeterminate = true
					break
				case 'readDevProperty':
					this.readTslChecked = []
					this.readTslIndeterminate = true
					break
				case 'invokeDevCommand':
					this.serviceTslChecked = []
					this.serviceTslIndeterminate = true
			}
		},
		creatNetwork() {
			var reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id
			}
			this.$LotApi.device.gatewayDefaultFormZBNwk.post(reqData).then((res) => {
				if (res.code === 200) {
					this.closeAction()
					const loading = ElLoading.service({
						lock: true,
						text: '网关Zigbee网络创建中...',
						fullscreen: true,
						background: 'rgba(122, 122, 122, 0.8)'
					})
					setTimeout(() => {
						loading.close()
						ElMessageBox.alert('网关Zigbee网络创建成功，子设备请重新注册入网')
						this.$emit('refresh')
					}, 3000)
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		reset() {
			var reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id
			}
			this.$LotApi.device.gatewayResetFactory.post(reqData).then((res) => {
				if (res.code === 200) {
					this.closeAction()
					const loading = ElLoading.service({
						lock: true,
						text: '网关设备重置中...',
						fullscreen: true,
						background: 'rgba(122, 122, 122, 0.8)'
					})
					setTimeout(() => {
						loading.close()
						ElMessageBox.alert('网关设备重置成功，请重新给网关设备和子设备进行配网注册入网')
						this.$emit('refresh')
					}, 3000)
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		reboot() {
			var reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id
			}
			this.$LotApi.device.gatewayReboot.post(reqData).then((res) => {
				if (res.code === 200) {
					this.closeAction()
					const loading = ElLoading.service({
						lock: true,
						text: '网关设备重启中...',
						fullscreen: true,
						background: 'rgba(122, 122, 122, 0.8)'
					})
					setTimeout(() => {
						loading.close()
						ElMessageBox.alert('设备重启成功，请等待设备重连，可稍后刷新查看设备状态')
						this.$emit('refresh')
					}, 3000)
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		unbind() {
			var reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id
			}
			this.$LotApi.device.gatewayUnBind.post(reqData).then((res) => {
				if (res.code === 200) {
					this.closeAction()
					const loading = ElLoading.service({
						lock: true,
						text: '网关设备解绑中...',
						fullscreen: true,
						background: 'rgba(122, 122, 122, 0.8)'
					})
					setTimeout(() => {
						loading.close()
						ElMessageBox.alert('设备解绑成功，请重新配网注册入网')
						this.$emit('refresh')
					}, 3000)
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		getAppVer() {
			var reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id
			}
			this.$LotApi.device.gatewayGetAppVer.post(reqData).then((res) => {
				if (res.code === 200) {
					const loading = ElLoading.service({
						lock: true,
						text: '获取中...',
						fullscreen: true,
						background: 'rgba(122, 122, 122, 0.8)'
					})
					setTimeout(() => {
						loading.close()
						ElMessageBox.alert('设备版本已刷新！')
						this.$emit('refresh')
					}, 1500)
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		queryZBNwkInfo() {
			var reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id
			}
			this.$LotApi.device.gatewayQueryZBNwkInfo.post(reqData).then((res) => {
				if (res.code === 200) {
					const loading = ElLoading.service({
						lock: true,
						text: '获取中...',
						fullscreen: true,
						background: 'rgba(122, 122, 122, 0.8)'
					})
					setTimeout(() => {
						loading.close()
						this.getZBNwkInfo()
					}, 1500)
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		getZBNwkInfo() {
			var reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id
			}
			this.$LotApi.device.getZBNwkInfo.post(reqData).then((res) => {
				if (res.code === 200) {
					ElMessageBox.alert(
						'<pre style="padding: 10px; background: #ededed;border-radius: 6px">' +
							JSON.stringify(JSON.parse(res.data.network_info), null, 4) +
							'</pre>',
						'Zigbee网络信息',
						{
							confirmButtonText: '确认',
							dangerouslyUseHTMLString: true
						}
					)
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		// 属性获取
		getDeviceAttr() {
			let reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id,
				tsl_ids: this.readTslChecked
			}
			this.$LotApi.device.readDevProperty.post(reqData).then((res) => {
				if (res.code === 200) {
					ElMessage.success('执行成功！')
					let log = []
					log = res.data.map((item) => {
						return {
							title: '属性获取',
							message: JSON.parse(item)
						}
					})
					this.callLog.push(...log)
					//this.callLog.push(...res.data)
					clearInterval(this.callTimer)
					this.callTimer = null
					this.getDeviceLastLog()
				}
			})
		},
		// 属性设置
		setDeviceAttr() {
			if (this.writerTslChecked.length === 0) {
				ElMessage.error('请选择要设置的属性！')
				return
			}
			let property = []
			this.writerTslList
				.filter((item) => this.writerTslChecked.includes(item.id))
				.map((item) => {
					if (item.value !== null && item.value !== '') {
						property.push({
							tsl_id: item.id,
							value: item.value.toString()
						})
					}
				})
			if (property.length === 0) {
				ElMessage.error('请选择要设置的属性和设置值！')
				return
			}
			let reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id,
				property: property
			}
			this.$LotApi.device.writeDevProperty.post(reqData).then((res) => {
				if (res.code === 200) {
					ElMessage.success('执行成功！')

					let log = []
					log = res.data.map((item) => {
						return {
							title: '属性设置',
							message: JSON.parse(item)
						}
					})
					this.callLog.push(...log)
					clearInterval(this.callTimer)
					this.callTimer = null
					this.getDeviceLastLog()
				}
			})
		},
		// 服务调用
		callDeviceService() {
			console.log(this.selectServiceTsl)
			if (!this.selectServiceTsl) {
				ElMessage.error('请选择要调用的服务！')
				return
			}
			let params = {}
			this.selectServiceTsl.input_params.map((item) => {
				if (item.value !== null && item.value !== undefined && item.value !== '') {
					params[item.code] = item.value
				}
			})
			let reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id,
				tsl_id: this.selectServiceTsl.id,
				params: params
			}
			this.$LotApi.device.invokeDevCommand.post(reqData).then((res) => {
				if (res.code === 200) {
					ElMessage.success('执行成功！')

					let log = []
					log = res.data.map((item) => {
						return {
							title: '服务调用',
							message: JSON.parse(item)
						}
					})
					this.callLog.push(...log)
					clearInterval(this.callTimer)
					this.callTimer = null
					this.getDeviceLastLog()
				}
			})
		},
		//允许所有子设备加入ZigBee网络
		permitAllJoin() {
			let reqData = {
				device_id: this.deviceInfo.id,
				tenant_id: this.deviceInfo.tenant_id,
				campus_id: this.deviceInfo.campus_id
			}
			this.time = dayjs().format('YYYY-MM-DD HH:mm:ss')
			this.$LotApi.device.permitAllJoin.post(reqData).then((res) => {
				if (res.code === 200) {
					ElMessage.success('执行成功！')
					let log = []
					log = res.data.map((item) => {
						return {
							title: '网络命令',
							message: JSON.parse(item)
						}
					})
					this.callLog.push(...log)
					clearInterval(this.callTimer)
					this.callTimer = null
					this.getDeviceLastLog()
				}
			})
		},
		// 获取设备最新日志
		getDeviceLastLog() {
			this.num = 0
			this.callTimer = setInterval(() => {
				this.num++
				if (this.num > 60) {
					clearInterval(this.callTimer)
					this.callTimer = null
					return
				}
				let reqData = {
					device_id: this.deviceInfo.id,
					tenant_id: this.deviceInfo.tenant_id,
					campus_id: this.deviceInfo.campus_id,
					time: this.time,
					max_id: this.max_id
				}
				this.$LotApi.device.getDeviceLastLog.get(reqData).then((res) => {
					let log = []
					if (res.data.length) {
						this.max_id = res.data[0]?.id
						log = res.data.map((item) => {
							let title = '操作成功'
							if (item.function_code === 'DevReportProperty') {
								title = '属性读取成功'
							}
							return {
								title: title,
								message: JSON.parse(item.original)
							}
						})
						this.callLog.push(...log)
					}
				})
			}, 2000)
		},
		// 清除日志
		clearLog() {
			this.callLog = []
		}
	}
}
</script>

<style scoped lang="scss">
h3 {
	margin-bottom: 20px;
}

.actions {
	padding: 15px 0px;
	border-bottom: 1px solid var(--el-border-color-light);
	border-radius: 6px;
}

.countdown {
	text-align: center;
	padding-bottom: 10px;

	span {
		width: 45px;
		height: 45px;
		border: 3px solid var(--el-color-error);
		border-radius: 50%;
		display: inline-block;
		line-height: 40px;
		font-size: 18px;
		font-weight: bold;
		color: var(--el-color-error);
		text-align: center;
	}
}

.action_content {
	font-size: 14px;
	width: 80%;
	line-height: 24px;
	margin: 0 auto;
	font-weight: bold;
}

.debug-main {
	width: 100%;
	display: flex;
	border: 1px solid var(--el-border-color-light);

	.debug-left {
		width: 35%;
		height: 500px;
		border-right: 1px solid var(--el-border-color-light);
	}

	.debug-left-content {
		height: 450px;
		overflow-x: hidden;
		overflow-y: auto;
		border-bottom: 1px solid var(--el-border-color-light);
	}

	.debug-left-action {
		display: flex;
		justify-content: flex-end;
		padding: 10px;
	}

	.debug-right {
		flex: 1;
		height: 500px;
		width: 65%;

		&-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px;
			border-bottom: 1px solid var(--el-border-color-light);

			h3 {
				margin-bottom: 0;
			}
		}

		&-content {
			height: 447px;
			padding: 15px;
			overflow-x: hidden;
			overflow-y: auto;

			> div {
				margin-bottom: 10px;
			}
		}
	}
}

h2 {
	margin-bottom: 10px;
}

:deep(.el-scrollbar__wrap) {
	overflow-x: hidden !important;
}

.tslItem {
	width: 100%;
	margin-bottom: 10px;
	padding-bottom: 15px;
	border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.demo-tabs) {
	.el-tabs__item {
		font-size: 12px;
	}
}

.logItem {
	padding: 15px;
	margin-bottom: 10px;
	background-color: var(--el-color-primary-light-9);
	border: 1px solid var(--el-color-primary-light-4);
	font-size: 12px;
	border-radius: 6px;
}
</style>
