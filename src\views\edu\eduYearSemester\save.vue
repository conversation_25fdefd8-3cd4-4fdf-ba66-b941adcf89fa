<template>
	<el-dialog
		v-model="visible"
		:title="titleMap[mode] + (form.parent_id ? '学期' : '学年')"
		:width="500"
		destroy-on-close
		@closed="$emit('closed')"
	>
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'">
			<el-form-item v-if="form.parent_id !== 0" label="上级名称" prop="parentId">
				<el-cascader
					v-model="form.parent_id"
					:options="groups"
					:props="groupsProps"
					:show-all-levels="false"
					clearable
					style="width: 100%"
				></el-cascader>
			</el-form-item>
			<el-form-item label="名称" prop="semester_name">
				<el-input v-model="form.semester_name" placeholder="请输入名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="开始日期" prop="semester_begin_date">
				<el-date-picker
					v-model="form.semester_begin_date"
					type="date"
					placeholder="请选择日期"
					format="YYYY/MM/DD"
					value-format="YYYY-MM-DD"
				></el-date-picker>
			</el-form-item>
			<el-form-item label="结束日期" prop="semester_end_date">
				<el-date-picker
					v-model="form.semester_end_date"
					type="date"
					placeholder="请选择日期"
					format="YYYY/MM/DD"
					value-format="YYYY-MM-DD"
				></el-date-picker>
			</el-form-item>
			<el-form-item v-if="form.parent_id !== 0" label="序号" prop="order">
				<el-input v-model="form.order" type="number" placeholder="请输入序号" style="width: 150px" clearable></el-input>
			</el-form-item>
			<el-form-item label="是否有效" prop="status">
				<el-switch v-model="form.status" :active-value="1" :inactive-value="-1"></el-switch>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input v-model="form.remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const defaultData = () => {
	return {
		parent_id: 0,
		semester_name: '',
		status: 1,
		order: '',
		remark: '',
		semester_begin_date: '',
		semester_end_date: '',
		tenant_id: '',
		campus_id: ''
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				semester_begin_date: [{ required: true, message: '请选择学期开始日期', trigger: 'blur' }],
				semester_end_date: [{ required: true, message: '请选择学期结束日期', trigger: 'blur' }],
				semester_name: [{ required: true, message: '请输入学年学期名称' }],
				order: [{ required: true, message: '请输入学年学期序号' }]
			},
			//所需数据选项
			groups: [],
			groupsProps: {
				value: 'id',
				label: 'semester_name'
			},
			pData: {}
		}
	},
	mounted() {
		this.getGroup()
	},
	methods: {
		//显示
		open(mode = 'add', pData) {
			this.mode = mode
			this.visible = true
			if (pData) this.form.parent_id = pData.id
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id

			return this
		},
		//加载树数据
		async getGroup() {
			var res = await this.$API.eduYearSemester.eduYear.all.get(this.params)
			this.groups = cusTom.arrayToTree(res.data.filter((v) => v.parent_id === 0))
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					if (Array.isArray(this.form.parent_id)) {
						this.form.parent_id = this.form.parent_id[0]
					}
					this.form.order = parseInt(this.form.order)
					var res = await this.$API.eduYearSemester.eduYear.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			// this.form.id = data.id
			// this.form.label = data.label
			// this.form.status = data.status
			// this.form.sort = data.sort
			// this.form.parentId = data.parentId
			// this.form.remark = data.remark

			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
