const mediaLimitInfo = () => {
	return {
		myRadio: ['mp3', 'm4a', 'aac'],
		myAmr: ['amr'],
		myVideo: [
			'mp4',
			'avi',
			'mov',
			'f4v',
			'm4v',
			'rmvb',
			'ogg',
			'webm',
			'quicktime',
			'x-msvideo',
			'x-ms-wmv',
			'mpeg',
			'mpg',
			'mts',
			'm2ts',
			'3gpp',
			'3gpp2',
			'flv',
			'wmv'
		],
		myAudio: [
			'wav',
			'wave',
			'mp3',
			'aac',
			'flac',
			'ogg',
			'wma',
			'm4a',
			'mid',
			'midi',
			'aiff',
			'aif',
			'alac',
			'dsf',
			'dff',
			'opus',
			'ape',
			'mka',
			'ac3',
			'dts'
		],
		myZip: [
			'zip',
			'rar',
			'7z',
			'tar',
			'gz',
			'bz2',
			'xz',
			'cab',
			'dmg',
			'pkg',
			'exe',
			'msi',
			'deb',
			'rpm',
			'apk',
			'ipa',
			'apkx',
			'apkp',
			'apkc',
			'apks',
			'apkm',
			'apkf',
			'apkz',
			'apkzx',
			'ear',
			'war',
			'jar',
			'ace',
			'lzh',
			'lha',
			'arj',
			'iso'
		],
		myImage: ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff', 'tif', 'ico', 'ai'],
		myWord: ['doc', 'docx', 'dot', 'txt', 'wps', 'wpt', 'dotx', 'docm', 'dotm', 'rtf'],
		myExcel: ['et', 'xls', 'xlt', 'xlsx', 'xlsm', 'xltx', 'xltm', 'csv'],
		myPdf: ['pdf'],
		myPPT: ['ppt', 'pptx', 'pptm', 'ppsx', 'ppsm', 'pps', 'potx', 'potm', 'dpt', 'dps'],
		myOffice: ['doc', 'docx', 'xls', 'xlsx'],
		webOffice: [
			'doc',
			'docx',
			'txt',
			'dot',
			'wps',
			'wpt',
			'dotx',
			'docm',
			'dotm',
			'rtf',
			'ppt',
			'pptx',
			'pptm',
			'ppsx',
			'ppsm',
			'pps',
			'potx',
			'potm',
			'dpt',
			'dps',
			'et',
			'xls',
			'xlt',
			'xlsx',
			'xlsm',
			'xltx',
			'xltm',
			'csv',
			'pdf'
		],
		allFileType: [
			'image/jpeg',
			'image/png',
			'image/jpg',
			'audio/mpeg',
			'audio/mp3',
			'audio/x-m4a',
			'audio/vnd.dlna.adts',
			'video/mp4',
			'video/avi',
			'video/mov',
			'video/rmvb',
			'video/quicktime',
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'application/vnd.ms-excel',
			'application/msword',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'application/pdf',
			'application/x-ppt',
			'application/ppt',
			'application/pptx',
			'applications-powerpoint',
			'application-powerpoint',
			'application/vnd.openxmlformats-officedocument.presentationml.presentation'
		],
		allFileTypeStr:
			'image/jpeg,image/png,image/jpg,audio/mpeg,audio/mp3,audio/vnd.dlna.adts,audio/aac,audio/x-m4a,video/mp4,video/avi,video/mov,video/rmvb,video/quicktime,application/vnd.ms-excel,application/msword,application/pdf,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.wordprocessingml.document,image/jpeg,image/png,image/jpg,application/vnd.ms-excel,application/msword,application/pdf,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/ppt,application/pptx,application/vnd.openxmlformats-officedocument.presentationml.presentation',
		imgLimitType: ['image/jpeg', 'image/png', 'image/jpg'],
		imgLimitTypeStr: 'image/jpeg,image/png,image/jpg',
		radioLimitType: ['audio/mpeg', 'audio/mp3', 'audio/x-m4a', 'audio/vnd.dlna.adts'], //MP3 MIME-type
		radioLimitTypeStr: 'audio/mpeg,audio/mp3,audio/x-m4a,audio/vnd.dlna.adts',
		videoLimitType: ['video/mp4', 'video/avi', 'video/mov', 'video/rmvb', 'video/quicktime'],
		videoLimitTypeStr: 'video/mp4,video/avi,video/mov,video/rmvb,video/quicktime',
		documentFileLimitType: [
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'application/vnd.ms-excel',
			'application/msword',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'application/pdf'
		],
		documentFileLimitTypeStr:
			'application/vnd.ms-excel,application/msword,application/pdf,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
		comFileLimitType: [
			'image/jpeg',
			'image/png',
			'image/jpg',
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'application/vnd.ms-excel',
			'application/msword',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'application/pdf'
		],
		comFileLimitTypeStr:
			'image/jpeg,image/png,image/jpg,application/vnd.ms-excel,application/msword,application/pdf,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
		officeFileLimitType: [
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'application/vnd.ms-excel',
			'application/msword',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'application/pdf',
			'application/x-ppt',
			'application/ppt',
			'application/pptx',
			'applications-powerpoint',
			'application-powerpoint',
			'application/vnd.openxmlformats-officedocument.presentationml.presentation'
		]
	}
}
export function webPreview(url) {
	let index = url?.lastIndexOf('.')
	let ext = url?.substr(index + 1)
	return mediaLimitInfo().webOffice.indexOf(ext?.toLowerCase()) !== -1
}
export function wordSuffix(url) {
	let index = url?.lastIndexOf('.')
	let ext = url?.substr(index + 1)
	return mediaLimitInfo().myWord.indexOf(ext?.toLowerCase()) !== -1
}
export function excelSuffix(url) {
	let index = url?.lastIndexOf('.')
	let ext = url?.substr(index + 1)
	return mediaLimitInfo().myExcel.indexOf(ext?.toLowerCase()) !== -1
}
export function pptSuffix(url) {
	let index = url?.lastIndexOf('.')
	let ext = url?.substr(index + 1)
	return mediaLimitInfo().myPPT.indexOf(ext?.toLowerCase()) !== -1
}
export function imageSuffix(url) {
	let index = url?.lastIndexOf('.')
	let ext = url?.substr(index + 1)
	return mediaLimitInfo().myImage.indexOf(ext?.toLowerCase()) !== -1
}
export function videoSuffix(url) {
	let index = url?.lastIndexOf('.')
	let ext = url?.substr(index + 1)
	return mediaLimitInfo().myVideo.indexOf(ext.toLowerCase()) !== -1
}
export function audioSuffix(url) {
	let index = url?.lastIndexOf('.')
	let ext = url?.substr(index + 1)
	return mediaLimitInfo().myAudio.indexOf(ext.toLowerCase()) !== -1
}
export function zipSuffix(url) {
	let index = url?.lastIndexOf('.')
	let ext = url?.substr(index + 1)
	return mediaLimitInfo().myZip.indexOf(ext?.toLowerCase()) !== -1
}

export function getMediaLimitInfo() {
	return mediaLimitInfo()
}

export function checkRadio(url) {
	var index = url?.lastIndexOf('.')
	var ext = url?.substr(index + 1)
	return mediaLimitInfo().myRadio.indexOf(ext?.toLowerCase()) !== -1
}
export function checkVideo(url) {
	var index = url?.lastIndexOf('.')
	var ext = url?.substr(index + 1)
	return mediaLimitInfo().myVideo.indexOf(ext?.toLowerCase()) !== -1
}

export function checkAmr(url) {
	var index = url.lastIndexOf('.')
	var ext = url.substr(index + 1)
	return mediaLimitInfo().myAmr.indexOf(ext.toLowerCase()) != -1
}

export function checkImage(url) {
	var index = url.lastIndexOf('.')
	var ext = url.substr(index + 1)
	return mediaLimitInfo().myImage.indexOf(ext.toLowerCase()) != -1
}

export function checkWord(url) {
	var index = url.lastIndexOf('.')
	var ext = url.substr(index + 1)
	return mediaLimitInfo().myWord.indexOf(ext.toLowerCase()) != -1
}
export function checkExcel(url) {
	var index = url.lastIndexOf('.')
	var ext = url.substr(index + 1)
	return mediaLimitInfo().myExcel.indexOf(ext.toLowerCase()) != -1
}
export function checkPdf(url) {
	var index = url.lastIndexOf('.')
	var ext = url.substr(index + 1)
	return mediaLimitInfo().myPdf.indexOf(ext.toLowerCase()) != -1
}
export function checkPPT(url) {
	var index = url.lastIndexOf('.')
	var ext = url.substr(index + 1)
	return mediaLimitInfo().myPPT.indexOf(ext.toLowerCase()) != -1
}

export function checkOffice(url) {
	return true
	var index = url.lastIndexOf('.')
	var ext = url.substr(index + 1)
	return mediaLimitInfo().myOffice.indexOf(ext.toLowerCase()) != -1
}

export function checkFileIsImage(fileType) {
	console.log(fileType)
	var limit = mediaLimitInfo()
	return limit.imgLimitType.indexOf(fileType.toLowerCase()) != -1
}

export function checkFileIsVideo(fileType) {
	console.log(fileType)
	var limit = mediaLimitInfo()
	return limit.videoLimitType.indexOf(fileType.toLowerCase()) != -1
}

export function checkFileIsAllFile(fileType) {
	console.log(fileType)
	var limit = mediaLimitInfo()
	console.log(limit.allFileType)
	return limit.allFileType.indexOf(fileType.toLowerCase()) != -1
}

export function checkFileIsRadio(fileType) {
	console.log(fileType)
	var limit = mediaLimitInfo()
	return limit.radioLimitType.indexOf(fileType.toLowerCase()) != -1
}

export function checkFileIsCom(fileType) {
	console.log(fileType)
	var limit = mediaLimitInfo()
	return limit.comFileLimitType.indexOf(fileType.toLowerCase()) != -1
}

export function checkFileIsOffice(fileType) {
	var limit = mediaLimitInfo()
	return limit.officeFileLimitType.indexOf(fileType.toLowerCase()) != -1
}
