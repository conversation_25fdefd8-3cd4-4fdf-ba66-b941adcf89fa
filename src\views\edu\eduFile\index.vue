<template>
	<el-container>
		<el-aside width="270px">
			<el-container>
				<el-header>
					<div class="add-lay">
						<el-button type="primary" icon="el-icon-plus" @click="addFileType">新增文件分类</el-button>
					</div>
				</el-header>
				<el-main>
					<el-scrollbar width="160px">
						<el-tree
							ref="group"
							draggable
							:allow-drop="handelAllowDrop"
							node-key="id"
							:highlight-current="highlight"
							class="tree"
							:current-node-key="-1"
							:data="fileTypeList"
							:props="defaultProps"
							@node-click="treeClick"
							@node-drop="handleDrop"
						>
							<template #default="{ node, data }">
								<span class="custom-tree-node" @mouseenter="handleShowTools(data)" @mouseleave="handleShowTools(data)">
									<span>{{ node.label }}</span>
									<span v-if="data.id > 0 && data.id === currentId">
										<a style="margin-left: 8px" @click.stop="edit(data)">
											<el-icon><el-icon-edit /></el-icon>
										</a>
										<a style="margin-left: 8px" @click.stop="remove(data)">
											<el-icon><el-icon-delete /></el-icon>
										</a>
									</span>
								</span>
							</template>
						</el-tree>
						<!--						<el-tree :data="treeData" :props="treeProps" @node-click="handlerTreeClick"></el-tree>-->
					</el-scrollbar>
				</el-main>
			</el-container>
		</el-aside>
		<!--动态展示表格组件-->
		<FileTable v-if="tableStatus === 0" ref="tableRef" :Tableparams="params"></FileTable>
		<ShareMyTable v-if="tableStatus === 1"></ShareMyTable>
		<MyShareTable v-if="tableStatus === 2" ref="myShareTableRef"></MyShareTable>
	</el-container>
	<SaveDialog ref="dialogRef" :fileParams="fileParams" @success="handlerSuccess"></SaveDialog>
	<el-dialog v-model="visible" title="视频播放" destroy-on-close>
		<VideoPlay :source="source"></VideoPlay>
	</el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
import SaveDialog from './save.vue'
import FileTable from './components/FileTable.vue'
import ShareMyTable from './components/ShareMyTable.vue'
import MyShareTable from './components/MyShareTable.vue'
import VideoPlay from '@/components/videoPlay/index.vue'
import { ElMessageBox } from 'element-plus'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
// 文件分类获取字段
const fileParams = ref({
	tenant_id: tenantId,
	campus_id: campusId,
	model: 2
})
const breadcrumb = ref([{ id: -1, root: 1, name: '默认文件夹' }])
// 树排序字段
const treeOrderParams = ref({
	tenant_id: tenantId,
	campus_id: campusId,
	model: 2,
	order: [
		{
			id: null,
			listorder: null
		}
	]
})
/*const treeData = ref([
	{
		name: '分享给我',
		value: 1
	},
	{
		name: '我的分享',
		value: 2
	}
])*/

const treeDefaultData = ref([
	{
		name: '默认文件夹',
		id: -1
	}
])
const treeProps = {
	label: 'name'
}
// 文件分类列表
const fileTypeList = ref([])
// 节点当前id
const currentId = ref(0)
let dialogRef = ref(null)
let table = ref(null)
let myShareTableRef = ref(null)
let visible = ref(false)
let highlight = ref(true)
let source = ref('')
let tableStatus = ref(0)
const defaultProps = {
	label: 'name'
}
const params = ref({
	type_id: null,
	treeList: []
})
// 获取文件分类列表
const getFileTypeList = async () => {
	const res = await globalPropValue.fileManagement.file.file_type.get(fileParams.value)
	if (res.code === 200) {
		if (res.data?.length > 0) {
			fileTypeList.value = [...res.data, ...treeDefaultData.value]
		} else {
			fileTypeList.value = treeDefaultData.value
		}
	}
}
let tableRef = ref(null)
// 树点击事件
const treeClick = (data) => {
	tableStatus.value = 0
	params.value.treeList = data
	params.value.type_id = data.id
	setTimeout(() => {
		tableRef.value.upadteTable()
	}, 100)
}
const handlerTreeClick = async (data) => {
	breadcrumb.value = [{ id: data.value, root: 1, name: data.name }]
	highlight.value = false
	if (data.value === 1) {
		tableStatus.value = 1
	}
	if (data.value === 2) {
		tableStatus.value = 2
		setTimeout(() => {
			myShareTableRef.value.tableTotal()
		}, 100)
	}
}
const handelAllowDrop = (draggingNode, dropNode, type) => {
	return !(draggingNode.data.id < 0 || type === 'inner')
}
// 树拖拽成功事件
const handleDrop = async () => {
	treeOrderParams.value.order = fileTypeList.value.map((v, index) => {
		return {
			id: v.id,
			listorder: index + 1
		}
	})
	treeOrderParams.value.order = treeOrderParams.value.order.filter((v) => v.id !== -1)
	await globalPropValue.fileManagement.file.file_type_order.post(treeOrderParams.value)
}
// 鼠标事件
const handleShowTools = (item) => {
	currentId.value = item.id
}
// 新增文件分类事件
const addFileType = () => {
	dialogRef.value.open()
}
// 编辑文件分类事件
const edit = (row) => {
	dialogRef.value.open('edit')
	dialogRef.value.setData(row)
}
// 删除文件分类事件
const remove = (data) => {
	ElMessageBox.confirm(`确定删除${data.name}文件分类?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning'
	})
		.then(() => {
			// 删除
			globalPropValue.fileManagement.file.file_type_del
				.post({
					id: data.id,
					campus_id: fileParams.value.campus_id,
					tenant_id: fileParams.value.tenant_id
				})
				.then((res) => {
					if (res.code === 200) {
						getFileTypeList()
						ElMessage({
							type: 'success',
							message: '删除成功'
						})
					}
				})
		})
		.catch(() => {})
}
// 搜索点击事件
const upsearch = () => {
	console.log(222)
	table.value?.upData(params.value)
}
// 自定义事件
const handlerSuccess = () => {
	getFileTypeList()
}
onMounted(async () => {
	await getFileTypeList()
	// console.log(table.value.total)
	// checkedTotal.value = table.value.total
})
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;

	.el-button {
		position: fixed;
		z-index: 99;
	}
}

.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;

	a {
		font-size: 18px;
	}
}

.tree {
	height: auto;
	padding-bottom: 10px;
	border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-tree-node__content) {
	margin-top: 12px;
}

:deep(.el-tree-node) {
	height: 35px;
	line-height: 35px;
}

:deep(.el-tree-node__content) {
	height: 35px;
	line-height: 35px;
	margin-top: 8px;
}

.footer-check-btn {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-top: -40px;
	overflow: hidden;

	.checkout {
		margin-right: 10px;
	}
}
</style>
