<template>
    <div>
        <div class="add-btn">
            <p></p>
            <div>
                <el-input v-model="staffName" style="width: 200px" clearable placeholder="输入姓名搜索"
                    prefix-icon="el-icon-search" />
                <el-button type="primary" style="margin-left: 10px" @click="exportExcel">导出</el-button>
            </div>
        </div>
        <el-table :data="filteredArrangeList" style="width: 100%" max-height="550">
            <el-table-column prop="staff_info" label="教师姓名" align="center">
                <template #default="{ row }">
                    <div>{{ row.staff_info?.name }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="count" label="段数" align="center" sortable>
                <template #default="{ row }">
                    <div>{{ row.count }}</div>
                </template>
            </el-table-column>
            <el-table-column v-for="item, index in dateList" :key="index" :label="item.date" align="center">
                <el-table-column v-for="items in item.course" :key="items.id" :prop="items.time"
                    :label="`${items.course_name}`" align="center">
                    <template #header="{ column }">
                        <div>{{ column.label }}</div>
                        <div>{{ column.property }}</div>
                    </template>
                    <template #default="{ row }">
                        <el-icon v-if="row.time_list[items.id] == 1" size="20" color="#67c23a">
                            <el-icon-select />
                        </el-icon>
                    </template>
                </el-table-column>
            </el-table-column>
        </el-table>
    </div>
</template>
<script setup>
import { excelExport } from 'pikaz-excel-js'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const { query } = useRoute()

const defaultParams = () => {
    return {
        tenant_id: tenantId,
        campus_id: campusId,
        examine_id: query.id,
    }
}
const params = ref(defaultParams())

// 导出
const exportExcel = async () => {
    let merges = ['1-2:1-3', '2-2:2-3', '3-2:3-3']
    const headers = [
        { label: '序号', key: 'index' },
        { label: '教师姓名', key: 'teacher' },
        { label: '段数', key: 'count' }
    ]

    let startColumn = 4 // 从第4列开始（前3列是固定的：序号、教师姓名、段数）
    dateList.value.forEach((date, index) => {
        // 计算当前日期的课程占用的总列数（每个课程占1列）
        const coursesColumns = date.course.length
        // 合并日期行，从startColumn列开始，跨coursesColumns列
        merges.push(`${startColumn - 4 + 1 + 3}-2:${startColumn + coursesColumns - 1}-2`)

        date.course.forEach((course, courseIndex) => {
            // 计算当前课程的起始列
            const courseStartColumn = startColumn + courseIndex
            // 每个课程占1列，每个课程合并3行
            // merges.push(`${courseStartColumn}-3:${courseStartColumn + 1}-5`)

            headers.push(
                { label: `${course.course_name}\r\n${course.time}`, key: `course_${course.id}`, date: courseIndex == 0 ? date.date : '' },
            )
        })

        // 更新下一个日期的起始列
        startColumn += coursesColumns
    })

    const tableData = workList.value.map((item, index) => {
        let data = {
            index: index + 1,
            teacher: item.staff_info?.name,
            count: Object.values(item.time_list).filter(value => value === 1).length
        }
        dateList.value.forEach(date => {
            date.course.forEach(course => {
                // 查找对应考试时段的教师信息
                data[`course_${course.id}`] = item.time_list[course.id] === 1 ? '√' : ''
            })
        })
        return data
    })
    await excelExport({
        sheet: [{
            title: '监考分工表',
            globalStyle: { font: { sz: 14 }, alignment: { wrapText: true } },
            cellStyle: [
                { cell: 'A1', font: { bold: true, sz: 18 } },
            ],
            sheetName: '监考分工表',
            merges: merges,
            keys: headers.map(h => h.key),
            table: [
                Object.fromEntries(headers.map((h, i) => [h.key, i < 3 ? h.label : h.date || ''])),
                Object.fromEntries(headers.map((h, i) => [h.key, i > 2 && h.key.includes('course') ? h.label : ''])),
                ...tableData
            ]
        }],
        filename: query.name + '-监考分工表',
    })
}

//搜索
const staffName = ref('')
const filteredArrangeList = computed(() => {
    let list = staffName.value ? workList.value.filter(item =>
        item.staff_info.name.toLowerCase().includes(staffName.value.toLowerCase())
    ) : workList.value
    return list
})

const workList = ref([])
const dateList = ref([])
const getMonitorWorkList = () => {
    globalPropValue.examine.getMonitorWork.get(params.value).then(res => {
        if (res.code == 200) {
            workList.value = res.data.map(item => {
                return {
                    ...item,
                    count: Object.values(item.time_list).filter(value => value === 1).length
                }
            })
        }
    })
}
// 获取时间段列表
const getTimeData = () => {
    globalPropValue.examine.timeList.get(params.value).then(res => {
        if (res.code === 200) {
            const groupedData = {}
            res.data.forEach(item => {
                if (!groupedData[item.examine_date]) {
                    groupedData[item.examine_date] = {
                        date: item.examine_date,
                        course: []
                    }
                }
                groupedData[item.examine_date].course.push({
                    id: item.id,
                    course_name: item.course_name,
                    time: item.begin_time + '-' + item.end_time
                })
            })
            dateList.value = Object.values(groupedData)
        }
    })
}

onMounted(() => {
    getMonitorWorkList()
    getTimeData()
})
</script>
<style lang="scss" scoped>
.add-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    p {
        font-size: 15px;
    }
}
</style>