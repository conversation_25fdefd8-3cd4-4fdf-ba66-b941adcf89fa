<template>
	<el-dialog v-model="visible" title="激活入网" width="600" destroy-on-close :show-close="false" @closed="close">
		<el-steps :active="activeStep" style="width: 500px; padding-left: 50px" align-center finish-status="success">
			<el-step title="入网前检查" />
			<el-step title="入网前确定" />
			<el-step title="注册入网" />
		</el-steps>
		<div class="activationMain">
			<div v-if="activeStep === 0" class="check">
				<div v-if="checkStatus === false" v-loading="checkLoad" class="checkContent" element-loading-text="检测中...">
					<div v-if="checkMessage.length > 0" style="padding-left: 100px; line-height: 30px">
						<p v-for="(item, index) in checkMessage" :key="index" style="color: red">{{ index + 1 }}. {{ item }}</p>
						<el-button style="margin-top: 15px" @click="close">关闭</el-button>
					</div>
				</div>
				<div v-if="checkStatus === true">
					<el-result icon="success" title="检测通过" sub-title="请点击下一步操作">
						<template #extra>
							<el-button @click="close">取消</el-button>
							<el-button :disabled="checkStatus === false" type="primary" @click="nextStep">下一步</el-button>
						</template>
					</el-result>
				</div>
			</div>
			<div v-if="activeStep === 1" class="check">
				<div style="padding-left: 100px">
					<el-checkbox-group v-model="checked" text-color="#00B42A" size="large" fill="#00B42A">
						<p><el-checkbox label="已确保当前设备已通电开机" :value="1" /></p>
						<p><el-checkbox label="已确保当前设备和上级网关在同一场室内" :value="2" /></p>
						<p><el-checkbox label="已按照设备手册进行对设备开启网络配置" :value="3" /></p>
					</el-checkbox-group>
				</div>
				<div class="check" style="margin-top: 20px; text-align: center">
					<el-button @click="close">取消</el-button>
					<el-button type="primary" :disabled="checkStatus2 === false" @click="register">下一步</el-button>
				</div>
			</div>
			<div v-if="activeStep >= 2" class="check">
				<div
					v-if="registerStatus === 0"
					v-loading="registerLoad"
					style="padding-left: 100px; height: 100px"
					:element-loading-text="registerLoadText"
				></div>
				<div v-if="registerStatus === 1">
					<el-result icon="success" title="注册入网成功" sub-title="请检测设备是否正常连接成功">
						<template #extra>
							<el-button type="success" @click="close">完成</el-button>
						</template>
					</el-result>
				</div>
				<div v-if="registerStatus === -1">
					<el-result icon="error" title="注册入网失败" sub-title="请检测设备是否开启网络配置，请开启后重试">
						<template #extra>
							<el-button type="danger" @click="close">关闭</el-button>
							<el-button type="primary" @click="register">重试</el-button>
						</template>
					</el-result>
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},

	data() {
		return {
			activeStep: 0,
			CampusManagementList: [],
			productList: [],
			num: 0,
			checked: [],
			gatewayDeviceList: [],
			mode: 'add',
			visible: false,
			checkLoad: true,
			checkStatus2: false,
			checkStatus: false,
			registerLoad: true,
			registerLoadText: '设备入网配置中...',
			registerStatus: 0,
			checkMessage: [],
			deviceData: {},
			templateSelectVisible: false,
			isSaveing: false,
			showGateway: false,
			//表单数据
			enumConfig: []
		}
	},
	watch: {
		checked: function (val) {
			if (val.length >= 3) {
				this.checkStatus2 = true
			}
		}
	},
	mounted() {},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	methods: {
		async register() {
			this.activeStep++
			this.registerStatus = 0
			this.registerLoad = true
			var reqData = {
				id: this.deviceData.id,
				tenant_id: this.deviceData.tenant_id,
				campus_id: this.deviceData.campus_id
			}
			this.checkLoad = true
			var res = await this.$LotApi.device.register.post(reqData)
			this.isSaveing = false
			if (res.code === 200) {
				setTimeout(() => {
					this.checkDevStatus()
				}, 2000)
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		checkDevStatus() {
			this.num++
			console.log(this.num)
			if (this.num > 60) {
				this.registerStatus = -1
				return
			}
			this.registerLoad = true
			this.$LotApi.device.one
				.get({
					id: this.deviceData.id,
					tenant_id: this.deviceData.tenant_id,
					campus_id: this.deviceData.campus_id
				})
				.then((res) => {
					if (res.code === 200) {
						if (res.data.status === 2) {
							this.registerLoadText = '设备注册入网成功'
							this.registerLoad = false
							this.registerStatus = 1
						} else {
							setTimeout(() => {
								this.checkDevStatus()
							}, 2000)
						}
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
		},
		nextStep() {
			this.activeStep++
		},
		//显示
		open(deviceData) {
			this.activeStep = 0
			this.deviceData = deviceData
			this.visible = true
			this.registerStatus = 0
			this.checkMessage = []
			this.checkStatus = false
			this.checkStatus2 = false
			this.registerLoad = true
			this.checkRegister()
			return this
		},
		close() {
			this.activeStep = 0
			this.visible = false
			this.$emit('success')
		},
		async checkRegister() {
			var reqData = {
				id: this.deviceData.id,
				tenant_id: this.deviceData.tenant_id,
				campus_id: this.deviceData.campus_id
			}
			this.checkLoad = true
			var res = await this.$LotApi.device.checkRegister.post(reqData)
			this.isSaveing = false
			if (res.code === 200) {
				this.checkLoad = false
				if (res.data && res.data.length > 0) {
					this.checkStatus = false
				} else {
					this.checkStatus = true
				}
				this.checkMessage = res.data
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let subForm
					if (this.mode === 'add') {
						subForm = { ...this.params, ...this.form }
					} else if (this.mode === 'edit') {
						subForm = { ...this.form }
					}
					if (this.form.room && this.form.room.length > 0) {
						subForm.room_id = this.form.room[0].id
					}
					var res = await this.$LotApi.device.save.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data, productList, campus_id) {
			this.productList = productList
			this.campus_id = campus_id
			this.form.campus_id = campus_id
			Object.assign(this.form, data)
			if (data && data.room_info) {
				this.form.room = [{ label: data.room_info.name, id: data.room_info.id }]
			}
			if (data && data.product_info && data.product_info.node_type === 2) {
				this.showGateway = true
			}
			this.getGatewayDevice()
		}
	}
}
</script>

<style scoped lang="scss">
.selectTemplate {
	cursor: pointer;
}
.activationMain {
	padding: 20px 10px 10px 10px;
}
.check {
}
.checkContent {
	height: 100px;
}
:deep(.el-result) {
	padding: 10px;
}
</style>
