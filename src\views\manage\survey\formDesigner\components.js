import config from '@/config'

const rules = {
	trigger: 'blur', //触发的事件
	enum: '', // 数组，指定字段值必须是该数组中的一个选项。
	len: undefined, // 字符串，指定字段值的长度。
	max: undefined, // 字符串，指定字段值的最大长度。
	min: undefined, // 字符串，指定字段值的最小长度。
	message: '', // 验证失败时显示的提示信息
	pattern: '', // 正则表达式，指定字段值的格式。
	required: false, // 布尔值，指定字段是否必填。
	type: 'any' // 字符串，指定字段值的类型。
}
export const widgetForm = {
	list: [],
	config: {
		size: 'default',
		hideRequiredAsterisk: false,
		labelWidth: 100,
		labelPosition: 'top'
	}
}
export const CodeType = {
	Vue: 'vue',
	Html: 'html'
}

export const basicComponents = [
	{
		label: '单行文本',
		type: 'input',
		options: {
			width: '100%',
			defaultValue: '',
			placeholder: '请输入',
			minlength: null,
			maxlength: null,
			showWordLimit: false,
			// prefix: '',
			// suffix: '',
			// prepend: '',
			// append: '',
			disabled: false,
			clearable: false,
			readonly: false,
			rules,
			showCode: null
		}
	},
	{
		label: '多行文本',
		type: 'textarea',
		options: {
			width: '100%',
			defaultValue: '',
			placeholder: '请输入',
			minlength: null,
			maxlength: null,
			rows: 2,
			autosize: false,
			showWordLimit: false,
			disabled: false,
			clearable: false,
			readonly: false,
			rules,
			showCode: null
		}
	},
	{
		label: '计数器',
		type: 'number',
		options: {
			width: '',
			defaultValue: 0,
			min: 0,
			max: 100,
			step: 1,
			disabled: false,
			rules,
			showCode: null
		}
	},
	{
		label: '单选框组',
		type: 'radio',
		options: {
			defaultValue: '',
			width: '100%',
			inline: false,
			remote: false,
			showLabel: false,
			remoteFunc: '',
			options: [
				{
					value: '选项1',
					label: '选项1'
				},
				{
					value: '选项2',
					label: '选项2'
				},
				{
					value: '选项3',
					label: '选项3'
				}
			],
			remoteOptions: [],
			props: {
				value: 'value',
				label: 'label'
			},
			disabled: false,
			rules,
			showCode: null
		}
	},
	{
		label: '多选框组',
		type: 'checkbox',
		options: {
			defaultValue: [],
			width: '100%',
			inline: false,
			remote: false,
			showLabel: false,
			remoteFunc: '',
			options: [
				{
					label: '选项1',
					value: '选项1'
				},
				{
					label: '选项2',
					value: '选项2'
				},
				{
					label: '选项3',
					value: '选项3'
				}
			],
			remoteOptions: [],
			props: {
				value: 'value',
				label: 'label'
			},
			disabled: false,
			rules,
			showCode: null
		}
	},
	{
		label: '时间选择器',
		type: 'time',
		options: {
			defaultValue: '',
			width: '',
			placeholder: '请选择时间',
			format: 'HH:mm:ss',
			valueFormat: 'HH:mm:ss',
			readonly: false,
			editable: true,
			clearable: true,
			disabled: false,
			rules,
			showCode: null
		}
	},
	{
		label: '日期选择器',
		type: 'date',
		options: {
			defaultValue: '',
			width: '',
			placeholder: '请选择日期',
			type: 'date',
			format: 'YYYY-MM-DD',
			valueFormat: 'YYYY-MM-DD',
			readonly: false,
			editable: true,
			clearable: true,
			disabled: false,
			rules,
			showCode: null
		}
	},
	{
		label: '评分',
		type: 'rate',
		options: {
			defaultValue: 0,
			max: 5,
			allowHalf: false,
			disabled: false,
			rules,
			showCode: null
		}
	},
	{
		label: '下拉选择框',
		type: 'select',
		options: {
			defaultValue: '',
			width: '100%',
			multiple: false,
			collapseTags: true,
			placeholder: '请选择',
			multipleLimit: 0,
			remote: false,
			showLabel: false,
			filterable: false,
			clearable: false,
			disabled: false,
			props: {
				label: 'label',
				value: 'value'
			},
			options: [
				{
					label: '选项1',
					value: '选项1'
				},
				{
					label: '选项2',
					value: '选项2'
				},
				{
					label: '选项3',
					value: '选项3'
				}
			],
			remoteOptions: [],
			remoteFunc: '',
			rules,
			showCode: null
		}
	},
	{
		label: '开关',
		type: 'switch',
		options: {
			defaultValue: false,
			disabled: false,
			activeText: '',
			inactiveText: '',
			rules,
			showCode: null
		}
	},
	{
		label: '文字',
		type: 'text',
		options: {
			defaultValue: '',
			placeholder: '请输入内容',
			showCode: null
		}
	}
]

export const advanceComponents = [
	{
		label: '图片上传',
		type: 'img-upload',
		options: {
			defaultValue: [],
			name: 'image',
			action: `${config.API_URL}/api/common/upload`,
			method: 'post',
			listType: 'picture-card',
			accept: 'image/*',
			limit: 3,
			multiple: false,
			disabled: false,
			showCode: null
		}
	},
	{
		label: '文件上传',
		type: 'file-upload',
		options: {
			defaultValue: [],
			name: 'file',
			action: 'http://example.com/upload',
			method: 'post',
			listType: '',
			accept: 'file/*',
			limit: 3,
			multiple: false,
			disabled: false,
			showCode: null
		}
	},
	{
		label: '分割线',
		type: 'divider',
		options: {
			direction: 'horizontal',
			borderStyle: 'solid',
			content: '',
			contentPosition: 'left',
			showCode: null
		}
	},
	{
		label: '插入图片',
		type: 'insert-img',
		options: {
			defaultValue: '',
			name: 'image',
			action: `${config.API_URL}/api/common/upload`,
			method: 'post',
			listType: 'picture-card',
			accept: 'image/*',
			limit: 1,
			showCode: null
		}
	}
]
