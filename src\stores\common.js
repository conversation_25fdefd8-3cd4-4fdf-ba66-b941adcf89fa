import { defineStore } from 'pinia'
import api from '../api'
import tool from '../utils/tool'
import { ElMessage } from 'element-plus'
export const useCommonStore = defineStore('common', {
	state: () => {
		return {
			tenant_id: null,
			campus_id: null
		}
	},
	actions: {
		async saveCommonData() {
			var res = await api.common.getCommonData.get()
			if (res.code === 200) {
				tool.data.set('COMMON_DATA', res.data)
			} else {
				ElMessage.warning(res.message)
				return false
			}
		},
		removeCommonData() {
			tool.data.remove('COMMON_DATA')
		},
		SET_tenant_id(data) {
			this.tenant_id = data
		},
		SET_campus_id(data) {
			this.campus_id = data
		}
	}
})
