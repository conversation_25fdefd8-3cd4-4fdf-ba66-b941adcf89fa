import config from '@/config'
import http from '@/utils/request'
export default {
	file: {
		file_type: {
			url: `${config.API_URL}/sysapi/file_type/list`,
			name: '获取分类列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		file_type_order: {
			url: `${config.API_URL}/sysapi/file_type/order`,
			name: '文件分类排序',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		file_type_add_edit: {
			url: `${config.API_URL}/sysapi/file_type/save`,
			name: '修改或新增文件分类',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		file_type_del: {
			url: `${config.API_URL}/sysapi/file_type/del`,
			name: '删除文件分类',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		file_list: {
			url: `${config.API_URL}/sysapi/file/list`,
			name: '获取文件列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add_file: {
			url: `${config.API_URL}/sysapi/file/creat`,
			name: '新增文件/文件夹',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		edit_file: {
			url: `${config.API_URL}/sysapi/file/rename`,
			name: '修改文件/文件夹',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del_file: {
			url: `${config.API_URL}/sysapi/file/del`,
			name: '删除文件/文件夹',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		weboffice_preview: {
			url: `${config.API_URL}/sysapi/file/getWebofficeToken`,
			name: '获取文件在线预览凭证',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		file_share_me: {
			url: `${config.API_URL}/sysapi/file_share/share_me`,
			name: '获取共享给我列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		file_my_share: {
			url: `${config.API_URL}/sysapi/file_share/my_share`,
			name: '获取我共享的列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		file_share: {
			url: `${config.API_URL}/sysapi/file_share/share`,
			name: '分享',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		file_share_cancel: {
			url: `${config.API_URL}/sysapi/file_share/cancel`,
			name: '取消分享',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		file_share_details: {
			url: `${config.API_URL}/sysapi/file_share/details`,
			name: '获取共享详情',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		file_share_set_read: {
			url: `${config.API_URL}/sysapi/file_share/set_read`,
			name: '设置已读',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		file_get_type: {
			url: `${config.API_URL}/sysapi/file/getType`,
			name: '获取文件夹目录树',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		file_move: {
			url: `${config.API_URL}/sysapi/file/move`,
			name: '移动文件',
			post: async function (data) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		}
	}
}
