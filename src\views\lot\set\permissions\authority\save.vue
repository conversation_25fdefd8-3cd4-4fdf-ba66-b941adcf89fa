<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" width="550" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-position="top">
			<el-form-item label="人员" prop="user_obj">
				<cusSelectTeacher
					v-model="form.user_obj"
					placeholder="请选择人员"
					width="100%"
					:disabled="mode === 'edit'"
					:multiple="false"
				></cusSelectTeacher>
			</el-form-item>
			<el-form-item label="权限范围" prop="authority_type">
				<el-radio-group v-model="form.authority_type">
					<el-radio-button label="选择场室" :value="2" />
					<el-radio-button label="全部场室" :value="1" />
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="form.authority_type === 2" label="场室范围" prop="room_list">
				<cusSelectField
					v-model="form.room_list"
					width="100%"
					:multiple="true"
					placeholder="请选择场室范围"
				></cusSelectField>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import CusSelectTeacher from '@/components/custom/cusSelectTeacher.vue'

const { tenantId, campusId } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		authority_type: 2,
		user_id: null,
		user_obj: null,
		room_id: [],
		room_list: []
	}
}

export default {
	components: { CusSelectTeacher },
	emits: ['success', 'closed'],
	props: {},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增场室权限',
				edit: '编辑场室权限',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			enumConfig: [],
			filterDisable: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				user_obj: [{ required: true, message: '请选择人员' }],
				authority_type: [{ required: true, message: '请选择权限范围' }],
				room_list: [{ required: true, message: '请选择场室' }]
			}
		}
	},
	created() {
		cusTom.getEnumConfig().then((res) => {
			this.enumConfig = res
		})
	},
	watch: {},
	mounted() {},
	methods: {
		delItem(index) {
			if (this.form.action_list.length > 1) {
				this.form.action_list.splice(index, 1)
				this.selectTriggerTslInfo.splice(index, 1)
				this.triggerWriterTsl.splice(index, 1)
				this.triggerDeviceList.splice(index, 1)
			}
		},
		//显示
		open(mode = 'add', campus_id) {
			this.form.campus_id = campus_id
			this.mode = mode
			this.visible = true
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					let subForm
					if (this.mode === 'add') {
						subForm = { ...this.params, ...this.form }
					} else if (this.mode === 'edit') {
						subForm = { ...this.form }
					}
					if (this.form.room_list && this.form.room_list.length > 0) {
						let room_id = []
						this.form.room_list.forEach((item) => {
							room_id.push(item.id)
						})
						subForm.room_id = room_id
					} else {
						subForm.room_id = []
					}
					if (this.form.user_obj && this.form.user_obj.length > 0) {
						subForm.user_id = this.form.user_obj[0].id
					} else {
						subForm.user_id = 0
					}
					var res = await this.$LotApi.authority.save.post(subForm)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		async setData(data) {
			Object.assign(this.form, data)
			let room_list = []
			if (data && data.room_info) {
				data.room_info.forEach((item) => {
					room_list.push({ label: item.name, id: item.id })
				})
			}
			this.form.room_list = room_list
			let user_obj = []
			if (data && data.user_info) {
				user_obj.push({ label: data.user_info.name, id: data.user_info.id })
			}
			this.form.user_obj = user_obj
		}
	}
}
</script>

<style scoped lang="scss">
:deep(.el-card__header) {
	font-size: 14px;
}
.triggerItem {
	border: 1px dashed var(--el-color-primary);
	padding: 10px 10px 0px 10px;
	margin-bottom: 15px;
	border-radius: 6px;
	position: relative;
}
.delAction {
	position: absolute;
	right: 10px;
	bottom: 18px;
}
</style>
