<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" destroy-on-close width="400" @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'">
			<el-row>
				<el-form-item label="人员类型" prop="user_type">
					<el-radio-group v-model="form.user_type" @change="userTypeChange">
						<el-radio-button :label="1"> 学生 </el-radio-button>
						<el-radio-button :label="2"> 教职工 </el-radio-button>
					</el-radio-group>
				</el-form-item>
			</el-row>
			<el-form-item label="人员" prop="user_idObj">
				<cusSelectTeacher v-if="form.user_type == 2" v-model="form.user_idObj"></cusSelectTeacher>
				<cusSelectStudent v-if="form.user_type == 1" v-model="form.user_idObj"></cusSelectStudent>
			</el-form-item>
			<el-form-item label="学年" prop="academic_id">
				<el-select v-model="form.academic_id" placeholder="请选择学年" filterable clearable>
					<el-option v-for="item in getAcademic_year" :key="item.code" :label="item.name" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="学期" prop="semester_id">
				<el-select v-model="form.semester_id" placeholder="请选择学期" filterable clearable>
					<el-option v-for="item in getSemester" :key="item.code" :label="item.name" :value="item.value" />
				</el-select>
			</el-form-item>

			<el-form-item label="床位号" prop="bed_number">
				<el-input-number v-model="form.bed_number" :min="1" />
			</el-form-item>
			<el-form-item label="住宿类型" prop="accommodation_type">
				<el-radio-group v-model="form.accommodation_type">
					<el-radio-button :label="1">全寄</el-radio-button>
					<el-radio-button :label="2">半寄</el-radio-button>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="寝室长" prop="is_head">
				<el-switch v-model="form.is_head" :active-value="1" :inactive-value="-1"></el-switch>
			</el-form-item>
			<el-form-item v-if="form.user_type == 2" label="入住时间" prop="checkin_time">
				<el-date-picker
					v-model="form.checkin_time"
					type="date"
					:placeholder="'请选择日期'"
					format="YYYY-MM-DD"
					value-format="YYYY-MM-DD"
				/>
			</el-form-item>

			<el-form-item label="备注" prop="remark">
				<el-input v-model="form.remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import CusCascader from '@/components/custom/cusCascader.vue'
import cusTom from '@/utils/cusTom'
const { semesterInfo } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		tenant_id: null,
		campus_id: null,
		dormitory_room_id: null,
		user_type: 1, //1学员 2.教职工
		academic_id: null, //学年
		semester_id: null, //学期
		user_idObj: [], //住宿人员
		user_id: null, //住宿人员
		bed_number: 1, //床位号
		accommodation_type: null, //住宿类型
		is_head: null, //是否宿舍长
		checkin_time: null, //入住时间
		remark: null //备注
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		disciplineOptions: {
			type: Object,
			default: () => []
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				user_type: [{ required: true, message: '请选择' }],
				user_idObj: [{ required: true, message: '请选择人员' }],
				academic_id: [{ required: true, message: '请选择' }],
				semester_id: [{ required: true, message: '请选择' }],
				bed_number: [{ required: true, message: '请输入' }],
				accommodation_type: [{ required: true, message: '请选择' }],
				checkin_time: [{ required: true, message: '请选择' }]
			},
			semesterInfo,
			course: [],
			discipline: [],
			treeData: [],
			position: []
		}
	},
	computed: {
		getAcademic_year() {
			return this.semesterInfo.filter((v) => v.parent_id == 0 && v.campus_id == this.form.campus_id)
		},
		getSemester() {
			return this.semesterInfo.filter(
				(v) => v.parent_id != 0 && v.parent_id == this.form.academic_id && v.campus_id == this.form.campus_id
			)
		}
	},
	mounted() {},
	created() {},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			this.form.dormitory_room_id = this.params.room_id

			return this
		},
		userTypeChange(val) {
			this.form = defaultData()
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			this.form.dormitory_room_id = this.params.room_id
			this.form.user_type = val
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					console.log(this.form.user_idObj)
					this.form.user_id = this.form.user_idObj[0].id
					var res = await this.$API.buildingRooms.personnel.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			if (data && data.user_type == 1) {
				this.form.user_idObj = [
					{
						id: data.user_id,
						label: data.student_info.student_name
					}
				]
			}
			if (data && data.user_type == 2) {
				this.form.user_idObj = [
					{
						id: data.user_id,
						label: data.staff_info.name
					}
				]
			}
		}
	},
	components: { CusCascader }
}
</script>

<style></style>
