import config from '@/config'
import http from '@/utils/request'

export default {
	recruit: {
		list: {
			url: `${config.API_URL}/finapi/item/list`,
			name: '获取缴费项列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		add: {
			url: `${config.API_URL}/finapi/item/creat`,
			name: '新增缴费项',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		edit: {
			url: `${config.API_URL}/finapi/item/edit`,
			name: '修改缴费项',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		cancel: {
			url: `${config.API_URL}/finapi/item/cancel`,
			name: '取消缴费项',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/finapi/item/del`,
			name: '删除缴费项',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		typeList: {
			url: `${config.API_URL}/finapi/item_type/all`,
			name: '获取缴费项类别',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		typeAdd: {
			url: `${config.API_URL}/finapi/item_type/creat`,
			name: '新增缴费项类型',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		typeEdit: {
			url: `${config.API_URL}/finapi/item_type/edit`,
			name: '编辑缴费项类型',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		typeDel: {
			url: `${config.API_URL}/finapi/item_type/del`,
			name: '删除缴费项类型',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		orderList: {
			url: `${config.API_URL}/finapi/order/list`,
			name: '获取缴费单列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		orderDetail: {
			url: `${config.API_URL}/finapi/order/one`,
			name: '获取缴费单列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		orderDetailList: {
			url: `${config.API_URL}/finapi/order_detail/list`,
			name: '获取缴费单明细列表',
			get: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, data)
			}
		},
		orderDetailStudent: {
			url: `${config.API_URL}/finapi/order_detail/creat`,
			name: '增加缴费学员',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		orderDetailDel: {
			url: `${config.API_URL}/finapi/order_detail/del`,
			name: '删除缴费单明细',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		orderQrpay: {
			url: `${config.API_URL}/finapi/order/qrpay`,
			name: '支付二维码',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		orderQuery: {
			url: `${config.API_URL}/finapi/order/payQuery`,
			name: '支付查询',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		refund: {
			url: `${config.API_URL}/finapi/order/refund`,
			name: '退款',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		refundQuery: {
			url: `${config.API_URL}/finapi/order/refund_query`,
			name: '退款查询',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		orderPublish: {
			url: `${config.API_URL}/finapi/order/publish`,
			name: '发布缴费单',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		orderAdd: {
			url: `${config.API_URL}/finapi/order/creat`,
			name: '新增缴费单',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		orderEdit: {
			url: `${config.API_URL}/finapi/order/edit`,
			name: '编辑缴费单',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		orderCancel: {
			url: `${config.API_URL}/finapi/order/cancel`,
			name: '作废缴费单',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		orderDel: {
			url: `${config.API_URL}/finapi/order/del`,
			name: '删除缴费单',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		// 收支管理
		recordList: {
			url: `${config.API_URL}/finapi/record/list`,
			name: '获取收支列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		recordAdd: {
			url: `${config.API_URL}/finapi/record/creat`,
			name: '新增收支',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		recordCancal: {
			url: `${config.API_URL}/finapi/record/cancal`,
			name: '作废收支',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		recordTypeList: {
			url: `${config.API_URL}/finapi/record_type/all`,
			name: '收支类型',
			get: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, data)
			}
		},
		recordTypeAdd: {
			url: `${config.API_URL}/finapi/record_type/creat`,
			name: '新增收支',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		recordTypeEdit: {
			url: `${config.API_URL}/finapi/record_type/edit`,
			name: '编辑收支',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		},
		recordTypeDel: {
			url: `${config.API_URL}/finapi/record_type/del`,
			name: '删除收支',
			post: async function (data = {}) {
				// eslint-disable-next-line no-return-await
				return await http.post(this.url, data)
			}
		}
	}
}
