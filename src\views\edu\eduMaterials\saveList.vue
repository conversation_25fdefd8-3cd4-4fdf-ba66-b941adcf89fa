<template>
	<el-drawer v-model="visible" :title="type[model]" destroy-on-close size="50%">
		<el-form ref="formref" :model="form" :rules="rules">
			<el-row>
				<el-col :span="12">
					<el-form-item prop="is_video" label="是否视频" label-width="130px">
						<el-radio-group v-model="form.is_video">
							<el-radio :label="1">是</el-radio>
							<el-radio :label="-1">否</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item prop="recommend" label="是否推荐" label-width="130px">
						<el-radio-group v-model="form.recommend">
							<el-radio :label="1">是</el-radio>
							<el-radio :label="-1">否</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item prop="is_public" label="是否公开" label-width="130px">
						<el-radio-group v-model="form.is_public">
							<el-radio :label="1">是</el-radio>
							<el-radio :label="-1">否</el-radio>
						</el-radio-group>
					</el-form-item>
					<el-form-item v-show="form.is_public !== 1" label="密码" label-width="130px">
						<el-input
							v-model="form.password"
							placeholder="请输入密码"
							clearable
							show-password
							style="width: 150px"
						></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item prop="allow_download" label="是否下载" label-width="130px">
						<el-radio-group v-model="form.allow_download">
							<el-radio :label="1">是</el-radio>
							<el-radio :label="-1">否</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="学科" label-width="130px">
						<el-select v-model="form.course_id" clearable placeholder="请选择学科">
							<el-option v-for="item in getCourse" :key="item.id" :label="item.course_name" :value="item.id">
							</el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="专辑类型" prop="type_id" label-width="130px">
						<el-cascader
							v-model="form.type_id"
							:options="treeList"
							clearable
							:props="{ label: 'type_name', children: 'child', value: 'id', expandTrigger: 'hover' }"
						></el-cascader>
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item label="教材名称" prop="name" label-width="130px">
				<el-input v-model="form.name" placeholder="请输入教材名称"></el-input>
			</el-form-item>
			<el-form-item label="封面" label-width="130px">
				<scUpload v-model="form.cover_url" fileTypeTag="eduMaterials"></scUpload>
			</el-form-item>
			<Editor ref="editorRef" v-model:description="form.description"></Editor>
			<el-form-item v-show="form.is_video !== 1" label="教材上传" labelWidth="130px">
				<scUploadFile v-model="form.obj" fileTypeTag="eduMaterials" :allowSuffix="allowSuffix" accept="*" @suc="handlerSuc"></scUploadFile>
			</el-form-item>
			<el-form-item v-show="form.is_video === 1" label="视频上传" labelWidth="130px">
				<el-upload
					v-model="form.obj"
					show-file-list
					accept="video/*"
					:file-list="form.obj?[{name:form.obj,url:form.obj}]:[]"
					:http-request="uploadRequest"
					:before-upload="beforeUploadHandler"
					:on-change="changeHandler"
					:on-exceed="exceedHandler"
					:limit="1"
					:on-remove="removeHandler"
				>
					<template #trigger>
						<el-button type="primary" plain :disabled="uploadConfig.choiseDisabled" style="margin-right: 10px"
							>选取文件
						</el-button>
					</template>
					<el-button type="success" :disabled="uploadConfig.uploadDisabled" @click="authUpload">开始上传</el-button>
					<el-button type="danger" :disabled="uploadConfig.pauseDisabled" @click="pauseUpload">暂停上传</el-button>
					<el-button type="primary" :disabled="uploadConfig.resumeDisabled" @click="resumeUpload">恢复上传</el-button>
					<el-progress v-show="uploadConfig.status === 1" :percentage="percentage" class="progress" />
				</el-upload>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :disabled="status === 0" :loading="isSaveing" @click="save">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, computed, onMounted } from 'vue'
import { getMediaLimitInfo } from '@/utils/mediaLib'
import { cloneDeep} from 'lodash'

// 引入阿里云视频上传sdk
import '@/utils/sdk/lib/aliyun-oss-sdk-6.17.1.min.js'
import '@/utils/sdk/aliyun-upload-sdk-1.5.7.min.js'
import Editor from './editor.vue'
const props = defineProps({
	params: {
		type: Object,
		default: () => {}
	},
	disciplineOptions: {
		type: Array,
		default: () => {
			return []
		}
	},
	treeCurrentId: {
		type: Number,
		default: 0
	},
	course: {
		type: Array,
		default: () => {
			return []
		}
	}
})
const allowSuffix = ref([])
const rules = reactive({
	is_video: [{ required: true, message: '请选择教材是否为视频', trigger: 'change' }],
	is_public: [{ required: true, message: '请选择是否公开', trigger: 'change' }],
	recommend: [{ required: true, message: '请选择是否推荐', trigger: 'change' }],
	allow_download: [{ required: true, message: '请选择是否可下载', trigger: 'change' }],
	type_id: [{ required: true, message: '请选择专辑类型', trigger: 'change' }],
	password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
	name: [{ required: true, message: '请输入教材名称', trigger: 'blur' }]
})
onMounted(() => {
	allowSuffix.value = getMediaLimitInfo().webOffice
})
const emit = defineEmits(['success'])
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
import { ElMessage } from 'element-plus'
const defaultData = () => {
	return {
		id: null,
		tenant_id: null,
		campus_id: null,
		is_video: 1,
		type_id: [],
		discipline_id: null,
		course_id: null,
		name: '',
		description: '',
		video_vod_id: '',
		size: null,
		duration: 0,
		obj: '',
		cover_url: '',
		recommend: 1,
		is_public: 1,
		password: '',
		allow_download: 1
	}
}
// 获取视频上传凭证字段
const videoData = ref({
	tenant_id: null,
	campus_id: null,
	file_name: '',
	name: '',
	cover_url: '',
	description: ''
})
const uploadConfig = reactive({
	timeout: '',
	partSize: '',
	parallel: '',
	retryCount: '',
	retryDuration: '',
	region: 'cn-shanghai',
	userId: '1303984639806000',
	file: null,
	uploadDisabled: true,
	resumeDisabled: true,
	pauseDisabled: true,
	choiseDisabled: false,
	uploader: null,
	status: 0,
	statusText: ''
})
// 凭证字段
const videoInfo = ref({
	upload_auth: '',
	upload_address: '',
	video_vod_id: ''
})
let status = ref(0)
let percentage = ref(0)
let model = ref('add')
let isSaveing = ref(false)
let type = { add: '新增', edit: '修改' }
let formref = ref(null)
let editorRef = ref(null)
const form = ref(defaultData())
let visible = ref(false)
let treeList = ref([])
const open = async (mode = 'add', treeData) => {
	treeList.value = cloneDeep(treeData).slice(1)
	if (mode === 'add') {
		form.value = defaultData()
		status.value = 0
		console.log(form.value)
		// debugger
	}
	if(mode === 'edit'){
		status.value = 1
	}
	model.value = mode
	form.value.tenant_id = props.params.tenant_id
	form.value.campus_id = props.params.campus_id
	form.value.type_id = props.treeCurrentId
	videoData.value.tenant_id = props.params.tenant_id
	videoData.value.campus_id = props.params.campus_id
	/*const {
		data: { rows }
	} = await globalPropValue.eduMaterials.material.list.get(props.params)
	form.value.type_id = rows?.map((v) => v.type_id)[0]*/
}
const getCourse = computed(() => {
	return props.course.filter((v) => {
		return v.campus_id === form.value.campus_id
	})
})
const removeHandler=(file)=>{
	if(file.status === 'success'){
		form.value.obj = ''
		status.value = 0
	}else{
		uploadConfig.uploadDisabled = true
	}
	console.log(file,'视频remove事件')
}
// 视频文件改变事件
const changeHandler = (file) => {
	//判断视频类型
	videoData.value.file_name = file.name
	videoData.value.name = file.name.split('.')[0]
	const validMimeTypes = [
		'video/mp4',
		'video/ogg',
		'video/webm',
		'video/avi',
		'video/quicktime',
		'video/x-msvideo',
		'video/x-ms-wmv',
		'video/mpeg',
		'video/3gpp',
		'video/3gpp2',
		'video/mov',
		'video/flv',
		'video/rmvb',
		'video/wmv'
	]
	if (!validMimeTypes.includes(file.raw.type)) {
		return false
	}
	// 创建一个Video元素
	const video = document.createElement('video')
	// 设置video的src为上传文件的地址
	video.src = URL.createObjectURL(file.raw)
	// 加载元数据完成后获取视频长度
	video.onloadedmetadata = () => {
		// 获取视频时长（单位：秒）
		form.value.duration = video.duration
	}
	uploadConfig.file = file.raw
	if (!uploadConfig.file) {
		ElMessage.error('请先选择文件')
		return false
	}
	// let Title = uploadConfig.affairFile.name
	let userData = '{"Vod":{}}'
	if (uploadConfig.uploader) {
		uploadConfig.uploader.stopUpload()
		uploadConfig.statusText = ''
	}
	uploadConfig.uploader = createUploader()
	uploadConfig.uploader.addFile(uploadConfig.file, null, null, null, userData)
	uploadConfig.uploadDisabled = false
	uploadConfig.pauseDisabled = true
	uploadConfig.resumeDisabled = true
}
// 保存按钮事件
const save = async () => {
	await formref.value.validate()
	isSaveing.value = true
	if (form.value.type_id instanceof Array) {
		form.value.type_id = form.value.type_id[form.value.type_id.length - 1]
	}
	console.log(form.value)
	const res = await globalPropValue.eduMaterials.material.materials_add.post(form.value)
	isSaveing.value = false
	if (res.code === 200) {
		emit('success', form.value, model.value)
		ElMessage({ type: 'success', message: `${model.value === 'add' ? '新增成功' : '修改成功'}` })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
	visible.value = false
}
// 自定义上传事件
const uploadRequest = () => {}
// 开始上传事件
const authUpload = () => {
	// 然后调用 startUpload 方法, 开始上传
	if (uploadConfig.uploader !== null) {
		uploadConfig.status = 1
		uploadConfig.uploader.startUpload()
		uploadConfig.choiseDisabled = true
		uploadConfig.uploadDisabled = true
		uploadConfig.pauseDisabled = false
	}
}
// 暂停上传事件
const pauseUpload = () => {
	if (uploadConfig.uploader !== null) {
		uploadConfig.uploader.stopUpload()
		uploadConfig.resumeDisabled = false
		uploadConfig.pauseDisabled = true
	}
}
// 恢复上传事件
const resumeUpload = () => {
	if (uploadConfig.uploader !== null) {
		this.uploader.startUpload()
		uploadConfig.resumeDisabled = true
		uploadConfig.pauseDisabled = false
	}
}
// 视频上传前钩子
const beforeUploadHandler = (file) => {
	videoData.value.file_name = file.name
	videoData.value.name = file.name.split('.')[0]
	const validMimeTypes = [
		'video/mp4',
		'video/ogg',
		'video/webm',
		'video/avi',
		'video/quicktime',
		'video/x-msvideo',
		'video/x-ms-wmv',
		'video/mpeg',
		'video/3gpp',
		'video/3gpp2',
		'video/mov',
		'video/flv',
		'video/rmvb',
		'video/wmv'
	]
	if (!validMimeTypes.includes(file.type)) {
		ElMessage({ type: 'warning', message: '请上传视频格式文件' })
		return false
	}
}
// 超出文件限制执行事件
const exceedHandler = () => {
	ElMessage.warning('暂不支持多文件上传')
}
// 获取视频上传凭证
const getUploadAuth = async () => {
	const res = await globalPropValue.eduMaterials.material.get_auth.post(videoData.value)
	Object.assign(videoInfo.value, res.data)
	form.value.video_vod_id = res.data.video_vod_id
}
// 刷新视频上传凭证
const refreshUploadAuth = async () => {
	const res = await globalPropValue.eduMaterials.material.refresh_auth.post({
		tenant_id: props.params.tenant_id,
		campus_id: props.params.campus_id,
		video_vod_id: props.params.video_vod_id
	})
	Object.assign(videoInfo.value, res.data)
}
// 声明初始化回调
const createUploader = () => {
	let uploader = new AliyunUpload.Vod({
		timeout: uploadConfig.timeout || 60000,
		partSize: Math.round(uploadConfig.partSize || 1048576),
		parallel: uploadConfig.parallel || 5,
		retryCount: uploadConfig.retryCount || 3,
		retryDuration: uploadConfig.retryDuration || 2,
		region: uploadConfig.region,
		userId: uploadConfig.userId,
		localCheckpoint: true, //此参数是禁用服务端缓存，不影响断点续传
		//添加文件成功
		addFileSuccess: function (uploadInfo) {
			uploadConfig.uploadDisabled = false
			uploadConfig.resumeDisabled = true
			uploadConfig.statusText = '添加文件成功, 等待上传...'
			console.log('addFileSuccess: ' + uploadInfo.file.name)
		},
		// 开始上传
		onUploadstarted: async function (uploadInfo) {
			// 如果是 UploadAuth 上传方式, 需要调用 uploader.setUploadAuthAndAddress 方法
			// 如果是 UploadAuth 上传方式, 需要根据 uploadInfo.videoId是否有值，调用点播的不同接口获取uploadauth和uploadAddress
			// 如果 uploadInfo.videoId 有值，调用刷新视频上传凭证接口，否则调用创建视频上传凭证接口
			if (!uploadInfo.videoId) {
				await getUploadAuth()
				console.log(videoInfo.value)
				uploader.setUploadAuthAndAddress(
					uploadInfo,
					videoInfo.value.upload_auth,
					videoInfo.value.upload_address,
					videoInfo.value.video_vod_id
				)
				uploadConfig.statusText = '文件开始上传...'
				console.log(
					'onUploadStarted:' +
						uploadInfo.file.name +
						', endpoint:' +
						uploadInfo.endpoint +
						', bucket:' +
						uploadInfo.bucket +
						', object:' +
						uploadInfo.object
				)
			} else {
				await refreshUploadAuth()
				uploader.setUploadAuthAndAddress(
					uploadInfo,
					videoInfo.value.upload_auth,
					videoInfo.value.upload_address,
					videoInfo.value.video_id
				)
			}
		},
		// 文件上传成功
		onUploadSucceed: function (uploadInfo) {
			console.log(
				'onUploadSucceed: ' +
					uploadInfo.file.name +
					', endpoint:' +
					uploadInfo.endpoint +
					', bucket:' +
					uploadInfo.bucket +
					', object:' +
					uploadInfo.object
			)
			status.value = 1
			uploadConfig.status = 0 // 上传完成调整进度条状态
			uploadConfig.choiseDisabled = false
			form.value.obj = uploadInfo.object
			uploadConfig.statusText = '文件上传成功...'
		},
		// 文件上传失败
		onUploadFailed: function (uploadInfo, code, message) {
			console.log('onUploadFailed: affairFile:' + uploadInfo.file.name + ',code:' + code + ', message:' + message)
			uploadConfig.statusText = '文件上传失败!'
		},
		// 取消文件上传
		onUploadCanceled: function (uploadInfo, code, message) {
			console.log('Canceled affairFile: ' + uploadInfo.file.name + ', code: ' + code + ', message:' + message)
			uploadConfig.statusText = '文件已暂停上传'
		},
		// 文件上传进度，单位：字节, 可以在这个函数中拿到上传进度并显示在页面上
		onUploadProgress: function (uploadInfo, totalSize, progress) {
			console.log(
				'onUploadProgress:affairFile:' +
					uploadInfo.file.name +
					', fileSize:' +
					totalSize +
					', percent:' +
					Math.ceil(progress * 100) +
					'%'
			)
			form.value.size = totalSize
			percentage.value = progress * 100
			uploadConfig.statusText = '文件上传中...'
		},
		// 上传凭证超时
		onUploadTokenExpired: function (uploadInfo) {
			// 上传大文件超时, 如果是上传方式一即根据 UploadAuth 上传时
			// 需要根据 uploadInfo.videoId 调用刷新视频上传凭证接口(https://help.aliyun.com/document_detail/55408.html)重新获取 UploadAuth
			// 然后调用 resumeUploadWithAuth 方法
			refreshUploadAuth()
			uploader.resumeUploadWithAuth(videoInfo.value.upload_auth)
			console.log('upload expired and resume upload with uploadauth ' + uploadInfo)
			uploadConfig.statusText = '上传凭证超时, 重新获取上传凭证并继续上传...'
		},
		// 全部文件上传结束
		onUploadEnd: function () {
			console.log('onUploadEnd: uploaded all the files')
			uploadConfig.statusText = '文件上传完毕'
		}
	})
	return uploader
}
const handlerSuc = (value) => {
	status.value = 1
	form.value.obj = value.key
	form.value.size = value.size
	form.value.duration = 0
}
// 表单数据注入
const setData = (data) => {
	Object.assign(form.value, data)
}
defineExpose({
	visible,
	open,
	setData
})
</script>

<style lang="scss" scoped>
.progress {
	margin-top: 30px;
}
</style>
