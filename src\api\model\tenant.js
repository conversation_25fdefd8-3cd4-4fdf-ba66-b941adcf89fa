import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/sysapi/tenant/list`,
		name: '获取学校列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	all: {
		url: `${config.API_URL}/sysapi/tenant/all`,
		name: '获取学校列表不分页',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	listOne: {
		url: `${config.API_URL}/sysapi/tenant/getOne`,
		name: '获取单个学校',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/sysapi/tenant/save`,
		name: '新增学校/修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	edit: {
		url: `${config.API_URL}/sysapi/tenant/changeStatus`,
		name: '修改学校状态',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	getTenant: {
		url: `${config.API_URL}/sysapi/tenant/getOne`,
		name: '获取学校信息',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	permissions: {
		//url: `${config.MOCK_URL}/system/menu/list`,
		url: `${config.API_URL}/sysapi/tenant/permissions`,
		name: '获取/保存学校权限',
		get: async function (tenantId) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, { tenantId })
		},
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
}
