<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode == 'show'">
			<el-form-item label="学科名称" prop="course_name">
				<el-input v-model="form.course_name" placeholder="请输入名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="课程体系" prop="discipline_id">
				<el-select v-model="form.discipline_id" placeholder="请选择" filterable clearable>
					<el-option
						v-for="item in disciplineOptions"
						:key="item.id"
						:label="item.discipline_name"
						:value="item.id"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="性质" prop="course_type">
				<el-radio-group v-model="form.course_type">
					<el-radio :label="1">必修</el-radio>
					<el-radio :label="2">选修</el-radio>
				</el-radio-group>
			</el-form-item>
			<!-- <el-form-item label="是否有效" prop="status">
				<el-switch v-model="form.status" :active-value="1" :inactive-value="-1"></el-switch>
			</el-form-item> -->
			<el-form-item label="学分" prop="course_score">
				<el-input-number v-model="form.course_score" :min="1"></el-input-number>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input v-model="form.remark" clearable type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const defaultData = () => {
	return {
		course_name: '',
		status: 1,
		remark: '',
		course_score: 10,
		course_type: 1,
		tenant_id: '',
		campus_id: '',
		discipline_id: ''
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		disciplineOptions: {
			type: Object,
			default: () => []
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				listorder: [{ required: true, message: '请输入排序', trigger: 'change' }],
				course_name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
			}
		}
	},
	mounted() {},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					this.form.discipline_id = Number(this.form.discipline_id)
					var res = await this.$API.eduCourseSet.course.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			// this.form.id = data.id
			// this.form.label = data.label
			// this.form.status = data.status
			// this.form.sort = data.sort
			// this.form.parentId = data.parentId
			// this.form.remark = data.remark

			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			if (data.discipline_id === 0) {
				data.discipline_id = ''
			}
			Object.assign(this.form, data)
		}
	}
}
</script>

<style></style>
