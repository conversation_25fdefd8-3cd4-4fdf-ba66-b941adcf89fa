<template>
	<div class="center-box">
		<div
			v-if="canvasConfig.show === true"
			class="canvas-box"
			:style="{
				width: canvasConfig.width + 'px',
				height: canvasConfig.height + 'px',
				scale: scale / 100
			}"
		>
			<template v-for="(item, index) in certContent" :key="index">
				<vue-draggable-resizable
					v-model:w="item.w"
					v-model:h="item.h"
					v-model:x="item.x"
					v-model:y="item.y"
					v-model:active="item.active"
					:min-width="20"
					:min-height="20"
					:draggable="draggable"
					:resizable="resizable"
					:grid="[10,10]"
					:parent="true"
					:scale="scale / 100"
					:lockAspectRatio="item.scale"
					:z-index="index"
					:prevent-deactivation="preventDeactivation"
					v-on:dragging="onDrag"
					v-on:resizing="onResize"
					@activated="onActivated(item)"
				>
					<div v-if="item.type === 'image' && item.field !== 'qrcode'" class="drag-img">
						<img :src="item.content" />
					</div>
					<div v-else-if="item.type === 'image' && item.field === 'qrcode'" class="qrcode">
						<sc-qr-code :text="item.content"></sc-qr-code>
					</div>
					<div v-else class="drag-item">
						<div v-html="item.content"></div>
					</div>
				</vue-draggable-resizable>
			</template>
		</div>
	</div>
	<div class="scale">
		<el-button-group>
			<el-button
				size="small"
				type="info"
				icon="el-icon-zoom-out"
				@click="scale > 10 ? (scale = scale - 10) : (scale = 10)"
			></el-button>
			<el-button type="info" plain size="small">{{ scale }}</el-button>
			<el-button
				size="small"
				type="info"
				icon="el-icon-zoom-in"
				@click="scale < 100 ? (scale = scale + 10) : (scale = 100)"
			></el-button>
		</el-button-group>
	</div>
</template>

<script>
import VueDraggableResizable from 'vue-draggable-resizable'
import { nextTick } from 'vue'

export default {
	components: {
		VueDraggableResizable
	},
	props: {
		draggable: {
			type: Boolean,
			default: true
		},
		resizable: {
			type: Boolean,
			default: true
		},
		preventDeactivation: {
			type: Boolean,
			default: true
		},
		list: {
			type: Object,
			default: () => ({})
		},
		canvasConfig: {
			type: Object,
			default: () => ({
				width: 600,
				height: 400
			})
		}
	},
	data() {
		return {
			scale: 100,
			certContent: this.list,
			currentNode: {}
		}
	},
	watch: {
		list: {
			handler(newVal) {
				this.certContent = newVal
			},
			deep: true
		}
	},
	mounted() {
		nextTick(() => {
			this.$emit('generateAction')
		})
	},
	methods: {
		onActivated(e) {
			if (this.draggable === false || this.resizable === false) {
				return false
			}
			/*this.currentNode = null
			this.$emit('activatedAction', this.currentNode)*/
			this.$nextTick(() => {
				this.currentNode = e
				this.$emit('activatedAction', this.currentNode)
			})
		},
		onResize(x, y, width, height) {
			if (this.resizable === false) {
				return false
			}
			this.certContent.forEach((item) => {
				if (item.id === this.currentNode.id) {
					item.x = x
					item.y = y
					item.w = width
					item.h = height
				}
			})
		},
		onDrag(x, y) {
			if (this.draggable === false) {
				return false
			}
			this.certContent.forEach((item) => {
				if (item.id === this.currentNode.id) {
					item.x = x
					item.y = y
				}
			})
		}
	}
}
</script>
<style lang="scss">
@import 'vue-draggable-resizable/style.css';

.center-box {
	position: relative;
	height: 100%;
	background: #f2f3f5;
	border-radius: 6px;
	overflow: auto;
}

.canvas-box {
	background-size: 100% 100%;
	background: #dedede no-repeat;
	border-radius: 6px;
	margin: 0 auto;
}

.vdr {
	border: unset;
}

.canvas-box .active {
	border: 1px dashed #00b42a;

	.handle {
		border: 1px solid #00b42a;
	}

	.handle-bm {
		bottom: -5px;
	}

	.handle-br {
		right: -5px;
		bottom: -5px;
	}

	.handle-bl {
		left: -5px;
		bottom: -5px;
	}

	.handle-tl {
		left: -5px;
		top: -5px;
	}

	.handle-tm {
		top: -5px;
	}

	.handle-tr {
		top: -5px;
		right: -5px;
	}

	.handle-mr {
		right: -5px;
	}

	.handle-ml {
		left: -5px;
	}
}

.drag-item {
	font-size: 14px;
}

.qrcode {
	padding: 5px;
	background: #fff;
	width: 100%;
	height: 100%;

	img {
		width: 100%;
		height: 100%;
	}
}

.drag-img {
	background-repeat: no-repeat;
	background-size: 100% 100%;
	width: 100%;
	height: 100%;

	img {
		width: 100%;
		height: 100%;
	}
}

.scale {
	position: absolute;
	left: auto;
	bottom: 10px;
	/* width: 200px; */
	right: 15px;
}
</style>
