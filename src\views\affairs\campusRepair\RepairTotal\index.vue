<template>
	<el-container>
		<el-header>
			<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"></cusForm>
		</el-header>
		<div class="echartsOut">
			<div>
				<scEcharts class="scEcharts" :option="option" width="100%" height="300px"></scEcharts>
			</div>
			<div>
				<scEcharts class="scEcharts" :option="option1" width="100%" height="300px"></scEcharts>
			</div>

			<div>
				<scEcharts class="scEcharts" :option="option2" width="100%" height="300px"></scEcharts>
			</div>
			<div>
				<scEcharts class="scEcharts" :option="option3" width="100%" height="300px"></scEcharts>
			</div>
			<div>
				<scEcharts class="scEcharts" :option="option4" width="100%" height="300px"></scEcharts>
			</div>
			<div>
				<scEcharts class="scEcharts" :option="option5" width="100%" height="300px"></scEcharts>
			</div>
		</div>
	</el-container>
</template>

<script>
import scEcharts from '@/components/scEcharts'
import cusTom from '@/utils/cusTom'

const { campusId, tenantId, campusInfo, repairTypeMap } = cusTom.getBaseQuery()

import { getMonthRange } from '@/utils/dayjs'

const defaultData = () => {
	return {
		begin_date: null,
		end_date: null,
		date: getMonthRange(),
		tenant_id: tenantId,
		campus_id: campusId,
		name: null
	}
}

export default {
	name: '',
	components: { scEcharts },
	watch: {
		'params.date': {
			handler(newVal) {
				if (newVal) {
					this.params.begin_date = newVal[0]
					this.params.end_date = newVal[1]
					this.getStatistics()
				}
			},
			deep: true,
			immidate: true
		},
		'params.campus_id': {
			handler(newVal) {
				if (newVal) {
					this.getStatistics()
				}
			}
		}
	},
	data() {
		return {
			params: defaultData(),
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'date',
						value: null,
						component: 'cusDate',
						options: {
							placeholder: '请选择校区',
							type: 'daterange'
						}
					}
				]
			},
			option: {
				title: {
					text: '受理情况',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						name: '受理情况',
						type: 'pie',
						radius: ['50%', '60%'],
						center: ['50%', '50%'],
						label: false,
						data: []
					}
				],
				legend: {
					bottom: '0'
				}
			},
			option1: {
				title: [],
				series: []
			},
			option2: {
				title: {
					text: '接单情况'
				},
				legend: {
					bottom: 0
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '25%',
					containLabel: true
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: []
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						name: '信息化报修',
						type: 'line',
						stack: 'Total',
						data: []
					},
					{
						name: '总务报修',
						type: 'line',
						stack: 'Total',
						data: []
					}
				],
				dataZoom: [
					{
						type: 'inside',
						start: 0,
						end: 100
					},
					{
						start: 0,
						end: 100,
						height: 20,
						bottom: '12%'
					}
				]
			},
			option3: {
				title: {
					text: '每天报修数量'
				},
				legend: {
					bottom: 0
				},
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow'
					}
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '25%',
					containLabel: true
				},
				xAxis: {
					type: 'category',
					boundaryGap: true,
					data: []
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						name: '信息化报修',
						type: 'bar',
						barMaxWidth: 30,
						stack: 'Total',
						data: []
					},
					{
						name: '总务报修',
						type: 'bar',
						barMaxWidth: 30,
						stack: 'Total',
						data: []
					}
				],
				dataZoom: [
					{
						type: 'inside',
						start: 0,
						end: 100
					},
					{
						start: 0,
						end: 100,
						height: 20,
						bottom: '12%'
					}
				]
			},
			option4: {
				title: {
					text: '响应时间平均值'
				},

				grid: {
					top: '20%',
					containLabel: true
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: []
				},
				yAxis: {
					name: '小时',
					type: 'value'
				},
				series: [
					{
						name: '响应时间',
						type: 'line',
						stack: 'Total',
						data: []
					}
				]
			},
			option5: {
				title: {
					text: '评价平均值'
				},

				grid: {
					top: '20%',
					containLabel: true
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: []
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						name: '评价平均值',
						type: 'line',
						stack: 'Total',
						data: []
					}
				]
			}
		}
	},
	created() {},
	methods: {
		async getStatistics() {
			var res = await this.$API.campusRepair.statistics.get(this.params)
			this.option.series[0].data = res.data.deal_situation.map((v) => {
				return {
					value: v.value,
					name: v.name
				}
			})
			var total=0
			res.data.deal_situation.map((v) => {
				total = total + v.value
			})
			console.log(total)
			var typeData = res.data.type_situation.map((v) => {
				return {
					value: v.value,
					name: v.name
				}
			})
			let { titleArr, seriesArr } = this.getCharts(total, typeData)
			titleArr.push({
				text: '报修类型'
			})
			this.option1.title = titleArr
			this.option1.series = seriesArr
			//接单情况
			this.option2.xAxis.data = res.data.deal_order_situation.map((v) => {
				return v.name
			})
			this.option2.xAxis.data = res.data.deal_order_situation.map((v) => {
				return v.name
			})
			this.option2.series[0].data = res.data.deal_order_situation.map((v) => {
				return v.information_value
			})
			this.option2.series[1].data = res.data.deal_order_situation.map((v) => {
				return v.affairs_value
			})

			this.option3.xAxis.data = res.data.day_repair_situation.map((v) => {
				return v.date
			})
			this.option3.series[0].data = res.data.day_repair_situation.map((v) => {
				return v.information_value
			})
			this.option3.series[1].data = res.data.day_repair_situation.map((v) => {
				return v.affairs_value
			})

			this.option4.xAxis.data = res.data.repair_response_situation.map((v) => {
				return v.name
			})
			this.option4.series[0].data = res.data.repair_response_situation.map((v) => {
				return v.value
			})

			this.option5.xAxis.data = res.data.repair_appraise_situation.map((v) => {
				return v.name
			})
			this.option5.series[0].data = res.data.repair_appraise_situation.map((v) => {
				return v.value
			})
		},
		getCharts(total, data) {
			var titleArr = [],
				seriesArr = [],
				colors = [
					['#389af4', '#dfeaff'],
					['#ff8c37', '#ffdcc3'],
					['#ffc257', '#ffedcc'],
					['#fd6f97', '#fed4e0'],
					['#a181fc', '#e3d9fe']
				]
			data.forEach(function (item, index) {
				titleArr.push({
					text: item.name,
					left: index * 30 + 33 + '%',
					bottom: '10%',
					textAlign: 'center',
					textStyle: {
						fontWeight: 'normal',
						fontSize: '16',
						color: colors[index][0],
						textAlign: 'center'
					}
				})
				seriesArr.push({
					name: item.name,
					type: 'pie',
					clockWise: false,
					radius: [60, 70],
					itemStyle: {
						normal: {
							color: colors[index][0],
							shadowColor: colors[index][0],
							shadowBlur: 0,
							label: {
								show: false
							},
							labelLine: {
								show: false
							}
						}
					},
					hoverAnimation: false,
					center: [index * 30 + 33 + '%', '50%'],
					data: [
						{
							value: item.value,
							label: {
								normal: {
									formatter: function (params) {
										return params.value + '次'
									},
									position: 'center',
									show: true,
									textStyle: {
										fontSize: '20',
										fontWeight: 'bold',
										color: colors[index][0]
									}
								}
							}
						},
						{
							value:  total-item.value,
							name: 'invisible',
							itemStyle: {
								normal: {
									color: colors[index][1]
								},
								emphasis: {
									color: colors[index][1]
								}
							}
						}
					]
				})
			})

			return {
				titleArr,
				seriesArr
			}
		}
	}
}
</script>

<style scoped lang="scss">
.echartsOut {
	flex: 1;
	margin-top: 10px;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-template-rows: repeat(3, 1fr);
	grid-column-gap: 10px;
	grid-row-gap: 10px;

	& > div {
		background-color: var(--el-bg-color-overlay);
		border-radius: 5px;
		padding: 10px;
	}
}

.scEcharts {
	width: 100%;
	height: 100%;
}
</style>
