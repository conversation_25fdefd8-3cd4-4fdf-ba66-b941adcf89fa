<template>
	<el-dialog v-model="visible" :title="type[model]" destroy-on-close width="25%">
		<el-form ref="formRef" :model="formData" :rules="rules">
			<el-form-item label="分类名称" prop="name">
				<el-input v-model="formData.name" placeholder="请输入"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button size="small" @click="visible = false">取消</el-button>
			<el-button type="primary" :loading="isSaveing" size="small" @click="save">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const defaultData = () => {
	return {
		id: null,
		tenant_id: null,
		campus_id: null,
		model: null,
		name: ''
	}
}
import { reactive, ref, getCurrentInstance } from 'vue'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['fileParams'])
const emit = defineEmits(['success'])
console.log(props)
let visible = ref(false)
const type = ref({ add: '新增', edit: '编辑' })
let model = ref('add')
let formRef = ref(null)
let isSaveing = ref(false)
const formData = ref(defaultData())
const rules = reactive({
	name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }]
})
const open = (mode = 'add') => {
	formData.value = defaultData()
	formData.value.campus_id = props.fileParams.campus_id
	formData.value.tenant_id = props.fileParams.tenant_id
	formData.value.model = props.fileParams.model
	model.value = mode
	visible.value = true
}
// 保存按钮事件
const save = async () => {
	await formRef.value.validate()
	isSaveing.value = true
	const res = await globalPropValue.fileManagement.file.file_type_add_edit.post(formData.value)
	if (res.code === 200) {
		isSaveing.value = false
		ElMessage.success(model.value === 'add' ? '添加成功' : '修改成功')
		emit('success', formData.value, model.value)
	} else {
		ElMessage.error(res.msg)
	}
	visible.value = false
}
// 表单数据注入
const setData = (data) => {
	Object.assign(formData.value, data)
}
defineExpose({
	open,
	setData
})
</script>

<style scoped></style>
