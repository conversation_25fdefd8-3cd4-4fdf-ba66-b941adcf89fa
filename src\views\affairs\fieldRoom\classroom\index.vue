<template>
	<el-container>
		<el-aside width="270px">
			<el-container>
				<el-header v-if="CampusManagementList.length > 1">
					<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-header>
				<el-main>
					<div class="add-lay">
						<el-button type="primary" icon="el-icon-plus" @click="addLou">新增建筑楼层</el-button>
					</div>
					<el-tree
						ref="group"
						class="menu"
						node-key="id"
						:data="groupsAdd"
						:highlight-current="true"
						:expand-on-click-node="false"
						:props="defaultProps"
						:default-expanded-keys="[0]"
						@node-click="groupClick"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node" @mouseenter="handleShowTools(data)" @mouseleave="handleShowTools(data)">
								<span>{{ node.label }}</span>
								<span v-if="data.id != 0 && data.id == currentId">
									<a v-if="data.parent_id <= 0" @click="appendCeng(data)">
										<el-icon><el-icon-plus /></el-icon>
									</a>
									<a style="margin-left: 8px" @click.stop="edit(node, data)">
										<el-icon><el-icon-edit /></el-icon>
									</a>
									<a style="margin-left: 8px" @click.stop="remove(node, data)">
										<el-icon><el-icon-delete /></el-icon>
									</a>
								</span>
							</span>
						</template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<div class="left-panel">
					<el-input
						v-model="params.name"
						placeholder="请输入场室名称搜索"
						clearable
						style="width: 200px; margin-right: 15px"
					></el-input>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
				<div class="right-panel">
					<el-button type="primary" icon="el-icon-plus" @click="addRoom(null)">新增教室</el-button>
					<!-- <el-button type="primary">批量更新</el-button>
					<el-button type="primary">批量录入</el-button>
					<el-button type="primary">导出</el-button>
					<el-button type="primary">排序</el-button> -->
				</div>
			</el-header>
			<el-main>
				<scTable
					ref="table"
					default-expand-all
					row-key="id"
					stripe
					:apiObj="list.apiObj"
					:params="params"
					@dataChange="dataChange"
				>
					<el-table-column label="建筑" prop="building_name" width="150"></el-table-column>
					<el-table-column label="楼层" prop="floor_name" width="100"></el-table-column>

					<el-table-column label="房间名称" prop="room_name" width="200"></el-table-column>
					<el-table-column label="房间属性" prop="room_type" width="120">
						<template #default="scope">
							{{ formData(roomTypeMap, scope.row.room_type) }}
						</template>
					</el-table-column>
					<el-table-column label="容纳人数" prop="room_capacity" width="120">
						<template #default="scope">
							{{ formData(roomCapacityMap, scope.row.room_capacity) }}
						</template>
					</el-table-column>
					<el-table-column label="是否开放预约" prop="is_open" width="120">
						<template #default="scope">
							<el-tooltip
								v-if="scope.row.is_open === 1 && scope.row.open_type === 1"
								effect="dark"
								:content="scope.row.allow_start + ' - ' + scope.row.allow_end"
								placement="right-start"
							>
								<el-tag type="success">开放预约</el-tag>
							</el-tooltip>
							<el-tooltip
								v-else-if="scope.row.is_open === 1 && scope.row.open_type === 2"
								effect="dark"
								:content="'课程时段：' + scope.row.periods?.name"
								placement="right-start"
							>
								<el-tag type="success">开放预约</el-tag>
							</el-tooltip>
							<el-tag v-else type="info">未开放</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="房间标签" prop="tags_ary" width="300" show-overflow-tooltip>
						<template #default="scope">
							<el-tag v-for="tag in scope.row.tags_ary" :key="tag" :disable-transitions="false">{{ tag }}</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
					<el-table-column label="操作" fixed="right" align="center" width="250">
						<template #default="scope">
							<el-button-group>
								<el-button text type="primary" size="small" @click="showSchedule(scope.row)">课表</el-button>
								<el-button v-if="scope.row.parent_id === 0" text type="success" size="small" @click="addRoom(scope.row)"
									>添加子房间
								</el-button>
								<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)"
									>编辑
								</el-button>
								<!--								<el-button text type="primary" size="small" @click="showClassTable(scope.row, scope.$index)"
									>课表</el-button
								>-->
								<el-popconfirm title="确定删除吗？" @confirm="delRoom(scope.row, scope.$index)">
									<template #reference>
										<el-button type="danger" size="small" text>删除</el-button>
									</template>
								</el-popconfirm>
							</el-button-group>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
		<!-- {{ $refs.table }}--- -->
		<!-- {{ roomNameFilters }}  -->
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>

	<saveRoomDialog
		v-if="dialog1.save"
		ref="saveRoomDialog"
		:params="params"
		:groupData="groupData"
		@success="handleSaveSuccess1"
		@closed="dialog1.save = false"
	>
	</saveRoomDialog>
	<scheduleDialog v-if="dialog.schedule" ref="scheduleDialog" @closed="dialog.schedule = false" />
</template>

<script>
import cusTom from '@/utils/cusTom'
import saveDialog from './save'
import scheduleDialog from './schedule'
import saveRoomDialog from './saveRoom'

const defaultProps = {
	children: 'floor',
	label: 'name'
}
const { campusId, tenantId, campusInfo, roomTypeMap, roomCapacityMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'

const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		building_id: null,
		floor_id: null,
		name: null,
	}
}

export default {
	name: 'fieldRoom',
	data() {
		return {
			currentId: -1,
			groupFilterText: '',
			groupData: [],
			defaultProps,
			list: {
				apiObj: this.$API.fieldRoom.rooms.list
			},
			params: defaultParams(),

			CampusManagementList: campusInfo,
			JobData: [],
			dialog: {
				save: false,
				schedule: false
			},
			dialog1: {
				save: false
			},
			showTools: false,
			roomNameFilters: [],
			roomTypeMap,
			roomCapacityMap
		}
	},
	components: {
		saveDialog,
		saveRoomDialog,
		scheduleDialog
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.building_id = null
				this.floor_id = null
				this.getLou()
				this.getRoom()
			}
		}
	},
	computed: {
		floorFilters() {
			let res = cusTom
				.treeToArray(this.groupData)
				.filter((v) => {
					return v.parent_id > 0
				})
				.map((v) => {
					return {
						text: v.building_name,
						value: v.building_name
					}
				})

			return cusTom.uniqueByValue(res, 'text')
		},
		groupsAdd() {
			let arr = [
				{
					id: 0,
					name: '全部'
				},
				...this.groupData
			]
			return arr
		}
	},
	async created() {
		this.getLou()
	},
	methods: {
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		handleShowTools(item) {
			this.currentId = item.id
		},
		dataChange(val) {
			if (!val.data.rows) return
			let arr = val.data.rows.map((v) => {
				return {
					text: v.room_name,
					value: v.room_name
				}
			})
			this.roomNameFilters = cusTom.uniqueByValue(arr, 'text')
		},
		filterHandler(value, row, column) {
			const property = column['property']
			return row[property] === value
		},
		// //获取校区列表
		// async getCampus() {
		// 	let tId = this.$TOOL.data.get('USER_INFO').tenant_id
		// 	const { data } = await this.$API.CampusManagement.all.get({
		// 		tenant_id: tId
		// 	})
		// 	this.CampusManagementList = data
		// 	this.params.campus_id = data[0].id
		// 	this.params1.campus_id = data[0].id
		// },
		//获取宿舍楼
		async getLou() {
			const res = await this.$API.fieldRoom.all.get({ ...this.params, building_type: null })
			if (!res.data) res.data = []
			let data1 = res.data.map((v) => {
				return {
					...v,
					showTools: false
				}
			})
			this.groupData = cusTom.arrayToTree(data1)
		},
		//获取房间列表
		async getRoom() {
			this.$refs.table.upData(this.params1)
		},

		//树点击事件
		groupClick(data) {
			if (data.parent_id <= 0 && data.id != 0) {
				this.params.building_id = data.id
				this.params.floor_id = null
			} else if (data.parent_id > 0 && data.id != 0) {
				this.params.building_id = data.parent_id
				this.params.floor_id = data.id
			} else {
				this.params.building_id = null
				this.params.floor_id = null
			}

			this.getRoom()
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		},
		//新增宿舍楼
		addLou() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},
		//楼层保存回调
		handleSaveSuccess() {
			this.getLou()
		},
		//房间保存回调
		handleSaveSuccess1() {
			// this.$refs.table.upData(this.search)
			this.$refs.table.refresh()
		},
		//编辑
		table_edit(row) {
			this.dialog1.save = true
			this.$nextTick(() => {
				this.$refs.saveRoomDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		showClassTable() {
			this.$message.info('正在开发中')
		},

		//添加层
		appendCeng(data) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', 2, data.id)
			})
		},
		//添加房间
		addRoom(room) {
			this.dialog1.save = true

			this.$nextTick(() => {
				if (!room) {
					this.$refs.saveRoomDialog.open('add').setData({
						building_id: this.params.building_id,
						floor_id: this.params.floor_id
					})
				} else {
					this.$refs.saveRoomDialog.open('add').setParent(room)
				}
			})
		},
		//查看课表
		showSchedule(room) {
			this.dialog.schedule = true
			this.$nextTick(() => {
				this.$refs.scheduleDialog.open(room)
			})
		},
		//楼层树 编辑
		edit(node, data) {
			console.log(data, 'datttt')
			let obj = JSON.parse(JSON.stringify(data))
			delete obj.children
			obj.building_name = [{ value: obj.building_name }]
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit', obj.building_type, obj.parent_id).setData(obj)
			})
		},
		// 楼层树 删除
		del() {},
		remove(node, data) {
			const title = data.building_type == 2 ? `是否删除楼层${data.name}?` : `是否删除建筑${data.name}?`

			ElMessageBox.confirm(title, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$API.fieldRoom.del
						.post({
							...data,
							tenant_id: this.params.tenant_id,
							campus_id: this.params.campus_id
						})
						.then((res) => {
							if (res.code === 200) {
								ElMessage({
									type: 'success',
									message: '删除成功'
								})
								this.getLou()
							}
						})
				})
				.catch(() => {})
		},
		//删除房间
		async delRoom(row, index) {
			var res = await this.$API.fieldRoom.rooms.del.post({
				id: row.id,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			})
			if (res.code === 200) {
				this.handleSaveSuccess1()

				this.$message.success('操作成功')
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		}
	}
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;

	.el-button {
		width: 80%;
		margin-bottom: 10px;
	}
}

.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;
	box-sizing: border-box;

	a {
		font-size: 18px;
	}
}
</style>
