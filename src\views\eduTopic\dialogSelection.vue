<template>
  <el-dialog v-model="dialogVisible" title="选择题目">
    <scTable ref="myScTable" row-key="id" :params="params.obj" :apiObj="apiObj" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
      <el-table-column label="类型" prop="topic_type">
        <template #default="{ row }">
          <span v-if="row.topic_type == 1">判断题</span>
          <span v-if="row.topic_type == 2">单选题</span>
          <span v-if="row.topic_type == 3">多选题</span>
        </template>
      </el-table-column>
      <el-table-column label="题目" prop="topic_name">
        <template #default="{ row }">
          <div v-html="row.topic_name"></div>
        </template>
      </el-table-column>
      <el-table-column label="分值" prop="topic_score"></el-table-column>
      <el-table-column label="年级" prop="grade_name"></el-table-column>
      <el-table-column label="学科" prop="course_name"></el-table-column>
    </scTable>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, defineExpose,defineEmits, nextTick } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage, ElAlert } from 'element-plus'
const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
const defaultParams = () => {
  return {
    tenant_id: tenantId,
    campus_id: campusId,
    grade_id: '',
    discipline_id: '',
    course_id: '',
    topic_type: ''
  }
}
const myScTable = ref(null)
let dialogVisible = ref(false)
let params = reactive({
  obj: defaultParams()
})
const selectionRows = reactive({
    list:[]
})
const emit = defineEmits(['selectionChange'])
const { proxy } = getCurrentInstance()
let apiObj = proxy.$API.eduWork.storeList
const show = () => {
    dialogVisible.value = true;
    nextTick(()=>{
        myScTable.value.clearSelection()
    })
}
const handleSelectionChange = (val) => {
    selectionRows.list = val
}
const submit = () => {
    let ids = []
    selectionRows.list.forEach(item=>{
        ids.push(item.id)
    })
    emit('selectionChange',ids)
}
defineExpose({
    dialogVisible,
    show
})
</script>

<style>
</style>
