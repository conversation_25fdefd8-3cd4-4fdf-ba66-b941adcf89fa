<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-select
						v-if="CampusManagementList.length > 1"
						v-model="params.campus_id"
						placeholder="校区"
						filterable
						style="margin-right: 15px"
						@change="campusChange"
					>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
					<cusSelectSemester
						v-model="params.semester_id"
						:params="params"
						:show-default-value="true"
						:width="'214px'"
						clearable
						style="margin-right: 15px"
						@semesterChange="semesterChange"
					/>
				</div>
			</div>
		</el-header>
		<div v-loading="loading" class="echartsOut">
			<el-row :gutter="20">
				<el-col :span="4">
					<el-card shadow="hover">
						<el-statistic :value="overviewData.class_count">
							<template #title> 班级</template>
							<template #suffix> 个</template>
						</el-statistic>
					</el-card>
				</el-col>
				<el-col :span="4">
					<el-card shadow="hover">
						<el-statistic :value="overviewData.student_count">
							<template #title>学生</template>
							<template #suffix>名</template>
						</el-statistic>
					</el-card>
				</el-col>

				<el-col :span="4">
					<el-card shadow="hover">
						<el-statistic :value="overviewData.teacher_count">
							<template #title>教师</template>
							<template #suffix>名</template>
						</el-statistic>
					</el-card>
				</el-col>

				<el-col :span="4">
					<el-card shadow="hover">
						<el-statistic :value="overviewData.course_count">
							<template #title>学科</template>
							<template #suffix>个</template>
						</el-statistic>
					</el-card>
				</el-col>

				<el-col :span="4">
					<el-card shadow="hover">
						<el-statistic :value="overviewData.student_leave_count">
							<template #title>学员请假</template>
							<template #suffix>次</template>
						</el-statistic>
					</el-card>
				</el-col>

				<el-col :span="4">
					<el-card shadow="hover">
						<el-statistic :value="overviewData.material_count">
							<template #title>课例专辑</template>
							<template #suffix>个</template>
						</el-statistic>
					</el-card>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-card shadow="hover">
						<scEcharts class="scEcharts" :option="teacherGrade" width="100%" height="300px"></scEcharts>
					</el-card>
				</el-col>
				<el-col :span="8">
					<el-card shadow="hover">
						<scEcharts class="scEcharts" :option="teacherCourse" width="100%" height="300px"></scEcharts>
					</el-card>
				</el-col>
				<el-col :span="8">
					<el-card shadow="hover">
						<scEcharts class="scEcharts" :option="studentGrade" width="100%" height="300px"></scEcharts>
					</el-card>
				</el-col>
				<el-col :span="8">
					<el-card shadow="hover">
						<scEcharts class="scEcharts" :option="studentSex" width="100%" height="300px"></scEcharts>
					</el-card>
				</el-col>
				<el-col :span="16">
					<el-card shadow="hover">
						<scEcharts class="scEcharts" :option="materialData" width="100%" height="300px"></scEcharts>
					</el-card>
				</el-col>
			</el-row>
		</div>
	</el-container>
</template>
<script>
import cusTom from '@/utils/cusTom'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'
import scEcharts from '@/components/scEcharts/index.vue'
import { ElMessage } from 'element-plus'

const { campusInfo, tenantId, campusId } = cusTom.getBaseQuery()

export default {
	name: 'index',
	components: {
		scEcharts,
		cusSelectSemester
	},
	data() {
		return {
			params: {
				tenant_id: tenantId,
				campus_id: campusId,
				semester_id: null
			},
			loading: false,
			overviewData: {},
			CampusManagementList: [],
			teacherGrade: {
				color: [
					'#D8C608',
					'#FFA1E0',
					'#6DC8EC',
					'#945FB9',
					'#FF9845',
					'#237CBC',
					'#2DE379',
					'#CE8032',
					'#FF7AF4',
					'#545FD3',
					'#AFE410'
				],
				title: {
					text: '教师年级分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			teacherCourse: {
				color: ['#545FD3', '#2DE379'],
				title: {
					text: '学科教师分布',
					subtext: ''
				},
				grid: {
					top: '80px'
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: []
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						barWidth: 20,
						data: [],
						type: 'bar'
					}
				]
			},
			studentGrade: {
				color: [
					'#237CBC',
					'#1E9493',
					'#FF99C3',
					'#AABA01',
					'#BC7CFC',
					'#2DE379',
					'#CE8032',
					'#FF7AF4',
					'#545FD3',
					'#AFE410',
					'#D8C608',
					'#FFA1E0',
					'#6DC8EC',
					'#945FB9',
					'#FF9845'
				],
				title: {
					text: '学生年级分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			studentSex: {
				color: ['#FFA1E0', '#237CBC', '#CE8032'],
				title: {
					text: '学生性别分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			materialData: {
				color: ['#F6BD16', '#6F5EF9'],
				title: {
					text: '课例专辑分布',
					subtext: ''
				},
				grid: {
					top: '80px'
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: []
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						barWidth: 20,
						data: [],
						type: 'bar',
						name: '视频资源'
					},
					{
						barWidth: 20,
						data: [],
						type: 'bar',
						name: '非视频资源'
					}
				]
			}
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	created() {
		//获取总览数据
		this.CampusManagementList = campusInfo
	},
	methods: {
		campusChange(val) {
			this.params.campus_id = val
			this.getData()
		},
		semesterChange(val) {
			this.params.semester_id = val
			this.getData()
		},
		async getData() {
			this.loading = true
			const res = await this.$API.common.eduOverview.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.overviewData = res.data

				if (res.data.teacher_grade !== null) {
					this.teacherGrade.series[0].data = res.data.teacher_grade.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.teacherGrade.series[0].data = []
				}
				if (res.data.teacher_course !== null) {
					this.teacherCourse.series[0].data = res.data.teacher_course.map((v) => {
						return v.count
					})

					this.teacherCourse.xAxis.data = res.data.teacher_course.map((v) => {
						return v.name
					})
				} else {
					this.teacherCourse.series[0].data = []
					this.teacherCourse.xAxis.data = []
				}

				if (res.data.student_grade !== null) {
					this.studentGrade.series[0].data = res.data.student_grade.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.studentGrade.series[0].data = []
				}

				if (res.data.student_sex !== null) {
					this.studentSex.series[0].data = res.data.student_sex.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.studentSex.series[0].data = []
				}
				if (res.data.material_data !== null) {
					this.materialData.series[0].data = res.data.material_data.map((v) => {
						return v.video_count
					})
					this.materialData.series[1].data = res.data.material_data.map((v) => {
						return v.not_video_count
					})

					this.materialData.xAxis.data = res.data.material_data.map((v) => {
						return v.type_name
					})
				} else {
					this.materialData.series[0].data = []
					this.materialData.series[1].data = []
					this.materialData.xAxis.data = []
				}
			} else {
				ElMessage({ type: 'error', message: res.message })
			}
		}
	}
}
</script>
<style scoped lang="scss">
.echartsOut {
	margin-top: 10px;
}

:deep(.el-statistic) {
	.el-statistic__number {
		font-size: 26px;
	}

	.el-statistic__head {
		font-size: 16px;
		font-weight: bold;
		line-height: 30px;
	}

	.el-statistic__content {
		text-align: center;
		line-height: 30px;
	}

	.el-statistic__suffix {
		color: var(--el-text-color-secondary);
		font-size: 14px;
	}
}
</style>
