<template>
	<el-dialog v-model="visible" title="属性设置" width="400" destroy-on-close @closed="$emit('closed')">
		<div class="tslItem">
			<div style="padding-bottom: 10px">
				{{ propertiesInfo.name
				}}<span style="font-size: 12px; padding-left: 10px; color: var(--el-color-info)">{{
					propertiesInfo.code
				}}</span>
			</div>
			<el-input
				v-if="propertiesInfo.type_spec.type === 'text'"
				v-model="propertiesInfo.value"
				:maxlength="propertiesInfo.type_spec.specs.len"
				placeholder="请输入内容"
			/>
			<el-input-number
				v-if="propertiesInfo.type_spec.type === 'int'"
				v-model="propertiesInfo.value"
				:step="propertiesInfo.type_spec.specs.step"
				:max="propertiesInfo.type_spec.specs.max"
				:min="propertiesInfo.type_spec.specs.min"
				:step-strictly="true"
				:precision="0"
				:controls="false"
				style="width: 100%"
				type="number"
				:placeholder="propertiesInfo.type_spec.specs.min + ' - ' + propertiesInfo.type_spec.specs.max"
			>
				<template v-if="propertiesInfo.type_spec.specs.unit_name" #suffix>
					{{ propertiesInfo.type_spec.specs.unit_name }} / {{ propertiesInfo.type_spec.specs.unit_symbol }}
				</template>
			</el-input-number>
			<el-input-number
				v-if="propertiesInfo.type_spec.type === 'float'"
				v-model="propertiesInfo.value"
				:step="propertiesInfo.type_spec.specs.step"
				:max="propertiesInfo.type_spec.specs.max"
				:min="propertiesInfo.type_spec.specs.min"
				style="width: 100%"
				:step-strictly="true"
				type="number"
				:precision="2"
				:controls="false"
				:placeholder="propertiesInfo.type_spec.specs.min + ' - ' + propertiesInfo.type_spec.specs.max"
			>
				<template v-if="propertiesInfo.type_spec.specs.unit_name" #suffix>
					{{ propertiesInfo.type_spec.specs.unit_name }} / {{ propertiesInfo.type_spec.specs.unit_symbol }}
				</template>
			</el-input-number>
			<el-select
				v-if="propertiesInfo.type_spec.type === 'enum'"
				v-model="propertiesInfo.value"
				style="width: 100%; max-width: unset"
			>
				<el-option
					v-for="(item, index) in propertiesInfo.type_spec.specs"
					:key="index"
					:label="item.name"
					:value="item.value"
				>
					{{ item.name }}
				</el-option>
			</el-select>
			<el-radio-group v-if="propertiesInfo.type_spec.type === 'bool'" v-model="propertiesInfo.value">
				<el-radio-button v-for="(item, index) in propertiesInfo.type_spec.specs" :value="index"
					>{{ item }}
				</el-radio-button>
			</el-radio-group>
			<div style="padding-top: 15px; text-align: right">
				<el-button type="primary" @click="setProperties">确定</el-button>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import dayjs from 'dayjs'
import scEcharts from '@/components/scEcharts/index.vue'
import { ElMessage } from 'element-plus'

export default {
	name: 'logList',
	data() {
		return {
			visible: false,
			loading: false,
			logData: [],
			propertiesInfo: {},

			params: {
				tenant_id: null,
				campus_id: null,
				device_id: null,
				tsl_id: null,
				time: [],
				begin_time: null,
				end_time: null
			}
		}
	},
	watch: {},
	created() {},
	computed: {},
	methods: {
		show(data) {
			console.log(data)
			this.visible = true
			this.params.device_id = data.device_id
			this.params.tenant_id = data.tenant_id
			this.params.campus_id = data.campus_id
			this.params.tsl_id = data.tsl_info.properties_info.tsl_id
			this.propertiesInfo = data.tsl_info.properties_info
			this.propertiesInfo.value = data.tsl_info.last_value?.value
			if (['int', 'float', 'enum', 'bool'].includes(this.propertiesInfo.type_spec.type)) {
				this.propertiesInfo.value = this.propertiesInfo.value * 1
			}
		},
		//搜索
		upsearch() {
			this.params.page = 1
		},
		refresh() {
			this.params.page = 1
		},
		setProperties() {
			let reqData = {
				device_id: this.params.device_id,
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				property: [
					{
						tsl_id: this.params.tsl_id,
						value: this.propertiesInfo.value.toString()
					}
				]
			}
			this.$LotApi.device.writeDevProperty.post(reqData).then((res) => {
				if (res.code === 200) {
					ElMessage.success('设置成功！可点击刷新查看')
					this.visible = false
					this.$emit('closed')
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.left-panel {
	padding-bottom: 15px;
}

.page {
	padding: 20px;
	display: flex;
	justify-content: flex-end;
}

.payload {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
</style>
