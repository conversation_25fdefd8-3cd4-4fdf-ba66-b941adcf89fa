<template>
	<el-dialog v-model="visible" :title="title" width="30%" destroy-on-close @close="close">
		<el-form ref="dialogForm" :model="formData" :rules="rules" label-width="auto">
			<el-form-item label="考试场地" prop="room">
				<cusSelectField v-model="formData.room" multiple placeholder="请选择考试场地"> </cusSelectField>
			</el-form-item>
			<el-form-item label="考生人数" prop="student_num">
				<el-input-number v-model="formData.student_num" :min="1" />
			</el-form-item>
			<el-form-item label="监考人数" prop="teacher_num">
				<el-input-number v-model="formData.teacher_num" :min="1" />
			</el-form-item>
			<el-form-item label="考场每列人数" prop="column">
				<el-input-number v-model="formData.column" :min="1" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="close">取 消</el-button>
				<el-button type="primary" @click="save">确 定</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus'
import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const emit = defineEmits(['addRoom'])
const defaultData = () => {
	return {
		student_num: 0,
		teacher_num: 0,
		column: 0,
		room: null
	}
}
const dialogForm = ref()
const save = () => {
	dialogForm.value.validate((valid) => {
		if (valid) {
			console.log(formData.value)
			let addList
			addList = formData.value.room.map((item) => {
				return {
					room_id: item.id,
					student_num: formData.value.student_num,
					teacher_num: formData.value.teacher_num,
					column: formData.value.column,
					room_info: { name: item.label || item.name, id: item.id }
				}
			})
			emit('addRoom', addList)
			close()
			console.log(addList, 'addList')
		}
	})
}

const dataChange = (val) => {
	console.log(val)
	// label,id
}

const visible = ref(false)
const title = ref('新增场地')
const formData = ref(defaultData())
const rules = ref({
	room: [{ required: true, message: '请选择考试场地', trigger: 'blur' }],
	student_num: [{ required: true, message: '请输入考生人数', trigger: 'blur' }]
})

const open = (type, data) => {
	title.value = type === 'add' ? '新增场地' : '编辑场地'
	formData.value = defaultData()
	if (type === 'edit') {
		formData.value = data
		formData.value.room = [data.room_info].map((item) => {
			return {
				label: item.name,
				value: item.id
			}
		})
	}
	visible.value = true
}
const close = () => {
	visible.value = false
}

defineExpose({
	open,
	close
})
</script>
