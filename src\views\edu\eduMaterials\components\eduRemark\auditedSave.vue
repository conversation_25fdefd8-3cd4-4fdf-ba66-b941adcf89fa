<template>
	<el-dialog v-model="dialogVisible" title="审核" width="20%" destroy-on-close>
		<el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
			<el-form-item label="审核操作" prop="audit" label-width="100px">
				<el-radio-group v-model="form.audit">
					<el-radio :label="2" size="small">审核通过</el-radio>
					<el-radio :label="3" size="small">审核不通过</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="审核备注" label-width="100px" prop="comment_score">
				<el-input v-model="form.audit_remark" autosize type="textarea" placeholder="请输入"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="dialogVisible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import { reactive, ref, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps({
	params: {
		type: Object,
		default: () => {
			return {}
		}
	}
})
const emit = defineEmits(['success'])
const defaultData = () => {
	return {
		id: null,
		tenant_id: 0,
		campus_id: 0,
		material_id: 0,
		audit: null,
		audit_remark: ''
	}
}
const rules = reactive({
	audit: [{ required: true, message: '请选择审核状态', trigger: 'blur' }]
})
let dialogVisible = ref(false)
let formRef = ref(null)
let isSaveing = ref(false)
const form = ref(defaultData())
const open = (id) => {
	form.value.id = id
	form.value.campus_id = Number(props.params.campus_id)
	form.value.material_id = Number(props.params.material_id)
	form.value.tenant_id = Number(props.params.tenant_id)
	dialogVisible.value = true
}
// 保存按钮点击事件
const submit = async () => {
	await formRef.value.validate()
	isSaveing.value = true
	const res = await globalPropValue.eduMaterials.material.audit_comment.post(form.value)
	isSaveing.value = false
	if (res.code === 200) {
		ElMessage.success('操作成功')
		emit('success', form.value)
	} else {
		ElMessage.error(res.message)
	}
	dialogVisible.value = false
}
defineExpose({
	open
})
</script>

<style scoped></style>
