<template>
	<el-cascader
		v-model="value"
		:options="treeData"
		:props="prop"
		collapse-tags
		collapse-tags-tooltip
		:placeholder="placeholder"
		:disabled="disabled"
		:style="{ width: width }"
		@change="handleChange"
	/>
</template>

<script>
import cusTom from '@/utils/cusTom'

export default {
	props: {
		modelValue: {
			type: [String, Number, Object],
			default: ''
		},
		multiple: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: String,
			default: '请选择'
		},
		disabled: {
			type: Boolean,
			default: false
		},
		showDefaultValue: {
			type: Boolean,
			default: false
		},
		width: {
			type: String,
			default: '100%'
		},
		params: {
			type: Object,
			default: () => {
			}
		}
	},
	data() {
		return {
			treeData: [],
			prop: {
				multiple: false,
				emitPath: false,
				value: 'value',
				label: 'name',
				checkStrictly: false
			},
			value: this.modelValue,
			/*params: {
				tenant_id: '',
				campus_id: ''
			},*/
			semesterInfo: []
		}
	},
	watch: {
		modelValue: {
			handler(val) {
				this.value = val
			},
			immediate: true
		},
		multiple: {
			handler(val) {
				this.prop.multiple = val
			},
			deep: true,
			immediate: true
		},
		value(newVal) {
			this.$emit('update:modelValue', newVal)
		},
		"params.campus_id": {
			handler(val) {
				this.getSemester()
			},
			immediate: true
		}
	},
	created() {
		const { campusId, tenantId, semesterInfo } = cusTom.getBaseQuery()
		/*	this.params = {
			tenant_id: tenantId,
			campus_id: campusId
		}*/
		this.semesterInfo = semesterInfo
		this.getSemester()
	},
	methods: {
		handleChange(val) {
			this.$emit('semesterChange', val)
		},
		getSemester() {
			this.treeData = cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.params.campus_id),
				'value',
				'parent_id'
			)
			if (this.showDefaultValue) {
				let value = this.findSelectedChildren(this.treeData)
				if (value.length > 1) {
					this.value = value[1].value
					this.$emit('semesterChange', this.value)
				}
			}
		},
		findSelectedChildren(tree) {
			let selectedChildren = []

			function recurse(nodes) {
				for (const node of nodes) {
					if (node.selected === 1) {
						selectedChildren.push(node)
					}
					if (node.children && node.children.length > 0) {
						recurse(node.children)
					}
				}
			}

			recurse(tree)
			return selectedChildren
		}
	}
}
</script>

<style scoped></style>
