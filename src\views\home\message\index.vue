<template>
    <el-container>
        <el-header>
            <el-tabs v-model="activeName" @tab-change="handleClick">
                <el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
            </el-tabs>
        </el-header>
        <el-main>
            <component :is="currComponent.component">
            </component>
        </el-main>
    </el-container>
</template>
<script>
import notice from './notice.vue';
import message from './message.vue';
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, studentVacateTypeMap } = cusTom.getBaseQuery()
export default {
    components: {
        notice,
        message
    },
    data() {
        return {
            campusId,
            tenantId,
            activeName: 'notice',
            currComponent: {
                name: 'notice',
                component: notice
            },
            tabs: [
                {
                    name: 'notice',
                    label: '公告通知',
                    component: notice
                },
                {
                    name: 'message',
                    label: '消息通知',
                    component: message
                }
            ]
        }
    },
    methods: {
        handleClick(name) {
            this.currComponent = this.tabs.find((item) => item.name === name)
        },
    }
}

</script>

<style lang="scss" scoped></style>