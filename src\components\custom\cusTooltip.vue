<template>
	<div class="tooltip-wrap">
		<el-tooltip
			ref="tlp"
			effect="dark"
			:disabled="!tooltipFlag"
			:placement="placement"
			:fallback-placements="['bottom', 'top', 'right', 'left']"
		>
			<template #content>
				<div style="max-width: 500px">
					{{ text }}
				</div>
			</template>
			<span :class="className" @mouseenter="visibilityChange($event)">{{ text }}</span>
		</el-tooltip>
	</div>
</template>

<script>
export default {
	name: 'ellipsisTooltip',
	props: {
		text: {
			// 文本内容
			type: String,
			default: () => ''
		},
		className: {
			type: String,
			default: () => 'text'
		},
		placement: {
			type: String,
			default: () => 'top-start'
		}
	},
	data() {
		return {
			disabledTip: false,
			tooltipFlag: false
		}
	},
	mounted() {},
	methods: {
		visibilityChange(e) {
			const ev = e.target
			const evWidth = ev.clientWidth
			const evSWidth = ev.scrollWidth
			this.tooltipFlag = evSWidth > evWidth
		}
	}
}
</script>

<style lang="scss" scoped>
.tooltip-wrap {
	width: 100%;

	span {
		white-space: nowrap;
		display: inline-block;
		width: 100%;
		text-overflow: ellipsis;
		overflow: hidden;
		line-height: 100%;
	}
}
</style>
