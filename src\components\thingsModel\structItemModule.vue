<template>
	<el-drawer v-model="showItem" title="结构体参数" size="550" @close="close">
		<el-form ref="form" class="tsl" label-position="top" :model="structItem" :rules="rules">
			<el-form-item label="参数名称" prop="name">
				<el-input v-model="structItem.name" placeholder="请输入参数名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="标识符" prop="code">
				<el-input
					v-model="structItem.code"
					placeholder="支持英文、数字、下划线的组合，首字符不能是数字，最多32个字符"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item label="数据类型" prop="type_spec.type">
				<el-select
					v-model="structItem.type_spec.type"
					placeholder="请选择数据类型"
					style="width: 100%; max-width: unset"
					@change="typeChange"
				>
					<el-option
						v-for="item in typeConfig"
						:key="item.value"
						:label="item.value + ' (' + item.label + ')'"
						:value="item.value"
					/>
				</el-select>
			</el-form-item>
			<div class="dataDefinition">
				<template v-if="structItem.type_spec.type === 'int'">
					<int-module v-model="structItem.type_spec.specs"></int-module>
				</template>
				<template v-if="structItem.type_spec.type === 'float'">
					<float-module v-model="structItem.type_spec.specs"></float-module>
				</template>
				<template v-if="structItem.type_spec.type === 'enum'">
					<enum-module v-model="structItem.type_spec.specs"></enum-module>
				</template>
				<template v-if="structItem.type_spec.type === 'bool'">
					<bool-module v-model="structItem.type_spec.specs"></bool-module>
				</template>
				<template v-if="structItem.type_spec.type === 'text'">
					<text-module v-model="structItem.type_spec.specs"></text-module>
				</template>
				<template v-if="structItem.type_spec.type === 'date'">
					<date-module v-model="structItem.type_spec.specs"></date-module>
				</template>
			</div>
		</el-form>
		<template #footer>
			<el-button @click="close">取 消</el-button>
			<el-button type="primary" @click="submit()">保 存</el-button>
		</template>
	</el-drawer>
</template>
<script>
import DateModule from '@/components/thingsModel/dateModule.vue'
import IntModule from '@/components/thingsModel/intModule.vue'
import FloatModule from '@/components/thingsModel/floatModule.vue'
import BoolModule from '@/components/thingsModel/boolModule.vue'
import TextModule from '@/components/thingsModel/textModule.vue'
import EnumModule from '@/components/thingsModel/enumModule.vue'
import tool from '@/utils/tool'

export default {
	name: 'structItemModule',
	components: { EnumModule, TextModule, BoolModule, FloatModule, IntModule, DateModule },
	props: {},
	data() {
		return {
			unitConfig: [],
			showItem: false,
			structItem: null,
			rules: {
				name: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
				code: [{ required: true, message: '请输入参数标识符', trigger: 'blur' }],
				'type_spec.type': [{ required: true, message: '请选择参数类型', trigger: 'blur' }]
			},
			typeConfig: [
				{
					label: '整数型',
					value: 'int'
				},
				{
					label: '浮点型',
					value: 'float'
				},
				{
					label: '字符串',
					value: 'text'
				},
				{
					label: '枚举',
					value: 'enum'
				},
				{
					label: '布尔类型',
					value: 'bool'
				},
				{
					label: '时间型',
					value: 'date'
				}
			],
			mode: 'add'
		}
	},
	watch: {},
	created() {},
	methods: {
		typeChange(value) {
			let specs = null
			switch (value) {
				case 'int':
				case 'float':
					specs = {
						max: null,
						min: null,
						step: null,
						unit: null,
						unit_name: null,
						unit_symbol: null
					}
					break
				case 'enum':
					specs = [{ value: null, name: null }]
					break
				case 'text':
					specs = {
						len: ''
					}
					break
				case 'bool':
					specs = ['关', '开']
					break
			}
			this.structItem.type_spec.specs = specs
		},
		show(data, mode) {
			this.mode = mode
			if (data) {
				this.structItem = tool.objCopy(data)
			} else {
				this.structItem = {
					name: null,
					code: null,
					type_spec: {
						type: null,
						specs: null
					}
				}
			}
			if (this.structItem.type_spec.specs) {
				this.structItem.type_spec.specs = JSON.parse(this.structItem.type_spec.specs)
			}
			this.$nextTick(() => {
				this.showItem = true
			})
		},
		submit() {
			this.$refs.form.validate((valid) => {
				if (!valid) return
				this.showItem = false
				this.structItem.type_spec.specs = JSON.stringify(this.structItem.type_spec.specs)
				this.$emit('submit', this.structItem, this.mode)
			})
		},
		close() {
			this.showItem = false
			this.$emit('close')
		}
	}
}
</script>

<style scoped lang="scss">
.enum_header {
	font-size: 12px !important;
	color: #666;
	width: 100%;
}
.tsl {
	padding: 0 15px;
	font-size: 12px;
}
.enum_item {
	margin: 5px 0;
	font-size: 12px !important;
	color: #666;
	width: 100%;
}
.dataDefinition {
	padding-left: 15px;
}
.text-center {
	text-align: center;
}
</style>
