import config from '@/config'
import http from '@/utils/request'

export default {
	all: {
		url: `${config.API_URL}/affapi/assets/type_all`,
		name: '获取资产类别列表不分页',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	create: {
		url: `${config.API_URL}/affapi/assets/type_creat`,
		name: '新增资产类别',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	save: {
		url: `${config.API_URL}/affapi/assets/type_save`,
		name: '资产类别修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},

	del: {
		url: `${config.API_URL}/affapi/assets/type_del`,
		name: '删除资产类别',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	myAssets: {
		url: `${config.API_URL}/affapi/assets/my`,
		name: '我的资产',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	rooms: {
		all:{
			url: `${config.API_URL}/affapi/assets/all`,
			name: '获取资产列表不分页',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		list: {
			url: `${config.API_URL}/affapi/assets/list`,
			name: '获取资产列表',
			get: async function (params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/affapi/assets/save`,
			name: '新增资产',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		update: {
			url: `${config.API_URL}/affapi/assets/update`,
			name: '新增资产',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},

		incr: {
			url: `${config.API_URL}/affapi/assets/incr`,
			name: '资产修改',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/affapi/assets/del`,
			name: '删除资产',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		action: {
			url: `${config.API_URL}/affapi/assets/action`,
			name: '资产出退库',
			post: async function (data = {}) {
				return await http.post(this.url, data)
			}
		},
		log: {
			url: `${config.API_URL}/affapi/assets/log`,
			name: '单个资产出入库记录',
			get: async function (data = {}) {
				return await http.get(this.url, data)
			}
		},
		record: {
			url: `${config.API_URL}/affapi/assets/record`,
			name: '资产出入库记录',
			get: async function (data = {}) {
				return await http.get(this.url, data)
			}
		},
	},
	tree: {
		url: `${config.API_URL}/affapi/assets/tree`,
		name: '获取资产',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	}
}
