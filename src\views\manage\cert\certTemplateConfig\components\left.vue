<template>
	<div class="left-container">
		<div class="left-field">
			<div class="left-field-title">
				<h2>证书字段</h2>
			</div>
			<ul class="field-list">
				<li class="field-item">
					证书二维码
					<div class="add-btn" @click="addCertQr()">新 增</div>
				</li>
				<li class="field-item">
					公章图片
					<div class="add-btn" @click="addCertSealImg">新 增</div>
				</li>
				<li class="field-item">
					公章落款
					<div class="add-btn" @click="copyCertItem(`{{signed}}`)">复制编码</div>
				</li>
				<li class="field-item">
					证书编号
					<div class="add-btn" @click="copyCertItem(`{{cert_number}}`)">复制编码</div>
				</li>
				<li class="field-item">
					学年
					<div class="add-btn" @click="copyCertItem(`{{academic_name}}`)">复制编码</div>
				</li>
				<li class="field-item">
					学期
					<div class="add-btn" @click="copyCertItem(`{{semester_name}}`)">复制编码</div>
				</li>
				<li v-if="certTemplate.object === 2" class="field-item">
					教师姓名
					<div class="add-btn" @click="copyCertItem(`{{teacher_name}}`)">复制编码</div>
				</li>
				<li
					v-if="certTemplate.object === 1 || certTemplate.object === 4 || certTemplate.object === 5"
					class="field-item"
				>
					年级
					<div class="add-btn" @click="copyCertItem(`{{grade_name}}`)">复制编码</div>
				</li>
				<li v-if="certTemplate.object === 1 || certTemplate.object === 5" class="field-item">
					班级
					<div class="add-btn" @click="copyCertItem(`{{class_name}}`)">复制编码</div>
				</li>
				<li v-if="certTemplate.object === 1" class="field-item">
					学生姓名
					<div class="add-btn" @click="copyCertItem(`{{student_name}}`)">复制编码</div>
				</li>
				<li v-if="certTemplate.object === 3" class="field-item">
					部门
					<div class="add-btn" @click="copyCertItem(`{{department_name}}`)">复制编码</div>
				</li>
				<li v-if="certTemplate.object === 6" class="field-item">
					学科
					<div class="add-btn" @click="copyCertItem(`{{course_name}}`)">复制编码</div>
				</li>
				<li class="field-item">
					获奖名称
					<div class="add-btn" @click="copyCertItem(`{{award_name}}`)">复制编码</div>
				</li>
				<li class="field-item">
					获奖名次
					<div class="add-btn" @click="copyCertItem(`{{award_ranking}}`)">复制编码</div>
				</li>
				<li class="field-item">
					获奖信息
					<div class="add-btn" @click="copyCertItem(`{{award_desc}}`)">复制编码</div>
				</li>
				<li class="field-item">
					获奖日期
					<div class="add-btn" @click="copyCertItem(`{{award_date}}`)">复制编码</div>
				</li>
			</ul>
		</div>
	</div>
</template>

<script>
import useClipboard from 'vue-clipboard3'
import tool from '@/utils/tool'

export default {
	props: {
		certTemplate: {
			type: Object,
			default: () => ({})
		},
		list: {
			type: Array,
			default: () => []
		},
		canvasConfig: {
			type: Object,
			default: () => ({
				width: 600,
				height: 400
			})
		}
	},
	data() {
		return {
			certData: this.list
		}
	},

	watch: {
		canvasConfig: {
			handler(newVal) {
				this.canvasSet = newVal
				/*if (newVal.bgImg === '' || newVal.bgImg === undefined || newVal.bgImg == null) {
					this.delFristBg()
				}*/
			},
			deep: true
		},
		list: {
			handler(newVal) {
				this.certData = newVal
			},
			deep: true
		}
	},
	methods: {
		addCertQr() {
			console.log(this.certData)
			this.certData.push({
				id: tool.getRandomString(16, 'alphanumeric'),
				type: 'image',
				field: 'qrcode',
				content: '{"type":"cert","content":{"cert_number":"{{cert_number}}"}}',
				w: 100,
				scale: true,
				active: false,
				x: this.canvasConfig.width / 2 - 50,
				y: this.canvasConfig.height / 2 - 50,
				h: 100
			})
			console.log(this.certData)
			this.$emit('update:list', this.certData)
		},
		addCertSealImg() {
			// 使用示例
			let img = this.certTemplate.seal?.seal_img
			if (!img) {
				this.$message.error('请先设置证书模板电子公章！')
				return false
			}
			let _this = this
			_this.certData.push({
				id: tool.getRandomString(16, 'alphanumeric'),
				type: 'image',
				field: 'seal_img',
				content: img,
				w: 100,
				scale: true,
				active: false,
				x: this.canvasConfig.width / 2 - 50,
				y: this.canvasConfig.height / 2 - 50,
				h: 100
			})
			_this.$emit('update:list', _this.certData)
			/*this.getBase64(img).then(
				(base64) => {
					_this.certData.push({
						id: tool.getRandomString(16, 'alphanumeric'),
						type: 'image',
						field: 'seal_img',
						content: base64,
						w: 100,
						scale: true,
						active: false,
						x: this.canvasConfig.width / 2 - 50,
						y: this.canvasConfig.height / 2 - 50,
						h: 100
					})
					_this.$emit('update:list', _this.certData)
				},
				(err) => {
					console.log(err)
				}
			)*/
		},
		getBase64(img) {
			function getBase64Image(img, width, height) {
				//width、height调用时传入具体像素值，控制大小 ,不传则默认图像大小
				var canvas = document.createElement('canvas')
				canvas.width = width ? width : img.width
				canvas.height = height ? height : img.height

				var ctx = canvas.getContext('2d')
				ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
				var dataURL = canvas.toDataURL()
				return dataURL
			}
			var image = new Image()
			image.crossOrigin = ''
			image.src = img
			return new Promise((resolve, reject) => {
				image.onload = function () {
					resolve(getBase64Image(image)) //将base64传给done上传处理
				}
			})
		},
		async copyCertItem(encoding) {
			try {
				const { toClipboard } = useClipboard()
				await toClipboard(encoding)
				this.$message.success('复制成功')
			} catch (error) {
				this.$message.error('复制失败')
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.left-container {
	.left-field {
		border-radius: 4px;
		margin-bottom: 10px;

		.left-field-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20px;
			margin-bottom: 10px;
			border-bottom: 1px solid #e5e5e5;

			h2 {
				font-size: 16px;
				font-weight: 500;
			}
		}

		.field-list {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			padding: 15px;

			.field-item {
				width: 45%;
				cursor: pointer;
				background-color: var(--el-color-info-light-8);
				margin-bottom: 15px;
				padding: 10px 15px;
				border-radius: 4px;
				font-size: 14px;
				box-sizing: border-box;
				position: relative;
				text-align: center;
				border: 1px solid #f6f8fa;

				.add-btn {
					display: none;
					width: 100%;
					height: 100%;
					position: absolute;
					left: 0;
					top: 0;
					border-radius: 4px;
				}

				&:hover {
					.add-btn {
						display: flex;
						border: 1px solid #00b42a;
						justify-content: center;
						align-items: center;
						background-color: #f5f5f5;
						color: #00b42a;
					}
				}
			}
		}
	}
}
</style>
