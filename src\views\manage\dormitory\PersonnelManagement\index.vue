<template>
	<el-container>
		<el-header>
			<div class="left-panel-search">
				<el-form-item v-if="CampusManagementList.length > 1" label="">
					<el-select v-model="params.campus_id" placeholder="校区" filterable>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="">
					<cusSelectSemester
						v-model="params.semester_id"
						:params="params"
						:show-default-value="true"
						:width="'214px'"
						clearable
						style="margin-right: 15px"
					/>
					<!--					<el-select v-model="params.academic_id" placeholder="请选择学年" filterable clearable>
						<el-option v-for="item in getAcademic_year" :key="item.value" :label="item.name" :value="item.value" />
					</el-select>-->
				</el-form-item>
			</div>
		</el-header>
		<el-main class="el-main-hascontainer">
			<el-container>
				<el-aside width="270px">
					<el-container>
						<el-main>
							<el-tree
								ref="group"
								class="menu"
								node-key="id"
								:data="groupData"
								:highlight-current="true"
								:expand-on-click-node="false"
								:props="defaultProps"
								:default-expanded-keys="defaultKeys"
								@node-click="groupClick"
							>
								<template #default="{ node, data }">
									<span class="custom-tree-node">
										<span>{{ node.label }}</span>
									</span>
								</template>
							</el-tree>
						</el-main>
					</el-container>
				</el-aside>
				<el-container>
					<el-header>
						<div class="left-panel">
							<h3>宿舍人员安排</h3>
						</div>
						<div class="right-panel">
							<el-button type="primary" icon="el-icon-plus" @click="open">人员安排</el-button>
							<el-button type="primary" icon="el-icon-minus" @click="remove">人员搬离</el-button>
							<!-- <el-button type="primary" icon="el-icon-edit" @click="editRecord">写记录</el-button> -->
						</div>
					</el-header>
					<el-main>
						<div class="roomList">
							<div
								v-for="(item, index) in bedList"
								class="roomItem"
								:class="{ currentBed: index == setCurrentBed }"
								@click="setCurrent(index)"
							>
								<!-- <div
								class="roomItem"
								v-for="(item, index) in bedList"
								:class="{ currentBed: item.has == true }"
								@click="chooseStatus(item)"
							> -->
								<template v-if="!item.staff_info && !item.student_info">
									<el-empty>
										<template #description>
											<text></text>
										</template>
										<el-tag type="warning">空余床位</el-tag>
									</el-empty>
								</template>

								<template v-if="item.staff_info">
									<div class="roomItem-head">
										<div class="flex_bc">
											<cusHead
												loading="lazy"
												:lazy="true"
												fit="contain"
												style="width: 50px; height: 50px"
												:src="item.staff_info.user_head"
												:preview-src-list="[item.staff_info.user_head]"
												preview-teleported
											>
											</cusHead>
											<text style="margin-left: 10px">{{ item.staff_info.name }}</text>
										</div>

										<div>
											<el-tag type="success">{{ item.bed_number }}床</el-tag>
										</div>
									</div>
									<div class="roomItem-body">
										<div>
											<el-tag v-if="item.is_head" type="danger">宿舍长</el-tag>
											<el-tag type="warning">教职工</el-tag>
											<el-tag v-if="item.accommodation_type == 1" type="info">全寄</el-tag>
											<el-tag v-if="item.accommodation_type == 2" type="info">半寄</el-tag>
										</div>
										<div class="roomItem-body-row">性别：{{ formData(sexMap, item.staff_info.sex) }}</div>
										<div class="roomItem-body-row">工号：{{ item.staff_info.serial_number }}</div>
										<div class="roomItem-body-row">手机：{{ item.staff_info.phone }}</div>
										<div class="roomItem-body-row">
											<div class="label">备注：</div>
											<el-input
												v-model="item.remark"
												type="textarea"
												:autosize="{ minRows: 4, maxRows: 6 }"
												:disabled="!item.isCanEdit"
												placeholder=""
											/>
										</div>
									</div>
								</template>
								<template v-if="item.student_info">
									<div class="roomItem-head">
										<div class="flex_bc">
											<el-image
												:lazy="true"
												fit="contain"
												style="width: 50px; height: 50px; border-radius: 50%; border: 1px solid #ccc"
												:src="item.student_info.user_head"
												:preview-src-list="[item.student_info.user_head]"
												preview-teleported
											/>

											<text style="margin-left: 10px">{{ item.student_info.student_name }}</text>
										</div>

										<div>
											<el-tag type="success">{{ item.bed_number }}床</el-tag>
										</div>
									</div>
									<div class="roomItem-body">
										<div>
											<el-tag v-if="item.is_head" type="danger">宿舍长</el-tag>
											<el-tag>学生</el-tag>
											<el-tag v-if="item.accommodation_type == 1" type="info">全寄</el-tag>
											<el-tag v-if="item.accommodation_type == 2" type="info">半寄</el-tag>
										</div>
										<div class="roomItem-body-row">性别：{{ formData(sexMap, item.student_info.sex) }}</div>
										<div class="roomItem-body-row">
											班级：{{ item.student_info.grade_name }}-{{ item.student_info.class_name }}
										</div>
										<div class="roomItem-body-row">手机：{{ item.student_info.phone }}</div>
										<div class="roomItem-body-row">
											班主任：{{ item.student_info.head_teacher_name }} - {{ item.student_info.head_teacher_phone }}
										</div>
										<div class="roomItem-body-row">
											<div class="label">备注：</div>
											<el-input
												v-model="item.remark"
												type="textarea"
												:autosize="{ minRows: 4, maxRows: 6 }"
												:disabled="!item.isCanEdit"
												placeholder=""
											/>
										</div>
									</div>
								</template>
								<div v-if="item.staff_info || item.student_info" class="roomItem-foot">
									<text v-if="!item.isCanEdit" @click="item.isCanEdit = !item.isCanEdit">
										<el-icon><el-icon-editPen /></el-icon>写备注
									</text>
									<text v-else v-loading="Editing">
										<el-tag type="info" @click=";(item.isCanEdit = !item.isCanEdit), (item.remark = item.remarkCopy)"
											>取消</el-tag
										>
										<el-tag type="success" @click="editRemark(item)">完成</el-tag>
									</text>
								</div>
							</div>
						</div>
					</el-main>
				</el-container>
			</el-container>
		</el-main>
		<save-dialog
			v-if="dialog.save"
			ref="saveDialog"
			:params="params"
			@success="handleSaveSuccess"
			@closed="dialog.save = false"
		></save-dialog>

		<leave-dialog
			v-if="dialog.leave"
			ref="leaveDialog"
			:params="params"
			@success="handleSaveSuccess"
			@closed="dialog.leave = false"
		></leave-dialog>
		<edit-dialog
			v-if="dialog.edit"
			ref="editDialog"
			:params="params"
			@success="handleSaveSuccess"
			@closed="dialog.edit = false"
		></edit-dialog>
	</el-container>
</template>

<script>
import cusTom from '@/utils/cusTom'
import saveDialog from './save'
import leaveDialog from './leave'

const defaultProps = {
	children: 'children',
	label: 'label'
}

const { campusId, tenantId, campusInfo, semesterInfo, semesterId, academicYearId, sexMap } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'
import cusHead from '@/components/custom/cusStaffHead.vue'
import CusSelectSemester from '@/components/custom/cusSelectSemester.vue'
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: academicYearId, //注意
		semester_id: semesterId, //注意
		room_id: null
	}
}

export default {
	name: 'buildingRooms',
	data() {
		return {
			currentId: -1,
			groupFilterText: '',
			groupData: [],
			defaultProps,
			list: {
				apiObj: this.$API.buildingRooms.personnel.list
			},
			params: defaultParams(),

			CampusManagementList: campusInfo,
			dialog: {
				save: false,
				leave: false,
				edit: false
			},
			dialog1: {
				save: false
			},
			showTools: false,
			semesterInfo,
			bedList: [],
			defaultKeys: [],
			sexMap,
			room_capacity: 0,
			setCurrentBed: -1,
			Editing: false
		}
	},
	components: { CusSelectSemester, cusHead, saveDialog, leaveDialog },
	watch: {
		'params.campus_id': {
			handler(val) {
				this.params.academic_id = null
			}
		},
		'params.academic_id': {
			handler(val) {
				this.params.semester_id = this.getSemester[0] && this.getSemester[0].value
			}
		},
		'params.semester_id': {
			handler(val) {
				this.getLou()
			}
		},
		'params.room_id': {
			handler(val) {
				if (val) {
					this.getRoom()
				}
			}
		}
	},
	computed: {
		getAcademic_year() {
			return this.semesterInfo.filter((v) => v.parent_id == 0 && v.campus_id == this.params.campus_id)
		},
		getSemester() {
			return this.semesterInfo.filter(
				(v) => v.parent_id != 0 && v.parent_id == this.params.academic_id && v.campus_id == this.params.campus_id
			)
		}
	},
	async created() {
		this.getLou()
	},
	mounted() {},
	methods: {
		chooseStatus(item) {
			if (!item.user_id) {
				return false
			}
			item.has = !item.has
		},
		editRecord() {
			let checkUser = this.bedList.filter((v) => v.has)
			this.dialog.edit = true
			this.$nextTick(() => {
				this.$refs.editDialog.open('edit').setData(checkUser)
			})
		},
		//添加 床位
		open() {
			this.dialog.save = true
			this.$nextTick(() => {
				let hasData = this.bedList[this.setCurrentBed]
				if (hasData && hasData.id) {
					this.$refs.saveDialog.open('edit').setData(hasData)
				} else {
					this.$refs.saveDialog.open().setData({
						bed_number: this.setCurrentBed + 1
					})
				}
			})
		},
		async editRemark(row) {
			this.Editing = true
			var res = await this.$API.buildingRooms.personnel.save.post(row)
			this.Editing = false
			if (res.code === 200) {
				this.$message.success('操作成功')
				this.handleSaveSuccess()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		setCurrent(index) {
			if (this.setCurrentBed == index) this.setCurrentBed = -1
			this.setCurrentBed = index
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || val
		},
		handleShowTools(item) {
			this.currentId = item.id
		},
		dataChange(val) {
			if (!val.data.rows) return
			let arr = val.data.rows.map((v) => {
				return {
					text: v.room_name,
					value: v.room_name
				}
			})
			this.roomNameFilters = cusTom.uniqueByValue(arr, 'text')
		},
		filterHandler(value, row, column) {
			const property = column['property']
			return row[property] === value
		},

		//获取宿舍楼
		async getLou() {
			const { data } = await this.$API.buildingRooms.buildingTree.get({ ...this.params, building_type: null })
			data.forEach((v) => {
				v.uid = v.id
				v.id = v.id + 'a'
				v.children.forEach((vv) => {
					vv.uid = vv.id
					vv.id = vv.id + 'b'
					vv.children.forEach((vvv) => {
						vvv.uid = vvv.id
						vvv.id = vvv.id + 'c'
					})
				})
			})
			this.groupData = data
			this.getGroupDataDefault()
		},
		getGroupDataDefault() {
			if (this.groupData.length) {
				this.defaultKeys = [this.groupData[0].children[0].children[0].id]
				this.$nextTick(() => {
					this.$refs.group?.setCurrentKey(...this.defaultKeys)
					const curr = this.$refs.group?.getCurrentNode()
					this.room_capacity = curr.room_capacity
					this.params.room_id = curr.uid
				})
				this.getRoom()
			}
		},
		//获取房间列表
		async getRoom() {
			if (!this.params.room_id) return false
			const { data } = await this.$API.buildingRooms.personnel.list.get(this.params)

			let data1
			Array.isArray(data) ? (data1 = data) : (data1 = [])
			while (data1.length < this.room_capacity) {
				data1.push({})
			}
			this.bedList = data1.map((v) => {
				return {
					...v,
					isCanEdit: false,
					has: false,
					remarkCopy: v.remark
				}
			})
		},

		//树点击事件
		groupClick(data, node, TreeNode) {
			if (data.id.indexOf('c') == -1) {
				ElMessage.warning('请选择房间')
				this.$refs.group?.setCurrentKey(...this.defaultKeys)
				return false
			} else {
				this.defaultKeys = [data.id]
			}

			this.room_capacity = data.room_capacity
			this.params.room_id = data.uid
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		},
		//新增宿舍楼
		addLou() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},
		remove() {
			let hasData = this.bedList[this.setCurrentBed]
			if (!hasData.id) return ElMessage.warning('请选择非空床位')
			this.dialog.leave = true

			this.$nextTick(() => {
				this.$refs.leaveDialog.open('edit').setData(hasData)
			})
		},
		//楼层保存回调
		handleSaveSuccess() {
			this.getRoom()
		}
	}
}
</script>

<style scoped lang="scss">
.currentBed {
	position: relative;
	&::after {
		content: '';
		width: 100%;
		height: 100%;
		border: 3px solid gold;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		border-radius: 8px;
		pointer-events: none;
	}
}
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;
	.el-button {
		width: 80%;
	}
}
.roomList {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	grid-gap: 20px;
	.roomItem {
		--cc: #eee;

		height: auto;
		border: 1px solid var(--cc);
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		&-head {
			width: 100%;
			padding: 10px 20px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			position: relative;
			border-bottom: 1px solid var(--cc);
		}
		&-body {
			padding: 10px;
			flex: 1;
			&-row {
				display: flex;
				margin-top: 10px;
				color: #615d5d;
				.label {
					flex-shrink: 0;
				}
			}
		}
		&-foot {
			padding: 10px 20px;
			text-align: right;
			text {
				cursor: pointer;
			}
		}
	}
}
</style>
