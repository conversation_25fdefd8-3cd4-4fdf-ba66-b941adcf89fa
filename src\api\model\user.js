import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		//url: `${config.MOCK_URL}/system/user/list`,
		url: `${config.API_URL}/sysapi/user/list`,
		name: '获取用户列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/user/save`,
		name: '新增用户/修改',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	setRoles: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/user/setRoles`,
		name: '批量分配角色',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	saveGrid: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/user/saveGrid`,
		name: '保存用户控制台视图',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	saveMods: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/user/saveMods`,
		name: '保存用户工作台配置',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	del: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/user/del`,
		name: '删除用户',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	passwordResetBatch: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/user/passwordResetBatch`,
		name: '批量重置密码',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	passwordReset: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/user/passwordReset`,
		name: '密码重置',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	updateUserPassword: {
		//url: `${config.MOCK_URL}/system/user/save`,
		url: `${config.API_URL}/sysapi/user/updateUserPassword`,
		name: '修改密码',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	},
	updateUserInfo: {
		url: `${config.API_URL}/sysapi/user/updateUserInfo`,
		name: '保存用户信息',
		post: async function (data = {}) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, data)
		}
	}
}
