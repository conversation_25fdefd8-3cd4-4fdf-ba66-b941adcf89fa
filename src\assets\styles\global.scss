.clear:after {
  content: "";
  display: block;
  clear: both;
}
.fl {
  float: left;
}
.fr {
  float: right;
}

.bold {
  font-weight: bold;
}

.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}

.overflow-hidden {
  overflow: hidden;
}
.overflow-scroll {
  overflow: scroll;
}
.overflow-y-auto {
  overflow-y: auto;
}

// 鼠标设为触手
.pointer {
  cursor: pointer;
}

// 定位
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}
.static {
  position: static;
}
.sticky {
  position: sticky;
}

// 宽高
.w-screen {
  width: 100vw;
}
.h-screen {
  height: 100vh;
}

// 设置超出显示省略号
@for $i from 1 through 10 {
  .ellipsis-#{$i} {
    display: -webkit-box !important;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    -webkit-line-clamp: #{$i};
    -webkit-box-orient: vertical !important;
  }
}

@mixin flexCustom() {
  display: flex;
}

// 盒子布局
.flex {
  @include flexCustom();

  &-center {
    @include flexCustom();
    align-items: center;
    justify-content: center;
  }

  &-align-center {
    @include flexCustom();
    align-items: center;
  }

  &-align-start {
    @include flexCustom();
    align-items: flex-start;
  }

  &-align-end {
    @include flexCustom();
    align-items: flex-end;
  }

  &-justify-center {
    @include flexCustom();
    justify-content: center;
  }

  &-justify-start {
    @include flexCustom();
    justify-content: flex-start;
  }

  &-justify-end {
    @include flexCustom();
    justify-content: flex-end;
  }

  &-between {
    @include flexCustom();
    justify-content: space-between;
  }

  &-column {
    @include flexCustom();
    flex-direction: column;
  }

  &-wrap {
    @include flexCustom();
    flex-wrap: wrap;
  }
}

@mixin gridCustom() {
  display: grid;
}

// 网格布局
@for $i from 0 through 500 {
  // 透明度
  .opacity-#{$i} {
    opacity: $i * 0.1;
  }

  .flex-#{$i} {
    flex: $i;
  }

  // 层级
  .z-#{$i} {
    z-index: $i;
  }
  .-z-#{$i} {
    z-index: -$i;
  }

  // 定位
  .left-#{$i} {
    left: #{$i}px;
  }
  .top-#{$i} {
    top: #{$i}px;
  }
  .right-#{$i} {
    right: #{$i}px;
  }
  .bottom-#{$i} {
    bottom: #{$i}px;
  }
  .-left-#{$i} {
    left: -#{$i}px;
  }
  .-top-#{$i} {
    top: -#{$i}px;
  }
  .-right-#{$i} {
    right: -#{$i}px;
  }
  .-bottom-#{$i} {
    bottom: -#{$i}px;
  }

  // 宽度
  .w-#{$i} {
    width: 1px * $i;
  }
  .wMax-#{$i} {
    max-width: 1px * $i;
  }
  .wMin-#{$i} {
    min-width: 1px * $i;
  }

  // 高度
  .h-#{$i} {
    height: 1px * $i;
  }
  .hMax-#{$i} {
    max-height: 1px * $i;
  }
  .hMin-#{$i} {
    min-height: 1px * $i;
  }

  // 字体大小
  .font-#{$i} {
    font-size: #{$i}px;
  }

  .lineH-#{$i} {
    line-height: #{$i}px;
  }

  // // 内边距 padding
  .p-#{$i} {
    padding: #{$i}px;
  }
  .px-#{$i} {
    padding-top: #{$i}px;
    padding-bottom: #{$i}px;
  }
  .py-#{$i} {
    padding-left: #{$i}px;
    padding-right: #{$i}px;
  }
  .pl-#{$i} {
    padding-left: #{$i}px;
  }
  .pt-#{$i} {
    padding-top: #{$i}px;
  }
  .pr-#{$i} {
    padding-right: #{$i}px;
  }
  .pb-#{$i} {
    padding-bottom: #{$i}px;
  }
  .-p-#{$i} {
    padding: -#{$i}px;
  }
  .-px-#{$i} {
    padding-top: -#{$i}px;
    padding-bottom: #{$i}px;
  }
  .-py-#{$i} {
    padding-left: -#{$i}px;
    padding-right: #{$i}px;
  }
  .-pl-#{$i} {
    padding-left: -#{$i}px;
  }
  .-pt-#{$i} {
    padding-top: -#{$i}px;
  }
  .-pr-#{$i} {
    padding-right: -#{$i}px;
  }
  .-pb-#{$i} {
    padding-bottom: -#{$i}px;
  }

  // 外边距 margin
  .m-#{$i} {
    margin: #{$i}px;
  }
  .mx-#{$i} {
    margin-top: #{$i}px;
    margin-bottom: #{$i}px;
  }
  .my-#{$i} {
    margin-left: #{$i}px;
    margin-right: #{$i}px;
  }
  .ml-#{$i} {
    margin-left: #{$i}px;
  }
  .mt-#{$i} {
    margin-top: #{$i}px;
  }
  .mr-#{$i} {
    margin-right: #{$i}px;
  }
  .mb-#{$i} {
    margin-bottom: #{$i}px;
  }
  .-m-#{$i} {
    margin: -#{$i}px;
  }
  .-mx-#{$i} {
    margin-top: -#{$i}px;
    margin-bottom: #{$i}px;
  }
  .-my-#{$i} {
    margin-left: -#{$i}px;
    margin-right: #{$i}px;
  }
  .-ml-#{$i} {
    margin-left: -#{$i}px;
  }
  .-mt-#{$i} {
    margin-top: -#{$i}px;
  }
  .-mr-#{$i} {
    margin-right: -#{$i}px;
  }
  .-mb-#{$i} {
    margin-bottom: -#{$i}px;
  }

  // // 圆角 border-radius
  .brs-#{$i} {
    border-radius: #{$i}px;
  }

  // 网格布局
  .grid {
    @include gridCustom();

    &-columns-#{$i} {
      @include gridCustom();
      grid-template-columns: repeat(#{$i}, 1fr);
    }

    &-rows-#{$i} {
      @include gridCustom();
      grid-template-rows: repeat(#{$i}, 1fr);
    }

    &-gap-#{$i} {
      @include gridCustom();
      gap: #{$i}px;
    }

    &-column-gap-#{$i} {
      @include gridCustom();
      column-gap: #{$i}px;
    }

    &-row-gap-#{$i} {
      @include gridCustom();
      row-gap: #{$i}px;
    }
  }
  .row-gap-#{$i} {
    row-gap: #{$i}px;
  }
}

$uv-primary: #409eff;
$uv-success: #67c23a;
$uv-warning: #e6a23c;
$uv-error: #f56c6c;
$uv-info: #909399;

.text-color-white {
  color: #fff;
}
.text-color-black {
  color: #000;
}

.bg-color-white {
  background: #fff;
}
.bg-color-black {
  background: #000;
}

.bg-color-primary {
  background: $uv-primary;
}
.bg-color-success {
  background: $uv-success;
}
.bg-color-warning {
  background: $uv-warning;
}
.bg-color-error,
.bg-color-danger {
  background: $uv-error;
}

.border-color-black {
  border-color: black !important;
}
.border-color-white {
  border-color: white !important;
}
.border-color-primary {
  border-color: $uv-primary !important;
}
.border-color-success {
  border-color: $uv-success !important;
}
.border-color-warning {
  border-color: $uv-warning !important;
}
.border-color-error,
.border-color-danger {
  border-color: $uv-error !important;
}

@for $i from 0 through 100 {
  .text-color-black-#{$i} {
    color: rgba(0, 0, 0, $i * 0.01);
  }
  .text-color-white-#{$i} {
    color: rgba(255, 255, 255, $i * 0.01);
  }
  .text-color-primary-#{$i} {
    color: rgba($color: $uv-primary, $alpha: $i * 0.01);
  }
  .text-color-success-#{$i} {
    color: rgba($color: $uv-success, $alpha: $i * 0.01);
  }
  .text-color-warning-#{$i} {
    color: rgba($color: $uv-warning, $alpha: $i * 0.01);
  }
  .text-color-error-#{$i},
  .text-color-danger-#{$i} {
    color: rgba($color: $uv-error, $alpha: $i * 0.01);
  }

  .bg-color-black-#{$i} {
    background: rgba(0, 0, 0, $i * 0.01);
  }
  .bg-color-white-#{$i} {
    background: rgba(255, 255, 255, $i * 0.01);
  }
  .bg-color-primary-#{$i} {
    background: rgba($color: $uv-primary, $alpha: $i * 0.01);
  }
  .bg-color-success-#{$i} {
    background: rgba($color: $uv-success, $alpha: $i * 0.01);
  }
  .bg-color-warning-#{$i} {
    background: rgba($color: $uv-warning, $alpha: $i * 0.01);
  }
  .bg-color-error-#{$i},
  .bg-color-danger-#{$i} {
    background: rgba($color: $uv-error, $alpha: $i * 0.01);
  }

  .border-color-black-#{$i} {
    border-color: rgba(0, 0, 0, $i * 0.01) !important;
  }
  .border-color-white-#{$i} {
    border-color: rgba(255, 255, 255, $i * 0.01) !important;
  }
  .border-color-primary-#{$i} {
    border-color: rgba($color: $uv-primary, $alpha: $i * 0.01) !important;
  }
  .border-color-success-#{$i} {
    border-color: rgba($color: $uv-success, $alpha: $i * 0.01) !important;
  }
  .border-color-warning-#{$i} {
    border-color: rgba($color: $uv-warning, $alpha: $i * 0.01) !important;
  }
  .border-color-error-#{$i},
  .border-color-danger-#{$i} {
    border-color: rgba($color: $uv-error, $alpha: $i * 0.01) !important;
  }
}

// ====================================================

// 主题色
.text-color-info {
  color: $uv-info;
}
.text-color-primary {
  color: $uv-primary;
}
.text-color-success {
  color: $uv-success;
}
.text-color-warning {
  color: $uv-warning;
}
.text-color-error,
.text-color-danger {
  color: $uv-error;
}

// 置灰
.setGreyOut {
  filter: grayscale(1);
}
