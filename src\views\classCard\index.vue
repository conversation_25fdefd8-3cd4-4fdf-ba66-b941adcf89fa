<template>
	<div class="el-main">
		<el-row :gutter="10" style="height: 100%">
			<el-col :span="6" style="height: 100%">
				<el-card class="classInfo">
					<template v-if="classInfo != null">
						<div class="class">
							<div class="className">
								<p>{{ classInfo.class_name }}</p>
								<p>{{ classInfo.room?.name }}</p>
							</div>
							<div v-if="classInfo.leader_teacher !== ''" class="headTeacher">
								<p>班主任</p>
								<p>{{ classInfo.leader_teacher }}</p>
							</div>
						</div>
						<div class="people">
							<el-row>
								<el-col :span="8"><el-statistic title="学生" :value="classInfo.student_num" /> </el-col>
								<el-col :span="8"><el-statistic title="教师" :value="classInfo.teacher_num" /> </el-col>
							</el-row>
						</div>
					</template>
					<el-empty v-else description="无关联班级数据" :image-size="120"></el-empty>
				</el-card>
				<el-card class="todayCourse">
					<template #header>
						<div class="card-header">
							<span>今日课表</span>
						</div>
					</template>
					<el-scrollbar v-if="scheduleData.length > 0">
						<ul>
							<li
								v-for="(item, index) in scheduleData"
								:key="index"
								class="course"
								:class="index < current ? 'expired' : index === current ? 'current' : ''"
							>
								<div class="course_time">
									<p>第{{ index + 1 }}节</p>
									<p class="time">{{ item.begin_time }} - {{ item.end_time }}</p>
								</div>
								<div class="course_name">
									<span>{{ item.course_name }}</span>
								</div>
								<div class="course_teacher">
									<span>{{ item.teacher_name }}</span>
								</div>
							</li>
						</ul>
					</el-scrollbar>
					<el-empty v-if="scheduleData.length === 0" description="今日无课程数据"></el-empty>
				</el-card>
			</el-col>
			<el-col :span="12" style="height: 100%">
				<el-card class="publicity">
					<el-carousel
						v-if="classMien.list.length > 0"
						height="100%"
						:interval="8000"
						:autoplay="hasVideo === false"
						arrow="always"
						motion-blur
					>
						<el-carousel-item v-for="(item, index) in classMien.list" :key="index">
							<video
								v-if="item.is_video === 1"
								controls
								style="width: 100%; height: 100%; background-color: #000"
								controlslist="nodownload"
							>
								<source :src="item.url" />
								<p>当前设备不支持 HTML5 视频。</p>
							</video>
							<el-image v-else :src="item.url" fit="cover" style="width: 100%; height: 100%" />
						</el-carousel-item>
					</el-carousel>
					<el-empty v-if="classMien.list.length === 0" description="暂无班级风采数据"></el-empty>
				</el-card>
				<el-card class="current_class">
					<el-row v-if="scheduleData.length > 0" :gutter="10">
						<el-col :span="12" :class="{ bag: current != null && scheduleData[current] }">
							<div v-if="current != null && scheduleData[current]" class="current_class_info">
								<div>
									<p class="info">正在上课</p>
									<p class="course_name">{{ scheduleData[current].course_name }}</p>
								</div>
								<div>
									<p class="info">{{ scheduleData[current].begin_time }}-{{ scheduleData[current].end_time }}</p>
									<p class="info">{{ scheduleData[current].teacher_name }}</p>
								</div>
							</div>
							<el-empty v-else description="当前时间无课程" :image-size="120"></el-empty>
						</el-col>
						<el-col :span="6">
							<div v-if="next != null && scheduleData[next]" class="next_class_info">
								<div>
									<p class="course_name">{{ scheduleData[next].course_name }}</p>
									<p class="info">{{ scheduleData[next].begin_time }}-{{ scheduleData[next].end_time }}</p>
									<p class="info">{{ scheduleData[next].teacher_name }}</p>
								</div>
							</div>
						</el-col>
						<el-col :span="6">
							<div v-if="next != null && scheduleData[next + 1]" class="next_class_info">
								<div>
									<p class="course_name">{{ scheduleData[next + 1].course_name }}</p>
									<p class="info">{{ scheduleData[next + 1].begin_time }}-{{ scheduleData[next + 1].end_time }}</p>
									<p class="info">{{ scheduleData[next + 1].teacher_name }}</p>
								</div>
							</div>
						</el-col>
					</el-row>
					<el-empty v-if="scheduleData.length === 0" description="今日无课程数据"></el-empty>
				</el-card>
			</el-col>
			<el-col :span="6" style="height: 100%">
				<!--				<el-card class="todayAttendance">
					<div>
						<scEcharts class="scEcharts" :option="attendanceView" width="100%" height="100%"></scEcharts>
					</div>
				</el-card>-->
				<el-card class="attendance">
					<template #header>
						<div class="card-header">
							<span>考勤记录</span>
						</div>
					</template>
					<div class="attendance_content">
						<el-table v-if="attendanceData != null && attendanceData.length > 0" :data="attendanceData">
							<el-table-column prop="created_at" label="考勤时间" width="120"></el-table-column>
							<el-table-column prop="name" label="学生">
								<template #default="scope">
									{{ scope.row.student?.name }}
								</template>
							</el-table-column>
							<el-table-column prop="status" label="考勤状态" width="80">
								<template #default="scope">
									<el-tag v-if="scope.row.attendance_status === 1" type="success">正常</el-tag>
									<el-tag v-else-if="scope.row.attendance_status === 2" type="warning">迟到</el-tag>
								</template>
							</el-table-column>
						</el-table>
						<el-empty v-else description="暂无考勤数据"></el-empty>
					</div>
				</el-card>
				<el-card class="notice">
					<template #header>
						<div class="card-header">
							<span>通知公告</span>
						</div>
					</template>
					<el-text v-if="notice.content !== ''" class="notice_content" line-clamp="6">
						{{ notice.content }}
					</el-text>
					<el-empty v-if="notice.content === ''" description="暂无公告" :image-size="120"></el-empty>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>
<script>
//import scEcharts from '@/components/scEcharts/index.vue'
import { ElMessageBox } from 'element-plus'

export default {
	name: 'index',
	//components: { scEcharts },
	data() {
		return {
			interval: null,
			activeTab: 'elegant',
			current: null,
			next: null,
			notice: {
				content: '',
				title: ''
			},
			classInfo: null,
			hasVideo: false,
			scheduleData: [],
			attendanceData: [],
			classMien: {
				list: [],
				code: ''
			},
			orgId: 0,
			deviceSn: ''
		}
	},
	created() {
		this.orgId = this.$route.query.orgId
		this.deviceSn = this.$route.query.deviceSn
		this.getInfo()
		/*if (this.interval !== null) {
			clearInterval(this.interval)
		}*/
		//this.interval = setTimeout(this.getInfo, 60 * 1000)
	},
	unmounted() {
		clearTimeout(this.interval)
		this.interval = null
	},
	mounted() {},
	methods: {
		tabChange(activeName) {
			if (activeName !== 'topic') {
				this.$refs.videoPlayer.pause()
			} else {
				this.$refs.videoPlayer.play()
			}
		},
		getInfo() {
			if (this.orgId === 0 || this.deviceSn === '') {
				return
			}
			clearTimeout(this.interval)
			this.interval = null
			this.$API.classCard.info.get({ orgId: this.orgId, deviceSn: this.deviceSn }).then((res) => {
				if (res.code === 200) {
					this.classInfo = res.data.class_info
					this.scheduleData = res.data.schedule_list
					let now = new Date()
					let hourMinute = now.getHours() + ':' + now.getMinutes()
					hourMinute = parseInt(hourMinute.split(':').join(''))
					//hourMinute = '12:30'
					for (let i = 0; i < this.scheduleData.length; i++) {
						let begin_time = this.scheduleData[i].begin_time
						let end_time = this.scheduleData[i].end_time
						begin_time = parseInt(begin_time.split(':').join(''))
						end_time = parseInt(end_time.split(':').join(''))
						if (hourMinute >= begin_time && hourMinute <= end_time) {
							this.current = i
							this.next = i + 1
							break
						}
						if (begin_time >= hourMinute) {
							this.next = i
							break
						}
					}

					this.attendanceData = res.data.attendance_list
					if (this.classMien.code !== res.data.class_mien.code) {
						this.classMien = res.data.class_mien
						for (let i = 0; i < this.classMien.length; i++) {
							if (this.classMien[i].is_video === 1) {
								this.hasVideo = true
								break
							}
						}
					}
					this.notice = res.data.notice
					this.interval = setTimeout(this.getInfo, 60 * 1000)
				} else {
					ElMessageBox.alert(res.message, '提示', {
						// if you want to disable its autofocus
						// autofocus: false,
						confirmButtonText: '重试',
						callback: () => {
							this.getInfo()
						}
					})
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
.el-main {
	background: linear-gradient(to right, #318dfe, #1d9ffd, #0f5ecd);
	width: 100%;
	height: 100%;
}
.grid-content {
	border-radius: 4px;
	min-height: 36px;
}
.ep-bg-purple {
	background: #2a3746;
}
:deep(.el-card) {
	border-radius: 8px;
	border: unset;
}
:deep(.publicity) {
	height: calc(65% - 15px);
	.el-card__body {
		padding: unset !important;
		line-height: 0;
		height: 100%;
	}
	.el-tabs {
		height: 100%;
	}
	.el-tabs__content {
		padding: unset !important;
		line-height: 0;
		height: 100%;
	}
	.el-tab-pane {
		height: 100%;
	}
	.el-tabs--border-card {
		border: unset;
	}
	.el-carousel {
		width: 100%;
		height: 100%;
	}
}
.classInfo {
	background: url('https://educdn.xjzredu.cn/scms/system/front/pc/member.png');
	background-size: 30%;
	background-color: #fff;
	height: 30%;
	background-repeat: no-repeat;
	background-position: right bottom;
}
.class {
	background: linear-gradient(
		to right,
		var(--el-color-primary),
		var(--el-color-primary-light-2),
		var(--el-color-primary)
	);
	color: #fff;
	height: 85px;
	display: flex;
	border-radius: 10px;
	padding: 10px 20px;
	.className {
		width: 70%;
		p:first-child {
			font-size: 20px;
			font-weight: 600;
			line-height: 35px;
		}
		p:nth-child(2) {
			font-size: 12px;
			line-height: 25px;
		}
	}
	.headTeacher {
		width: 30%;
		p:first-child {
			font-size: 14px;
			line-height: 25px;
		}
		p:nth-child(2) {
			font-size: 18px;
			font-weight: 600;
			line-height: 30px;
		}
	}
}
:deep(.people) {
	padding: 10px 10px;
	.el-statistic__head {
		color: var(--el-text-color-secondary);
		font-size: 14px;
		line-height: 25px;
	}
	.el-statistic__content {
		font-size: 32px;
		font-weight: 600;
		line-height: 35px;
		color: var(--el-color-primary);
	}
}
:deep(.todayCourse) {
	height: calc(70% - 15px);
	margin-bottom: 0px;
	.el-card__body {
		height: calc(100% - 40px);
	}
}

.course {
	display: flex;
	height: 65px;
	align-items: center;
	font-size: 14px;
	line-height: 20px;
	border-radius: 10px;
	border-bottom: 1px solid var(--el-border-color-light);
	div {
		width: 33%;
	}
	.course_time {
		text-align: center;
		.time {
			color: var(--el-text-color-secondary);
			font-size: 12px;
		}
	}
	.course_name {
		text-align: center;
	}
	.course_teacher {
		text-align: right;
		padding-right: 20px;
	}
}
.todayAttendance {
	height: 30%;
}
.current {
	background-color: var(--el-color-primary-light-8);
}
.expired {
	color: var(--el-text-color-disabled);
	.course_time .time {
		color: var(--el-text-color-disabled);
	}
}
.current_class {
	height: 35%;
	margin-bottom: 0;
	.el-row {
		height: 100%;
		.el-col {
			height: 100%;
			border-right: 1px solid var(--el-border-color-light);
		}
	}
	.bag {
		background: url('https://educdn.xjzredu.cn/scms/system/front/pc/flower3.png');
		background-size: 45%;
		background-color: #fff;
		background-repeat: no-repeat;
		background-position: right bottom;
	}
	.current_class_info {
		display: flex;
		padding: 20px;
		.info {
			color: var(--el-text-color-secondary);
			font-size: 14px;
		}
		div {
			width: 50%;
			line-height: 40px;
		}
		div:nth-child(2) {
			text-align: right;
		}
		.course_name {
			font-size: 24px;
			font-weight: 600;
		}
	}
	.next_class_info {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		line-height: 30px;
		text-align: center;
		.course_name {
			font-size: 16px;
			font-weight: 600;
		}
		.info {
			font-size: 14px;
			color: var(--el-text-color-secondary);
		}
	}
}
:deep(.attendance) {
	height: calc(65% - 15px);
	.el-card__body {
		height: calc(100% - 40px);
	}
	.attendance_content {
		height: 100%;
	}
	.el-table {
		height: 100%;
		width: 100%;
	}
}
:deep(.current_class .el-card__body) {
	padding: unset !important;
	height: 100%;
}

:deep(.notice) {
	height: 35%;
	margin-bottom: 0;
	.notice_content {
		height: 100%;
		line-height: 25px;
		font-size: 14px;
		overflow: hidden; /* 隐藏超出容器的内容 */
		text-overflow: ellipsis; /* 超出部分使用省略号表示 */
	}
	.el-card__body {
		height: calc(100% - 40px);
	}
}
</style>
