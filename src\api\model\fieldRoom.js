import config from '@/config'
import http from '@/utils/request'

export default {
	all: {
		url: `${config.API_URL}/affapi/building/all`,
		name: '获取场室楼列表不分页',
		get: async function(params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	create: {
		url: `${config.API_URL}/affapi/building/creat`,
		name: '新增场室楼',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	save: {
		url: `${config.API_URL}/affapi/building/save`,
		name: '场室楼修改',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	openDoor: {
		url: `${config.API_URL}/affapi/rooms/openDoor`,
		name: '远程开门',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	openDoorByDeviceId: {
		url: `${config.API_URL}/affapi/rooms/openDoorByDeviceId`,
		name: '远程开门',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/affapi/building/del`,
		name: '删除场室楼',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	status: {
		url: `${config.API_URL}/manapi/dormitory/building_changeStatus`,
		name: '修改场室楼状态',
		post: async function(data = {}) {
			return await http.post(this.url, data)
		}
	},
	tree: {
		url: `${config.API_URL}/affapi/rooms/tree`,
		name: '获取场室树',
		get: async function(params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	rooms: {
		list: {
			url: `${config.API_URL}/affapi/rooms/list`,
			name: '获取场室楼房间列表',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		all: {
			url: `${config.API_URL}/affapi/rooms/all`,
			name: '获取场室楼房间所有的',
			get: async function(params) {
				// eslint-disable-next-line no-return-await
				return await http.get(this.url, params)
			}
		},
		save: {
			url: `${config.API_URL}/affapi/rooms/save`,
			name: '场室楼房间修改',
			post: async function(data = {}) {
				return await http.post(this.url, data)
			}
		},
		del: {
			url: `${config.API_URL}/affapi/rooms/del`,
			name: '删除场室楼房间',
			post: async function(data = {}) {
				return await http.post(this.url, data)
			}
		},
		status: {
			url: `${config.API_URL}/affapi/rooms/room_changeStatus`,
			name: '修改场室楼房间状态',
			post: async function(data = {}) {
				return await http.post(this.url, data)
			}
		},
		schedule: {
			url: `${config.API_URL}/affapi/rooms/schedule`,
			name: '获取房间课表',
			get: async function(data = {}) {
				return await http.get(this.url, data)
			}
		}
	},
	view: {
		url: `${config.API_URL}/manapi/dormitory/building_view`,
		name: '获取宿舍',
		get: async function(params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	}
}
