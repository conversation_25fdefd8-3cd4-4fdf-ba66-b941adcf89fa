<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" :width="500" destroy-on-close @closed="$emit('closed')">
		<cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode"> </cusForm>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { semesterInfo, dormitoryEvaluateTypeMap } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		semester_id: null,
		tenant_id: null,
		campus_id: null,
		dormitory_usersObj: [],
		dormitory_room_id: null,
		dormitory_room_idObj: [],
		evaluate_id: null,
		academic_id: null,
		nature: null,
		img_file: null,
		score: null,
		chooseType: null
	}
}
import { ElMessageBox } from 'element-plus'

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		},
		chooseType: {
			type: Number,
			default: 1
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '一键配置',
				edit: '住宿记录',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				oth_semester_id: [{ required: true, message: '请选择学期' }],
				oth_academic_id: [{ required: true, message: '请选择学年' }]
			},
			//所需数据选项
			groups: [],
			groupsProps: {
				value: 'id',
				label: 'evaluate_name',
				emitPath: false
			},
			tempArr: [],
			gradeOptions: [],
			semesterInfo,
			grade: [],
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				labelWidth: '80px',

				formItems: [
					{
						label: '寝室',
						name: 'dormitory_room_idObj',
						value: null,
						component: 'cusSelectTree',
						options: {
							title: '选择寝室',
							tree: this.$API.fieldRoom.tree,
							disabled: 'true'
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }],
						hideHandle: '$.chooseType == 2'
					},
					{
						label: '住宿生',
						name: 'dormitory_usersObj',
						component: 'cusSelectStudent',
						options: {
							disabled: 'true'
						},
						hideHandle: '$.chooseType == 1'
					},
					{
						label: '评价配置',
						name: 'evaluate_id',
						value: null,
						component: 'cascader',
						options: {
							width: '100%',
							prop: {
								emitPath: false,
								value: 'id',
								label: 'evaluate_name',
								children: 'children'
							},
							items: []
						},
						rules: [{ required: true, message: '请选择', trigger: 'blur' }],
						hideHandle: '$.type == "已有耗材增加库存"'
					},
					{
						label: '性质',
						name: 'nature',
						value: null,
						component: 'radio',
						options: {
							placeholder: '请输入',
							items: [
								{ label: '正面', value: 1 },
								{ label: '负面', value: -1 }
							],
							disabled: true
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					{
						label: '分值',
						name: 'score',
						value: null,
						component: 'number',
						options: {
							placeholder: '',
							controlsPosition: '1',
							disabled: true
						},
						rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					},
					// {
					// 	label: '最大分值',
					// 	name: 'score',
					// 	value: null,
					// 	component: 'number',
					// 	options: {
					// 		placeholder: '',
					// 		controlsPosition: '1'
					// 	},
					// 	rules: [{ required: true, message: '请输入', trigger: 'blur' }]
					// },
					{
						label: '附件',
						component: 'upload',
						options: {
							items: [
								{
									name: 'img_file',
									label: '附件',
									type: '/dormitory_evaluate'
								}
							]
						}
					},
					{
						label: '备注',
						name: 'remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入'
						}
					}
				]
			}
		}
	},
	mounted() {},
	watch: {
		'form.evaluate_id': {
			handler(val) {
				let cur = this.tempArr.find((v) => v.id == val)
				this.form.nature = cur.nature
				this.form.score = cur.score
			}
		}
	},
	computed: {},
	methods: {
		async getTreeData() {
			let res = await this.$API.buildingRooms.evaluate.all.get({
				campus_id: this.form.campus_id,
				tenant_id: this.form.tenant_id
			})
			let data1 = []
			let typeArr = dormitoryEvaluateTypeMap.map((v) => {
				return {
					id: v.value,
					evaluate_name: v.name
				}
			})
			if (res.data) {
				data1 = [...res.data, ...typeArr]
			}
			this.tempArr = res.data
			this.groups = cusTom.arrayToTree(data1, 'id', 'type_id', 'children')
			this.formConfig.formItems.find((v) => v.name == 'evaluate_id').options.items = this.groups
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.form.mode = mode
			this.form.chooseType = this.chooseType
			if (mode == 'edit') {
				this.form.campus_id = this.params.campus_id
				this.form.tenant_id = this.params.tenant_id
				this.form.academic_id = this.params.academic_id
				this.form.semester_id = this.params.semester_id
				this.form.dormitory_room_id = this.params.room_id
			}
			this.getTreeData()
			this.visible = true
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.formref.validate(async (valid) => {
				if (valid) {
					this.form.dormitory_users = this.form.dormitory_usersObj.map((v) => v.id).toString()
					if (this.chooseType == 1) {
						this.form.dormitory_room_id = this.form.dormitory_room_idObj.map((v) => v.id)[0]
					}
					this.isSaveing = true
					var res = await this.$API.buildingRooms.evaluate.action.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			if (this.chooseType == 2) {
				this.form.dormitory_usersObj = data.map((v) => {
					if (v.staff_info) {
						return {
							label: v.staff_info.name,
							id: v.user_id
						}
					}
					if (v.student_info) {
						return {
							label: v.student_info.student_name,
							id: v.user_id
						}
					}
				})
			} else {
				this.form.dormitory_room_idObj = data.checkRoomList.map((v) => {
					return {
						label: v.label,
						id: v.id
					}
				})
				console.log(data, 'data')
			}
		}
	}
}
</script>

<style scoped lang="scss">
.tip {
	color: #808080;
	background-color: #f6f8fa;
	padding: 20px;
}
</style>
