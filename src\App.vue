<template>
	<el-config-provider
		:locale="locale"
		:size="config.size"
		:message="config.messageSize"
		:zIndex="config.zIndex"
		:button="config.button"
	>
		<router-view></router-view>
	</el-config-provider>
</template>

<script>
import colorTool from '@/utils/color'
export default {
	name: 'App',
	data() {
		return {
			layoutWaterMark: this.$CONFIG.LAYOUT_WATER_MARK || false,
			config: {
				size: this.$CONFIG.LAYOUT_SIZE || 'default',
				zIndex: 2000,
				messageSize: { max: 3 },
				button: {
					autoInsertSpace: false
				}
			}
		}
	},
	computed: {
		locale() {
			return this.$i18n.messages[this.$i18n.locale].el
		}
	},
	mounted() {
		let html_box = document.getElementById('ash')
		let tenant_id = localStorage.getItem('tenant_id')
		if (tenant_id) {
			this.$API.common.getAsh
				.get({
					tenant_id: tenant_id
				})
				.then((res) => {
					if (res.code === 200 && res.data !== null && res.data.length > 0) {
						html_box.style.filter = 'grayscale(100%)'
					} else {
						html_box.style.filter = 'grayscale(0)'
					}
				})
				.catch((err) => {
					console.log(err)
					html_box.style.filter = 'grayscale(0)'
				})
		}
	},
	created() {
		//设置主题颜色
		const app_color = this.$CONFIG.COLOR || this.$TOOL.data.get('APP_COLOR')
		if (app_color) {
			document.documentElement.style.setProperty('--el-color-primary', app_color)
			for (let i = 1; i <= 9; i++) {
				document.documentElement.style.setProperty(
					`--el-color-primary-light-${i}`,
					colorTool.lighten(app_color, i / 10)
				)
			}
			for (let i = 1; i <= 9; i++) {
				document.documentElement.style.setProperty(`--el-color-primary-dark-${i}`, colorTool.darken(app_color, i / 10))
			}
		}
	}
}
</script>

<style lang="scss">
@import '@/style/style.scss';
</style>
