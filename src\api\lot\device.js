import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/lot/device/list`,
		name: '获取设备列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	all: {
		url: `${config.API_URL}/lot/device/all`,
		name: '获取所有设备列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getGatewayDevice: {
		url: `${config.API_URL}/lot/device/getGatewayDevice`,
		name: '获取网关设备列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/lot/device/save`,
		name: '新增设备/修改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	del: {
		url: `${config.API_URL}/lot/device/del`,
		name: '删除设备',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	one: {
		url: `${config.API_URL}/lot/device/one`,
		name: '获取单个设备',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getDeviceProperties: {
		url: `${config.API_URL}/lot/device/getDeviceProperties`,
		name: '获取设备属性值',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getDeviceTslWithLastValue: {
		url: `${config.API_URL}/lot/device/getDeviceTslWithLastValue`,
		name: '获取设备物模型及最后值',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getDeviceEventsLog: {
		url: `${config.API_URL}/lot/device/getDeviceEventsLog`,
		name: '获取设备事件记录',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getDevicePropertiesLog: {
		url: `${config.API_URL}/lot/device/getDevicePropertiesLog`,
		name: '获取属性历史值',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getDevicePropertiesChart: {
		url: `${config.API_URL}/lot/device/getDevicePropertiesChart`,
		name: '获取属性历史值',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getDeviceServiceLog: {
		url: `${config.API_URL}/lot/device/getDeviceServiceLog`,
		name: '获取设备服务记录',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getDeviceOriginalLog: {
		url: `${config.API_URL}/lot/device/getDeviceOriginalLog`,
		name: '获取设备日志',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	gatewayReboot: {
		url: `${config.API_URL}/lot/device/gatewayReboot`,
		name: '网关重启',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	deviceHeart: {
		url: `${config.API_URL}/lot/device/deviceHeart`,
		name: '设置设备心跳周期',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	gatewayGetAppVer: {
		url: `${config.API_URL}/lot/device/gatewayGetAppVer`,
		name: '获取网关版本',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	gatewayResetFactory: {
		url: `${config.API_URL}/lot/device/gatewayResetFactory`,
		name: '网关恢复出厂设置',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	gatewayUnBind: {
		url: `${config.API_URL}/lot/device/gatewayUnBind`,
		name: '网关解绑',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	getZBNwkInfo: {
		url: `${config.API_URL}/lot/device/getZBNwkInfo`,
		name: '查询网关ZigBee网络信息',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	gatewayQueryZBNwkInfo: {
		url: `${config.API_URL}/lot/device/gatewayQueryZBNwkInfo`,
		name: '获取网关ZigBee网络信息',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	gatewayDefaultFormZBNwk: {
		url: `${config.API_URL}/lot/device/gatewayDefaultFormZBNwk`,
		name: '网关创建ZigBee网络',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	gatewayGetDevList: {
		url: `${config.API_URL}/lot/device/gatewayGetDevList`,
		name: '刷新子设备列表',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	gatewayDeleteDev: {
		url: `${config.API_URL}/lot/device/gatewayDeleteDev`,
		name: '网关删除子设备',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	writeDevProperty: {
		url: `${config.API_URL}/lot/device/writeDevProperty`,
		name: '写设备属性',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	checkRegister: {
		url: `${config.API_URL}/lot/device/checkRegister`,
		name: '注册入网检测',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	register: {
		url: `${config.API_URL}/lot/device/register`,
		name: '注册入网',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	invokeDevCommand: {
		url: `${config.API_URL}/lot/device/invokeDevCommand`,
		name: '调用设备服务',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	permitAllJoin: {
		url: `${config.API_URL}/lot/device/permitAllJoin`,
		name: '允许所有子设备加入ZigBee网络',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	readDevProperty: {
		url: `${config.API_URL}/lot/device/readDevProperty`,
		name: '读取设备属性',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	getDeviceLastLog: {
		url: `${config.API_URL}/lot/device/getDeviceLastLog`,
		name: '获取设备最新日志',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	getAllDeviceRoom: {
		url: `${config.API_URL}/lot/device/getAllDeviceRoom`,
		name: '获取所有的设备场室',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	}
}
