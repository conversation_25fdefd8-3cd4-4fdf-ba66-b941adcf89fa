<template>
	<el-form-item label="时间格式" prop="time_format">
		<el-input v-model="time_format" style="width: 100%" disabled readonly></el-input>
	</el-form-item>
</template>
<script>
export default {
	name: 'dateModule',
	props: {
		modelValue: {
			type: Object,
			default: () => {
				return {}
			}
		}
	},
	data() {
		return {
			time_format: '整数类型Int64的UTC时间戳（单位：毫秒）',
			unitConfig: []
		}
	},
	emits: ['update:modelValue'], // 明确声明该组件会触发 update:modelValue 事件
	watch: {
		modelValue: {
			handler(val) {
				this.$emit('update:modelValue', val)
			},
			deep: true
		}
	},
	created() {},
	methods: {}
}
</script>

<style scoped lang="scss">
.enum_header {
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.enum_item {
	margin: 5px 0;
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.text-center {
	text-align: center;
}
</style>
