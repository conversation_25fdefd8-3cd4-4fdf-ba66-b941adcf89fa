<template>
	<el-container>
		<el-aside width="270px">
			<el-container>
				<el-header v-if="CampusManagementList.length > 1">
					<el-select v-if="CampusManagementList.length > 1" v-model="params.campus_id" placeholder="校区" filterable>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-header>
				<el-main>
					<div class="add-lay">
						<el-button type="primary" icon="el-icon-plus" @click="addLou">新增宿舍楼</el-button>
					</div>
					<el-tree
						ref="group"
						class="menu"
						node-key="id"
						:data="groupsAdd"
						:highlight-current="true"
						:props="defaultProps"
						:default-expanded-keys="[0]"
						@node-click="groupClick"
					>
						<template #default="{ node, data }">
							<span class="custom-tree-node" @mouseenter="handleShowTools(data)" @mouseleave="handleShowTools(data)">
								<span>{{ node.label }}</span>
								<span v-if="data.id != 0 && data.id == currentId">
									<a v-if="data.parent_id <= 0" @click="appendCeng(data)">
										<el-icon><el-icon-plus /></el-icon>
									</a>
									<a style="margin-left: 8px" @click.stop="edit(node, data)">
										<el-icon><el-icon-edit /></el-icon>
									</a>
									<a style="margin-left: 8px" @click.stop="remove(node, data)">
										<el-icon><el-icon-delete /></el-icon>
									</a>
								</span>
							</span>
						</template>
					</el-tree>
				</el-main>
			</el-container>
		</el-aside>
		<el-container>
			<el-header>
				<div class="left-panel"></div>
				<div class="right-panel">
					<el-button type="primary" icon="el-icon-plus" @click="addRoom">新增房间</el-button>
					<!-- <el-button type="primary">批量更新</el-button>
					<el-button type="primary">批量录入</el-button>
					<el-button type="primary">导出</el-button>
					<el-button type="primary">排序</el-button> -->
				</div>
			</el-header>
			<el-main>
				<scTable ref="table" row-key="id" stripe :apiObj="list.apiObj" :params="params" @dataChange="dataChange">
					<el-table-column label="宿舍楼" prop="building_name" width="150"></el-table-column>
					<el-table-column
						label="楼层"
						prop="floor_name"
						width="150"
						:filters="floorFilters"
						:filter-method="filterHandler"
					></el-table-column>
					<el-table-column
						label="房间名称"
						prop="room_name"
						:filters="roomNameFilters"
						:filter-method="filterHandler"
					></el-table-column>
					<el-table-column label="房间号" prop="room_code" width="150"></el-table-column>

					<el-table-column label="床位数" prop="room_capacity" width="150" sortable></el-table-column>
					<el-table-column label="空床数" prop="room_empty_num" width="150" sortable></el-table-column>
					<el-table-column label="热水器" prop="status" width="150">
						<template #default="scope">
							<el-tag v-if="scope.row.has_geyser === 1" type="success">有</el-tag>
							<el-tag v-else type="danger">无</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="空调" prop="status" width="150">
						<template #default="scope">
							<el-tag v-if="scope.row.has_air_conditioner === 1" type="success">有</el-tag>
							<el-tag v-else type="danger">无</el-tag>
						</template>
					</el-table-column>

					<el-table-column label="操作" fixed="right" align="center" width="150">
						<template #default="scope">
							<el-button-group>
								<el-button text type="primary" size="small" @click="table_edit(scope.row, scope.$index)"
									>编辑</el-button
								>
								<el-popconfirm title="确定删除吗？" @confirm="delRoom(scope.row, scope.$index)">
									<template #reference>
										<el-button type="danger" size="small" text>删除</el-button>
									</template>
								</el-popconfirm>
							</el-button-group>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
		<!-- {{ $refs.table }}--- -->
		<!-- {{ roomNameFilters }}  -->
	</el-container>
	<save-dialog
		v-if="dialog.save"
		ref="saveDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.save = false"
	></save-dialog>
	<saveRoomDialog
		v-if="dialog1.save"
		ref="saveRoomDialog"
		:params="params"
		:groupData="groupData"
		@success="handleSaveSuccess1"
		@closed="dialog1.save = false"
	>
	</saveRoomDialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
import saveDialog from './save'
import saveRoomDialog from './saveRoom'
const defaultProps = {
	children: 'children',
	label: 'building_name'
}
const { campusId, tenantId, campusInfo, tenantInfo } = cusTom.getBaseQuery()
import { ElMessage, ElMessageBox } from 'element-plus'
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId,
		building_id: null,
		floor_id: null
	}
}

export default {
	name: 'buildingRooms',
	data() {
		return {
			currentId: -1,
			groupFilterText: '',
			groupData: [],
			defaultProps,
			list: {
				apiObj: this.$API.buildingRooms.rooms.list
			},
			params: defaultParams(),

			CampusManagementList: campusInfo,
			JobData: [],
			dialog: {
				save: false
			},
			dialog1: {
				save: false
			},
			showTools: false,
			roomNameFilters: []
		}
	},
	components: {
		saveDialog,
		saveRoomDialog
	},
	watch: {
		'params.campus_id': {
			handler(val) {
				this.building_id = null
				this.floor_id = null
				this.getLou()
				this.getRoom()
			}
		}
	},
	computed: {
		floorFilters() {
			let res = cusTom
				.treeToArray(this.groupData)
				.filter((v) => {
					return v.parent_id > 0
				})
				.map((v) => {
					return {
						text: v.building_name,
						value: v.building_name
					}
				})

			return cusTom.uniqueByValue(res, 'text')
		},
		groupsAdd() {
			let arr = [
				{
					id: 0,
					building_name: '全部'
				},
				...this.groupData
			]
			return arr
		}
	},
	async created() {
		this.getLou()
	},
	methods: {
		handleShowTools(item) {
			this.currentId = item.id
		},
		dataChange(val) {
			if (!val.data.rows) return
			let arr = val.data.rows.map((v) => {
				return {
					text: v.room_name,
					value: v.room_name
				}
			})
			this.roomNameFilters = cusTom.uniqueByValue(arr, 'text')
		},
		filterHandler(value, row, column) {
			const property = column['property']
			return row[property] === value
		},
		// //获取校区列表
		// async getCampus() {
		// 	let tId = this.$TOOL.data.get('USER_INFO').tenant_id
		// 	const { data } = await this.$API.CampusManagement.all.get({
		// 		tenant_id: tId
		// 	})
		// 	this.CampusManagementList = data
		// 	this.params.campus_id = data[0].id
		// 	this.params1.campus_id = data[0].id
		// },
		//获取宿舍楼
		async getLou() {
			const { data } = await this.$API.buildingRooms.all.get({ ...this.params, building_type: null })
			let data1 = data.map((v) => {
				return {
					...v,
					showTools: false
				}
			})
			this.groupData = cusTom.arrayToTree(data1)
		},
		//获取房间列表
		async getRoom() {
			this.$refs.table.upData(this.params1)
		},

		//树点击事件
		groupClick(data) {
			if (data.children && data.children.length > 0) {
				return
			}
			if (data.parent_id <= 0 && data.id != 0) {
				this.params.building_id = data.id
				this.params.floor_id = null
			} else if (data.parent_id > 0 && data.id != 0) {
				this.params.building_id = data.parent_id
				this.params.floor_id = data.id
			} else {
				this.params.building_id = null
				this.params.floor_id = null
			}

			this.getRoom()
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		},
		//新增宿舍楼
		addLou() {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open()
			})
		},
		remove(node, data) {
			const title =
				data.building_type == 2 ? `是否删除楼层${data.building_name}?` : `是否删除宿舍楼${data.building_name}?`

			ElMessageBox.confirm(title, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$API.buildingRooms.del
						.post({
							...data,
							tenant_id: this.params.tenant_id,
							campus_id: this.params.campus_id
						})
						.then((res) => {
							if (res.code === 200) {
								ElMessage({
									type: 'success',
									message: '删除成功'
								})
								this.getLou()
							}
						})
				})
				.catch(() => {})
		},
		//楼层保存回调
		handleSaveSuccess() {
			this.getLou()
		},
		//房间保存回调
		handleSaveSuccess1(data, mode) {
			if (mode == 'add') {
				this.$refs.table.upData(this.search)
			} else if (mode == 'edit') {
				this.$refs.table.refresh()
			}
		},
		//编辑
		table_edit(row) {
			this.dialog1.save = true
			this.$nextTick(() => {
				this.$refs.saveRoomDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row) {
			var reqData = { id: row.id, tenant_id: this.params.tenant_id, campus_id: this.params.campus_id }
			var res = await this.$API.JobManagement.del.post(reqData)

			if (res.code === 200) {
				this.$message.success('删除成功')
				this.getRoom()
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		},
		//状态改变
		statusChange(row) {
			let query = {
				id: row.id,
				status: row.status
			}
			this.$API.JobManagement.status.post(query).then((res) => {
				if (res.code === 200) {
					this.$message.success('操作成功')
					this.getLou()
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//添加层
		appendCeng(data) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add', 2, data.id)
			})
		},
		//添加房间
		addRoom() {
			this.dialog1.save = true
			this.$nextTick(() => {
				this.$refs.saveRoomDialog.open('add').setData({
					building_id: this.params.building_id,
					floor_id: this.params.floor_id
				})
			})
		},
		//楼层树 编辑
		edit(node, data) {
			let obj = JSON.parse(JSON.stringify(data))
			delete obj.children
			obj.building_name = [{ value: obj.building_name }]
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit', obj.building_type, obj.parent_id).setData(obj)
			})
		},
		// 楼层树 删除
		del() {},
		//删除房间
		async delRoom(row, index) {
			var res = await this.$API.buildingRooms.rooms.del.post({
				tenant_id: row.tenant_id,
				campus_id: row.campus_id,
				id: row.id
			})
			if (res.code === 200) {
				this.$emit('success')
				this.visible = false
				// this.upsearch()
				this.$refs.table.refresh()
				this.$message.success('操作成功')
			} else {
				this.$alert(res.message, '提示', { type: 'error' })
			}
		}
	}
}
</script>

<style scoped lang="scss">
.add-lay {
	width: 100%;
	height: 40px;
	text-align: center;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--el-border-color-light);
	justify-content: center;
	.el-button {
		width: 80%;
	}
}
.custom-tree-node {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding-right: 30px;
	box-sizing: border-box;
	align-items: center;
	box-sizing: border-box;
	a {
		font-size: 18px;
	}
}
</style>
