<!--
 * @Descripttion: 表格选择器组件
 * @version: 1.3
 * @Author: sakuya
 * @Date: 2021年6月10日10:04:07
 * @LastEditors: Xujianchen
 * @LastEditTime: 2023-03-18 13:12:15
-->

<template>
	<el-container>
		<el-header class="el-h">
			<el-select
				v-if="CampusManagementList.length > 1"
				v-model="params.campus_id"
				placeholder="校区"
				@change="campusChange"
			>
				<el-option
					v-for="item in CampusManagementList"
					:key="item.code"
					:label="item.name"
					:value="item.value"
				></el-option>
			</el-select>
			<el-select
				v-if="staffTypeMap.length > 1"
				v-model="params.staff_type"
				placeholder="教职工类型"
				style="width: 150px"
				clearable
				@change="getData"
			>
				<el-option
					v-for="(item, index) in staffTypeMap"
					:key="index"
					:label="item.name"
					:value="item.value"
				></el-option>
			</el-select>
			<el-select
				v-if="workStatusMap.length > 1"
				v-model="params.work_status"
				placeholder="在职状态"
				style="width: 150px"
				clearable
				@change="getData"
			>
				<el-option
					v-for="(item, index) in workStatusMap"
					:key="index"
					:label="item.name"
					:value="item.value"
				></el-option>
			</el-select>

			<el-input v-model="formData.name" style="width: 350px" placeholder="请输入姓名" @input="searchKey">
				<template #prefix>
					<el-icon class="el-input__icon"><el-icon-search /></el-icon>
				</template>
			</el-input>
		</el-header>
		<el-tabs v-model="tabName" type="border-card" class="tea_select" @tab-change="tabChange">
			<el-tab-pane label="部门" name="group">
				<el-container>
					<el-aside :style="{ width: treeWidth + 'px' }">
						<el-container>
							<el-main>
								<el-tree
									ref="group"
									class="menu"
									node-key="id"
									:data="groupsAdd"
									:highlight-current="true"
									:expand-on-click-node="false"
									:props="treeDefaultProps"
									:default-expanded-keys="[]"
									@node-click="groupClick"
								>
								</el-tree>
							</el-main>
						</el-container>
					</el-aside>
					<el-main>
						<div v-loading="loading" class="sc-table-select__table">
							<el-table
								ref="table"
								:data="tableData"
								:height="245"
								:highlight-current-row="!multiple"
								@select="select"
								@select-all="selectAll"
							>
								<el-table-column v-if="multiple" type="selection"></el-table-column>
								<el-table-column prop="name" label="姓名" width="90"></el-table-column>
								<el-table-column prop="serial_number" label="编号" width="120"></el-table-column>
								<el-table-column prop="phone" label="电话" width="120"></el-table-column>
								<el-table-column prop="staff_type" label="类型">
									<template #default="scope">
										{{ formaData(staffTypeMap, scope.row.staff_type) }}
									</template>
								</el-table-column>
								<el-table-column prop="work_status" label="状态">
									<template #default="scope">
										{{ formaData(workStatusMap, scope.row.work_status) }}
									</template>
								</el-table-column>
							</el-table>
							<div class="sc-table-select__page">
								<el-pagination
									v-model:currentPage="currentPage"
									small
									background
									layout="prev, pager, next"
									:total="total"
									:page-size="pageSize"
									@current-change="reload"
								></el-pagination>
							</div>
						</div>
					</el-main>
				</el-container>
			</el-tab-pane>
			<el-tab-pane label="学科" name="subject">
				<el-container>
					<el-aside :style="{ width: treeWidth + 'px' }">
						<el-container>
							<el-main>
								<el-tree
									ref="group"
									class="menu"
									node-key="id"
									:data="groupsAdd"
									:highlight-current="true"
									:expand-on-click-node="false"
									:props="treeDefaultProps"
									:default-expanded-keys="[]"
									@node-click="groupClick"
								>
								</el-tree>
							</el-main>
						</el-container>
					</el-aside>
					<el-main>
						<div v-loading="loading" class="sc-table-select__table">
							<el-table
								ref="table"
								:data="tableData"
								:height="245"
								:highlight-current-row="!multiple"
								@select="select"
								@select-all="selectAll"
							>
								<el-table-column v-if="multiple" type="selection"></el-table-column>
								<!--								<el-table-column v-else type="index">
									<template #default="scope"
										><span>{{ scope.$index + (currentPage - 1) * pageSize + 1 }}</span></template
									>
								</el-table-column>-->
								<el-table-column prop="name" label="姓名" width="90"></el-table-column>
								<el-table-column prop="serial_number" label="编号" width="120"></el-table-column>
								<el-table-column prop="phone" label="电话" width="120"></el-table-column>
								<el-table-column prop="staff_type" label="类型">
									<template #default="scope">
										{{ formaData(staffTypeMap, scope.row.staff_type) }}
									</template>
								</el-table-column>
								<el-table-column prop="work_status" label="状态">
									<template #default="scope">
										{{ formaData(workStatusMap, scope.row.work_status) }}
									</template>
								</el-table-column>
							</el-table>
							<div class="sc-table-select__page">
								<el-pagination
									v-model:currentPage="currentPage"
									small
									background
									layout="prev, pager, next"
									:total="total"
									:page-size="pageSize"
									@current-change="reload"
								></el-pagination>
							</div>
						</div>
					</el-main>
				</el-container>
			</el-tab-pane>
			<el-tab-pane label="已选" name="selected">
				<div class="selectBox">
					<div class="selectBox_one">
						<template v-if="multiple">
							<div
								v-for="(v, index) in statusData"
								:key="v.id"
								:class="{ is_active: index == activeIndexStatus }"
								class="st"
								@click="clickStatus(v, index)"
							>
								{{ v.label }}
							</div>
						</template>
						<template v-else>
							<template v-if="statusData.length">
								<section
									v-for="(v, index) in statusData"
									:key="v.id"
									:class="{ is_active: index == activeIndexStatus }"
									class="single-section"
									@click="clickStatus(v, index)"
								>
									{{ v.label }}
								</section>
							</template>
						</template>
					</div>
					<div class="selectBox_three">
						<template v-if="multiple">
							<template v-if="statusData[activeIndexStatus].children.length">
								<section v-for="(v, index) in statusData[activeIndexStatus].children" :key="v.id" class="selectItem">
									<el-button class="el-b" type="primary">
										<span>{{ v.label }}</span>
									</el-button>
								</section>
							</template>
							<template v-else>
								<el-empty description="暂无数据" :image-size="80"></el-empty>
							</template>
						</template>
						<template v-else>
							<template v-if="statusData.length">
								<section v-for="(v, index) in statusData[activeIndexStatus].children" :key="v.id" class="selectItem">
									<el-button class="el-b" type="primary">
										<span>{{ v.label }}</span>
									</el-button>
								</section>
							</template>
							<template v-else>
								<el-empty description="暂无数据" :image-size="80"></el-empty>
							</template>
						</template>
					</div>
				</div>
			</el-tab-pane>
		</el-tabs>
	</el-container>
</template>

<script>
import config from '@/config/tableSelect'
import api from '@/api'
import cusTom from '@/utils/cusTom'
export default {
	props: {
		apiObj: {
			type: Object,
			default: () => {
				return api.personnel.staff.list
			}
		},
		multiple: { type: Boolean, default: false },
		tableWidth: { type: Number, default: 600 },
		treeWidth: { type: Number, default: 240 },
		width: { type: String, default: '200px' },
		props: { type: Object, default: () => {} }
	},
	data() {
		return {
			loading: false,
			keyword: null,
			defaultValue: [],
			tableData: [],
			pageSize: config.pageSize,
			total: 0,
			currentPage: 1,
			defaultProps: {
				label: config.props.label,
				value: config.props.value,
				page: config.request.page,
				pageSize: config.request.pageSize,
				keyword: config.request.keyword
			},
			formData: {},
			params: {
				campus_id: '',
				tenant_id: '',
				work_status: '',
				staff_type: ''
			},
			CampusManagementList: [],
			workStatusMap: [],
			staffTypeMap: [],
			groupData: [],
			treeDefaultProps: {
				children: 'children',
				label: 'name'
			},
			treeNodeId: null,
			typeMap: [
				{
					name: 'group',
					field: 'department_id'
				},
				{
					name: 'subject',
					field: 'course_id'
				}
			],
			type: 'group',
			timer: null,
			rowClickData: [],
			dialogVisible: false,
			statusData: [{ id: 1, label: '已选择', children: [], data: [] }],
			activeIndexStatus: 0,
			shuldBlur: false,
			searchSelect: 1,
			tabName: 'group'
		}
	},
	computed: {
		getWidth() {
			return this.tableWidth + this.treeWidth + 20 + 'px'
		},
		groupsAdd() {
			let arr = [...this.groupData]
			return arr
		}
	},
	watch: {
		'params.campus_id': {
			handler() {},
			immediate: true
		},
		type: {
			handler(val = 'group') {
				// this.callSet(val)
			},
			immediate: true
		},
		rowClickData: {
			handler(newval, oldval) {
				console.log(newval, oldval, 'rowClickData')
				if (!this.multiple) {
					this.statusData[0].children = [newval]
					this.statusData[0].data = this.statusData[0].children.map((item) => item.id)
				} else {
					this.statusData[0].children = Array.from(new Set(newval.map(JSON.stringify))).map(JSON.parse)
					this.statusData[0].data = this.statusData[0].children.map((item) => item.id)
				}
				this.$emit('valChange', newval)
			},
			deep: true
		}
	},
	created() {
		const { campusId, tenantId, campusInfo, workStatusMap, staffTypeMap } = cusTom.getBaseQuery()
		this.CampusManagementList = campusInfo
		this.workStatusMap = workStatusMap
		this.staffTypeMap = staffTypeMap
		this.params.campus_id = campusId
		this.params.tenant_id = tenantId
		this.getDept()
		this.getData()
	},
	mounted() {
		this.defaultProps = Object.assign(this.defaultProps, this.props)
	},
	methods: {
		formaData(arr, val) {
			return arr.find((v) => v.value == val)?.name || '-'
		},
		campusChange() {
			this.callSet(this.tabName)
			this.getData()
		},
		tabChange(val) {
			console.log(val, 'tabChange')
			switch (val) {
				case 'group':
					this.getDept()
					break
				case 'subject':
					this.getEduDiscipline()
					break
			}
		},
		// 点击更改显示未提交/已提交的children
		clickStatus(val, index) {
			console.log(val, index, 'clickStatus')
			this.activeIndexStatus = index
		},
		searchKey() {
			if (this.timer) clearTimeout(this.timer)
			this.timer = setTimeout(() => {
				this.getData()
			}, 700)
		},
		//组件副作用回调
		callSet(val) {
			this.treeNodeId = null
			this.groupData = []
			switch (val) {
				case 'group':
					this.getDept()
					break
				case 'subject':
					this.getEduDiscipline()
					break
			}
		},
		//获取部门
		async getDept() {
			const res = await this.$API.system.dept.all.get(this.params)
			this.transData(res, 'department_name', 'tree')
		},
		//获取学科
		async getEduDiscipline() {
			var res = await this.$API.eduCourseSet.course.all.get(this.params)
			this.transData(res, 'course_name')
		},
		transData(res, field, type) {
			if (!res.data) res.data = []
			this.groupData = res.data.map((v) => {
				v.name = v[field]
				return v
			})
			if (type == 'tree') {
				this.groupData = cusTom.arrayToTree(this.groupData, 'id', 'parent_id')
			}
		},
		//树点击事件
		groupClick(data) {
			this.treeNodeId = data.id
			this.getData()
		},
		//表格显示隐藏回调
		visibleChange(visible) {
			if (visible) {
				this.currentPage = 1
				this.keyword = null
				this.formData = {}
				this.getData()
			} else {
			}
		},
		//获取表格数据
		async getData() {
			this.loading = true
			var reqData = {
				[this.defaultProps.page]: this.currentPage,
				[this.defaultProps.pageSize]: this.pageSize,
				[this.defaultProps.keyword]: this.keyword
			}
			var customParams = {
				[this.typeMap.find((v) => v.name == this.tabName).field]: this.treeNodeId
			}
			Object.assign(reqData, this.params, this.formData, customParams)
			var res = await this.apiObj.get(reqData)
			var parseData = config.parseData(res)
			this.tableData = parseData.rows || []
			this.total = parseData.total
			this.loading = false
		},
		//分页刷新表格
		reload() {
			this.getData()
		},
		//表格勾选事件
		select(rows, row) {
			let isSelect = rows.length && rows.indexOf(row) !== -1
			if (isSelect) {
				this.rowClickData.push({
					label: row.name,
					id: row.id
				})
			} else {
				this.rowClickData.splice(
					this.defaultValue?.findIndex((item) => item[this.defaultProps.value] === row[this.defaultProps.value]),
					1
				)
			}
		},
		//表格全选事件
		selectAll(rows) {
			var isAllSelect = rows.length > 0
			if (isAllSelect) {
				rows.forEach((row) => {
					this.rowClickData.push({
						label: row.name,
						id: row.id
					})
				})
			} else {
				this.tableData.forEach((row) => {
					var isHas = this.defaultValue.find((item) => item[this.defaultProps.value] === row[this.defaultProps.value])
					if (isHas) {
						this.rowClickData.splice(
							this.rowClickData.findIndex((item) => item[this.defaultProps.value] === row[this.defaultProps.value]),
							1
						)
					}
				})
			}
		},
		// 关键值查询表格数据行
		findRowByKey(value) {
			return this.tableData.find((item) => item[this.defaultProps.value] === value)
		},
		filterMethod(keyword) {
			if (!keyword) {
				this.keyword = null
				return false
			}
			this.keyword = keyword
			this.getData()
		}
	}
}
</script>

<style lang="scss">
.select-dialog {
	.el-dialog__body {
		padding-top: 12px;
	}
}
</style>
<style scoped lang="scss">
.el-h {
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	border: none;
	padding-left: 0;

	> .el-select {
		margin-right: 15px;
	}
}

.is_active {
	background-color: #e9ecf7;
	position: relative;

	&::after {
		content: '';
		width: 3px;
		height: 100%;
		position: absolute;
		right: 0;
		background-color: var(--el-color-primary-light-3);
	}
}

.selectBox {
	display: flex;
	min-height: 300px;
	border: 1px solid #eee;

	section {
		padding: 0 10px;
	}

	& > div {
		padding: 10px;
	}

	&_one {
		flex: 1;
		padding-right: 0 !important;
		border-right: 1px solid #eee;
	}

	.single-section {
		margin-bottom: 10px;
	}

	&_three {
		flex: 3;
		max-height: 300px;
		overflow: auto;

		.selectItem {
			display: inline-block;
			width: 120px;

			.el-b {
				min-width: 100%;

				:deep(.el-checkbox-button__inner) {
					width: 100%;
				}
			}
		}

		.el-b {
			min-width: 30%;

			:deep(.el-checkbox-button__inner) {
				width: 100%;
			}
		}

		section + section {
			margin-top: 5px;
		}
	}
}

.st {
	height: 32px;
	font-size: 16px;
	padding-left: 10px;
	display: flex;
	align-items: center;
}

.single-section {
	height: 32px;
	line-height: 32px;
}

.menu {
	height: 300px;
	overflow: auto;
}

.sc-table-select__table {
	padding: 0px;
}

.sc-table-select__page {
	padding-top: 12px;
}

.el-f {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 10px;
	height: 50px;
}

.selectTitle {
	padding: 10px;
	font-weight: bold;
	color: #333;
	font-size: 18px;
	border-bottom: 1px solid #eee;
}
</style>
