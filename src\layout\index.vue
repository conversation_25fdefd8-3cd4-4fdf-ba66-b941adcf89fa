<template>
	<sc-water-mark v-if="layoutWaterMark" ref="wm" :text="water_mark_text" :subtext="water_mark_subtext">
		<layout />
	</sc-water-mark>
	<template v-else>
		<layout />
	</template>
</template>

<script>
import Layout from './layout.vue'

import { useGlobalStore } from '@/stores/global.js'

export default {
	name: 'index',
	components: {
		Layout
	},
	data() {
		return {
			globalStore: useGlobalStore(),
			water_mark_text: this.$CONFIG.LAYOUT_WATER_MARK_TEXT || '',
			water_mark_subtext: this.$CONFIG.LAYOUT_WATER_MARK_SUBTEXT || ''
		}
	},
	computed: {
		layoutWaterMark() {
			return this.globalStore.layoutWaterMark
		}
	},
	created() {
		this.userInfo = this.$TOOL.data.get('USER_INFO')
		if (this.userInfo.tenant_info) {
			this.appName = this.userInfo.tenant_info.name ? this.userInfo.tenant_info.name : this.$CONFIG.APP_NAME
			this.appLogo = this.userInfo.tenant_info.img ? this.userInfo.tenant_info.img : this.appLogo
		}
		this.water_mark_text = this.appName
		this.water_mark_subtext = this.userInfo.nickname + ' @ ' + this.userInfo.phone.slice(-4)
	},
	watch: {},
	methods: {}
}
</script>
