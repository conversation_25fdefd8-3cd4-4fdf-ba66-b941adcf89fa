<template>
	<!-- 暂存在此 -->
	<!-- <el-select ref="select" v-model="defaultValue" popper-class="pop-class" :size="size" :clearable="clearable"
		:multiple="true" :collapse-tags="collapseTags" :collapse-tags-tooltip="collapseTagsTooltip"
		:filterable="filterable" :placeholder="placeholder" :disabled="disabled" :filter-method="filterMethod"
		@remove-tag="removeTag" @visible-change="visibleChange" @clear="clear" @focus="showDialog"
		:style="{ width: width }">
	</el-select> -->
	<el-input
		ref="select"
		:disabled="disabled"
		:readonly="readonly"
		:style="{ width: width }"
		:placeholder="!defaultValue || defaultValue.length == 0 ? placeholder : ''"
		@focus="showDialog"
	>
		<template v-if="defaultValue && defaultValue.length > 0" #prefix>
			<template v-for="(item, index) in defaultValue.slice(0, 2)" :key="item.id">
				<el-tag effect="dark" closable type="info" @close="removeTag(item, index)">{{ item.label }}</el-tag>
			</template>
			<template v-if="defaultValue.length > 2">
				<el-popover placement="bottom" trigger="hover" popper-class="input-tag-popover">
					<template #reference>
						<el-tag effect="dark" type="info">+{{ defaultValue.length - 2 }}</el-tag>
					</template>
					<el-tag
						v-for="(item, index) in defaultValue.slice(2)"
						effect="dark"
						closable
						type="info"
						@close="removeTag(item, index + 2)"
						>{{ item.label }}
					</el-tag>
				</el-popover>
			</template>
		</template>
		<template #suffix>
			<el-icon class="el-input__icon">
				<el-icon-arrow-down />
			</el-icon>
		</template>
	</el-input>
	<el-dialog
		v-model="dialogVisible"
		title="选择学生"
		width="900"
		append-to-body
		:close-on-press-escape="false"
		class="select-dialog"
		@closed="closeDialog"
	>
		<el-header class="el-h">
			<el-select
				v-if="CampusManagementList.length > 1"
				v-model="params.campus_id"
				placeholder="请选择校区"
				clearable
				style="width: 240px"
				@change="campusChange"
			>
				<el-option
					v-for="item in CampusManagementList"
					:key="item.code"
					:label="item.name"
					:value="item.value"
				></el-option>
			</el-select>
			<el-select v-model="params.status" placeholder="学生状态" style="width: 200px" clearable @change="getData">
				<el-option
					v-for="(item, index) in studentStatusMap"
					:key="index"
					:label="item.name"
					:value="item.value"
				></el-option>
			</el-select>
			<el-date-picker
				v-model="params.entrance_year"
				style="width: 130px; margin-right: 10px"
				type="year"
				placeholder="入学年份"
				value-format="YYYY"
				@change="getData"
			/>
			<el-input v-model="formData.name" style="width: 350px" placeholder="姓名或学号" @input="searchKey">
				<!--				<template #prepend>
									<el-select v-model="searchSelect" placeholder="请选择" style="width: 100px">
										<el-option label="模糊搜索" :value="1" />
										<el-option label="精确搜索" :value="2" />
									</el-select>
								</template>-->
				<template #prefix>
					<el-icon class="el-input__icon">
						<el-icon-search />
					</el-icon>
				</template>
			</el-input>
		</el-header>
		<el-tabs v-model="tabName" type="border-card" class="stu_select" @tab-change="tabChange">
			<el-tab-pane label="年级" name="grade">
				<el-container>
					<el-aside :style="{ width: treeWidth + 'px' }">
						<el-container>
							<el-main>
								<el-tree
									ref="group"
									class="menu"
									node-key="id"
									:data="groupsAdd"
									:highlight-current="true"
									:expand-on-click-node="false"
									default-expand-all
									:props="treeDefaultProps"
									:default-expanded-keys="[]"
									@node-click="groupClick"
								>
								</el-tree>
							</el-main>
						</el-container>
					</el-aside>
					<el-main>
						<div v-loading="loading" class="sc-table-select__table">
							<el-table
								ref="table"
								:data="tableData"
								:height="245"
								:highlight-current-row="!multiple"
								row-key="id"
								@row-click="click"
								@select="select"
								@select-all="selectAll"
							>
								<el-table-column v-if="multiple" type="selection" reserve-selection></el-table-column>
								<!--								<el-table-column v-else type="index">
									<template #default="scope"
										><span>{{ scope.$index + (currentPage - 1) * pageSize + 1 }}</span></template
									>
								</el-table-column>-->
								<el-table-column prop="student_name" label="姓名"></el-table-column>
								<el-table-column prop="serial_number" label="学号"></el-table-column>
								<el-table-column prop="entrance_year" label="入学年份"></el-table-column>
								<el-table-column prop="status" label="状态">
									<template #default="scope">
										{{ formaData(studentStatusMap, scope.row.status) }}
									</template>
								</el-table-column>
							</el-table>
							<div class="sc-table-select__page">
								<el-pagination
									v-model:currentPage="currentPage"
									small
									background
									layout="prev, pager, next"
									:total="total"
									:page-size="pageSize"
									@current-change="reload"
								></el-pagination>
							</div>
						</div>
					</el-main>
				</el-container>
			</el-tab-pane>
			<el-tab-pane label="班级" name="class">
				<el-container>
					<el-aside :style="{ width: treeWidth + 'px' }">
						<el-container>
							<el-main>
								<el-tree
									ref="group"
									class="menu"
									node-key="id"
									:data="groupsAdd"
									:highlight-current="true"
									:expand-on-click-node="false"
									default-expand-all
									:props="treeDefaultProps"
									:default-expanded-keys="[]"
									@node-click="groupClick"
								>
								</el-tree>
							</el-main>
						</el-container>
					</el-aside>
					<el-main>
						<div v-loading="loading" class="sc-table-select__table">
							<el-table
								ref="table"
								:data="tableData"
								:height="245"
								:highlight-current-row="!multiple"
								@row-click="click"
								@select="select"
								@select-all="selectAll"
							>
								<el-table-column v-if="multiple" type="selection"></el-table-column>
								<!--								<el-table-column v-else type="index">
									<template #default="scope"
										><span>{{ scope.$index + (currentPage - 1) * pageSize + 1 }}</span></template
									>
								</el-table-column>-->
								<el-table-column prop="student_name" label="姓名"></el-table-column>
								<el-table-column prop="serial_number" label="学号"></el-table-column>
								<el-table-column prop="entrance_year" label="入学年份"></el-table-column>
								<el-table-column prop="status" label="状态">
									<template #default="scope">
										{{ formaData(studentStatusMap, scope.row.status) }}
									</template>
								</el-table-column>
							</el-table>
							<div class="sc-table-select__page">
								<el-pagination
									v-model:currentPage="currentPage"
									small
									background
									layout="prev, pager, next"
									:total="total"
									:page-size="pageSize"
									@current-change="reload"
								></el-pagination>
							</div>
						</div>
					</el-main>
				</el-container>
			</el-tab-pane>
			<el-tab-pane label="已选">
				<div class="selectBox">
					<div class="selectBox_one">
						<template v-if="multiple">
							<div
								v-for="(v, index) in statusData"
								:key="v.id"
								:class="{ is_active: index == activeIndexStatus }"
								class="st"
								@click="clickStatus(v, index)"
							>
								{{ v.label }}
							</div>
						</template>
						<template v-else>
							<template v-if="statusData.length">
								<section
									v-for="(v, index) in statusData"
									:key="v.id"
									:class="{ is_active: index == activeIndexStatus }"
									class="single-section"
									@click="clickStatus(v, index)"
								>
									{{ v.label }}
								</section>
							</template>
						</template>
					</div>
					<div class="selectBox_three">
						<template v-if="multiple">
							<template v-if="statusData[activeIndexStatus].children.length">
								<section v-for="(v, index) in statusData[activeIndexStatus].children" :key="v.id" class="selectItem">
									<el-button class="el-b" type="primary">
										<span>{{ v.label }}</span>
									</el-button>
								</section>
							</template>
							<template v-else>
								<el-empty description="暂无数据" :image-size="80"></el-empty>
							</template>
						</template>
						<template v-else>
							<template v-if="statusData.length">
								<section v-for="(v, index) in statusData[activeIndexStatus].children" :key="v.id">
									<el-button class="el-b" type="primary">
										<span>{{ v.label }}</span>
									</el-button>
								</section>
							</template>
							<template v-else>
								<el-empty description="暂无数据" :image-size="80"></el-empty>
							</template>
						</template>
					</div>
				</div>
			</el-tab-pane>
		</el-tabs>
		<div class="el-f">
			<span
				>已选择
				<span style="color: red">{{ this.statusData[0].children.length || 0 }}</span>
				个</span
			>
			<el-button type="primary" @click="confirm">确定</el-button>
		</div>
	</el-dialog>
</template>

<script>
import config from '@/config/tableSelect'
import api from '@/api'
import cusTom from '@/utils/cusTom'

export default {
	props: {
		modelValue: null,
		apiObj: {
			type: Object,
			default: () => {
				return api.eduStudent.list
			}
		},

		placeholder: { type: String, default: '请选择' },
		size: { type: String, default: 'default' },
		clearable: { type: Boolean, default: false },
		multiple: { type: Boolean, default: false },
		filterable: { type: Boolean, default: false },
		collapseTags: { type: Boolean, default: true },
		collapseTagsTooltip: { type: Boolean, default: true },
		disabled: { type: Boolean, default: false },
		tableWidth: { type: Number, default: 600 },
		treeWidth: { type: Number, default: 240 },
		width: { type: String, default: '200px' },

		mode: { type: String, default: 'popover' },
		props: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			loading: false,
			keyword: null,
			defaultValue: [],
			tableData: [],
			pageSize: config.pageSize,
			total: 0,
			currentPage: 1,
			defaultProps: {
				label: config.props.label,
				value: config.props.value,
				page: config.request.page,
				pageSize: config.request.pageSize,
				keyword: config.request.keyword
			},
			formData: {},
			params: {
				campus_id: '',
				tenant_id: '',
				status: '',
				entrance_year: ''
			},
			CampusManagementList: [],
			studentStatusMap: [],
			groupData: [],
			treeDefaultProps: {
				children: 'children',
				label: 'name'
			},
			treeNodeId: null,
			typeMap: [
				{
					name: 'grade',
					field: 'grade_id'
				},
				{
					name: 'class',
					field: 'class_id'
				}
			],
			type: 'grade',
			timer: null,
			rowClickData: [],
			dialogVisible: false,
			statusData: [
				{ id: 1, label: '未提交', children: [], data: [] },
				{ id: 2, label: '已提交', children: [], data: [] }
			],
			activeIndexStatus: 0,
			shuldBlur: false,
			searchSelect: 1,
			tabName: 'grade'
		}
	},
	computed: {
		getWidth() {
			return this.tableWidth + this.treeWidth + 20 + 'px'
		},
		groupsAdd() {
			let arr = [{ id: 0, name: '全部' }, ...this.groupData]
			return arr
		}
	},
	watch: {
		modelValue: {
			handler(val) {
				console.log(val)
				if (val) {
					this.defaultValue = val
					this.statusData[1].children = val
					this.statusData[1].data = val.map((item) => item.id)
				}
			},
			immediate: true,
			deep: true
		},
		'params.campus_id': {
			handler() {
				// this.getData()
			},
			immediate: true
		},
		type: {
			handler(val = 'grade') {},
			immediate: true
		},
		rowClickData: {
			handler(newval, oldval) {
				console.log(newval, oldval, 'rowClickData')
				if (!this.multiple) {
					this.statusData[0].children = [newval]
					this.statusData[0].data = this.statusData[0].children.map((item) => item.id)
				} else {
					this.statusData[0].children = Array.from(new Set(newval.map(JSON.stringify))).map(JSON.parse)
					this.statusData[0].data = this.statusData[0].children.map((item) => item.id)
				}
			},
			deep: true
		}
	},
	created() {
		const { campusId, tenantId, campusInfo, studentStatusMap } = cusTom.getBaseQuery()
		this.CampusManagementList = campusInfo
		this.studentStatusMap = studentStatusMap
		this.params.campus_id = campusId
		this.params.tenant_id = tenantId
	},
	mounted() {
		this.defaultProps = Object.assign(this.defaultProps, this.props)
		this.defaultValue = this.modelValue
	},
	methods: {
		campusChange(val) {
			this.params.campus_id = val
			this.getDept()
			this.getData()
		},
		formaData(arr, val) {
			return arr.find((v) => v.value == val)?.name || '-'
		},
		showDialog() {
			if (!this.shuldBlur) {
				this.dialogVisible = true
				this.getDept()
				this.getData()
			}
		},
		closeDialog() {
			this.blur()
		},
		tabChange(val) {
			this.groupData = []
			switch (val) {
				case 'grade':
					this.getDept()
					break
				case 'class':
					this.getClass()
					break
			}
		},
		// 点击更改显示未提交/已提交的children
		clickStatus(val, index) {
			console.log(val, index, 'clickStatus')
			this.activeIndexStatus = index
		},
		confirm() {
			if (this.multiple) {
				this.defaultValue = Array.from(new Set(this.rowClickData.map(JSON.stringify))).map(JSON.parse)
				this.$emit('update:modelValue', this.defaultValue)
				this.$emit('change', this.defaultValue)
			} else {
				if (this.rowClickData.id) {
					this.defaultValue = [
						{
							label: this.rowClickData.label,
							id: this.rowClickData.id
						}
					]
				}
				this.$emit('update:modelValue', this.defaultValue)
				this.$emit('change', this.defaultValue)
			}
			this.dialogVisible = false
			this.shuldBlur = true
			this.$nextTick(() => {
				this.blur()
			})
		},
		searchKey() {
			if (this.timer) clearTimeout(this.timer)
			this.timer = setTimeout(() => {
				this.getData()
			}, 700)
		},
		//获取年级
		async getDept() {
			const res = await this.$API.eduGradeClass.grade.all.get(this.params)
			this.transData(res, 'grade_name')
		},
		//获取班级
		async getClass() {
			var res = await this.$API.eduGradeClass.class.all.get(this.params)
			this.transData(res, 'name', 'tree')
		},
		transData(res, field, type) {
			if (!res.data) res.data = []
			this.groupData = res.data.map((v) => {
				v.name = v[field]
				if (v.class_list && v.class_list.length > 0) {
					v.children = v.class_list.map((vv) => {
						vv.parent_id = v.id
						return vv
					})
				}
				return v
			})
			if (type == 'tree') {
				this.groupData = cusTom.arrayToTree(this.groupData, 'id', 'parent_id')
			}
		},
		//树点击事件
		groupClick(data, node) {
			console.log(node)
			if ((node.level != 1 || data.id == 0) && this.tabName == 'class') {
				this.treeNodeId = data.id
				this.getData()
			} else if (this.tabName == 'grade') {
				this.treeNodeId = data.id
				this.getData()
			}
		},
		//表格显示隐藏回调
		visibleChange(visible) {
			if (visible) {
				this.currentPage = 1
				this.keyword = null
				this.formData = {}
				this.getData()
			} else {
			}
		},
		//获取表格数据
		async getData() {
			this.loading = true
			var reqData = {
				[this.defaultProps.page]: this.currentPage,
				[this.defaultProps.pageSize]: this.pageSize,
				[this.defaultProps.keyword]: this.keyword
			}
			var customParams = {
				[this.typeMap.find((v) => v.name === this.tabName).field]: this.treeNodeId
			}
			Object.assign(reqData, this.params, this.formData, customParams)
			var res = await this.apiObj.get(reqData)
			var parseData = config.parseData(res)
			this.tableData = parseData.rows || []
			this.total = parseData.total
			this.loading = false
		},
		//插糟表单提交
		formSubmit() {
			this.currentPage = 1
			this.keyword = null
			this.getData()
		},
		//分页刷新表格
		reload() {
			this.getData()
		},
		//表格勾选事件
		select(rows, row) {
			var isSelect = rows.length && rows.indexOf(row) !== -1
			if (isSelect) {
				this.rowClickData.push({
					label: row.student_name,
					id: row.id
				})
				// this.defaultValue.push({
				// 	label: row.student_name,
				// 	id: row.id
				// })
			} else {
				this.rowClickData.splice(
					this.rowClickData?.findIndex((item) => item[this.defaultProps.value] === row[this.defaultProps.value]),
					1
				)
				// this.defaultValue.splice(
				// 	this.defaultValue.findIndex((item) => item[this.defaultProps.value] === row[this.defaultProps.value]),
				// 	1
				// )
			}
			// this.$emit('update:modelValue', this.defaultValue)
			// this.$emit('change', this.defaultValue)
		},
		//表格全选事件
		selectAll(rows) {
			var isAllSelect = rows.length > 0
			if (isAllSelect) {
				rows.forEach((row) => {
					// var isHas = this.defaultValue.find((item) => item[this.defaultProps.value] === row[this.defaultProps.value])
					// if (!isHas) {
					// this.defaultValue.push({
					// 	label: row.student_name,
					// 	id: row.id
					// })
					// }
					this.rowClickData.push({
						label: row.student_name,
						id: row.id
					})
				})
			} else {
				this.tableData.forEach((row) => {
					var isHas = this.defaultValue?.find((item) => item[this.defaultProps.value] === row[this.defaultProps.value])
					if (isHas) {
						this.rowClickData.splice(
							this.rowClickData.findIndex((item) => item[this.defaultProps.value] === row[this.defaultProps.value]),
							1
						)
						// this.defaultValue.splice(
						// 	this.defaultValue.findIndex((item) => item[this.defaultProps.value] === row[this.defaultProps.value]),
						// 	1
						// )
					}
				})
			}
			// this.$emit('update:modelValue', this.defaultValue)
			// this.$emit('change', this.defaultValue)
		},
		click(row) {
			if (this.multiple) {
				let has = this.defaultValue?.some((v) => v.id === row.id)
				if (has) return false
				this.rowClickData.push({
					label: row.student_name,
					id: row.id
				})
				//处理多选点击行
			} else {
				this.rowClickData = { label: row.student_name, id: row.id }
			}
		},
		//tags删除后回调
		removeTag(tag, index) {
			if (this.disabled) {
				return false
			}
			if (this.multiple) {
				this.rowClickData.splice(index, 1)
				this.defaultValue.splice(index, 1)
			} else {
				this.rowClickData = []
				this.defaultValue = []
			}
			var row = this.findRowByKey(tag[this.defaultProps.value])
			if (this.$refs.table) {
				this.$refs.table.toggleRowSelection(row, false)
			}
			this.$emit('update:modelValue', this.defaultValue)
		},
		//清空后的回调
		clear() {
			this.$emit('update:modelValue', this.defaultValue)
		},
		// 关键值查询表格数据行
		findRowByKey(value) {
			return this.tableData.find((item) => item[this.defaultProps.value] === value)
		},
		filterMethod(keyword) {
			if (!keyword) {
				this.keyword = null
				return false
			}
			this.keyword = keyword
			this.getData()
		},
		// 触发select隐藏
		blur() {
			if (this.shuldBlur) {
				this.shuldBlur = false
				return false
			}
			this.$refs.select.blur()
		},
		// 触发select显示
		focus() {
			this.$refs.select.focus()
		}
	}
}
</script>

<style lang="scss">
.select-dialog {
	.el-dialog__body {
		padding-top: 12px;
	}
}
</style>

<style scoped lang="scss">
.el-h {
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	border: none;
	padding-left: 0;

	> .el-select {
		margin-right: 15px;
	}
}

.is_active {
	background-color: #e9ecf7;
	position: relative;

	&::after {
		content: '';
		width: 3px;
		height: 100%;
		position: absolute;
		right: 0;
		background-color: var(--el-color-primary-light-3);
	}
}

.selectBox {
	display: flex;
	min-height: 300px;
	border: 1px solid #eee;
	border-top: unset;

	section {
		padding: 0 10px;
	}

	& > div {
		padding: 10px;
	}

	&_one {
		flex: 1;
		padding-right: 0 !important;
		border-right: 1px solid #eee;
	}
	.st {
		margin-bottom: 10px;
	}
	&_three {
		flex: 4;
		max-height: 300px;
		overflow: auto;

		.selectItem {
			display: inline-block;
			width: 120px;

			.el-b {
				min-width: 100%;

				:deep(.el-checkbox-button__inner) {
					width: 100%;
				}
			}
		}

		.el-b {
			min-width: 30%;

			:deep(.el-checkbox-button__inner) {
				width: 100%;
			}
		}

		section + section {
			margin-top: 5px;
		}
	}
}

.st {
	height: 32px;
	font-size: 16px;
	padding-left: 10px;
	display: flex;
	align-items: center;
}

.single-section {
	height: 32px;
	line-height: 32px;
}

.menu {
	height: 300px;
	overflow: auto;
}

.sc-table-select__table {
	padding: 0px;
}

.sc-table-select__page {
	padding-top: 12px;
}

.el-f {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 10px;
	height: 50px;
}

.selectTitle {
	padding: 10px;
	font-weight: bold;
	color: #333;
	font-size: 18px;
	border-bottom: 1px solid #eee;
}
</style>
