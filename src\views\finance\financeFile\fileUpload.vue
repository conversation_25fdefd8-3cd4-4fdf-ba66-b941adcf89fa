<template>
	<el-dialog v-model="fileVisable" title="文件上传" destroy-on-close width="25%">
		<el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
			<el-form-item label="文件" prop="url">
				<scUploadFile v-model="form.url" accept="*" fileTypeTag="finFile" @suc="handlerSuc"></scUploadFile>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button size="small" @click="fileVisable = false">取消</el-button>
			<el-button type="primary" :loading="isSaveing" size="small" :disabled="disabled" @click="save">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
const defaultForm = () => {
	return {
		tenant_id: null,
		campus_id: null,
		model: null,
		name: '',
		type_id: null,
		pid: null,
		file_type: null,
		suffix: '',
		suffix_type: 0,
		size: null,
		url: '',
		objkey: ''
	}
}
import { reactive, ref, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { wordSuffix, excelSuffix, pptSuffix, imageSuffix, videoSuffix, zipSuffix, audioSuffix } from '@/utils/mediaLib'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params'])
const emit = defineEmits(['success'])
let fileVisable = ref(false)
let isSaveing = ref(false)
let formRef = ref(null)
let form = ref(defaultForm())
let disabled = ref(true)
const rules = reactive({
	url: [{ required: true, message: '请选择文件', trigger: 'blur' }]
})

const open = (typeId) => {
	form.value.url = ''
	form.value.tenant_id = props.params.tenant_id
	form.value.campus_id = props.params.campus_id
	form.value.model = props.params.model
	form.value.pid = props.params.pid
	form.value.type_id = props.params.type_id
	form.value.file_type = typeId

	fileVisable.value = true
}
const fileSuffixType = (type, file_type) => {
	if (file_type === 2) {
		return (form.value.suffix_type = 0)
	}
	/*if (form.value.suffix_type === 0) {
		return (form.value.suffix_type = 99)
	}*/
	if (type === 'pdf') {
		return (form.value.suffix_type = 4)
	} else if (wordSuffix(type)) {
		return (form.value.suffix_type = 1)
	} else if (pptSuffix(type)) {
		return (form.value.suffix_type = 3)
	} else if (excelSuffix(type)) {
		return (form.value.suffix_type = 2)
	} else if (imageSuffix(type)) {
		return (form.value.suffix_type = 5)
	} else if (videoSuffix(type)) {
		return (form.value.suffix_type = 6)
	} else if (audioSuffix(type)) {
		return (form.value.suffix_type = 7)
	} else if (zipSuffix(type)) {
		return (form.value.suffix_type = 8)
	} else {
		return (form.value.suffix_type = 99)
	}
}
// 确定点击事件
const save = async () => {
	await formRef.value.validate()
	isSaveing.value = true
	const res = await globalPropValue.fileManagement.file.add_file.post(form.value)
	isSaveing.value = false
	if (res.code === 200) {
		ElMessage.success('上传成功')
		emit('success', form.value)
		fileVisable.value = false
	}
}
// 文件上传成功自定义事件
const handlerSuc = (res) => {
	disabled.value = false
	form.value.name = res.name.split('.')[0]
	form.value.size = res.size
	form.value.objkey = res.key
	form.value.suffix = res.key.split('.')[1]
	console.log(res)
	let type = res.key.split('.')[1]
	let file_type = form.value.file_type
	console.log(imageSuffix(type))
	fileSuffixType(type, file_type)
}
defineExpose({
	open
})
</script>

<style scoped lang="scss"></style>
