<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :key="item.name" :name="item.name" :label="item.label"></el-tab-pane>
			</el-tabs>
		</el-header>
		<component :is="currComponent.component" @handleTab="handleClick"></component>
	</el-container>
</template>

<script>
import assetComponent from './asset.vue'
import consumableComponent from './consumable.vue'
import { shallowRef } from 'vue'
export default {
	name: 'layoutTCB',
	components: {
		assetComponent,
		consumableComponent
	},
	props: {
		userId: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			activeName: 'asset',
			tabs: [
				{
					label: '我的资产',
					name: 'asset',
					component: shallowRef(assetComponent)
				},
				{
					label: '我的耗材',
					name: 'consumable',
					component: shallowRef(consumableComponent)
				},
			],
			currComponent: {},
			classData: {}
		}
	},
	created() {
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
			this.activeName = name
		}
	},
	provide() {
		return {
			userId: this.userId
		}
	}
}
</script>

<style></style>
