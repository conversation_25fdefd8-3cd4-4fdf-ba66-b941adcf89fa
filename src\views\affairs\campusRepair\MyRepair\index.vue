<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" icon="el-icon-plus" @click="addRepair">申请报修</el-button>
			</div>
		</el-header>

		<el-main class="outer-main">
			<el-row :gutter="20">
				<el-col v-for="item in repairList" :key="item.id" :xs="12" :sm="12" :md="12" :lg="8" :xl="6">
					<div class="repairPanel" @mouseenter="item.showEdit = true" @mouseleave="item.showEdit = false">
						<div class="repairPanel__top">
							<div class="repairPanel__top-title">
								{{ $formatDictionary(typeMap, item.type_id) }} - {{ item.config_parent_name }} -
								{{ item.config_name }}
							</div>
							<div class="repairPanel__top-status">
								<el-tag :type="['danger', 'warning', 'success', 'info'][item.repair_status - 1]">
									{{ $formatDictionary(repairStatusMap, item.repair_status) }}
								</el-tag>
							</div>
							<div v-if="item.repair_status === 3" class="repairPanel__top-evaluate">
								<el-button @click="appraise(item)">评价</el-button>
							</div>
							<div v-if="!item.is_urge && item.repair_status == 1" class="repairPanel__top-evaluate">
								<el-button @click="urgentProcessing(item.id)">催办</el-button>
							</div>
							<div v-if="item.is_urge" class="repairPanel__top-tag">
								<img :src="cuiban" alt="" />
							</div>
						</div>
						<div class="repairPanel__bottom">
							<div class="repairPanel__bottom-repairReport">
								<div class="repairPanel__bottom-repairReport-head">
									<span>报修</span>
									<span v-if="item.showEdit && item.repair_status === 1" class="edit">
										<el-icon @click="edit(item)"><el-icon-editPen /></el-icon>
									</span>
								</div>
								<div class="repairPanel__bottom-repairReport-content">
									<div class="repairPanel__bottom-repairReport-content-item">
										<span>类型：</span>
										<span>{{ $formatDictionary(typeMap, item.type_id) }}</span>
									</div>

									<div v-if="item.room" class="repairPanel__bottom-repairReport-content-item">
										<span>位置：</span>
										<span>
											<cusTooltip :text="`${item.room.name}`"></cusTooltip>
										</span>
									</div>

									<div class="repairPanel__bottom-repairReport-content-item">
										<span>报修人：</span>
										<span>{{ item.repairman_name }}</span>
									</div>
									<div class="repairPanel__bottom-repairReport-content-item">
										<span>报修人电话：</span>
										<span>{{ item.repairman_phone }}</span>
									</div>
									<div class="repairPanel__bottom-repairReport-content-item">
										<span>编号：</span>
										<span><cusTooltip :text="item.serial_number"></cusTooltip></span>
									</div>
									<div class="repairPanel__bottom-repairReport-content-item">
										<span>时间：</span>
										<span>{{ item.created_at }}</span>
									</div>

									<div class="repairPanel__bottom-repairReport-content-item">
										<span>问题描述：</span>
										<span><cusTooltip :text="item.repair_desc"></cusTooltip></span>
									</div>

									<div class="repairPanel__bottom-repairReport-content-item">
										<span>附件：</span>
										<span v-if="item.repair_img">
											<a :href="item.repair_img" target="_blank" style="color: blue">点击下载</a>
										</span>
										<span v-else>暂无</span>
									</div>
								</div>
							</div>
							<div class="repairPanel__bottom-acceptance">
								<div class="repairPanel__bottom-acceptance-head">
									<span>受理</span>
								</div>
								<div class="repairPanel__bottom-acceptance-content" v-if="item.repair_status !== 1">
									<div class="repairPanel__bottom-acceptance-content-item">
										<span>受理人：</span>
										<span>{{ item.deal_user_name }}</span>
									</div>

									<div class="repairPanel__bottom-acceptance-content-item">
										<span>受理人电话：</span>
										<span>{{ item.deal_user_phone }}</span>
									</div>

									<div class="repairPanel__bottom-acceptance-content-item">
										<span>受理时间：</span>
										<span>{{ item.deal_time }}</span>
									</div>
									<div class="repairPanel__bottom-acceptance-content-item">
										<span>响应时间：</span>
										<span>{{ item.response_time }}</span>
									</div>

									<div class="repairPanel__bottom-repairReport-content-item">
										<span>受理备注：</span>
										<span><cusTooltip :text="item.deal_remark"></cusTooltip></span>
									</div>

									<div class="repairPanel__bottom-acceptance-content-item">
										<span>受理附件：</span>
										<span v-if="item.deal_img">
											<a :href="item.deal_img" target="_blank" style="color: blue">点击下载</a>
										</span>
										<span v-else>暂无</span>
									</div>

									<div class="repairPanel__bottom-acceptance-content-item">
										<span>报修评价：</span>
										<span v-if="item.appraise_stars"> <el-rate v-model="item.appraise_stars" :disabled="true" /></span>
									</div>
									<!-- <el-tooltip effect="dark" :content="`${item.appraise_remark}`" placement="top-start"> -->
									<div class="repairPanel__bottom-acceptance-content-item">
										<span>评价备注：</span>
										<span><cusTooltip :text="item.appraise_remark"></cusTooltip></span>
									</div>
									<!-- </el-tooltip> -->
									<div class="repairPanel__bottom-acceptance-content-item">
										<span>评价附件：</span>
										<span v-if="item.appraise_img">
											<a :href="item.appraise_img" target="_blank" style="color: blue">点击下载</a>
										</span>
										<span v-else>暂无</span>
									</div>
								</div>
								<div style="height:155px;display:flex;justify-content: center;align-items: center;" v-else>
									<el-result icon="info" title="">
										<template #sub-title>
											<p>等待人员受理</p>
										</template>
										<template #extra>
										</template>
									</el-result>
								</div>
							</div>
						</div>
					</div>
				</el-col>
			</el-row>
			<div class="repairPagination">
				<el-pagination
					v-model:currentPage="params.page"
					background
					size="default"
					:layout="paginationLayout"
					:total="total"
					:hide-on-single-page="true"
					:pager-count="ismobile ? 5 : 7"
					:page-size="params.pageSize"
					:page-sizes="pageSizes"
					@current-change="paginationChange"
					@update:page-size="pageSizeChange"
				></el-pagination>
			</div>
		</el-main>
	</el-container>

	<apply-dialog
		v-if="dialog.apply"
		ref="applyDialog"
		:params="params"
		:treeData="treeData"
		@success="handleSaveSuccess"
		@closed="dialog.apply = false"
	></apply-dialog>
	<appraise-dialog
		v-if="dialog.appraise"
		ref="appraiseDialog"
		:params="params"
		@success="handleSaveSuccess"
		@closed="dialog.appraise = false"
	>
	</appraise-dialog>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import config from '@/config/table'
import { useGlobalStore } from '@/stores/global.js'
import cusTom from '@/utils/cusTom'
const { campusId, tenantId, campusInfo, repairStatusMap, repairTypeMap } = cusTom.getBaseQuery()
const defaultParams = () => {
	return {
		pageSize: 20,
		page: 1,
		tenant_id: tenantId,
		campus_id: campusId,
		type_id: null,
		config_id: null,
		repair_status: null
	}
}

import cuiban from '@/assets/img/custom/cuiban.png'
import applyDialog from './apply.vue'
import appraiseDialog from './appraise.vue'

export default {
	name: '',
	components: { applyDialog, appraiseDialog },
	data() {
		return {
			params: defaultParams(),
			statusMap: repairStatusMap,
			typeMap: repairTypeMap,
			configMap: [],
			CampusManagementList: campusInfo,
			repairList: [],
			total: 0,
			globalStore: useGlobalStore(),
			pageSizes: config.pageSizes,
			paginationLayout: config.paginationLayout,
			cuiban,
			repairStatusMap,
			dialog: {
				apply: false,
				appraise: false,
				evaluate: false
			},
			treeData: [],
			searchConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: null,
						name: 'campus_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择校区',
							noClearable: true,
							items: campusInfo.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'repair_status',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择报修状态',
							noClearable: false,
							items: repairStatusMap.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'type_id',
						value: null,
						component: 'select',
						options: {
							placeholder: '请选择报修类别',
							noClearable: false,
							items: repairTypeMap.map((v) => {
								return {
									label: v.name,
									value: v.value
								}
							})
						}
					},
					{
						label: null,
						name: 'config_id',
						value: null,
						component: 'cascader',
						options: {
							placeholder: '请选择报修配置',
							noClearable: false,
							items: []
						}
					}
				]
			}
		}
	},
	watch: {
		'params.campus_id': {
			async handler(val) {
				await this.getConfigTree()
				this.upsearch()
				this.$SET_campus_id(val)
			},
			immediate: true
		}
	},
	computed: {
		ismobile() {
			return this.globalStore.ismobile
		}
	},
	methods: {
		evaluate(row) {
			this.dialog.evaluate = true
			this.$nextTick(() => {
				this.$refs.evaluateDialog.open().setData(row)
			})
		},
		edit(row) {
			this.dialog.apply = true
			this.$nextTick(() => {
				this.$refs.applyDialog.open('edit').setData(row)
			})
		},
		async getConfigTree() {
			var res = await this.$API.campusRepair.configAll.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id
			})
			this.treeData = res.data.map((v) => {
				return {
					value: v.id,
					label: v.name,
					children:
						v.child &&
						v.child.map((vv) => {
							return {
								value: vv.id,
								label: vv.name,
								children:
									vv.child &&
									vv.child.map((vvv) => {
										return {
											value: vvv.id,
											label: vvv.name
										}
									})
							}
						})
				}
			})
			this.searchConfig.formItems[3].options.items = this.treeData
		},
		appraise(row) {
			this.dialog.appraise = true
			this.$nextTick(() => {
				this.$refs.appraiseDialog.open().setData(row)
			})
		},
		urgentProcessing(id) {
			ElMessageBox.confirm('是否催办该工单？', '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$API.campusRepair.urge
						.post({
							id,
							tenant_id: this.params.tenant_id,
							campus_id: this.params.campus_id
						})
						.then((res) => {
							if (res.code === 200) {
								ElMessage({
									type: 'success',
									message: '操作成功'
								})
								this.getMyRepair()
							}
						})
				})
				.catch(() => {})
		},
		//搜索
		upsearch() {
			this.getMyRepair()
		},
		//条件重置
		refresh() {
			this.params = defaultParams()
			this.upsearch()
		},
		async getMyRepair() {
			let t = {
				...this.params
			}
			delete t.config_id
			if (this.params.config_id) {
				t.config_id = this.params.config_id[this.params.config_id.length - 1]
			}

			let res = await this.$API.campusRepair.my.get(t)
			if (!res.data.rows) {
				return (this.repairList = [])
			}
			this.repairList = res.data.rows.map((v) => {
				return {
					...v,
					showEdit: false
				}
			})
			this.total = res.data.total
		},
		//分页点击
		paginationChange() {
			this.getMyRepair()
		},
		//条数变化
		pageSizeChange(size) {
			this.params.pageSize = size
			this.getMyRepair()
		},
		addRepair() {
			this.dialog.apply = true
			this.$nextTick(() => {
				this.$refs.applyDialog.open()
			})
		},
		handleSaveSuccess() {
			this.getMyRepair()
		}
	}
}
</script>

<style scoped lang="scss">
.outer-main {
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}
.edit {
	border-radius: 50%;
	height: 30px;
	width: 30px;
	border-radius: 50%;
	text-align: center;
	// color: #fff;
}
.edit:hover {
	cursor: pointer;
	background-color: var(--el-color-primary-light-4);
}
.repairPagination {
	padding-top: 10px;
	padding-bottom: 10px;
	padding-left: 20px;
}
.repair {
	flex: 1;
	width: 100%;
	/*display: grid;
	grid-template-columns: repeat(4, calc(25% - 18px));
	grid-gap: 20px;*/
	overflow: auto;
}
.repairPanel {
	--bgc: #f6f8fa;
	--bc: #dedede;

	border-radius: 5px;
	border: 1px solid var(--bc);
	margin-bottom: 20px;
	&__top {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-left: 10px;
		border-bottom: 1px solid var(--bc);
		height: 55px;
		padding-right: 35px;
		position: relative;
		&-title {
			font-size: 16px;
			flex: 1;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
		&-status {
			margin-right: 10px;
		}
		&-evaluate {
			margin-right: 10px;
		}
		&-tag {
			width: 55px;
			height: 55px;
			position: absolute;
			right: 0;
			img {
				width: 100%;
				height: 100%;
			}
		}
	}
	&__bottom {
		padding: 10px;
		&-repairReport {
			padding: 10px;
			background-color: var(--el-bg-color-overlay);
			line-height: 30px;
			&-head {
				display: flex;
				justify-content: space-between;
				font-weight: bold;
				font-size: 14px;
			}
			&-content {
				display: flex;
				flex-wrap: wrap;

				&-item {
					width: 50%;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					display: flex;
					span {
						&:nth-child(2) {
							flex: 1;
							overflow: hidden;
						}
					}
				}
			}
		}
		&-acceptance {
			margin-top: 10px;
			padding: 10px;
			border-top: 1px solid var(--el-border-color-light);

			background-color: var(--el-bg-color-overlay);
			padding: 10px;
			line-height: 30px;
			&-head {
				display: flex;
				justify-content: space-between;
				font-weight: bold;
				font-size: 14px;
			}
			&-content {
				display: flex;
				flex-wrap: wrap;
				&-item {
					width: 50%;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					display: flex;
					span {
						&:nth-child(2) {
							flex: 1;
							overflow: hidden;
						}
					}
				}
			}
		}
	}
}
</style>
