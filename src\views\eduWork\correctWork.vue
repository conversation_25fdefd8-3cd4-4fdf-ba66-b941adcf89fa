<template>
	<el-drawer
		v-model="drawer"
		:title="'作业批阅-' + detailParams.obj.answer_userobj?.student_name + '('+detailParams.obj.answer_userobj?.class_name+')'"
		size="70%"
		:before-close="save"
	>
		<el-row :gutter="20">
			<el-col :span="12">
				<div v-if="topics.list && topics.list.length" class="workPreview">
					<el-scrollbar max-height="calc(100vh - 162px)">
						<div v-for="(item, index) in topics.list" :key="item.id" class="topic_item">
							<div class="topic_top" style="margin: 8px 0; font-weight: bold">
								<span>{{ index + 1 }}、</span>
								<span v-if="item.topic_type === 1">（判断题）</span>
								<span v-if="item.topic_type === 2">（单选题）</span>
								<span v-if="item.topic_type === 3">（多选题）</span>
								<div class="topic_name" v-html="item.topic_name"></div>
								<!-- <el-icon><Edit /></el-icon> -->
								<!-- <el-icon color="#f56c6c" style="margin-left: 20px" size="16px" @click.stop="delFun(item)"
                  ><Delete
                /></el-icon> -->
							</div>
							<div v-for="(item2, index2) in JSON.parse(item.topic_desc)" :key="index2" class="topicDesc">
								<span>{{ item2.serial }}：</span>
								<div v-html="item2.desc"></div>
							</div>
						</div>
					</el-scrollbar>
				</div>
			</el-col>
			<el-col :span="12">
				<el-table :data="answerDetail.list" border max-height="calc(100vh - 140px)" style="width: 100%">
					<el-table-column type="index" label="题号" width="50" align="center" />
					<el-table-column prop="answer" label="学生答题" align="center" />
					<el-table-column prop="topicAnswer" label="正确答案" align="center" />
					<el-table-column label="是否正确" align="center" width="170px">
						<template #default="{ row }">
							<div>
								<el-radio-group v-model="row.is_right" size="small" @change="changeIsRight(row)">
									<el-radio :label="1" border>
										<el-icon>
											<Check />
										</el-icon>
									</el-radio>
									<el-radio :label="-1" border>
										<el-icon>
											<Close />
										</el-icon>
									</el-radio>
								</el-radio-group>
							</div>
						</template>
					</el-table-column>
				</el-table>
				<el-divider></el-divider>
				<el-input v-model="reviewDesc" :rows="4" type="textarea" placeholder="请输入作业评价" />
				<el-button style="margin-top: 10px" align="right" @click="submitComment">提交评价</el-button>
			</el-col>
		</el-row>
		<template #footer>
			<el-button @click="changeAnswer(2)">上一人</el-button>
			<el-button @click="changeAnswer(1)">下一人</el-button>
			<!-- <el-button type="primary" @click="over">完 成</el-button> -->
		</template>
	</el-drawer>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, defineExpose, defineEmits } from 'vue'
import { Check, Close } from '@element-plus/icons-vue'
import { ElAlert, ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()
const emit = defineEmits(['over'])
let drawer = ref(false)
const reviewDesc = ref('')
const detailParams = reactive({
	obj: {
		tenant_id: 0,
		campus_id: 0,
		work_id: 0,
		answer_user: 0,
		answer_user_name: '',
		get_type: 0,
		review_status: 0
	}
})
const correctParams = {
	tenant_id: 0,
	campus_id: 0,
	work_id: 0,
	topic_id: 0,
	answer_user: 0,
	review_desc: '',
	is_right: ''
}
const answerDetail = reactive({
	list: []
})
const topics = reactive({
	list: []
})
const show = (data) => {
	drawer.value = true
	Object.keys(detailParams.obj).forEach((key) => {
		detailParams.obj[key] = data[key]
		correctParams[key] = data[key]
	})
	correctParams.answer_userobj=data.answer_user
	detailParams.obj.answer_userobj=data.answer_user
	correctParams.answer_user = data.answer_user.student_id
	detailParams.obj.answer_user = data.answer_user.student_id
	getTopic()
}
const getTopic = async () => {
	let { data } = await proxy.$API.eduWork.workTopicList.get(detailParams.obj)
	topics.list = data
	geAnswerDetail()
}
const geAnswerDetail = async () => {
	let { data } = await proxy.$API.eduWork.answerDetail.get(detailParams.obj)
	detailParams.obj.answer_user = data.student_info.student_id
	detailParams.obj.answer_user_name = data.student_info.student_name
	reviewDesc.value = data.result_info.review_desc
	if (data.result_item) {
		for (var i = 0; i < topics.list.length; i++) {
			for (var j = 0; j < data.result_item.length; j++) {
				if (topics.list[i].id === data.result_item[j].topic_id) {
					data.result_item[j].topicAnswer = topics.list[i].topic_answer
				}
			}
		}
	}
	answerDetail.list = data.result_item
}
const changeIsRight = async (row) => {
	correctParams.work_id = row.work_id
	correctParams.topic_id = row.topic_id
	correctParams.answer_user = row.answer_user
	correctParams.is_right = row.is_right
	let res = await proxy.$API.eduWork.workCorrect.post(correctParams)
}
const submitComment = async () => {
	correctParams.review_desc = reviewDesc.value
	var res = await proxy.$API.eduWork.workReviewDesc.post(correctParams)
	if (res.code === 200) {
		ElMessage.success('提交成功')
	} else {
		ElAlert(res.message, '提示', { type: 'error' })
	}
}

const changeAnswer = (type) => {
	detailParams.obj.get_type = type
	geAnswerDetail()
}
const over = () => {
	emit('over')
	drawer.value = false
}
defineExpose({
	show
})
</script>

<style lang="scss" scoped>
.workPreview {
	padding: 10px;
	border: 1px solid var(--el-border-color);
	font-size: 14px;

	.topic_item {
		padding: 8px;
		margin-bottom: 12px;

		&:last-child {
			margin-bottom: 0;
		}

		.el-icon {
			display: none;
		}

		.topic_top {
			display: flex;
			flex-wrap: wrap;
			margin-bottom: 10px;
		}

		.topic_name {
			display: inline-block;
			margin-left: 5px;
			font-weight: bold;
		}
		:deep(.topic_name img) {
			max-width: 500px;
			max-height: 300px;
		}
		.topicDesc {
			display: flex;
			margin-bottom: 8px;

			&:last-child {
				margin-bottom: 0;
			}
		}
		:deep(.topicDesc img) {
			max-width: 500px;
			max-height: 300px;
		}
	}
}
</style>
