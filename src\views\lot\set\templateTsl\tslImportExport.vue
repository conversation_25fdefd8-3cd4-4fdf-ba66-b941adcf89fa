<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" width="600" destroy-on-close @closed="$emit('closed')">
		<div class="code-head">
			<div>物模型JSON</div>
			<div v-if="mode === 'export'">
				<el-button type="primary" text @click="copyCertItem">复制到剪贴板</el-button>
			</div>
			<div v-if="mode === 'import'" style="color: var(--el-color-danger)">注：不能导入相同“标识符code”的物模型</div>
		</div>
		<codemirror
			ref="cmRef"
			v-model:value="tslJson"
			:autofocus="true"
			:indent-with-tab="true"
			placeholder="请输入正确格式的物模型JSON"
			:options="cmOptions"
			height="400px"
			border
			width="100%"
			@ready="onReady"
		>
		</codemirror>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()">确 定</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { tenantId } = cusTom.getBaseQuery()
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/theme/dracula.css'
import Codemirror from 'codemirror-editor-vue3'
import useClipboard from 'vue-clipboard3'

export default {
	components: { Codemirror },
	emits: ['success', 'closed'],
	props: {},
	data() {
		return {
			form: {
				tenant_id: tenantId,
				template_id: 0
			},
			mode: 'import',
			isSaveing: false,
			cmOptions: {
				mode: 'text/javascript',
				theme: 'dracula',
				readOnly: false,
				lineNumbers: false,
				smartIndent: true, //智能缩进
				indentUnit: 4, // 智能缩进单元长度为 4 个空格
				matchBrackets: true, //每当光标位于匹配的方括号旁边时，都会使其高亮显示
				autofocus: true,
				showCursorWhenSelecting: true,
				autoRefresh: true,
				tabSize: 2,
				lineWiseCopyCut: true,
				gutters: ['CodeMirror-lint-markers'],
				foldGutter: true,
				styleActiveLine: true,
				line: true
			},
			titleMap: {
				import: '导入物模型',
				export: '导出物模型'
			},
			tslJson: null,
			visible: false
		}
	},
	created() {},
	mounted() {},
	methods: {
		onReady(cm) {
			cm.focus()
		},
		async copyCertItem() {
			try {
				const { toClipboard } = useClipboard()
				await toClipboard(this.tslJson)
				this.$message.success('复制成功')
			} catch (error) {
				this.$message.error('复制失败')
			}
		},
		open(template_id, mode = 'export') {
			this.mode = mode
			this.form.template_id = parseInt(template_id)

			if (this.mode === 'export') {
				this.cmOptions.readOnly = true
				//获取产品的物模型
				this.getTemplateTsl()
			} else {
				this.cmOptions.readOnly = false
				this.cmOptions.lineNumbers = false
				this.tslJson = null
			}
			this.visible = true
			return this
		},
		getTemplateTsl() {
			let productTsl = []
			this.$LotApi.templateTsl.list.get(this.form).then((res) => {
				if (res.code === 200) {
					res.data.forEach((item) => {
						if (item.type_spec.specs) {
							item.type_spec.specs = JSON.parse(item.type_spec.specs)
						}
						productTsl.push({
							tsl_type: item.tsl_type,
							name: item.name,
							code: item.code,
							access_mode: item.access_mode,
							description: item.description,
							event_type: item.event_type,
							call_type: item.call_type,
							input_params: item.input_params,
							output_params: item.output_params,
							type_spec: item.type_spec
						})
					})
					this.tslJson = JSON.stringify(productTsl, null, 2)
				} else {
					this.$alert(res.message, '提示', { type: 'error' })
				}
			})
		},
		//表单提交方法
		submit() {
			if (this.mode === 'import') {
				if (!this.tslJson) {
					this.$message.error('请输入物模型JSON')
					return
				}
				let tslJson = JSON.parse(this.tslJson)
				tslJson.map((item, index) => {
					if (item.type_spec.specs) {
						item.type_spec.specs = JSON.stringify(item.type_spec.specs)
					}
					return item
				})
				let data = {
					template_id: this.form.template_id,
					tenant_id: tenantId,
					tsl_list: tslJson
				}
				//导入操作
				this.$LotApi.templateTsl.import.post(data).then((res) => {
					if (res.code === 200) {
						this.$message.success('导入成功')
						this.$emit('success', this.mode)
						this.visible = false
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				})
			} else {
				this.$emit('success', this.mode)
				this.visible = false
			}
		},
		//表单注入数据
		setData(data) {
			console.log(data)
			//Object.assign(this.form, data)
		}
	}
}
</script>

<style scoped lang="scss">
.tsl {
	padding: 0 15px;
	font-size: 12px;
}

.enum_header {
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.enum_item {
	margin: 5px 0;
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.dataDefinition {
	padding-left: 15px;
}

.text-center {
	text-align: center;
}
.code-head {
	display: flex;
	line-height: 32px;
	justify-content: space-between;
}
:deep(.CodeMirror-gutters) {
	display: none;
}
</style>
