<template>
	<el-dialog v-model="visible" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<cusForm ref="dialogForm" v-model="form" :config="formConfig"> </cusForm>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { repairTypeMap } = cusTom.getBaseQuery()
const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		appraise_stars: 0,
		appraise_remark: null,
		appraise_img: [],
		id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			mode: 'add',
			titleMap: {
				add: '评价',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				appraise_stars: [{ required: true, message: '请打分', trigger: 'blur' }],
				appraise_remark: [{ required: true, message: '请输入评分备注', trigger: 'blur' }],
				appraise_img: [{ required: true, message: '请上传评价图片', trigger: 'blur' }]
			},
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: '评分',
						name: 'appraise_stars',
						value: null,
						component: 'rate',
						options: {
							temp: 0
						}
					},
					{
						label: '评价描述',
						name: 'appraise_remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入'
						}
					},
					{
						label: '评价图片',
						name: 'appraise_img',
						component: 'upload',
						options: {
							items: [
								{
									name: 'appraise_img',
									label: '评价图片',
									type: 'repair'
								}
							]
						}
					}
				]
			}
		}
	},
	computed: {},
	mounted() {},
	created() {},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id

			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.campusRepair.appraise.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
			this.formConfig.formItems.find((v) => v.name == 'appraise_stars').options.temp = this.form.appraise_stars
		}
	}
}
</script>

<style></style>
