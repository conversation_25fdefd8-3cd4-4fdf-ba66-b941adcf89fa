.el-header {
	height: auto;
	min-height: var(--el-header-padding);
	& > .el-input {
		margin: 5px 0;
	}
	& > .left-panel {
		flex: 2;
		position: relative;
		// &::after {
		// 	content: '';
		// 	width: 1px;
		// 	height: 80%;
		// 	position: absolute;
		// 	background-color: #ccc;
		// 	top: 50%;
		// 	transform: translateY(-50%);
		// 	right: 0;
		// }
	}
	& > .right-panel {
		flex: 1;
		// margin-left: 40px;
		align-items: flex-end;
		height: 100%;
		justify-content: end;
		.el-form-item__content{
			flex-wrap:unset;
		}
		.el-button {
			margin: 5px 0;
		}
		.el-button + .el-button {
			margin-left: 12px;
		}
	}
}
.cus-form-inline {
	.el-form-item {
		margin: unset;
		margin: 5px 0 !important;
	}
}
//页面的搜索的 el-form-item 不需要编剧
.left-panel-search {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	padding-right: 30px;
	align-items: center;

	.el-form-item {
		margin: unset;
		margin: 5px 0;
		margin-right: 10px;
	}
	.el-form-item__label {
		padding-left: 10px;
	}
	.el-form-item + .el-button {
		margin-left: 15px;
	}
	.el-skeleton {
		flex: 1;
	}
}
// 提示字体
.ExistingStudents {
	font-size: 14px;
	font-weight: 500;
	color: var(--el-color-primary);
}
//水平垂直居中
.flex_cc {
	display: flex;
	justify-content: center;
	align-items: center;
}
//左右 垂直居中
.flex_bc {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

//等分 垂直居中
.flex_ac {
	display: flex;
	justify-content: space-around;
	align-items: center;
}

// 部分页面背景色
.pageView {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
}
.adminui-main {
	& > .el-container {
		padding: 10px;
		box-sizing: border-box;
		& > .el-header,
		.el-main {
			border-radius: 5px;
			background-color: var(--el-bg-color-overlay);
		}

		& > .el-header + .el-main {
			margin-top: 10px;
		}
		& > .el-aside + .el-main {
			margin-left: 10px;
		}
		.el-container {
			padding: 0;
			& > .el-header,
			.el-main {
				border-radius: 5px;
				background-color: var(--el-bg-color-overlay);
			}
			.el-header + .el-main {
				margin-top: 10px;
			}
		}
		.el-aside {
			border-right: unset !important;
			& + .el-container {
				padding-left: 10px;
			}
		}
	}

	.el-main-hascontainer {
		padding: 0px;
		background-color: unset !important;
	}
}

.el-table {
	--el-table-header-bg-color: var(--el-fill-color-light) !important; //重新定义表头颜色
}

.el-tabs {
	width: 100%; //tabs宽度默认撑满
}

.el-form + .el-button {
	//表单和按钮同排
	margin-left: 15px;
}

.el-drawer {
	.el-drawer__body {
		//抽屉组件加个内边距
		padding: 0 20px;
	}
}


.mobileNavBox .el-drawer__body{
	padding: 0px;
}

.el-table tr{
	height: 50px;
}


//el官方设置的默认宽度是 220px 但是小屏幕会出现问题 故改成100%
.el-date-editor {
	--el-date-editor-width: 100%;
}
.stu_select .el-tabs__content {
	padding: 0 !important;
}
.tea_select .el-tabs__content {
	padding: 0 !important;
}
.el-tabs__item.is-active{
	font-weight: bold;
}
