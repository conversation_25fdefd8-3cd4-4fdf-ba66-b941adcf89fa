<template>
	<el-form-item label="元素个数" prop="modelValue.size">
		<el-input v-model="modelValue.size" placeholder="请输入元素个数" clearable type="number"> </el-input>
	</el-form-item>
	<el-form-item label="元素类型" prop="modelValue.type">
		<el-select
			v-model="modelValue.type"
			placeholder="请选择元素类型"
			style="width: 100%; max-width: unset"
			@change="typeChange"
		>
			<el-option
				v-for="item in typeConfig"
				:key="item.value"
				:label="item.value + ' (' + item.label + ')'"
				:value="item.value"
			/>
		</el-select>
	</el-form-item>
	<template v-if="modelValue.type === 'int'">
		<int-module v-model="modelValue.specs"></int-module>
	</template>
	<template v-if="modelValue.type === 'float'">
		<float-module v-model="modelValue.specs"></float-module>
	</template>
	<template v-if="modelValue.type === 'enum'">
		<enum-module v-model="modelValue.specs"></enum-module>
	</template>
	<template v-if="modelValue.type === 'bool'">
		<bool-module v-model="modelValue.specs"></bool-module>
	</template>
	<template v-if="modelValue.type === 'text'">
		<text-module v-model="modelValue.specs"></text-module>
	</template>
	<template v-if="modelValue.type === 'date'">
		<date-module v-model="modelValue.specs"></date-module>
	</template>
	<template v-if="modelValue.type === 'struct'">
		<struct-module v-model="modelValue.specs" @changeSize="changeSize"></struct-module>
	</template>
</template>
<script>
import DateModule from '@/components/thingsModel/dateModule.vue'
import IntModule from '@/components/thingsModel/intModule.vue'
import FloatModule from '@/components/thingsModel/floatModule.vue'
import BoolModule from '@/components/thingsModel/boolModule.vue'
import TextModule from '@/components/thingsModel/textModule.vue'
import EnumModule from '@/components/thingsModel/enumModule.vue'
import StructModule from '@/components/thingsModel/structModule.vue'

export default {
	name: 'arrayModule',
	components: { StructModule, EnumModule, TextModule, BoolModule, FloatModule, IntModule, DateModule },
	props: {
		modelValue: {
			type: Object,
			default: () => {
				return {
					size: null,
					type: null,
					specs: {}
				}
			}
		}
	},
	data() {
		return {
			unitConfig: [],
			typeConfig: [
				{
					label: '整数型',
					value: 'int'
				},
				{
					label: '浮点型',
					value: 'float'
				},
				{
					label: '字符串',
					value: 'text'
				},
				{
					label: '枚举',
					value: 'enum'
				},
				{
					label: '布尔类型',
					value: 'bool'
				},
				{
					label: '时间型',
					value: 'date'
				},
				{
					label: '结构体',
					value: 'struct'
				}
			]
		}
	},
	emits: ['update:modelValue', 'changeSize'], // 明确声明该组件会触发 update:modelValue 事件
	watch: {
		modelValue: {
			handler(val) {
				this.$emit('update:modelValue', val)
			},
			deep: true
		}
	},
	created() {},
	methods: {
		changeSize(size) {
			this.$emit('changeSize', size)
		},
		typeChange(value) {
			let specs = null
			switch (value) {
				case 'int':
				case 'float':
					specs = {
						max: null,
						min: null,
						step: null,
						unit: null,
						unit_name: null,
						unit_symbol: null
					}
					break
				case 'enum':
					specs = [{ value: null, name: null }]
					break
				case 'text':
					specs = {
						len: ''
					}
					break
				case 'bool':
					specs = ['关', '开']
					break
				case 'struct':
					specs = []
					break
			}
			this.$emit('update:modelValue', {
				size: this.modelValue.size,
				type: value,
				specs: specs
			})
		}
	}
}
</script>

<style scoped lang="scss">
.enum_header {
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.enum_item {
	margin: 5px 0;
	font-size: 12px !important;
	color: #666;
	width: 100%;
}

.text-center {
	text-align: center;
}
</style>
