<template>
	<el-header>
		<el-select v-model="params.status" placeholder="请选择状态" clearable @change="statusChange">
			<el-option v-for="item in statusMap" :key="item.value" :label="item.name" :value="item.value" />
		</el-select>
	</el-header>

	<scTable ref="table" row-key="id" stripe :apiObj="apiObj" :params="params" height="calc(100% - 59px)">
		<el-table-column prop="student" label="学生姓名">
			<template #default="{ row }">
				{{ row.student.name }}
			</template>
		</el-table-column>
		<el-table-column prop="vacate_type" label="请假类型">
			<template #default="{ row }">
				{{ formData(studentVacateTypeMap, row.vacate_type) }}
			</template>
		</el-table-column>
		<!--		<el-table-column prop="semester" label="学期">
			<template #default="{ row }">
				{{ row.academic.name + '-' + row.semester.name }}
			</template>
		</el-table-column>
		<el-table-column prop="calss" label="班级">
			<template #default="{ row }">
				{{ row.grade.name + row.class.name }}
			</template>
		</el-table-column>-->
		<el-table-column prop="begin_time" label="请假时间" width="180">
			<template #default="{ row }">
				{{ row.begin_time + '至' + row.end_time }}
			</template>
		</el-table-column>
		<el-table-column prop="reason" label="申请事由" min-width="200" max-width="300" show-overflow-tooltip>
		</el-table-column>
		<el-table-column prop="img" label="附件图片" width="300">
			<template #default="{ row }">
				<div v-if="row.img !== ''">
					<el-image
						v-for="(item, index) in row.img.split(',')"
						:src="item"
						:preview-src-list="row.img.split(',')"
						z-index="1000000000"
						:initial-index="index"
						preview-teleported
						style="width: 50px; height: 50px; margin: 0px 5px"
						fit="cover"
					>
					</el-image>
				</div>
			</template>
		</el-table-column>
		<el-table-column prop="status" label="审核状态">
			<template #default="{ row }">
				<el-tag v-if="row.status === 1" type="success">已审批</el-tag>
				<el-tag v-if="row.status === -1">审核中</el-tag>
				<el-tag v-if="row.status === 2" type="success">已销假</el-tag>
				<el-tag v-if="row.status === -2" type="warning">待销假</el-tag>
				<el-tag v-if="row.status === -3" type="danger">已驳回</el-tag>
			</template>
		</el-table-column>
		<el-table-column prop="audit" label="审核人">
			<template #default="{ row }">
				{{ row.audit?.name || '-' }}
			</template>
		</el-table-column>
		<el-table-column prop="audit_time" label="审核时间" width="180"></el-table-column>
		<el-table-column prop="audit_remark" label="审核备注" max-width="200" show-overflow-tooltip />
		<el-table-column label="操作" fixed="right" align="center" width="180">
			<template #default="scope">
				<el-button-group v-if="scope.row.status === -1 || scope.row.status === -2">
					<el-button text type="primary" size="small" @click="table_pass(scope.row, scope.$index)">通过</el-button>
					<el-button text type="danger" size="small" @click="table_reject(scope.row, scope.$index)">驳回</el-button>
				</el-button-group>
			</template>
		</el-table-column>
	</scTable>
	<el-dialog v-model="dialogFormVisible" :title="dialogTitle" width="500">
		<cusForm ref="formref" v-model="form" :config="formConfig"></cusForm>
		<template #footer>
			<el-button @click="dialogFormVisible = false">取消</el-button>
			<el-button type="primary" @click="confirm">确定</el-button>
		</template>
	</el-dialog>
</template>
<script>
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, campusInfo, studentVacateTypeMap } = cusTom.getBaseQuery()
export default {
	props: {
		data: {
			tpye: Array,
			default: () => []
		}
	},
	data() {
		return {
			campusId,
			tenantId,
			studentVacateTypeMap,
			params: {},
			apiObj: this.$API.class.vacate,
			statusMap: [
				{
					name: '审批中',
					value: -1
				},
				{
					name: '已审批',
					value: 1
				},
				{
					name: '待销假',
					value: -2
				},
				{
					name: '已销假',
					value: 2
				},
				{
					name: '已驳回',
					value: -3
				}
			],
			leaveList: [],
			dialogTitle: '通过',
			dialogFormVisible: false,
			form: {
				audit_remark: null
			},
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: '审批意见',
						name: 'audit_remark',
						value: null,
						component: 'textarea',
						options: {
							placeholder: '请输入审批意见',
							items: []
						},
						rules: [{ required: true, message: '请输入审批意见', trigger: 'blur' }]
					}
				]
			}
		}
	},
	created() {
		this.params = {
			class_id: this.$route.query.id,
			tenant_id: tenantId,
			campus_id: campusId
		}
	},
	methods: {
		statusChange(val) {
			this.params.status = val
			this.$refs.table.upData(this.params)
		},
		table_pass(row, index) {
			this.dialogTitle = '通过'
			this.dialogFormVisible = true
			this.form = {
				id: row.id,
				tenant_id: tenantId,
				campus_id: campusId,
				audit_action: 1,
				audit_remark: ''
			}
		},
		table_reject(row, index) {
			this.dialogTitle = '驳回'
			this.dialogFormVisible = true
			this.form = {
				id: row.id,
				tenant_id: tenantId,
				campus_id: campusId,
				audit_action: -1,
				audit_remark: ''
			}
		},
		confirm() {
			this.$refs.formref.validate((valid) => {
				if (valid) {
					let params = {
						id: this.form.id,
						tenant_id: tenantId,
						campus_id: campusId,
						audit_action: this.form.audit_action,
						audit_remark: this.form.audit_remark
					}
					this.$API.applyLeave.audit.post(params).then((res) => {
						if (res.code === 200) {
							ElMessage.success(res.message)
						} else {
							ElMessage.error(res.message)
						}
						this.$refs.table.refresh()
					})
					this.dialogFormVisible = false
					this.$refs.formref.resetFields()
				}
			})
		},
		//数据回显格式化
		formData(arr, val) {
			return arr.find((v) => v.value == val)?.name || '-'
		}
	}
}
</script>
<style lang="scss" scoped></style>
