<template>
    <el-dialog v-if="dialogTypeVisible" v-model="dialogTypeVisible" title="缴费项类别设置" width="600">
        <div class="right">
            <el-button type="primary" @click="type_add">新增类别项</el-button>
        </div>
        <scTable ref="table" row-key="id" :params="params" :data="objData" :hideDo="true">
            <el-table-column label="类别名称" prop="type_name" >
            </el-table-column>
            <el-table-column label="操作">
                <template #default="scope">
                    <el-button link type="primary" size="small" @click="type_edit(scope.row)">编辑</el-button>
                    <el-button link type="primary" size="small" @click="type_del(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </scTable>
        <typeSave ref="saveType" :params="params" @success="typeSaveSuccess"></typeSave>
    </el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance, nextTick, onMounted } from 'vue'
import cusTom from '@/utils/cusTom'
import typeSave from './typeSave'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, tenantInfo, campusInfo, semesterInfo } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params'])
const emit = defineEmits(['success'])
const dialogTypeVisible = ref(false)
const defaultParams = () => {
    return {
        item_name: '',
        tenant_id: tenantId,
        campus_id: campusId,
    }
}
const defaultForm = () => {
    return {
        item_name: '',
        tenant_id: tenantId,
        campus_id: campusId,
    }
}
let saveType = ref()
let params = ref(defaultParams())
let objData = ref()
let form = ref(defaultForm())
onMounted(() => {
    tableData()
})
const tableData = async () => {
    const { data } = await globalPropValue.finance.recruit.recordTypeList.get(params.value)
    objData.value = data
}
const open = () => {
    dialogTypeVisible.value = true
}
// 新增类别项
const type_add = async () => {
    nextTick(() => {
        saveType.value.open('add')
    })
}
// 编辑类别项
const type_edit = (row) => {
    console.log(row)
    nextTick(() => {
        saveType.value.open('edit').setData(row)
    })
}
// 删除类别项
const type_del = async (row) => {
    const res = await globalPropValue.finance.recruit.recordTypeDel.post(row)
    if (res.code === 200) {
        ElMessage({ type: 'success', message: '删除成功' });
        tableData()
    } else {
        ElMessage({ type: 'error', message: res.message });
    }
    console.log(row)
}
//表单注入数据
const setData = (data) => {
    form.value = { ...data }
}
const typeSaveSuccess = () => {
    tableData()
}

defineExpose({
    dialogTypeVisible,
    open,
    setData
})
</script>

<style lang="scss" scoped>
.right {
    margin-bottom: 5px;
    text-align: right;
}
</style>
