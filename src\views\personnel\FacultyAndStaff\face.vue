<template>
	<el-drawer v-model="visible" title="人脸识别" destroy-on-close>
		<el-alert
			title="照片要求"
			type="warning"
			:closable="false"
			description="要求图像分辨率：大于200*200像素，小于1920 * 1920像素，包含清晰的脸部图片，避免出现模糊、旋转、遮挡、剪裁等情况。以免识别失败！"
			show-icon
		/>
		<el-form ref="dialogForm" :model="userInfo" :rules="rules" label-width="100px" style="padding-top: 30px">
			<el-form-item label="照片" prop="face_img">
				<sc-upload
					v-model="userInfo.face_img"
					:width="200"
					:height="200"
					round
					icon="el-icon-avatar"
					title="照片"
					:cropper="true"
					:compress="1"
					:aspectRatio="1 / 1"
				></sc-upload>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="saveUserInfo">保存</el-button>
		</template>
	</el-drawer>
</template>

<script>
export default {
	data() {
		return {
			fileurl6: '',
			form: {
				id: null,
				tenant_id: null,
				campus_id: null
			},
			isSaveing: false,
			visible: false,
			userInfo: {
				face_img: ''
			},
			rules: {
				face_img: [{ required: true, message: '请上传照片' }]
			}
		}
	},
	created() {},
	methods: {
		//显示
		open(mode = 'edit') {
			this.mode = mode
			this.visible = true
			return this
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.userInfo, data)
		},
		saveUserInfo() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.personnel.staff.face.post({
						id: this.userInfo.id,
						tenant_id: this.userInfo.tenant_id,
						campus_id: this.userInfo.campus_id,
						face_img: this.userInfo.face_img
					})
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('修改成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		}
	}
}
</script>

<style></style>
