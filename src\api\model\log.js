import config from '@/config'
import http from '@/utils/request'

export default {
	login: {
		url: `${config.API_URL}/sysapi/user/loginLog`,
		name: '获取登录日志',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	action: {
		url: `${config.API_URL}/sysapi/user/actionLog`,
		name: '获取操作日志',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	}
}
