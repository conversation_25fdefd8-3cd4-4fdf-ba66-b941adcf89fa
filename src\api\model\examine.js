import config from '@/config'
import http from '@/utils/request'

export default {
	timeList: {
		url: `${config.API_URL}/eduapi/examine/timeList`,
		name: '获取考试时段列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	timeSave: {
		url: `${config.API_URL}/eduapi/examine/timeSave`,
		name: '新增/编辑考试时段',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	timeDel: {
		url: `${config.API_URL}/eduapi/examine/timeDel`,
		name: '删除考试时段',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	roomList: {
		url: `${config.API_URL}/eduapi/examine/roomList`,
		name: '获取考试场地列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	roomSave: {
		url: `${config.API_URL}/eduapi/examine/roomSave`,
		name: '新增/编辑考试场地',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	staffList: {
		url: `${config.API_URL}/eduapi/examine/staffList`,
		name: '获取考试人员列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	staffSave: {
		url: `${config.API_URL}/eduapi/examine/staffSave`,
		name: '新增/编辑考试人员',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	studentList: {
		url: `${config.API_URL}/eduapi/examine/studentList`,
		name: '获取考生安排列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	roomStudentList: {
		url: `${config.API_URL}/eduapi/examine/roomStudentList`,
		name: '获取单个考场/科目 考生安排列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	arrange: {
		url: `${config.API_URL}/eduapi/examine/arrange`,
		name: '一键安排考生',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	changeStudentArrange: {
		url: `${config.API_URL}/eduapi/examine/changeStudentArrange`,
		name: '更换考生考场',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	getMonitorTeacherList: {
		url: `${config.API_URL}/eduapi/examine/getMonitorTeacherList`,
		name: '获取监考安排列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	saveMonitorTeacher: {
		url: `${config.API_URL}/eduapi/examine/saveMonitorTeacher`,
		name: '保存监考安排',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	arrangeTeacher: {
		url: `${config.API_URL}/eduapi/examine/arrangeTeacher`,
		name: '一键安排监考老师',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	checkExam: {
		url: `${config.API_URL}/eduapi/examine/checkExam`,
		name: '检查排考',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	studentArrangeList: {
		url: `${config.API_URL}/eduapi/examine/studentArrangeList`,
		name: '获取考生安排列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getMonitorWork: {
		url: `${config.API_URL}/eduapi/examine/getMonitorWork`,
		name: '获取监考分工表',
		get: async function (data = {}) {
			return await http.get(this.url, data)
		}
	},
	list: {
		url: `${config.API_URL}/eduapi/examine/list`,
		name: '获取考试安排列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	save: {
		url: `${config.API_URL}/eduapi/examine/save`,
		name: '新增/编辑考试安排',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	one: {
		url: `${config.API_URL}/eduapi/examine/one`,
		name: '获取单个考试安排信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	del: {
		url: `${config.API_URL}/eduapi/examine/del`,
		name: '删除考试安排',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	changeStatus: {
		url: `${config.API_URL}/eduapi/examine/changeStatus`,
		name: '状态更改',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	sync: {
		url: `${config.API_URL}/eduapi/examine/sync`,
		name: '同步考试安排',
		post: async function (data = {}) {
			return await http.post(this.url, data)
		}
	},
	getSignInfo: {
		url: `${config.API_URL}/eduapi/examine/getSignInfo`,
		name: '获取签到信息',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	}
}
