<template>
	<el-dialog v-model="visible" title="提示" width="550" destroy-on-close @close="close">
		<div class="warn-list">
			<el-alert
				v-for="(item, index) in warnList"
				:key="index"
				:title="item.error"
				type="warning"
				show-icon
				:closable="false"
				style="margin-bottom: 10px"
			/>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="close">取 消</el-button>
				<el-button type="primary" @click="save">确定</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup>
const emit = defineEmits(['confirm'])
const save = () => {
	emit('confirm')
	visible.value = false
}
const warnList = ref([])
const visible = ref(false)
const open = (data) => {
	warnList.value = data
	visible.value = true
}
const close = () => {
	visible.value = false
}

defineExpose({
	open,
	close
})
</script>

<style lang="scss" scoped>
.warn-list {
}
</style>
