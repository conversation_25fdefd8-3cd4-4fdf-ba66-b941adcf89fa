<template>
	<el-dialog v-if="dialogFormVisible" v-model="dialogFormVisible" :title="titleMap[mode1]" width="500">
		<cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode1">
			<template #customItem>
				<el-form-item label="附件">
					<scUploadFile v-model="form.file" accept="*" fileTypeTag="finIncomeAndExpenditure"></scUploadFile>
				</el-form-item>
			</template>
		</cusForm>
		<template #footer>
			<el-button @click="dialogFormVisible = false">取消</el-button>
			<el-button type="primary" @click="confirm">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance, nextTick, onMounted } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, tenantInfo, campusInfo, semesterInfo, paymentTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params'])
const emit = defineEmits(['success'])
let mode1 = ref('add')
const dialogFormVisible = ref(false)
const defaultData = () => {
	return {
		id: null,
		item_name: '',
		item_type: '',
		tenant_id: tenantId,
		campus_id: campusId,
		amount: null,
		remark: '',
		semester_id: null,
		academic_id: null,
		campus_id: null,
		tenant_id: null,
		type_id: '',
		record_name: '',
		record_type: '',
		student_id: '',
		payment_type: '',
		file: '',
		mode: null
	}
}
let form = ref(defaultData())
let formConfig = ref({
	labelWidth: '150px',
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: '收支项目名称',
			name: 'record_name',
			value: null,
			component: 'input',
			options: {
				placeholder: '请输入收支项目名称',
				items: []
			},
			rules: [{ required: true, message: '请输入收支项目名称', trigger: 'blur' }]
		},
		{
			label: '收支项目类型',
			name: 'type_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择收支项目类型',
				items: []
			},
			rules: [{ required: true, message: '请选择收支项目类型', trigger: 'blur' }]
		},
		{
			label: '收支类型',
			name: 'record_type',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择收支类型',
				items: [
					{ label: '收入', value: 1 },
					{ label: '支出', value: 2 }
				]
			},
			rules: [{ required: true, message: '请选择收支类型', trigger: 'blur' }]
		},
		{
			label: '金额（单位：元）',
			name: 'amount',
			value: null,
			component: 'number',
			options: {
				placeholder: '请输入金额',
				items: []
			},
			rules: [{ required: true, message: '请输入金额', trigger: 'blur' }]
		},
		{
			label: '支付方式',
			name: 'payment_type',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择支付方式',
				items: paymentTypeMap.map((v) => {
					return {
						label: v.name,
						value: v.value
					}
				})
			},
			rules: [{ required: true, message: '请选择支付方式', trigger: 'blur' }]
		},
		// {
		//     label: '学年',
		//     name: 'academic_id',
		//     value: null,
		//     component: 'select',
		//     options: {
		//         placeholder: '请选择学年',
		//         items: semesterInfo.map((v) => {
		//             return {
		//                 label: v.name,
		//                 value: v.value
		//             }
		//         })
		//     },
		//     rules: [{ required: true, message: '请选择学年', trigger: 'blur' }]
		// },
		// {
		//     label: '学期',
		//     name: 'semester_id',
		//     value: null,
		//     component: 'select',
		//     options: {
		//         placeholder: '请选择学期',
		//         items: []
		//     },
		//     rules: [{ required: true, message: '请选择学期', trigger: 'blur' }]
		// },
		{
			label: '学生',
			name: 'student_id',
			value: null,
			component: 'cusSelectStudent',
			options: {
				placeholder: '请选择学生',
				items: []
			}
		},
		{
			label: '备注',
			name: 'remark',
			value: null,
			component: 'textarea',
			options: {
				placeholder: '请输入...',
				items: []
			}
		}
	]
})
const defaultParams = () => {
	return {
		tenant_id: tenantId,
		campus_id: campusId
	}
}
let params = ref(defaultParams())
let formref = ref(null)
let titleMap = ref({ add: '新增', edit: '编辑' })
onMounted(async () => {
	const { data } = await globalPropValue.finance.recruit.recordTypeList.get(params.value)
	formConfig.value.formItems[1].options.items = data.map((v) => {
		return {
			label: v.type_name,
			value: v.id
		}
	})
})
const open = async (mode = 'add') => {
	dialogFormVisible.value = true
	mode1.value = mode
	form.value.mode = mode
	if (mode === 'add') {
		form.value = defaultData()
		setTimeout(() => {
			formref.value.resetFields()
			form.value.campus_id = props.params.campus_id
			form.value.tenant_id = props.params.tenant_id
		}, 0)
	}
	if (mode === 'edit') {
		nextTick(() => {
			formref.value.resetFields()
		})
	}
}
const confirm = async () => {
	await formref.value.validate()
	let subForm = { ...form.value }
	const handleSuccess = (data) => {
		emit('success', form.value, mode1.value)
		dialogFormVisible.value = false
		ElMessage({ type: 'success', message: '操作成功' })
	}
	const handleError = (message) => {
		ElMessage({ type: 'error', message })
	}
	if (mode1.value === 'add') {
		if (subForm.student_id) {
			subForm.student_id = subForm.student_id[0].id
		} else {
			subForm.student_id = null
		}
		const res = await globalPropValue.finance.recruit.recordAdd.post(subForm)
		if (res.code === 200) {
			handleSuccess(res)
		} else {
			handleError(res.message)
		}
	} else if (mode1.value === 'edit') {
		const res = await globalPropValue.finance.recruit.recordEdit.post(subForm)
		if (res.code === 200) {
			handleSuccess(res)
		} else {
			handleError(res.message)
		}
	}
}

// watch(
//     () => form.value.campus_id,
//     (val) => {
//         console.log(val)
//         form.value.academic_id = null
//         formConfig.value.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
//             .filter((v) => v.parent_id === 0 && v.campus_id === val)
//             .map((v) => {
//                 return {
//                     label: v.name,
//                     value: v.value
//                 }
//             })
//     },
//     { immediate: true }
// )
// watch(
//     () => form.value.academic_id,
//     (val) => {
//         console.log(val)
//         form.value.semester_id = null
//         formConfig.value.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
//             .filter(
//                 (v) => v.parent_id !== 0 && v.parent_id === form.value.academic_id && v.campus_id === form.value.campus_id
//             )
//             .map((v) => {
//                 return {
//                     label: v.name,
//                     value: v.value
//                 }
//             })
//     },
//     { immediate: true }
// )
//表单注入数据
const setData = (data) => {
	data.student_id = [
		{
			id: data.student_id,
			label: data.student_name
		}
	]
	Object.assign(form.value, data)
	// form.value = { ...data }
}

defineExpose({
	dialogFormVisible,
	open,
	setData
})
</script>

<style lang="scss" scoped></style>
