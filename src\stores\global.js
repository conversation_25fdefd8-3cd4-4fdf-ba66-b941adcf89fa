import { defineStore } from 'pinia'
import config from '@/config'

export const useGlobalStore = defineStore('global', {
	state: () => {
		return {
			//移动端布局
			ismobile: false,
			//布局
			layout: config.LAYOUT,
			//菜单是否折叠 toggle
			menuIsCollapse: config.MENU_IS_COLLAPSE,
			//多标签栏
			layoutTags: config.LAYOUT_TAGS,
			//水印
			layoutWaterMark: config.LAYOUT_WATER_MARK,
			//主题
			theme: config.COLOR,
			size: config.LAYOUT_SIZE
		}
	},
	actions: {
		SET_ismobile(key) {
			this.ismobile = key
		},
		SET_layout(key) {
			this.layout = key
			/*if (key === 'dock') {
				this.layoutTags = false
			} else {
				this.layoutTags = true
			}*/
		},
		SET_theme(key) {
			this.theme = key
		},
		SET_size(key) {
			this.size = key
		},
		TOGGLE_menuIsCollapse() {
			this.menuIsCollapse = !this.menuIsCollapse
		},
		TOGGLE_layoutTags() {
			this.layoutTags = !this.layoutTags
		},
		TOGGLE_layoutWaterMark() {
			this.layoutWaterMark = !this.layoutWaterMark
		}
	}
})
