<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<el-select
						v-if="CampusManagementList.length > 1"
						v-model="params.campus_id"
						placeholder="校区"
						filterable
						style="margin-right: 15px"
						@change="campusChange"
					>
						<el-option
							v-for="item in CampusManagementList"
							:key="item.code"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
					<cusSelectSemester
						v-model="params.semester_id"
						:params="params"
						:show-default-value="true"
						:width="'214px'"
						clearable
						style="margin-right: 15px"
						@semesterChange="semesterChange"
					/>
				</div>
			</div>
		</el-header>
		<div v-loading="loading" class="echartsOut">
			<el-row :gutter="20">
				<el-col :span="8">
					<el-card shadow="hover">
						<scEcharts class="scEcharts" :option="studentGrade" width="100%" height="300px"></scEcharts>
					</el-card>
				</el-col>
				<el-col :span="8">
					<el-card shadow="hover">
						<scEcharts class="scEcharts" :option="studentSex" width="100%" height="300px"></scEcharts>
					</el-card>
				</el-col>
				<el-col :span="8">
					<el-card shadow="hover">
						<scEcharts class="scEcharts" :option="studentIdentity" width="100%" height="300px"></scEcharts>
					</el-card>
				</el-col>
				<el-col :span="24">
					<el-card shadow="hover">
						<scEcharts class="scEcharts" :option="studentAge" width="100%" height="300px"></scEcharts>
					</el-card>
				</el-col>
			</el-row>
		</div>
	</el-container>
</template>
<script>
import cusTom from '@/utils/cusTom'
import cusSelectSemester from '@/components/custom/cusSelectSemester.vue'
import scEcharts from '@/components/scEcharts/index.vue'
import { ElMessage } from 'element-plus'

const { campusInfo, tenantId, campusId } = cusTom.getBaseQuery()

export default {
	name: 'index',
	components: {
		scEcharts,
		cusSelectSemester
	},
	data() {
		return {
			params: {
				tenant_id: tenantId,
				campus_id: campusId,
				semester_id: null
			},
			loading: false,
			overviewData: {},
			CampusManagementList: [],
			studentGrade: {
				color: [
					'#5AD8A6',
					'#5D7092',
					'#F6BD16',
					'#6F5EF9',
					'#6DC8EC',
					'#945FB9',
					'#FF9845',
					'#1E9493',
					'#FF99C3',
					'#AABA01',
					'#BC7CFC',
					'#237CBC',
					'#2DE379',
					'#CE8032',
					'#FF7AF4',
					'#545FD3',
					'#AFE410',
					'#D8C608',
					'#FFA1E0'
				],
				title: {
					text: '学生年级分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			studentSex: {
				color: ['#FFA1E0', '#237CBC', '#CE8032'],
				title: {
					text: '学生性别分布',
					subtext: ''
				},
				tooltip: {
					trigger: 'item'
				},
				series: [
					{
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '45%'],
						label: false,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: '#fff',
							borderWidth: 2
						}
					}
				],
				legend: {
					bottom: '5%'
				}
			},
			studentAge: {
				color: ['#F6BD16'],
				title: {
					text: '学生年龄',
					subtext: ''
				},
				grid: {
					top: '80px'
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: []
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						barWidth: 20,
						data: [],
						type: 'bar'
					}
				]
			},
			studentIdentity: {
				color: ['#545FD3'],
				title: {
					text: '学生身份',
					subtext: ''
				},
				grid: {
					top: '80px'
				},
				tooltip: {
					trigger: 'axis'
				},
				xAxis: {
					type: 'category',
					data: []
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						barWidth: 20,
						data: [],
						type: 'bar'
					}
				]
			}
		}
	},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id === this.params.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	created() {
		//获取总览数据
		this.CampusManagementList = campusInfo
	},
	methods: {
		campusChange(val) {
			this.params.campus_id = val
			this.getData()
		},
		semesterChange(val) {
			this.params.semester_id = val
			this.getData()
		},
		async getData() {
			this.loading = true
			const res = await this.$API.common.stuOverview.get(this.params)
			this.loading = false
			if (res.code === 200) {
				this.overviewData = res.data
				if (res.data.student_grade !== null) {
					this.studentGrade.series[0].data = res.data.student_grade.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.studentGrade.series[0].data = []
				}

				if (res.data.student_sex !== null) {
					this.studentSex.series[0].data = res.data.student_sex.map((v) => {
						return {
							name: v.name,
							value: v.count
						}
					})
				} else {
					this.studentSex.series[0].data = []
				}

				if (res.data.student_age !== null) {
					this.studentAge.series[0].data = res.data.student_age.map((v) => {
						return v.count
					})

					this.studentAge.xAxis.data = res.data.student_age.map((v) => {
						return v.name
					})
				} else {
					this.studentAge.series[0].data = []
					this.studentAge.xAxis.data = []
				}
				if (res.data.student_identity !== null) {
					this.studentIdentity.series[0].data = res.data.student_identity.map((v) => {
						return v.count
					})

					this.studentIdentity.xAxis.data = res.data.student_identity.map((v) => {
						return v.name
					})
				} else {
					this.studentIdentity.series[0].data = []
					this.studentIdentity.xAxis.data = []
				}
			} else {
				ElMessage({ type: 'error', message: res.message })
			}
		}
	}
}
</script>
<style scoped lang="scss">
.echartsOut {
	margin-top: 10px;
}

:deep(.el-statistic) {
	.el-statistic__number {
		font-size: 26px;
	}

	.el-statistic__head {
		font-size: 16px;
		font-weight: bold;
		line-height: 30px;
	}

	.el-statistic__content {
		text-align: center;
		line-height: 30px;
	}

	.el-statistic__suffix {
		color: var(--el-text-color-secondary);
		font-size: 14px;
	}
}
</style>
