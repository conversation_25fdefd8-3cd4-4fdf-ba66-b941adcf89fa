import config from '@/config'
import http from '@/utils/request'

export default {
	list: {
		url: `${config.API_URL}/lot/room/list`,
		name: '获取',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	setConf: {
		url: `${config.API_URL}/lot/room/setConf`,
		name: '设置教室看板',
		post: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.post(this.url, params)
		}
	},
  getConf: {
		url: `${config.API_URL}/lot/room/getConf`,
		name: '获取教室看板',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getEnvironment: {
		url: `${config.API_URL}/lot/room/getEnvironment`,
		name: '获取环境数据',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getAirConditioner: {
		url: `${config.API_URL}/lot/room/getAirConditioner`,
		name: '获取空调数据',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getLamplight: {
		url: `${config.API_URL}/lot/room/getLamplight`,
		name: '获取灯列表',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	},
	getCurtain: {
		url: `${config.API_URL}/lot/room/getCurtain`,
		name: '获取窗帘数据',
		get: async function (params) {
			// eslint-disable-next-line no-return-await
			return await http.get(this.url, params)
		}
	}
}
