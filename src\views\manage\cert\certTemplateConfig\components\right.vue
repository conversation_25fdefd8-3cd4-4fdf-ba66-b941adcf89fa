<template>
	<div class="right-container" @mousedown.stop="mousedown">
		<div class="segmented-container">
			<el-segmented v-model="type" :options="typeList" block @change="typeChange" />
		</div>
		<div class="content">
			<!-- 设置画布尺寸 -->
			<div v-if="type === 1" class="canvas-set">
				<div class="size-set">
					<h3>画布尺寸</h3>
					<el-radio-group v-model="canvasSet.sizeType" @change="changeSizeType">
						<el-radio v-for="item in size" :value="item.value">{{ item.label }}</el-radio>
					</el-radio-group>
				</div>
				<div v-if="canvasSet.sizeType !== 3">
					<h3>画布方向</h3>
					<el-radio-group v-model="canvasSet.directionType" @change="changeDirectionType">
						<el-radio :value="1">纵向</el-radio>
						<el-radio :value="2">横向</el-radio>
					</el-radio-group>
				</div>
				<div v-else>
					<h3>自定义尺寸</h3>
					<div class="input-group">
						<el-input v-model="canvasSet.width" placeholder="宽度" style="margin-right: 5px">
							<template #suffix>
								<span>宽</span>
							</template>
						</el-input>
						<el-input v-model="canvasSet.height" placeholder="高度">
							<template #suffix>
								<span>高</span>
							</template>
						</el-input>
					</div>
					<div class="tips"><span style="color: red">*</span> 画布单位：px</div>
				</div>
			</div>
			<!-- 设置文本内容 -->
			<div v-else-if="type === 2" class="text-set">
				<div class="btn-group">
					<el-button icon="el-icon-plus" style="width: 100%" @click="addTextItem">新增文本框</el-button>
				</div>
				<div v-if="current !== null">
					<div class="input-group">
						<div style="margin-right: 5px">
							<h3>水平距离</h3>
							<el-input v-model="current.x" placeholder=""></el-input>
						</div>
						<div>
							<h3>垂直距离</h3>
							<el-input v-model="current.y" placeholder=""></el-input>
						</div>
					</div>

					<div class="input-group title">
						<h3>尺寸</h3>
						<el-checkbox v-model="current.scale" label="按比例缩放" />
					</div>
					<div>
						<div class="input-group">
							<el-input v-model="current.w" readonly placeholder="宽度" style="margin-right: 5px">
								<template #suffix>
									<span>宽</span>
								</template>
							</el-input>
							<el-input v-model="current.h" readonly placeholder="高度">
								<template #suffix>
									<span>高</span>
								</template>
							</el-input>
						</div>
					</div>
					<div>
						<h3>文本内容</h3>
						<cusEditor
							v-if="showEditor"
							ref="editor"
							v-model="current.content"
							placeholder="请输入文本内容"
							mode="default"
							height="350px"
							:exclude-keys="editorExcludeKeys"
						></cusEditor>
					</div>
				</div>
			</div>
			<!-- 图片设置 -->
			<div v-else class="image-set">
				<div v-if="current !== null">
					<div class="input-group">
						<div style="margin-right: 5px">
							<h3>水平距离</h3>
							<el-input v-model="current.x" placeholder=""></el-input>
						</div>
						<div>
							<h3>垂直距离</h3>
							<el-input v-model="current.y" placeholder=""></el-input>
						</div>
					</div>
					<div>
						<div class="input-group title">
							<h3>尺寸</h3>
							<el-checkbox v-model="current.scale" label="按比例缩放" />
						</div>
						<div>
							<div class="input-group">
								<el-input v-model="current.w" readonly placeholder="宽度" style="margin-right: 5px">
									<template #suffix>
										<span>宽</span>
									</template>
								</el-input>
								<el-input v-model="current.h" readonly placeholder="高度">
									<template #suffix>
										<span>高</span>
									</template>
								</el-input>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import cusEditor from '@/components/custom/cusEditor.vue'
import tool from '@/utils/tool'
export default {
	components: {
		cusEditor
	},
	props: {
		canvasConfig: {
			type: Object,
			default: () => ({
				width: 600,
				height: 400
			})
		},
		list: {
			type: Array,
			default: () => []
		},
		currentNode: {
			type: Object,
			default: null
		}
	},
	data() {
		return {
			certData: this.list,
			checked: true,
			showEditor: true,
			canvasSet: this.canvasConfig,
			current: this.currentNode,
			typeList: [
				{ label: '画布', value: 1 },
				{ label: '文本', value: 2 },
				{ label: '图片', value: 3 }
			],
			size: [
				{ label: 'A4', value: 1 },
				{ label: 'A5', value: 2 },
				{ label: '自定义', value: 3 }
			],
			editorExcludeKeys: [
				'insertLink',
				'headerSelect',
				'blockquote',
				'bgColor',
				'insertTable',
				'codeBlock',
				'emotion',
				'insertImage',
				'insertVideo',
				'uploadVideo',
				'uploadImage',
				'todo',
				'group-image',
				'group-video',
				'group-more-style',
				'|',
				'bulletedList',
				'numberedList',
				'divider',
				'undo',
				'redo'
			],
			type: 1,
			sizeType: 3,
			directionType: 1
		}
	},
	watch: {
		canvasConfig: {
			handler(newVal) {
				this.canvasSet = newVal
				/*if (newVal.bgImg === '' || newVal.bgImg === undefined || newVal.bgImg == null) {
					this.delFristBg()
				}*/
			},
			deep: true
		},
		list: {
			handler(newVal) {
				this.certData = newVal
			},
			deep: true
		},
		currentNode: {
			handler(newVal) {
				if (newVal === null) {
					this.current = null
					return
				}
				switch (newVal.type) {
					case 'text':
						this.type = 2
						if (newVal.id !== this.current.id) {
							this.showEditor = false
							this.$nextTick(() => {
								this.showEditor = true
							})
						}
						//this.$refs.editor.setHtml(newVal.content)
						break
					case 'image':
						this.type = 3
						break
					default:
						this.type = 1
						break
				}
				this.current = newVal
			},
			deep: true
		}
	},
	methods: {
		suc(e) {
			// 使用示例
			let _this = this
			this.getImageSize(e.url, function (size) {
				if (size) {
					_this.canvasSet.directionType = 1
					_this.canvasSet.width = size.width
					_this.canvasSet.height = size.height
					_this.canvasSet.sizeType = 3
					_this.canvasSet.show = false
					//_this.canvasSet.bgImg = e.url
					_this.addFristBg(size.width, size.height, e.url)
					_this.$nextTick(() => {
						_this.canvasSet.show = true
						/*_this.current = null
						_this.$emit('activatedAction', _this.current)*/
					})
				} else {
					console.log('无法获取图片尺寸')
				}
			})
		},
		delFristBg() {
			let fristItem = this.certData[0]
			if (fristItem && fristItem.field === 'bgImg') {
				this.certData.shift()
				this.$emit('update:list', this.certData)
			}

			/*this.$nextTick(() => {
				this.$emit('activatedAction', this.current)
			})*/
		},
		addFristBg(w, h, url) {
			let item = {
				id: tool.getRandomString(16, 'alphanumeric'),
				type: 'image',
				field: 'bgImg',
				content: url,
				w: w,
				scale: true,
				active: false,
				x: 0,
				y: 0,
				h: h
			}
			let fristItem = this.certData[0]
			if (fristItem && fristItem.field === 'bgImg') {
				this.certData[0] = item
			} else {
				this.certData.unshift(item)
			}
			this.$emit('update:list', this.certData)
			/*this.$nextTick(() => {
				this.$emit('activatedAction', this.current)
			})*/
		},
		getImageSize(url, callback) {
			var img = new Image()
			img.onload = function () {
				callback({
					width: img.width,
					height: img.height
				})
			}
			img.onerror = function () {
				callback(null)
			}
			img.src = url
		},
		changeSizeType(val) {
			switch (val) {
				case 1:
					if (this.canvasSet.directionType === 1) {
						this.canvasSet.width = 794
						this.canvasSet.height = 1123
					} else {
						this.canvasSet.width = 1123
						this.canvasSet.height = 794
					}
					break
				case 2:
					if (this.canvasSet.directionType === 1) {
						this.canvasSet.width = 561
						this.canvasSet.height = 794
					} else {
						this.canvasSet.width = 794
						this.canvasSet.height = 561
					}
					break
			}
			this.canvasSet.show = false
			this.$nextTick(() => {
				this.canvasSet.show = true
				this.current = null
				this.$emit('activatedAction', this.current)
			})
		},
		changeDirectionType(val) {
			this.changeSizeType(this.canvasSet.sizeType)
		},
		mousedown(e) {},
		addTextItem() {
			this.current = {
				id: tool.getRandomString(16, 'alphanumeric'),
				type: 'text',
				field: '',
				content: '请输入文本内容',
				w: 100,
				scale: true,
				active: false,
				x: this.canvasConfig.width / 2 - 50,
				y: this.canvasConfig.height / 2 - 50,
				h: 100
			}
			this.certData.push(this.current)
			this.$emit('update:list', this.certData)
			this.$nextTick(() => {
				this.$emit('activatedAction', this.current)
			})
		},
		typeChange(val) {
			this.current = null
			this.$emit('activatedAction', this.current)
			console.log(val)
			this.type = val
		}
	}
}
</script>
<style lang="scss">
.right-container {
	height: 100%;

	.content {
		padding-top: 15px;
		height: calc(100% - 25px);
		overflow-y: auto;
	}

	.segmented-container {
	}

	h3 {
		margin-bottom: 5px;
	}

	.input-group {
		display: flex;
		margin-bottom: 5px;
	}

	.title {
		justify-content: space-between;
		align-items: center;

		h3 {
			margin: 0;
		}
	}

	.canvas-set {
		.size-set {
			margin-top: 10px;
			margin-bottom: 10px;
		}
	}

	.text-set {
		overflow-y: auto;

		.btn-group {
			margin-bottom: 10px;
		}
	}
}
</style>
