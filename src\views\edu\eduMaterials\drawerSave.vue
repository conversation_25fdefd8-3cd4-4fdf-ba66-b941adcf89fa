<template>
	<el-drawer v-model="table" :title="title" direction="rtl" size="50%" destroy-on-close	>
		<scTable ref="tableRef" row-key="id" stripe :apiObj="apiObj" :params="params">
			<el-table-column label="操作人姓名" prop="action_user_name"></el-table-column>
			<el-table-column label="用户类型" prop="user_type">
				<template #default="{ row }">
					<el-tag v-if="row.user_type === 1" type="success">学员</el-tag>
					<el-tag v-if="row.user_type === 2" type="primary">教职工</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="操作类型" prop="action_type">
				<template #default="{ row }">
					<el-tag v-if="row.action_type === 1" type="success">点赞</el-tag>
					<el-tag v-else type="success">收藏</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="操作时间" prop="created_at"></el-table-column>
		</scTable>
	</el-drawer>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue'
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		tenant_id: 0,
		campus_id: 0,
		material_id: 0,
		action_type: 0
	}
}
let title = ref('')
const params = ref(defaultParams())
let table = ref(false)
let tableRef = ref(null)
const open = (row, action_type) => {
	table.value = true
	params.value.tenant_id = row.tenant_id
	params.value.campus_id = row.campus_id
	params.value.material_id = row.id
	params.value.action_type = action_type
	title.value = action_type === 1 ? '点赞记录' : '收藏记录'
}
const apiObj = ref(globalPropValue.eduMaterials.material.get_collect_lick)
defineExpose({
	open
})
</script>
<style scoped></style>
