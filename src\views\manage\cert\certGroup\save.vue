<template>
	<el-dialog v-model="visible" width="500" :title="titleMap[mode]" destroy-on-close @closed="$emit('closed')">
		<el-form ref="dialogForm" :model="form" :rules="rules" :disabled="mode === 'show'" label-width="100">
			<el-form-item label="学年学期" prop="semester_id">
				<cusCascader v-model="form.semester_id" :disabled="form.status !== 1" placeholder="请选择学期" :options="getSemester"></cusCascader>
			</el-form-item>
			<el-form-item label="证书组名称" prop="name">
				<el-input v-model="form.name" placeholder="请输入证书组名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="证书类型" prop="type">
				<el-select v-model="form.type" :disabled="form.status !== 1" placeholder="请选择证书类型" clearable>
					<el-option v-for="item in certTypeMap" :key="item.value" :label="item.name" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="证书对象" prop="object">
				<el-select v-model="form.object" :disabled="form.status !== 1|| mode !== 'add'" placeholder="请选择证书对象" clearable>
					<el-option v-for="item in certObjectMap" :key="item.value" :label="item.name" :value="item.value" />
				</el-select>
			</el-form-item>

			<el-form-item label="证书模板" prop="template_id">
				<el-select v-model="form.template_id" :disabled="form.status !== 1" placeholder="请选择证书模板" clearable>
					<el-option v-for="item in templateMap" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="备注" prop="copy">
				<el-input v-model="form.remark" type="textarea" rows="3" placeholder="请输入备注" clearable></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { certTypeMap, semesterInfo, certObjectMap } = cusTom.getBaseQuery()

const defaultData = () => {
	return {
		campus_id: null,
		tenant_id: null,
		semester_id: null,
		name: null,
		type: null,
		object: null,
		status: 1,
		template_id: null,
		remark: null,
		id: null
	}
}

export default {
	emits: ['success', 'closed'],
	props: {
		params: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			certTypeMap,
			certObjectMap,
			semesterInfo,
			mode: 'add',
			templateMap: [],
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			visible: false,
			isSaveing: false,
			//表单数据
			form: defaultData(),
			//验证规则
			rules: {
				name: [{ required: true, message: '请输入模板名称' }],
				type: [{ required: true, message: '请选择证书类型' }],
				object: [{ required: true, message: '请选择证书对象' }],
				template_id: [{ required: true, message: '请选择证书模板' }]
			},
			course: [],
			discipline: [],
			treeData: [],
			position: []
		}
	},
	watch: {
		'form.type': {
			handler(val) {
				this.getTemplate()
			},
			immediate: true
		},
		'form.object': {
			handler(val) {
				this.getTemplate()
			},
			immediate: true
		}
	},
	mounted() {},
	computed: {
		getSemester() {
			return cusTom.arrayToTree(
				this.semesterInfo.filter((v) => v.campus_id == this.form.campus_id),
				'value',
				'parent_id'
			)
		}
	},
	created() {},
	methods: {
		async getTemplate() {
			if (!this.form.type || !this.form.object) {
				return
			}
			const { data } = await this.$API.cert.certTemplate.all.get({
				tenant_id: this.params.tenant_id,
				campus_id: this.params.campus_id,
				type: this.form.type,
				object: this.form.object
			})
			this.templateMap = data
		},
		//显示
		open(mode = 'add') {
			this.mode = mode
			this.visible = true
			this.form.tenant_id = this.params.tenant_id
			this.form.campus_id = this.params.campus_id
			this.form.semester_id = this.params.semester_id
			return this
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true
					var res = await this.$API.cert.certGroup.save.post(this.form)
					this.isSaveing = false
					if (res.code === 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false
						this.$message.success('操作成功')
					} else {
						this.$alert(res.message, '提示', { type: 'error' })
					}
				}
			})
		},
		//表单注入数据
		setData(data) {
			Object.assign(this.form, data)
		}
	}
}
</script>

<style scoped lang="scss">
.el-select {
	width: 100%;
	max-width: unset;
}
</style>
