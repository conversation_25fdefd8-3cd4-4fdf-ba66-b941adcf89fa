<template>
	<el-container>
		<el-header>
			<el-tabs v-model="activeName" @tab-change="handleClick">
				<el-tab-pane v-for="item in tabs" :name="item.name" :label="item.label" :key="item.name"></el-tab-pane>
			</el-tabs>
		</el-header>
		<el-main>
			<component :is="currComponent.component"></component>
		</el-main>
	</el-container>
</template>

<script>
import AdministrativeScheduling from './AdministrativeScheduling'
import courseManagement from './courseManagement'
import courseSystem from './courseSystem'
import { shallowRef } from 'vue'
export default {
	name: 'layoutTCB',
	components: {
		AdministrativeScheduling,
		courseManagement,
		courseSystem
	},
	data() {
		return {
			activeName: 'courseManagement',
			tabs: [
				{
					label: '学科管理',
					name: 'courseManagement',
					component: shallowRef(courseManagement)
				},
				{
					label: '课程体系',
					name: 'courseSystem',
					component: shallowRef(courseSystem)
				},
				/*{
					label: '行政排课',
					name: 'class',
					component: shallowRef(AdministrativeScheduling)
				}*/
			],
			currComponent: {}
		}
	},
	created() {
		this.currComponent = this.tabs.find((item) => item.name === this.activeName)
	},
	methods: {
		handleClick(name) {
			this.currComponent = this.tabs.find((item) => item.name === name)
		}
	}
}
</script>

<style></style>
