<template>
    <el-container>
        <el-header>
            <el-page-header style="width: 100%" @back="goBack">
                <template #content>
                    <div class="headerContent">
                        <span class="headerTitle">{{ examineTitle }}</span>
                        <ul class="headerTabs">
                            <li class="headerTabsItem" v-for="item in options" :key="item.value"
                                :class="{ active: item.value == currentTab }" @click="selectTabs(item)">{{ item.label
                                }}</li>
                        </ul>
                    </div>
                </template>
                <template #extra>

                </template>
            </el-page-header>
        </el-header>
        <el-main>
            <!-- 考务安排 -->
            <el-tabs v-if="currentTab == 1">
                <el-tab-pane label="时段安排">
                    <timeList :courseList="courseList"></timeList>
                </el-tab-pane>
                <el-tab-pane label="场地安排">
                    <roomList></roomList>
                </el-tab-pane>
                <el-tab-pane label="人员安排">
                    <staffList></staffList>
                </el-tab-pane>
            </el-tabs>
            <!-- 考生安排 -->
            <div v-if="currentTab == 2">
                <studentList></studentList>
            </div>
            <!-- 监考安排 -->
            <div v-if="currentTab == 3">
                <monitorTeacherList></monitorTeacherList>
            </div>
        </el-main>
    </el-container>
</template>

<script setup>
import timeList from './components/timeList/index.vue'
import roomList from './components/roomList/index.vue'
import staffList from './components/staffList/index.vue'
import studentList from './components/studentList/index.vue'
import monitorTeacherList from './components/monitorTeacherList/index.vue'

import cusTom from '@/utils/cusTom'
const { tenantId, campusId, campusInfo, examineTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API

const { go } = useRouter()
const { query } = useRoute()
console.log(query)
const examineTitle = ref(query.name)
const goBack = () => {
    go(-1)
}
const options = [{
    label: '考务安排',
    value: 1
}, {
    label: '考生安排',
    value: 2
}, {
    label: '监考安排',
    value: 3
}]
const currentTab = ref(1)
const selectTabs = (item) => {
    currentTab.value = item.value
}

const courseList = ref([])
const getOne = () => {
    globalPropValue.examine.one.get({
        id: query.id,
        tenant_id: tenantId,
        campus_id: campusId
    }).then(res => {
        if (res.code === 200) {
            courseList.value = res.data.course_list
        }
    })
}
getOne()

</script>
<style lang="scss" scoped>
.headerContent {
    display: flex;

    .headerTitle {
        margin-right: 100px;
        padding: 5px 15px;
    }

    .headerTabs {
        display: flex;

        .headerTabsItem {
            cursor: pointer;
            padding: 5px 15px;
            // 添加过渡效果（颜色/背景色变化）
            transition: color 0.3s ease, background-color 0.3s ease;

            &:hover {
                color: var(--el-color-primary);
            }
        }

        .active {
            color: var(--el-color-primary);
            background: var(--el-color-primary-light-6);
            border-radius: 5px;
            animation: tabActive 0.3s ease;
        }
    }
}

@keyframes tabActive {
    from {
        transform: translateY(2px);
        opacity: 0.6;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}
</style>