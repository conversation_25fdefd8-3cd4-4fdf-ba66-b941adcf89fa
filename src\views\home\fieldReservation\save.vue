<template>
	<el-dialog v-model="visible" title="修改预约" :width="500" destroy-on-close>
		<cusForm ref="formref" v-model="form" :config="formConfig">
			<template #customItem>
				<el-col>
					<el-form-item label="预约日期">
						<el-date-picker
							v-model="form.date"
							type="date"
							:disabled-date="dateOptions.disabledDate"
							placeholder="请选择日期"
							value-format="YYYY-MM-DD"
							style="width: 100%"
						></el-date-picker>
					</el-form-item>
				</el-col>
				<el-col v-if="room_info.open_type === 1">
					<el-form-item label="预约时间段">
						<el-time-select
							v-model="form.startTime"
							:max-time="form.endTime"
							placeholder="开始时间"
							:start="timesData.startTime"
							step="00:30"
							:end="timesData.endTime"
							style="width: 47%; margin-right: 6%"
						/>
						<el-time-select
							v-model="form.endTime"
							:min-time="form.startTime"
							placeholder="结束时间"
							:start="timesData.startTime"
							step="00:30"
							:end="timesData.endTime"
							style="width: 47%"
						/>
					</el-form-item>
				</el-col>
				<el-col v-if="room_info.open_type === 2">
					<el-form-item label="预约课节">
						<el-select
							v-model="form.periods_item_id"
							style="width: 100%; max-width: unset"
							placeholder="请选择预约课节"
							clearable
						>
							<el-option
								v-for="item in room_info.periods_list"
								:key="item.id"
								:label="item.item_name + '(' + item.begin_time + '-' + item.end_time + ')'"
								:value="item.id"
							>
							</el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col>
					<el-form-item label="备注">
						<el-input
							v-model="form.remark"
							type="textarea"
							:rows="3"
							placeholder="请输入备注"
							show-word-limit
							autosize
							style="width: 100%"
						></el-input>
					</el-form-item>
				</el-col>
			</template>
		</cusForm>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" @click="submit">确 定</el-button>
		</template>
	</el-dialog>
</template>

<script>
import cusTom from '@/utils/cusTom'
const { campusId, tenantId } = cusTom.getBaseQuery()

export default {
	emits: ['success'],
	data() {
		return {
			visible: false,
			timesData: {
				startTime: '08:00',
				endTime: '20:00'
			},
			//表单数据
			form: {
				date: null,
				startTime: null,
				endTime: null,
				periods_item_id: null
			},
			room_info: null,
			dateOptions: {
				disabledDate(time) {
					return time.getTime() < Date.now() - 8.64e7 // 只能选择今天及今天之后的日期
				}
			},
			formConfig: {
				labelPosition: 'right',
				size: 'medium',
				formItems: [
					{
						label: '预约主题',
						name: 'title',
						value: null,
						component: 'input',
						options: {
							placeholder: '请输入预约主题'
						},
						rules: [{ required: true, message: '请输入预约主题', trigger: 'blur' }]
					},
					{
						label: '房间',
						name: 'room_id',
						value: null,
						component: 'cascader',
						options: {
							width: '100%',
							all: false,
							placeholder: '请选择开放房间',
							prop: { value: 'id' },
							items: []
						},
						hideHandle: '$.room_id===null'
					},
					{
						label: '参与老师',
						name: 'teachers',
						value: null,
						component: 'cusSelectTeacher',
						multiple: true,
						disabled: false,
						options: {
							placeholder: '请选择参与老师'
						}
					},
					{
						label: '参与学生',
						name: 'students',
						value: null,
						component: 'cusSelectStudent',
						multiple: true,
						disabled: false,
						options: {
							placeholder: '请选择参与学生'
						}
					}
				]
			}
		}
	},
	created() {
		this.getTreeRoom()
	},
	mounted() {},
	methods: {
		//显示
		open(data) {
			//console.log(data, 'open')
			this.visible = true
			this.room_info = data
			console.log(this.room_info)
			this.timesData = {
				startTime: data.allow_start,
				endTime: data.allow_end
			}
			if (data && data.room_id) {
				this.setData(data)
			}
		},
		//表单提交方法
		submit() {
			this.$emit('success', this.form)
			this.visible = false
		},
		//表单注入数据
		setData(data) {
			//console.log(data, 'setData')
			let dateb = data.begin_time.split(' ')
			let datee = data.end_time.split(' ')
			data.date = dateb[0]
			data.startTime = dateb[1]
			data.endTime = datee[1]
			if (data.teachers_list) {
				data.teachers = data.teachers_list.map((item) => {
					return {
						label: item.name,
						id: item.id
					}
				})
			}
			if (data.students_list) {
				data.students = data.students_list.map((item) => {
					return {
						label: item.name,
						id: item.id
					}
				})
			}
			Object.assign(this.form, data)
		},
		async getTreeRoom() {
			const res = await this.$API.fieldReservation.roomtree.get({
				tenant_id: tenantId,
				campus_id: campusId
			})
			this.formConfig.formItems[1].options.items = res.data
			//console.log(res, 'getTreeRoom')
		}
	}
}
</script>

<style></style>
