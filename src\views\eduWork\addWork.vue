<template>
	<el-drawer v-model="visible" :title="titleMap[mode]" size="650px">
		<div style="padding: 15px">
			<el-form
				ref="workFormRef"
				:model="workForm.obj"
				:rules="rules"
				label-position="right"
				label-suffix="："
				label-width="100px"
			>
				<el-row :gutter="10">
					<el-col :span="12">
						<el-form-item label="作业类型" prop="work_type">
							<el-radio-group v-model="workForm.obj.work_type" :disabled="mode === 'edit'">
								<el-radio :label="1">线上</el-radio>
								<el-radio :label="2">线下</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="开启评论" prop="allow_comments">
							<el-radio-group v-model="workForm.obj.allow_comments">
								<el-radio :label="1">是</el-radio>
								<el-radio :label="-1">否</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="10">
					<el-col :span="24">
						<el-form-item label="作业名称" prop="work_name">
							<el-input v-model="workForm.obj.work_name"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="10">
					<el-col :span="12">
						<el-form-item label="所属学科" prop="course_id">
							<el-select v-model="workForm.obj.course_id" placeholder="请选择" filterable clearable>
								<el-option
									v-for="item in getCourse"
									:key="item.id"
									:label="item.course_name"
									:value="item.id"
								></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<!--					          <el-col :span="12">
            <el-form-item label="课程" prop="course_id">
              <el-select v-model="workForm.obj.course_id" placeholder="请选择" filterable clearable>
                <el-option
                  :label="item.course_name"
                  :value="item.id"
                  v-for="item in getCourse"
                  :key="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>-->
				</el-row>
				<el-row :gutter="10">
					<!--					<el-col :span="12">
						<el-form-item label="学年" prop="academic_id">
							<el-select
								v-model="workForm.obj.academic_id"
								placeholder="请选择学年"
								filterable
								clearable
								@change="changeAcademicId"
							>
								<el-option v-for="item in getAcademic_year" :key="item.code" :label="item.name" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>-->
					<el-col :span="12">
						<el-form-item label="学年学期" prop="semester_id">
							<cusCascader
								v-model="workForm.obj.semester_id"
								placeholder="请选择学期"
								:options="getSemester"
							></cusCascader>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="10">
					<!--					<el-col :span="12">
						<el-form-item label="年级" prop="grade_id">
							<el-select
								v-model="workForm.obj.grade_id"
								placeholder="请选择年级"
								filterable
								clearable
								@change="changeGradeId"
							>
								<el-option v-for="item in getGrade" :key="item.id" :label="item.grade_name" :value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>-->
					<el-col :span="24">
						<el-form-item label="班级" prop="class_id">
							<!--							<el-select v-model="workForm.obj.class_id" placeholder="请选择班级" filterable clearable>
								<el-option v-for="item in getClass" :key="item.id" :label="item.class_name" :value="item.id" />
							</el-select>-->
							<el-cascader
								v-model="workForm.obj.class_id"
								:options="myClass.list"
								:props="{ multiple: true, label: 'name', value: 'id', emitPath: false, children: 'class_list' }"
								clearable
							/>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="评分类型" prop="scoring_type">
							<el-select v-model="workForm.obj.scoring_type" placeholder="请选择类型" filterable clearable>
								<el-option label="赋分制" :value="1" />
								<el-option label="通过/不通过制" :value="2" />
								<el-option label="三级评分制" :value="3" />
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="10">
					<el-col v-if="workForm.obj.scoring_type == 2" :span="12">
						<el-form-item label="通过分数" prop="pass_score">
							<el-input v-model="workForm.obj.pass_score" type="number" style="width: 190px"></el-input>
						</el-form-item>
					</el-col>
					<el-col v-if="workForm.obj.scoring_type == 3" :span="24">
						<el-form-item label="评分配置" prop="score_level">
							<el-row v-for="(item, index) in threeLevel.list" :key="index" :gutter="10" style="margin-bottom: 10px">
								<el-col :span="8"><el-input v-model="item.name" placeholder="评分等级"></el-input></el-col>
								<el-col :span="8"
									><el-input v-model="item.conditions[0]" type="number" placeholder="最高分"></el-input
								></el-col>
								<el-col :span="8"
									><el-input v-model="item.conditions[1]" type="number" placeholder="最低分"></el-input
								></el-col>
							</el-row>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="10">
					<el-col :span="24">
						<el-form-item label="附件">
							<scUploadFile v-model="workForm.obj.work_file" accept="*" fileTypeTag="eduWork"></scUploadFile>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</div>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button v-if="mode !== 'show'" type="primary" :loading="isSaveing" @click="save">保 存</el-button>
		</template>
	</el-drawer>
</template>

<script setup>
import { reactive, ref, unref, getCurrentInstance, defineEmits, defineExpose, defineProps, computed, watch } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
import { cloneDeep } from 'lodash'
let visible = ref(false)
const { proxy } = getCurrentInstance()
const props = defineProps({
	semesterInfo: {
		type: Array,
		default: () => {
			return []
		}
	},
	campusId: {
		type: [String, Number]
	},
	tenantId: {
		type: [String, Number]
	},
	myGrade: {
		type: Array,
		default: () => {
			return []
		}
	},
	disciplineOptions: {
		type: Array,
		default: () => {
			return []
		}
	},
	course: {
		type: Array,
		default: () => {
			return []
		}
	}
})
let myClass = reactive({})
const emit = defineEmits(['suc'])
// const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
let mode = ref('add')
const titleMap = reactive({
	add: '新增',
	edit: '编辑',
	show: '查看'
})
const workFormRef = ref(null)
const defaultWorkForm = () => {
	return {
		id: 0,
		tenant_id: props.tenantId,
		campus_id: props.campusId,
		work_type: 1,
		work_name: '',
		discipline_id: 0,
		course_id: '',
		semester_id: '',
		class_id: [],
		scoring_type: '',
		pass_score: 0,
		score_level: null,
		allow_comments: 1,
		work_file: ''
	}
}
const workForm = reactive({
	obj: defaultWorkForm()
})
const workFiles = reactive({
	list: []
})
const validateScore_level = (rule, value, callback) => {
	let flag = true
	threeLevel.list.forEach((item) => {
		if (item.name == '' || item.max == '' || item.min == '') {
			flag = false
		}
	})
	if (flag) {
		callback()
	} else {
		callback(new Error('请完善评分制配置'))
	}
}
const rules = reactive({
	work_type: [{ required: true, message: '请选择作业类型', trigger: 'change' }],
	work_name: [{ required: true, message: '请输入作业名称', trigger: 'blur' }],
	discipline_id: [{ required: true, message: '请选择所属学科', trigger: 'change' }],
	course_id: [{ required: true, message: '请选择课程', trigger: 'change' }],
	academic_id: [{ required: true, message: '请选择学年', trigger: 'change' }],
	semester_id: [{ required: true, message: '请选择学期', trigger: 'change' }],
	grade_id: [{ required: true, message: '请选择年级', trigger: 'change' }],
	class_id: [{ required: true, message: '请选择班级', trigger: 'change' }],
	scoring_type: [{ required: true, message: '请选择评分类型', trigger: 'change' }],
	pass_score: [{ required: true, message: '请输入通过分数', trigger: 'blur' }],
	allow_comments: [{ required: true, message: '请选择是否开启评论', trigger: 'change' }],
	score_level: [{ required: true, validator: validateScore_level, trigger: 'change' }]
})
const defaultThreeLevel = () => {
	return [
		{
			name: 'A',
			conditions: ['', '']
		},
		{
			name: 'B',
			conditions: ['', '']
		},
		{
			name: 'C',
			conditions: ['', '']
		}
	]
}
let threeLevel = reactive({
	list: defaultThreeLevel()
})
let isSaveing = ref(false)
const show = async (type, item) => {
	mode.value = type
	visible.value = true
	if (type === 'add') {
		workForm.obj = defaultWorkForm()
	} else {
		var reqData = { id: item.id, tenant_id: props.tenantId, campus_id: props.campusId }
		let { data } = await proxy.$API.eduWork.one.get(reqData)
		Object.keys(data).forEach((key) => {
			workForm.obj[key] = data[key]
		})
		workForm.obj.class_id = data.class_id.split(',')
		workForm.obj.class_id = workForm.obj.class_id.map(Number)
		console.log(workForm.obj)
		if (data.score_level) {
			threeLevel.list = JSON.parse(data.score_level)
		} else {
			threeLevel.list = defaultThreeLevel()
		}
	}
	getClassData()
}
const save = async () => {
	const form = unref(workFormRef)
	if (!form) {
		return
	}
	form.validate(async (valid) => {
		if (valid) {
			if (workForm.obj.scoring_type == 3) {
				threeLevel.list.forEach((item) => {
					item.conditions[0] = Number(item.conditions[0])
					item.conditions[1] = Number(item.conditions[1])
				})
				workForm.obj.score_level = threeLevel.list
			} else {
				workForm.obj.score_level = null
			}
			let arr = []
			workFiles.list.forEach((item) => {
				arr.push(item.url)
			})
			isSaveing.value = true
			console.log(workForm.obj.class_id)
			try {
				workForm.obj.pass_score = Number(workForm.obj.pass_score)
				let formData = cloneDeep(workForm.obj)
				formData.class_id = formData.class_id.join(',')
				let res = await proxy.$API.eduWork.add.post(formData)
				isSaveing.value = false
				if (res.code === 200) {
					ElMessage.success('新增成功')
					visible.value = false
					emit('suc')
				}
			} catch (err) {
				isSaveing.value = false
			}
		}
	})
}
let getSemester = computed(() => {
	return cusTom.arrayToTree(
		props.semesterInfo.filter((v) => v.campus_id == workForm.obj.campus_id),
		'value',
		'parent_id'
	)
})
let getGrade = computed(() => {
	return props.myGrade.filter(
		(v) => v.parent_id != 0 && v.semester_id == workForm.obj.semester_id && v.campus_id == workForm.obj.campus_id
	)
})
/*let getClass = computed(() => {
	return props.myClass.filter((v) => v.parent_id != 0)
})*/
const getClassData = async () => {
	var res = await proxy.$API.eduGradeClass.class.all.get({
		tenant_id: workForm.obj.tenant_id,
		campus_id: workForm.obj.campus_id,
		semester_id: workForm.obj.semester_id
	})
	myClass.list = res.data
}
let getCourse = computed(() => {
	return props.course.filter((item) => {
		return item.campus_id == workForm.obj.campus_id
	})
})
watch(
	() => workForm.obj.semester_id,
	(val) => {
		getClassData()
	}
)
// watch(
//   () => workForm.obj.grade_id,
//   (val) => {
//     let flag = getClass.value.some(item=>{
//         item.id == workForm.obj.class_id
//     })
//     if(!flag){
//         workForm.obj.class_id = null
//     }
//   }
// )
defineExpose({
	show
})
</script>

<style></style>
