<template>
    <el-dialog v-if="dialogFormVisible" v-model="dialogFormVisible" :title="titleMap[mode1]" width="500">
        <cusForm ref="formref" v-model="form" :config="formConfig" :mode="mode1"></cusForm>
        <template #footer>
            <el-button @click="dialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="confirm">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance, nextTick, onMounted } from 'vue'
import cusTom from '@/utils/cusTom'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, tenantInfo, campusInfo, semesterInfo, paymentTypeMap } = cusTom.getBaseQuery()
// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const props = defineProps(['params'])
const emit = defineEmits(['success'])
let mode1 = ref('add')
const dialogFormVisible = ref(false)
const defaultData = () => {
    return {
        tenant_id: tenantId,
        campus_id: campusId,
        remark: '',
        student_id: '',
        order_id:'',
        mode: null
    }
}
let form = ref(defaultData())
let formConfig = ref({
    labelPosition: 'right',
    size: 'medium',
    formItems: [
        {
            label: '学生',
            name: 'student_id',
            value: null,
            component: 'cusSelectStudent',
            options: {
                placeholder: '请选择学生',
                items: []
            },
            rules: [{ required: true, message: '请选择学生', trigger: 'blur' }]
        },
        {
            label: '备注',
            name: 'remark',
            value: null,
            component: 'textarea',
            options: {
                placeholder: '请输入...',
                items: []
            },
        }
    ]
})

let formref = ref(null)
let titleMap = ref({ add: '新增', edit: '编辑' })
onMounted(async () => {
    
})
const open = async (mode = 'add') => {
    dialogFormVisible.value = true
    mode1.value = mode
    form.value.mode = mode
    if (mode === 'add') {
        form.value = defaultData()
        setTimeout(() => {
            formref.value.resetFields()
        }, 0)
    }
    if (mode === 'edit') {
        nextTick(() => {
            formref.value.resetFields()
        })
    }
}
const confirm = async () => {
    await formref.value.validate()
    let subForm = { ...form.value }
    const handleSuccess = (data) => {
        emit('success', form.value, mode1.value);
        dialogFormVisible.value = false;
        ElMessage({ type: 'success', message: '操作成功' });
    };
    const handleError = (message) => {
        ElMessage({ type: 'error', message });
    };
    if (mode1.value === 'add') {
        subForm.order_id=Number(props.params.order_id)
        subForm.student_id=subForm.student_id[0].id
        const res = await globalPropValue.finance.recruit.orderDetailStudent.post(subForm)
        if (res.code === 200) {
            handleSuccess(res)
        } else {
            handleError(res.message);
        }
    } else if (mode1.value === 'edit') {
        const res = await globalPropValue.finance.recruit.recordEdit.post(subForm)
        if (res.code === 200) {
            handleSuccess(res)
        } else {
            handleError(res.message);
        }
    }
}

//表单注入数据
const setData = (data) => {
    data.student_id=[{
        id:data.student_id,
        label:data.student_name
    }]
    Object.assign(form.value, data)
    // form.value = { ...data }
}

defineExpose({
    dialogFormVisible,
    open,
    setData
})
</script>

<style lang="scss" scoped></style>
