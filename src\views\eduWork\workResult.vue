<template>
	<el-container>
		<el-header style="padding: 5px">
			<div class="detailHeader">
				<el-button text :icon="ArrowLeft" @click="goBack()">返回</el-button>
				<div class="detailHeader_title">{{ workName }}</div>
			</div>
		</el-header>
		<el-main>
			<div v-if="results.workDetail.work_type === 2" style="margin-bottom: 10px">
				<el-button type="primary" plain @click="showImportAction">导入线下成绩</el-button>
			</div>
			<el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleChange">
				<el-tab-pane v-if="results.workDetail.work_type === 1" label="待批阅" :name="1"></el-tab-pane>
				<el-tab-pane v-if="results.workDetail.work_type === 1" label="批阅中" :name="2"></el-tab-pane>
				<el-tab-pane label="已批阅" :name="3"></el-tab-pane>
				<el-tab-pane label="未提交" :name="-1"></el-tab-pane>
			</el-tabs>
			<div v-if="results.list && results.list.length" class="resultContent">
				<div
					v-for="item in results.list"
					:key="item.id"
					class="resultItem"
					:class="item.review_status !== -1 ? 'hover' : ''"
					@click="correct(item)"
				>
					<cus-student-head
						loading="lazy"
						:lazy="true"
						fit="contain"
						style="width: 45px; height: 45px"
						:src="item.answer_user?.user_head"
						preview-teleported
					>
					</cus-student-head>
					<div class="resultInfo">
						<div class="resultInfo_name">{{ item.answer_user?.student_name }} ({{ item.answer_user?.class_name }})</div>
						<div v-if="item.review_status === 3" class="resultInfo_data">
							<span>正确率：{{ (item.right_rate * 100).toFixed(2) }}%</span>
							<el-divider direction="vertical" />
							<span>得分：{{ item.score }}分&nbsp;</span>
							<!-- <el-divider direction="vertical" /> -->
							<span></span>
						</div>
						<div v-if="item.review_status === 3" class="resultInfo_data">
							<span>正确：{{ item.right_num }} 题</span>
							<el-divider direction="vertical" />
							<span>错误：{{ item.error_num }} 题</span>
						</div>
					</div>
				</div>
				<!-- <div class="item" v-for="item in results.list" :key="item.id"></div> -->
			</div>
			<el-empty v-else description="暂无数据" />
		</el-main>
		<correctWork ref="correctWorkRef" @over="geWorkDetail"></correctWork>
		<showImport ref="showImportRef" @over="geWorkDetail"></showImport>
	</el-container>
</template>

<script setup>
import { reactive, ref, computed, watch, getCurrentInstance, onMounted, nextTick, defineExpose } from 'vue'
import cusTom from '@/utils/cusTom'
import correctWork from './correctWork'
import showImport from './showImport'
import cusStudentHead from '@/components/custom/cusStudentHead.vue'

import { ArrowLeft } from '@element-plus/icons-vue'

const { campusId, tenantId, campusInfo, semesterInfo } = cusTom.getBaseQuery()
const { proxy } = getCurrentInstance()
let activeName = ref(1)
let work_id = ref('')
let workName = ref('')
let results = reactive({
	list: [],
	workDetail: {}
})
const correctWorkRef = ref(null)
const showImportRef = ref(null)
const geWorkOne = async () => {
	let { data } = await proxy.$API.eduWork.one.get({
		id: work_id.value,
		tenant_id: tenantId,
		campus_id: campusId
	})
	results.workDetail = data
}

const geWorkDetail = async () => {
	let { data } = await proxy.$API.eduWork.workDetail.get({
		work_id: work_id.value,
		review_status: activeName.value,
		tenant_id: tenantId,
		campus_id: campusId
	})
	results.list = data
}
const handleChange = () => {
	geWorkDetail()
}
const correct = (item) => {
	if (results.workDetail.work_type === 2 && item.review_status != 3) {
		proxy.$message.warning('线下作业不支持批阅,请导入成绩信息')
		return
	}
	if (results.workDetail.work_type === 2 && item.review_status !== 1) {
		proxy.$message.warning('线下作业不支持批阅查看')
		return
	}
	correctWorkRef.value.show(item)
}
const goBack = () => {
	proxy.$router.replace('/eduWork')
}
const showImportAction = () => {
	showImportRef.value.show(results.workDetail)
}
onMounted(async () => {
	work_id.value = proxy.$route.query.id
	workName.value = proxy.$route.query.name
	await geWorkOne()
	if (results.workDetail.work_type === 2) {
		activeName.value = 3
	}
	geWorkDetail()
})
</script>

<style lang="scss" scoped>
.detailHeader {
	display: flex;
	align-items: center;
	font-size: 14px;

	.el-button {
		padding: 8px 5px;
		margin-right: 10px;
		line-height: normal;
	}

	.detailHeader_title {
		color: var(--el-text-color-primary);
		font-weight: bold;
	}
}

.resultContent {
	display: grid;
	grid-template-columns: repeat(auto-fill, 260px);
	grid-gap: 15px;

	.resultItem {
		display: flex;
		align-items: center;
		width: 260px;
		height: auto;
		padding: 10px;
		border: 1px solid var(--el-border-color);
		border-radius: 4px;
		cursor: pointer;

		&.hover {
			cursor: pointer;

			&:hover {
				box-shadow: var(--el-box-shadow);
			}
		}

		.resultInfo {
			margin-left: 10px;

			.resultInfo_data {
				margin-top: 10px;
				color: #909399;
			}
		}
	}
}
</style>
