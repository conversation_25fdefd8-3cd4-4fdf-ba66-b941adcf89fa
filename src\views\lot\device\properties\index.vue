<template>
	<div class="alert">
		<div></div>
		<div v-if="propertiesList && propertiesList.length > 0">
			实时刷新：
			<el-switch v-model="refresh" @change="changeRefresh"></el-switch>
			<el-button
				icon="el-icon-refresh"
				style="margin-left: 10px"
				circle
				:loading="loading"
				@click="getPropertiesInfo"
			></el-button>
		</div>
	</div>

	<el-row v-loading="loading" :gutter="15">
		<el-col v-for="(item, index) in propertiesList" :key="index" :span="8">
			<el-card shadow="hover" class="properties">
				<template #header>
					<div class="card-header">
						<span
							>{{ item.properties_info.name }} <span class="code">({{ item.properties_info.code }})</span></span
						>
						<span class="history">
							<el-tooltip content="点击设置属性值" placement="top">
								<el-icon
									v-if="item.properties_info.access_mode === 2 || item.properties_info.access_mode === 3"
									@click="showSet(item)"
									><el-icon-edit
								/></el-icon>
							</el-tooltip>
							<el-tooltip content="点击查看历史数据" placement="top">
								<el-icon @click="showHistory(item.properties_info)"><el-icon-Clock /></el-icon>
							</el-tooltip>
							<!--							<el-icon
								v-if="item.properties_info.type_spec.type === 'int' || item.properties_info.type_spec.type === 'float'"
								@click="showChart(item.properties_info)"
								><el-icon-Histogram
							/></el-icon>-->
						</span>
					</div>
				</template>
				<div class="properties_value">
					<span v-if="item.last_value" class="value">{{ item.valueFormat }} </span>
					<span v-else class="value"> - </span>
					<span v-if="item.properties_info.type_spec.specs.unit_symbol" class="unit"
						>({{ item.properties_info.type_spec.specs.unit_symbol }})</span
					>
				</div>
				<div class="report_time">
					<span v-if="item.last_value">{{ item.last_value.report_time }}</span>
					<span v-else> - </span>
					<el-divider direction="vertical" />
					<span>{{ item.properties_info.type_spec.type }}</span>
					<el-divider direction="vertical" />
					<span v-if="item.properties_info.access_mode === 1">只读</span>
					<span v-if="item.properties_info.access_mode === 2">读写</span>
				</div>
			</el-card>
		</el-col>
	</el-row>
	<el-empty v-if="!propertiesList || propertiesList.length === 0" description="设备无属性"></el-empty>
	<history-dialog v-if="historyShow" ref="historyDialog" @closed="historyShow = false"></history-dialog>
	<set-dialog v-if="setShow" ref="setDialog" @closed="closedSet"></set-dialog>
</template>
<script>
import historyDialog from './history.vue'
import setDialog from './set.vue'
import { setInterval } from 'core-js/internals/schedulers-fix'

export default {
	name: 'propertiesIndex',
	components: { historyDialog, setDialog },
	props: {
		deviceInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			propertiesList: [],
			loading: false,
			refresh: false,
			timer: null,
			historyShow: false,
			setShow: false
		}
	},
	unmounted() {
		clearInterval(this.timer)
		this.timer = null
		this.refresh = false
	},
	created() {
		clearInterval(this.timer)
		this.timer = null
		this.refresh = false
		if (this.deviceInfo) {
			this.getPropertiesInfo()
		}
	},
	mounted() {},
	methods: {
		changeRefresh(val) {
			if (val === true) {
				//开启实时刷新
				this.timer = setInterval(this.getPropertiesInfo, 60000)
			} else {
				//关闭实时刷新
				clearInterval(this.timer)
				this.timer = null
				this.refresh = false
			}
		},
		showHistory(propertiesInfo) {
			this.historyShow = true
			this.$nextTick(() => {
				let data = {
					propertiesInfo: propertiesInfo,
					device_id: this.deviceInfo.id,
					tenant_id: this.deviceInfo.tenant_id,
					campus_id: this.deviceInfo.campus_id
				}
				this.$refs.historyDialog.show(data)
			})
		},
		showSet(propertiesInfo) {
			this.setShow = true
			this.$nextTick(() => {
				let data = {
					tsl_info: propertiesInfo,
					device_id: this.deviceInfo.id,
					tenant_id: this.deviceInfo.tenant_id,
					campus_id: this.deviceInfo.campus_id
				}
				this.$refs.setDialog.show(data)
			})
		},
		closedSet() {
			this.setShow = false
			setTimeout(() => {
				this.getPropertiesInfo()
			}, 3000)
		},
		getPropertiesInfo() {
			this.loading = true
			// 获取设备属性信息
			this.$LotApi.device.getDeviceProperties
				.get({
					id: this.deviceInfo.id,
					tenant_id: this.deviceInfo.tenant_id,
					campus_id: this.deviceInfo.campus_id
				})
				.then((res) => {
					this.loading = false
					if (res.code === 200) {
						this.propertiesList = res.data?.map((item) => {
							if (item.properties_info.type_spec.specs) {
								item.properties_info.type_spec.specs = JSON.parse(item.properties_info.type_spec.specs)
							}
							item.valueFormat = item.last_value?.value

							if (item.properties_info.type_spec.type === 'int' || item.properties_info.type_spec.type === 'float') {
								item.valueFormat = (item.last_value?.value * 1).toFixed(2)
							}
							if (item.properties_info.type_spec.type === 'bool' && item.last_value && item.last_value.value) {
								item.valueFormat = item.properties_info.type_spec.specs[item.last_value.value * 1]
							}
							if (item.properties_info.type_spec.type === 'enum' && item.last_value && item.last_value.value) {
								item.valueFormat = item.properties_info.type_spec.specs.find(
									(specsItem) => specsItem.value === item.last_value.value * 1
								)?.name
							}

							return item
						})
						if (this.deviceInfo.product_info?.panel_type === 1 && this.deviceInfo.channel !== '') {
							let channelMap = this.deviceInfo.channel.split(',')
							if (channelMap.length > 0) {
								let panelCode = []
								this.propertiesList.map((item) => {
									let codeAry = item.properties_info.code.split('_')
									if (channelMap.includes(codeAry[1])) {
										panelCode.push(item)
									}
								})
								this.propertiesList = panelCode
							}
						}
					}
				})
		}
	}
}
</script>
<style scoped lang="scss">
.card-header {
	display: flex;
	justify-content: space-between;
	font-size: 14px;

	span {
		font-weight: bold;
	}

	span.history {
		font-size: 18px;
		display: flex;
		align-items: center;
		font-weight: 400 !important;
		color: var(--el-text-color-regular);

		.el-icon {
			margin-left: 10px;
			cursor: pointer;
		}
	}
}

.properties_value {
	.value {
		font-size: 20px;
		color: var(--el-text-color-regular);
	}

	.unit {
		font-size: 16px;
		padding-left: 8px;
	}
}

.code {
	font-size: 12px;
	color: var(--el-text-color-secondary);
}

.report_time {
	font-size: 12px;
	color: var(--el-text-color-secondary);
	padding-top: 5px;
	line-height: 30px;
}

:deep(.properties) {
	.el-card__body {
		padding: 20px 20px 10px 20px !important;
	}
}

.alert {
	padding-bottom: 10px;
	display: flex;
	justify-content: space-between;
}
</style>
