<template>
	<el-container>
		<el-header>
			<div class="left-panel">
				<div class="left-panel-search">
					<cusForm ref="formref" v-model="params" :config="searchConfig" :inline="true"> </cusForm>
					<el-button type="primary" icon="el-icon-search" @click="upsearch">搜索</el-button>
					<el-button type="primary" icon="el-icon-refresh" @click="refresh">重置</el-button>
				</div>
			</div>
			<div class="right-panel">
				<el-button type="primary" @click="add_items">新增收支明细</el-button>
				<el-button type="primary" @click="type">收支类型管理</el-button>
			</div>
		</el-header>
		<el-main>
			<scTable ref="table" row-key="id" :params="params" :apiObj="apiObj">
				<el-table-column label="收支项名称" prop="record_name" width="150"></el-table-column>
				<el-table-column label="收支项类型名称" prop="type_name" width="120"></el-table-column>
				<el-table-column label="收支类型" prop="record_type" width="120">
                    <template #default="scope">
						<el-tag v-if="scope.row.record_type === 1" type="success">收入</el-tag>
						<el-tag v-if="scope.row.record_type === 2" type="danger">支出</el-tag>
					</template>
                </el-table-column>
                <el-table-column label="学生姓名" prop="student_name" width="120"></el-table-column>
                <el-table-column label="学生学号" prop="serial_number" width="150"></el-table-column>
				<el-table-column label="金额" prop="amount" width="120"></el-table-column>
				<el-table-column label="支付方式" prop="payment_type" width="150">
					<template #default="{ row }"> {{ formData(typeData, row.payment_type) }} </template>
				</el-table-column>
				<el-table-column label="关联订单号" prop="order_sn" width="180"></el-table-column>
				<el-table-column label="经办人名称" prop="order_manager_name" width="120"></el-table-column>
				<el-table-column label="创建时间" prop="created_at" width="150"></el-table-column>
				<el-table-column label="状态" prop="status" width="150">
					<template #default="scope">
						<el-tag v-if="scope.row.status === 1" type="success">正常</el-tag>
						<el-tag v-if="scope.row.status === 2" type="success">逾期</el-tag>
						<el-tag v-if="scope.row.status === 3" type="danger">作废</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="备注" prop="remark"></el-table-column>
				<el-table-column label="操作" fixed="right" align="center" width="180">
					<template #default="scope">
						<el-button-group>
							<el-button text type="primary" size="small"
								@click="table_edit(scope.row, scope.$index)" v-if="false">编辑</el-button>
							<el-popconfirm title="确定作废吗？" @confirm="table_cancel(scope.row, scope.$index)">
								<template #reference>
									<el-button text type="info" size="small">作废</el-button>
								</template>
							</el-popconfirm>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)" v-if="false">
								<template #reference>
									<el-button text type="danger" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</el-button-group>
					</template>
				</el-table-column>
			</scTable>
		</el-main>
	</el-container>
	<!-- 新增收支明细 -->
	<saveDialog ref="dialog" :params="params" @success="handleSaveSuccess"></saveDialog>
	<!-- 设置收支类别 -->
	<typeDialog ref="dialogType" :params="params"></typeDialog>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick, watch } from 'vue'
import saveDialog from './save.vue'
import cusTom from '@/utils/cusTom'
import typeDialog from './type.vue'
import { ElMessage } from 'element-plus'
const { campusId, tenantId, campusInfo, semesterInfo,paymentTypeMap } = cusTom.getBaseQuery()

// 获取当前组件实例
const instance = getCurrentInstance()
// 访问全局属性
const globalPropValue = instance.appContext.config.globalProperties.$API
const defaultParams = () => {
	return {
		name: null,
		tenant_id: tenantId,
		campus_id: campusId,
		academic_id: null,
		semester_id: null,
		record_type:null
	}
}
let apiObj = ref(globalPropValue.finance.recruit.recordList)
let params = ref(defaultParams())
console.log(params.value.tenant_id)
let searchConfig = ref({
	labelPosition: 'right',
	size: 'medium',
	formItems: [
		{
			label: null,
			name: 'campus_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择校区',
				noClearable: true,
				items: campusInfo.map((v) => {
					return {
						label: v.name,
						value: v.value
					}
				})
			}
		},
		{
			label: null,
			name: 'record_type',
			value: null,
			component: 'select',
			options: {
				placeholder: '请输入收支类型',
				items: [{
                    label: '收入',
                    value: 1
                },
                {
                    label: '支出',
                    value: 2
                }]
			}
		},
		{
			label: null,
			name: 'academic_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学年',
				items: []
			}
		},
		{
			label: null,
			name: 'semester_id',
			value: null,
			component: 'select',
			options: {
				placeholder: '请选择学期',
				items: []
			}
		}
	]
})
const handleSaveSuccess = (data,mode) => {
	if (mode === 'add') {
		upsearch()
	} else {
		table.value.refresh()
	}
}
watch(
	() => params.value.campus_id,
	(val) => {
		params.value.academic_id = null
		searchConfig.value.formItems.find((v) => v.name === 'academic_id').options.items = semesterInfo
			.filter((v) => v.parent_id === 0 && v.campus_id === val)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
watch(
	() => params.value.academic_id,
	() => {
		params.value.semester_id = null
		searchConfig.value.formItems.find((v) => v.name === 'semester_id').options.items = semesterInfo
			.filter(
				(v) => v.parent_id !== 0 && v.parent_id === params.value.academic_id && v.campus_id === params.value.campus_id
			)
			.map((v) => {
				return {
					label: v.name,
					value: v.value
				}
			})
	},
	{ immediate: true }
)
let dialog = ref()
let dialogType = ref()
let table = ref()
let typeData = ref(paymentTypeMap)
// let CampusManagementList = ref(campusInfo)
// 搜索按钮回调
const upsearch = () => {
	table.value.upData(params.value)
}
// 重置按钮回调
const refresh = () => {
	params.value = defaultParams()
	upsearch()
}
// 新增缴费项
const add_items = () => {
	nextTick(() => {
		dialog.value.open('add')
	})
}
const formData = (arr, val) => {
    return arr.find((v) => v.value == val)?.name || val
}
// 缴费类型管理
const type = () => {
	nextTick(() => {
		dialogType.value.open()
	})
}
// 编辑操作
const table_edit = (row) => {
	dialog.value.dialogFormVisible = true
	nextTick(() => {
		dialog.value.open('edit')
		dialog.value.setData(row)
	})
}
// 作废操作
const table_cancel = async (row) => {
	const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.finance.recruit.recordCancal.post(reqData)
	if (res.code === 200) {
		// upsearch()
		table.value.refresh()
		ElMessage({ type: 'success', message: '作废成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
// 删除操作
const table_del = async (row) => {
	const reqData = { id: row.id, tenant_id: params.value.tenant_id, campus_id: params.value.campus_id }
	const res = await globalPropValue.finance.recruit.del.post(reqData)
	if (res.code === 200) {
		// upsearch()
		table.value.refresh()
		ElMessage({ type: 'success', message: '删除成功' })
	} else {
		ElMessage({ type: 'error', message: res.message })
	}
}
</script>

<style lang="scss" scoped></style>
